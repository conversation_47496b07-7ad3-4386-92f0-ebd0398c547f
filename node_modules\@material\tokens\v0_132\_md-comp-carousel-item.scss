//
// !!! THIS FILE WAS AUTOMATICALLY GENERATED !!!
// !!! DO NOT MODIFY IT BY HAND !!!
// Design system display name: Google Material 3
// Design system version: v0.132
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-state';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-state': md-sys-state.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'container-shadow-color': map.get($deps, 'md-sys-color', 'shadow'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-extra-large'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'disabled-container-color': map.get($deps, 'md-sys-color', 'surface'),
    'disabled-container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'disabled-container-opacity': if($exclude-hardcoded-values, null, 0.38),
    'focus-container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'focus-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'focus-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'focus-state-layer-opacity'),
    'hover-container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'hover-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'hover-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'hover-state-layer-opacity'),
    'pressed-container-elevation': map.get($deps, 'md-sys-elevation', 'level0'),
    'pressed-state-layer-color': map.get($deps, 'md-sys-color', 'on-surface'),
    'pressed-state-layer-opacity':
      map.get($deps, 'md-sys-state', 'pressed-state-layer-opacity'),
    'with-outline-disabled-outline-color':
      map.get($deps, 'md-sys-color', 'outline'),
    'with-outline-disabled-outline-opacity':
      if($exclude-hardcoded-values, null, 0.12),
    'with-outline-focus-outline-color':
      map.get($deps, 'md-sys-color', 'on-surface'),
    'with-outline-hover-outline-color':
      map.get($deps, 'md-sys-color', 'outline'),
    'with-outline-outline-color': map.get($deps, 'md-sys-color', 'outline'),
    'with-outline-outline-width': if($exclude-hardcoded-values, null, 1),
    'with-outline-pressed-outline-color':
      map.get($deps, 'md-sys-color', 'outline')
  );
}
