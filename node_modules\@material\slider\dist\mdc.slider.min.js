!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("@material/slider",[],e):"object"==typeof exports?exports.slider=e():(t.mdc=t.mdc||{},t.mdc.slider=e())}(this,function(){return n={},i.m=r={0:function(t,e,r){"use strict";(function(t){}).call(this,r(20))},1:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapResourceUrl=e.isResourceUrl=e.createResourceUrl=e.TrustedResourceUrl=void 0,r(0);var i=r(4),a=r(9),o=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(t,e){this.privateDoNotAccessOrElseWrappedResourceUrl=t}var s=window.TrustedScriptURL;e.TrustedResourceUrl=null!=s?s:o,e.createResourceUrl=function(t){var e,r=t,n=null===(e=(0,a.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScriptURL(r);return null!=n?n:new o(r,i.secretToken)},e.isResourceUrl=function(t){return t instanceof e.TrustedResourceUrl},e.unwrapResourceUrl=function(t){var e;if(null===(e=(0,a.getTrustedTypes)())||void 0===e?void 0:e.isScriptURL(t))return t;if(t instanceof o)return t.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},10:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyle=e.isStyle=e.createStyle=e.SafeStyle=void 0,r(0);function a(){}var o=r(4);e.SafeStyle=a;var s,u=(i(l,s=a),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},l);function l(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=t,r}e.createStyle=function(t){return new u(t,o.secretToken)},e.isStyle=function(t){return t instanceof u},e.unwrapStyle=function(t){if(t instanceof u)return t.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},109:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.events=e.attributes=e.numbers=e.cssClasses=void 0,e.cssClasses={DISABLED:"mdc-slider--disabled",DISCRETE:"mdc-slider--discrete",INPUT:"mdc-slider__input",RANGE:"mdc-slider--range",THUMB:"mdc-slider__thumb",THUMB_FOCUSED:"mdc-slider__thumb--focused",THUMB_KNOB:"mdc-slider__thumb-knob",THUMB_TOP:"mdc-slider__thumb--top",THUMB_WITH_INDICATOR:"mdc-slider__thumb--with-indicator",TICK_MARKS:"mdc-slider--tick-marks",TICK_MARKS_CONTAINER:"mdc-slider__tick-marks",TICK_MARK_ACTIVE:"mdc-slider__tick-mark--active",TICK_MARK_INACTIVE:"mdc-slider__tick-mark--inactive",TRACK:"mdc-slider__track",TRACK_ACTIVE:"mdc-slider__track--active_fill",VALUE_INDICATOR_CONTAINER:"mdc-slider__value-indicator-container",VALUE_INDICATOR_TEXT:"mdc-slider__value-indicator-text"},e.numbers={STEP_SIZE:1,MIN_RANGE:0,THUMB_UPDATE_MIN_PX:5},e.attributes={ARIA_VALUETEXT:"aria-valuetext",INPUT_DISABLED:"disabled",INPUT_MIN:"min",INPUT_MAX:"max",INPUT_VALUE:"value",INPUT_STEP:"step",DATA_MIN_RANGE:"data-min-range"},e.events={CHANGE:"MDCSlider:change",INPUT:"MDCSlider:input"},e.strings={VAR_VALUE_INDICATOR_CARET_LEFT:"--slider-value-indicator-caret-left",VAR_VALUE_INDICATOR_CARET_RIGHT:"--slider-value-indicator-caret-right",VAR_VALUE_INDICATOR_CARET_TRANSFORM:"--slider-value-indicator-caret-transform",VAR_VALUE_INDICATOR_CONTAINER_LEFT:"--slider-value-indicator-container-left",VAR_VALUE_INDICATOR_CONTAINER_RIGHT:"--slider-value-indicator-container-right",VAR_VALUE_INDICATOR_CONTAINER_TRANSFORM:"--slider-value-indicator-container-transform"}},11:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributePolicyAction=e.SanitizerTable=void 0;var n,i,a=(o.prototype.isAllowedElement=function(t){return"form"!==t.toLowerCase()&&(this.allowedElements.has(t)||this.elementPolicies.has(t))},o.prototype.getAttributePolicy=function(t,e){var r=this.elementPolicies.get(e);return(null==r?void 0:r.has(t))?r.get(t):this.allowedGlobalAttributes.has(t)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(t)||{policyAction:n.DROP}},o);function o(t,e,r,n){this.allowedElements=t,this.elementPolicies=e,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}e.SanitizerTable=a,(i=n=e.AttributePolicyAction||(e.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},110:function(t,e,r){"use strict";var n,i;Object.defineProperty(e,"__esModule",{value:!0}),e.Thumb=e.TickMark=void 0,(n=e.TickMark||(e.TickMark={}))[n.ACTIVE=0]="ACTIVE",n[n.INACTIVE=1]="INACTIVE",(i=e.Thumb||(e.Thumb={}))[i.START=1]="START",i[i.END=2]="END"},12:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.isStyleSheet=e.createStyleSheet=e.SafeStyleSheet=void 0,r(0);function a(){}var o=r(4);e.SafeStyleSheet=a;var s,u=(i(l,s=a),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},l);function l(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=t,r}e.createStyleSheet=function(t){return new u(t,o.secretToken)},e.isStyleSheet=function(t){return t instanceof u},e.unwrapStyleSheet=function(t){if(t instanceof u)return t.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},13:function(t,e,r){"use strict";var i=this&&this.__makeTemplateObject||function(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||0<e--)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o},o=this&&this.__spreadArray||function(t,e){for(var r=0,n=e.length,i=t.length;r<n;r++,i++)t[i]=e[r];return t};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCComponent=void 0;var s=r(17),u=r(18),n=r(7);var l,c,d=(p.attachTo=function(t){return new p(t,new n.MDCFoundation({}))},p.prototype.initialize=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},p.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},p.prototype.initialSyncWithDOM=function(){},p.prototype.destroy=function(){this.foundation.destroy()},p.prototype.listen=function(t,e,r){this.root.addEventListener(t,e,r)},p.prototype.unlisten=function(t,e,r){this.root.removeEventListener(t,e,r)},p.prototype.emit=function(t,e,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(t,{bubbles:r,detail:e}):(n=document.createEvent("CustomEvent")).initCustomEvent(t,r,!1,e),this.root.dispatchEvent(n)},p.prototype.safeSetAttribute=function(t,e,r){if("tabindex"===e.toLowerCase())t.tabIndex=Number(r);else if(0===e.indexOf("data-")){var n=function(t){return String(t).replace(/\-([a-z])/g,function(t,e){return e.toUpperCase()})}(e.replace(/^data-/,""));t.dataset[n]=r}else u.safeElement.setPrefixedAttribute([s.safeAttrPrefix(l=l||i(["aria-"],["aria-"])),s.safeAttrPrefix(c=c||i(["role"],["role"]))],t,e,r)},p);function p(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=t,this.initialize.apply(this,o([],a(r))),this.foundation=void 0===e?this.getDefaultFoundation():e,this.foundation.init(),this.initialSyncWithDOM()}e.MDCComponent=d,e.default=d},14:function(t,e,r){"use strict";var h=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||0<e--)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.HtmlSanitizerImpl=void 0,r(0);var n=r(2),i=r(4),f=r(3),u=r(23),m=r(24),a=r(16),v=r(11),o=(s.prototype.sanitizeAssertUnchanged=function(t){this.changes=[];var e=this.sanitize(t);if(0===this.changes.length)return e;throw new Error("")},s.prototype.sanitize=function(t){var e=document.createElement("span");e.appendChild(this.sanitizeToFragment(t));var r=(new XMLSerializer).serializeToString(e);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},s.prototype.sanitizeToFragment=function(t){for(var e=this,r=(0,u.createInertFragment)(t),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(t){return e.nodeFilter(t)},!1),i=n.nextNode(),a=document.createDocumentFragment(),o=a;null!==i;){var s=void 0;if((0,m.isText)(i))s=this.sanitizeTextNode(i);else{if(!(0,m.isElement)(i))throw new Error("Node is not of type text or element");s=this.sanitizeElementNode(i)}if(o.appendChild(s),i=n.firstChild())o=s;else for(;!(i=n.nextSibling())&&(i=n.parentNode());)o=o.parentNode}return a},s.prototype.sanitizeTextNode=function(t){return document.createTextNode(t.data)},s.prototype.sanitizeElementNode=function(t){var e,r,n=(0,m.getNodeName)(t),i=document.createElement(n),a=t.attributes;try{for(var o=h(a),s=o.next();!s.done;s=o.next()){var u=s.value,l=u.name,c=u.value,d=this.sanitizerTable.getAttributePolicy(l,n);if(this.satisfiesAllConditions(d.conditions,a))switch(d.policyAction){case v.AttributePolicyAction.KEEP:i.setAttribute(l,c);break;case v.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var p=(0,f.restrictivelySanitizeUrl)(c);p!==c&&this.recordChange("Url in attribute ".concat(l,' was modified during sanitization. Original url:"').concat(c,'" was sanitized to: "').concat(p,'"')),i.setAttribute(l,p);break;case v.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(l,c.toLowerCase());break;case v.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(l,c);break;case v.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(l," was dropped"));break;default:T(d.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(l,"."))}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return i},s.prototype.nodeFilter=function(t){if((0,m.isText)(t))return NodeFilter.FILTER_ACCEPT;if(!(0,m.isElement)(t))return NodeFilter.FILTER_REJECT;var e=(0,m.getNodeName)(t);return null===e?(this.recordChange("Node name was null for node: ".concat(t)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(e)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(e," was dropped")),NodeFilter.FILTER_REJECT)},s.prototype.recordChange=function(t){0===this.changes.length&&this.changes.push("")},s.prototype.satisfiesAllConditions=function(t,e){var r,n,i;if(!t)return!0;try{for(var a=h(t),o=a.next();!o.done;o=a.next()){var s=d(o.value,2),u=s[0],l=s[1],c=null===(i=e.getNamedItem(u))||void 0===i?void 0:i.value;if(c&&!l.has(c))return!1}}catch(t){r={error:t}}finally{try{o&&!o.done&&(n=a.return)&&n.call(a)}finally{if(r)throw r.error}}return!0},s);function s(t,e){this.sanitizerTable=t,this.changes=[],(0,i.ensureTokenIsValid)(e)}e.HtmlSanitizerImpl=o;var l=function(){return new o(a.defaultSanitizerTable,i.secretToken)}();function T(t,e){throw void 0===e&&(e="unexpected value ".concat(t,"!")),new Error(e)}e.sanitizeHtml=function(t){return l.sanitize(t)},e.sanitizeHtmlAssertUnchanged=function(t){return l.sanitizeAssertUnchanged(t)},e.sanitizeHtmlToFragment=function(t){return l.sanitizeToFragment(t)}},15:function(t,e,r){"use strict";var i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||0<e--)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o},a=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,i=0,a=e.length;i<a;i++)!n&&i in e||((n=n||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.setPrefixedAttribute=e.buildPrefixedAttributeSetter=e.insertAdjacentHtml=e.setCssText=e.setOuterHtml=e.setInnerHtml=void 0;var o=r(8),s=r(2),n=r(10);function u(t,e,r,n){if(0===t.length)throw new Error("No prefixes are provided");var i=t.map(function(t){return(0,o.unwrapAttributePrefix)(t)}),a=r.toLowerCase();if(i.every(function(t){return 0!==a.indexOf(t)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));e.setAttribute(r,n)}function l(t){if("script"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}e.setInnerHtml=function(t,e){!function(t){return void 0!==t.tagName}(t)||l(t),t.innerHTML=(0,s.unwrapHtml)(e)},e.setOuterHtml=function(t,e){var r=t.parentElement;null!==r&&l(r),t.outerHTML=(0,s.unwrapHtml)(e)},e.setCssText=function(t,e){t.style.cssText=(0,n.unwrapStyle)(e)},e.insertAdjacentHtml=function(t,e,r){var n="beforebegin"===e||"afterend"===e?t.parentElement:t;null!==n&&l(n),t.insertAdjacentHTML(e,(0,s.unwrapHtml)(r))},e.buildPrefixedAttributeSetter=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=a([t],i(e),!1);return function(t,e,r){u(n,t,e,r)}},e.setPrefixedAttribute=u},16:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defaultSanitizerTable=void 0;var n=r(11);e.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},163:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__assign||function(){return(a=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCSliderFoundation=void 0;var d,o=r(63),p=r(57),s=r(7),l=r(109),h=r(110);(d=d||{}).SLIDER_UPDATE="slider_update";var u,f="undefined"!=typeof window,c=(u=s.MDCFoundation,i(m,u),Object.defineProperty(m,"defaultAdapter",{get:function(){return{hasClass:function(){return!1},addClass:function(){},removeClass:function(){},addThumbClass:function(){},removeThumbClass:function(){},getAttribute:function(){return null},getInputValue:function(){return""},setInputValue:function(){},getInputAttribute:function(){return null},setInputAttribute:function(){return null},removeInputAttribute:function(){return null},focusInput:function(){},isInputFocused:function(){return!1},shouldHideFocusStylesForPointerEvents:function(){return!1},getThumbKnobWidth:function(){return 0},getValueIndicatorContainerWidth:function(){return 0},getThumbBoundingClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},getBoundingClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},isRTL:function(){return!1},setThumbStyleProperty:function(){},removeThumbStyleProperty:function(){},setTrackActiveStyleProperty:function(){},removeTrackActiveStyleProperty:function(){},setValueIndicatorText:function(){},getValueToAriaValueTextFn:function(){return null},updateTickMarks:function(){},setPointerCapture:function(){},emitChangeEvent:function(){},emitInputEvent:function(){},emitDragStartEvent:function(){},emitDragEndEvent:function(){},registerEventHandler:function(){},deregisterEventHandler:function(){},registerThumbEventHandler:function(){},deregisterThumbEventHandler:function(){},registerInputEventHandler:function(){},deregisterInputEventHandler:function(){},registerBodyEventHandler:function(){},deregisterBodyEventHandler:function(){},registerWindowEventHandler:function(){},deregisterWindowEventHandler:function(){}}},enumerable:!1,configurable:!0}),m.prototype.init=function(){var t=this;this.isDisabled=this.adapter.hasClass(l.cssClasses.DISABLED),this.isDiscrete=this.adapter.hasClass(l.cssClasses.DISCRETE),this.hasTickMarks=this.adapter.hasClass(l.cssClasses.TICK_MARKS),this.isRange=this.adapter.hasClass(l.cssClasses.RANGE);var e=this.convertAttributeValueToNumber(this.adapter.getInputAttribute(l.attributes.INPUT_MIN,this.isRange?h.Thumb.START:h.Thumb.END),l.attributes.INPUT_MIN),r=this.convertAttributeValueToNumber(this.adapter.getInputAttribute(l.attributes.INPUT_MAX,h.Thumb.END),l.attributes.INPUT_MAX),n=this.convertAttributeValueToNumber(this.adapter.getInputAttribute(l.attributes.INPUT_VALUE,h.Thumb.END),l.attributes.INPUT_VALUE),i=this.isRange?this.convertAttributeValueToNumber(this.adapter.getInputAttribute(l.attributes.INPUT_VALUE,h.Thumb.START),l.attributes.INPUT_VALUE):e,a=this.adapter.getInputAttribute(l.attributes.INPUT_STEP,h.Thumb.END),o=a?this.convertAttributeValueToNumber(a,l.attributes.INPUT_STEP):this.step,s=this.adapter.getAttribute(l.attributes.DATA_MIN_RANGE),u=s?this.convertAttributeValueToNumber(s,l.attributes.DATA_MIN_RANGE):this.minRange;this.validateProperties({min:e,max:r,value:n,valueStart:i,step:o,minRange:u}),this.min=e,this.max=r,this.value=n,this.valueStart=i,this.step=o,this.minRange=u,this.numDecimalPlaces=v(this.step),this.valueBeforeDownEvent=n,this.valueStartBeforeDownEvent=i,this.mousedownOrTouchstartListener=this.handleMousedownOrTouchstart.bind(this),this.moveListener=this.handleMove.bind(this),this.pointerdownListener=this.handlePointerdown.bind(this),this.pointerupListener=this.handlePointerup.bind(this),this.thumbMouseenterListener=this.handleThumbMouseenter.bind(this),this.thumbMouseleaveListener=this.handleThumbMouseleave.bind(this),this.inputStartChangeListener=function(){t.handleInputChange(h.Thumb.START)},this.inputEndChangeListener=function(){t.handleInputChange(h.Thumb.END)},this.inputStartFocusListener=function(){t.handleInputFocus(h.Thumb.START)},this.inputEndFocusListener=function(){t.handleInputFocus(h.Thumb.END)},this.inputStartBlurListener=function(){t.handleInputBlur(h.Thumb.START)},this.inputEndBlurListener=function(){t.handleInputBlur(h.Thumb.END)},this.resizeListener=this.handleResize.bind(this),this.registerEventHandlers()},m.prototype.destroy=function(){this.deregisterEventHandlers()},m.prototype.setMin=function(t){this.min=t,this.isRange||(this.valueStart=t),this.updateUI()},m.prototype.setMax=function(t){this.max=t,this.updateUI()},m.prototype.getMin=function(){return this.min},m.prototype.getMax=function(){return this.max},m.prototype.getValue=function(){return this.value},m.prototype.setValue=function(t){if(this.isRange&&t<this.valueStart+this.minRange)throw new Error("end thumb value ("+t+") must be >= start thumb value ("+this.valueStart+") + min range ("+this.minRange+")");this.updateValue(t,h.Thumb.END)},m.prototype.getValueStart=function(){if(!this.isRange)throw new Error("`valueStart` is only applicable for range sliders.");return this.valueStart},m.prototype.setValueStart=function(t){if(!this.isRange)throw new Error("`valueStart` is only applicable for range sliders.");if(this.isRange&&t>this.value-this.minRange)throw new Error("start thumb value ("+t+") must be <= end thumb value ("+this.value+") - min range ("+this.minRange+")");this.updateValue(t,h.Thumb.START)},m.prototype.setStep=function(t){this.step=t,this.numDecimalPlaces=v(t),this.updateUI()},m.prototype.setMinRange=function(t){if(!this.isRange)throw new Error("`minRange` is only applicable for range sliders.");if(t<0)throw new Error("`minRange` must be non-negative. Current value: "+t);if(this.value-this.valueStart<t)throw new Error("start thumb value ("+this.valueStart+") and end thumb value ("+this.value+") must differ by at least "+t+".");this.minRange=t},m.prototype.setIsDiscrete=function(t){this.isDiscrete=t,this.updateValueIndicatorUI(),this.updateTickMarksUI()},m.prototype.getStep=function(){return this.step},m.prototype.getMinRange=function(){if(!this.isRange)throw new Error("`minRange` is only applicable for range sliders.");return this.minRange},m.prototype.setHasTickMarks=function(t){this.hasTickMarks=t,this.updateTickMarksUI()},m.prototype.getDisabled=function(){return this.isDisabled},m.prototype.setDisabled=function(t){(this.isDisabled=t)?(this.adapter.addClass(l.cssClasses.DISABLED),this.isRange&&this.adapter.setInputAttribute(l.attributes.INPUT_DISABLED,"",h.Thumb.START),this.adapter.setInputAttribute(l.attributes.INPUT_DISABLED,"",h.Thumb.END)):(this.adapter.removeClass(l.cssClasses.DISABLED),this.isRange&&this.adapter.removeInputAttribute(l.attributes.INPUT_DISABLED,h.Thumb.START),this.adapter.removeInputAttribute(l.attributes.INPUT_DISABLED,h.Thumb.END))},m.prototype.getIsRange=function(){return this.isRange},m.prototype.layout=function(t){var e=(void 0===t?{}:t).skipUpdateUI;this.isRange&&(this.startThumbKnobWidth=this.adapter.getThumbKnobWidth(h.Thumb.START),this.endThumbKnobWidth=this.adapter.getThumbKnobWidth(h.Thumb.END)),e||this.updateUI()},m.prototype.handleResize=function(){this.layout()},m.prototype.handleDown=function(t){if(!this.isDisabled){this.valueStartBeforeDownEvent=this.valueStart,this.valueBeforeDownEvent=this.value;var e=null!=t.clientX?t.clientX:t.targetTouches[0].clientX;this.downEventClientX=e;var r=this.mapClientXOnSliderScale(e);this.thumb=this.getThumbFromDownEvent(e,r),null!==this.thumb&&(this.handleDragStart(t,r,this.thumb),this.updateValue(r,this.thumb,{emitInputEvent:!0}))}},m.prototype.handleMove=function(t){if(!this.isDisabled){t.preventDefault();var e=null!=t.clientX?t.clientX:t.targetTouches[0].clientX,r=null!=this.thumb;if(this.thumb=this.getThumbFromMoveEvent(e),null!==this.thumb){var n=this.mapClientXOnSliderScale(e);r||(this.handleDragStart(t,n,this.thumb),this.adapter.emitDragStartEvent(n,this.thumb)),this.updateValue(n,this.thumb,{emitInputEvent:!0})}}},m.prototype.handleUp=function(){var t,e;if(!this.isDisabled&&null!==this.thumb){(null===(e=(t=this.adapter).shouldHideFocusStylesForPointerEvents)||void 0===e?void 0:e.call(t))&&this.handleInputBlur(this.thumb);var r=this.thumb===h.Thumb.START?this.valueStartBeforeDownEvent:this.valueBeforeDownEvent,n=this.thumb===h.Thumb.START?this.valueStart:this.value;r!==n&&this.adapter.emitChangeEvent(n,this.thumb),this.adapter.emitDragEndEvent(n,this.thumb),this.thumb=null}},m.prototype.handleThumbMouseenter=function(){this.isDiscrete&&this.isRange&&(this.adapter.addThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,h.Thumb.START),this.adapter.addThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,h.Thumb.END))},m.prototype.handleThumbMouseleave=function(){var t,e;this.isDiscrete&&this.isRange&&((null===(e=(t=this.adapter).shouldHideFocusStylesForPointerEvents)||void 0===e||!e.call(t))&&(this.adapter.isInputFocused(h.Thumb.START)||this.adapter.isInputFocused(h.Thumb.END))||this.thumb||(this.adapter.removeThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,h.Thumb.START),this.adapter.removeThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,h.Thumb.END)))},m.prototype.handleMousedownOrTouchstart=function(t){var e=this,r="mousedown"===t.type?"mousemove":"touchmove";function n(){e.handleUp(),e.adapter.deregisterBodyEventHandler(r,e.moveListener),e.adapter.deregisterEventHandler("mouseup",n),e.adapter.deregisterEventHandler("touchend",n)}this.adapter.registerBodyEventHandler(r,this.moveListener),this.adapter.registerBodyEventHandler("mouseup",n),this.adapter.registerBodyEventHandler("touchend",n),this.handleDown(t)},m.prototype.handlePointerdown=function(t){0===t.button&&(null!=t.pointerId&&this.adapter.setPointerCapture(t.pointerId),this.adapter.registerEventHandler("pointermove",this.moveListener),this.handleDown(t))},m.prototype.handleInputChange=function(t){var e=Number(this.adapter.getInputValue(t));t===h.Thumb.START?this.setValueStart(e):this.setValue(e),this.adapter.emitChangeEvent(t===h.Thumb.START?this.valueStart:this.value,t),this.adapter.emitInputEvent(t===h.Thumb.START?this.valueStart:this.value,t)},m.prototype.handleInputFocus=function(t){if(this.adapter.addThumbClass(l.cssClasses.THUMB_FOCUSED,t),this.isDiscrete&&(this.adapter.addThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,t),this.isRange)){var e=t===h.Thumb.START?h.Thumb.END:h.Thumb.START;this.adapter.addThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,e)}},m.prototype.handleInputBlur=function(t){if(this.adapter.removeThumbClass(l.cssClasses.THUMB_FOCUSED,t),this.isDiscrete&&(this.adapter.removeThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,t),this.isRange)){var e=t===h.Thumb.START?h.Thumb.END:h.Thumb.START;this.adapter.removeThumbClass(l.cssClasses.THUMB_WITH_INDICATOR,e)}},m.prototype.handleDragStart=function(t,e,r){var n,i;this.adapter.emitDragStartEvent(e,r),this.adapter.focusInput(r),(null===(i=(n=this.adapter).shouldHideFocusStylesForPointerEvents)||void 0===i?void 0:i.call(n))&&this.handleInputFocus(r),t.preventDefault()},m.prototype.getThumbFromDownEvent=function(t,e){if(!this.isRange)return h.Thumb.END;var r=this.adapter.getThumbBoundingClientRect(h.Thumb.START),n=this.adapter.getThumbBoundingClientRect(h.Thumb.END),i=t>=r.left&&t<=r.right,a=t>=n.left&&t<=n.right;return i&&a?null:i?h.Thumb.START:a?h.Thumb.END:e<this.valueStart?h.Thumb.START:e>this.value?h.Thumb.END:e-this.valueStart<=this.value-e?h.Thumb.START:h.Thumb.END},m.prototype.getThumbFromMoveEvent=function(t){if(null!==this.thumb)return this.thumb;if(null===this.downEventClientX)throw new Error("`downEventClientX` is null after move event.");return Math.abs(this.downEventClientX-t)<l.numbers.THUMB_UPDATE_MIN_PX?this.thumb:t<this.downEventClientX?this.adapter.isRTL()?h.Thumb.END:h.Thumb.START:this.adapter.isRTL()?h.Thumb.START:h.Thumb.END},m.prototype.updateUI=function(t){t?this.updateThumbAndInputAttributes(t):(this.updateThumbAndInputAttributes(h.Thumb.START),this.updateThumbAndInputAttributes(h.Thumb.END)),this.updateThumbAndTrackUI(t),this.updateValueIndicatorUI(t),this.updateTickMarksUI()},m.prototype.updateThumbAndInputAttributes=function(t){if(t){var e=this.isRange&&t===h.Thumb.START?this.valueStart:this.value,r=String(e);this.adapter.setInputAttribute(l.attributes.INPUT_VALUE,r,t),this.isRange&&t===h.Thumb.START?this.adapter.setInputAttribute(l.attributes.INPUT_MIN,String(e+this.minRange),h.Thumb.END):this.isRange&&t===h.Thumb.END&&this.adapter.setInputAttribute(l.attributes.INPUT_MAX,String(e-this.minRange),h.Thumb.START),this.adapter.getInputValue(t)!==r&&this.adapter.setInputValue(r,t);var n=this.adapter.getValueToAriaValueTextFn();n&&this.adapter.setInputAttribute(l.attributes.ARIA_VALUETEXT,n(e,t),t)}},m.prototype.updateValueIndicatorUI=function(t){if(this.isDiscrete){var e=this.isRange&&t===h.Thumb.START?this.valueStart:this.value;this.adapter.setValueIndicatorText(e,t===h.Thumb.START?h.Thumb.START:h.Thumb.END),!t&&this.isRange&&this.adapter.setValueIndicatorText(this.valueStart,h.Thumb.START)}},m.prototype.updateTickMarksUI=function(){if(this.isDiscrete&&this.hasTickMarks){var t=(this.valueStart-this.min)/this.step,e=(this.value-this.valueStart)/this.step+1,r=(this.max-this.value)/this.step,n=Array.from({length:t}).fill(h.TickMark.INACTIVE),i=Array.from({length:e}).fill(h.TickMark.ACTIVE),a=Array.from({length:r}).fill(h.TickMark.INACTIVE);this.adapter.updateTickMarks(n.concat(i).concat(a))}},m.prototype.mapClientXOnSliderScale=function(t){var e=this.adapter.getBoundingClientRect(),r=(t-e.left)/e.width;this.adapter.isRTL()&&(r=1-r);var n=this.min+r*(this.max-this.min);return n===this.max||n===this.min?n:Number(this.quantize(n).toFixed(this.numDecimalPlaces))},m.prototype.quantize=function(t){var e=Math.round((t-this.min)/this.step);return this.min+e*this.step},m.prototype.updateValue=function(t,e,r){var n=(void 0===r?{}:r).emitInputEvent;if(t=this.clampValue(t,e),this.isRange&&e===h.Thumb.START){if(this.valueStart===t)return;this.valueStart=t}else{if(this.value===t)return;this.value=t}this.updateUI(e),n&&this.adapter.emitInputEvent(e===h.Thumb.START?this.valueStart:this.value,e)},m.prototype.clampValue=function(t,e){return t=Math.min(Math.max(t,this.min),this.max),this.isRange&&e===h.Thumb.START&&t>this.value-this.minRange?this.value-this.minRange:this.isRange&&e===h.Thumb.END&&t<this.valueStart+this.minRange?this.valueStart+this.minRange:t},m.prototype.updateThumbAndTrackUI=function(r){var n=this,t=this.max,e=this.min,i=this.adapter.getBoundingClientRect(),a=(this.value-this.valueStart)/(t-e),o=a*i.width,s=this.adapter.isRTL(),u=f?p.getCorrectPropertyName(window,"transform"):"transform";if(this.isRange){var l=this.adapter.isRTL()?(t-this.value)/(t-e)*i.width:(this.valueStart-e)/(t-e)*i.width,c=l+o;this.animFrame.request(d.SLIDER_UPDATE,function(){!s&&r===h.Thumb.START||s&&r!==h.Thumb.START?(n.adapter.setTrackActiveStyleProperty("transform-origin","right"),n.adapter.setTrackActiveStyleProperty("left","auto"),n.adapter.setTrackActiveStyleProperty("right",i.width-c+"px")):(n.adapter.setTrackActiveStyleProperty("transform-origin","left"),n.adapter.setTrackActiveStyleProperty("right","auto"),n.adapter.setTrackActiveStyleProperty("left",l+"px")),n.adapter.setTrackActiveStyleProperty(u,"scaleX("+a+")");var t=s?c:l,e=n.adapter.isRTL()?l:c;r!==h.Thumb.START&&r&&n.initialStylesRemoved||(n.adapter.setThumbStyleProperty(u,"translateX("+t+"px)",h.Thumb.START),n.alignValueIndicator(h.Thumb.START,t)),r!==h.Thumb.END&&r&&n.initialStylesRemoved||(n.adapter.setThumbStyleProperty(u,"translateX("+e+"px)",h.Thumb.END),n.alignValueIndicator(h.Thumb.END,e)),n.removeInitialStyles(s),n.updateOverlappingThumbsUI(t,e,r)})}else this.animFrame.request(d.SLIDER_UPDATE,function(){var t=s?i.width-o:o;n.adapter.setThumbStyleProperty(u,"translateX("+t+"px)",h.Thumb.END),n.alignValueIndicator(h.Thumb.END,t),n.adapter.setTrackActiveStyleProperty(u,"scaleX("+a+")"),n.removeInitialStyles(s)})},m.prototype.alignValueIndicator=function(t,e){if(this.isDiscrete){var r=this.adapter.getThumbBoundingClientRect(t).width/2,n=this.adapter.getValueIndicatorContainerWidth(t),i=this.adapter.getBoundingClientRect().width;e+r<n/2?(this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_LEFT,r+"px",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_RIGHT,"auto",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_TRANSFORM,"translateX(-50%)",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_LEFT,"0",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_RIGHT,"auto",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_TRANSFORM,"none",t)):i-e+r<n/2?(this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_LEFT,"auto",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_RIGHT,r+"px",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_TRANSFORM,"translateX(50%)",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_LEFT,"auto",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_RIGHT,"0",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_TRANSFORM,"none",t)):(this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_LEFT,"50%",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_RIGHT,"auto",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CARET_TRANSFORM,"translateX(-50%)",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_LEFT,"50%",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_RIGHT,"auto",t),this.adapter.setThumbStyleProperty(l.strings.VAR_VALUE_INDICATOR_CONTAINER_TRANSFORM,"translateX(-50%)",t))}},m.prototype.removeInitialStyles=function(t){if(!this.initialStylesRemoved){var e=t?"right":"left";this.adapter.removeThumbStyleProperty(e,h.Thumb.END),this.isRange&&this.adapter.removeThumbStyleProperty(e,h.Thumb.START),this.initialStylesRemoved=!0,this.resetTrackAndThumbAnimation()}},m.prototype.resetTrackAndThumbAnimation=function(){var t=this;if(this.isDiscrete){var e=f?p.getCorrectPropertyName(window,"transition"):"transition",r="none 0s ease 0s";this.adapter.setThumbStyleProperty(e,r,h.Thumb.END),this.isRange&&this.adapter.setThumbStyleProperty(e,r,h.Thumb.START),this.adapter.setTrackActiveStyleProperty(e,r),requestAnimationFrame(function(){t.adapter.removeThumbStyleProperty(e,h.Thumb.END),t.adapter.removeTrackActiveStyleProperty(e),t.isRange&&t.adapter.removeThumbStyleProperty(e,h.Thumb.START)})}},m.prototype.updateOverlappingThumbsUI=function(t,e,r){var n=!1;if(this.adapter.isRTL())n=t-this.startThumbKnobWidth/2<=e+this.endThumbKnobWidth/2;else{var i=t+this.startThumbKnobWidth/2;n=e-this.endThumbKnobWidth/2<=i}n?(this.adapter.addThumbClass(l.cssClasses.THUMB_TOP,r||h.Thumb.END),this.adapter.removeThumbClass(l.cssClasses.THUMB_TOP,r===h.Thumb.START?h.Thumb.END:h.Thumb.START)):(this.adapter.removeThumbClass(l.cssClasses.THUMB_TOP,h.Thumb.START),this.adapter.removeThumbClass(l.cssClasses.THUMB_TOP,h.Thumb.END))},m.prototype.convertAttributeValueToNumber=function(t,e){if(null===t)throw new Error("MDCSliderFoundation: `"+e+"` must be non-null.");var r=Number(t);if(isNaN(r))throw new Error("MDCSliderFoundation: `"+e+"` value is `"+t+"`, but must be a number.");return r},m.prototype.validateProperties=function(t){var e=t.min,r=t.max,n=t.value,i=t.valueStart,a=t.step,o=t.minRange;if(r<=e)throw new Error("MDCSliderFoundation: min must be strictly less than max. Current: [min: "+e+", max: "+r+"]");if(a<=0)throw new Error("MDCSliderFoundation: step must be a positive number. Current step: "+a);if(this.isRange){if(n<e||r<n||i<e||r<i)throw new Error("MDCSliderFoundation: values must be in [min, max] range. Current values: [start value: "+i+", end value: "+n+", min: "+e+", max: "+r+"]");if(n<i)throw new Error("MDCSliderFoundation: start value must be <= end value. Current values: [start value: "+i+", end value: "+n+"]");if(o<0)throw new Error("MDCSliderFoundation: minimum range must be non-negative. Current min range: "+o);if(n-i<o)throw new Error("MDCSliderFoundation: start value and end value must differ by at least "+o+". Current values: [start value: "+i+", end value: "+n+"]");var s=(i-e)/a,u=(n-e)/a;if(!Number.isInteger(parseFloat(s.toFixed(6)))||!Number.isInteger(parseFloat(u.toFixed(6))))throw new Error("MDCSliderFoundation: Slider values must be valid based on the step value ("+a+"). Current values: [start value: "+i+", end value: "+n+", min: "+e+"]")}else{if(n<e||r<n)throw new Error("MDCSliderFoundation: value must be in [min, max] range. Current values: [value: "+n+", min: "+e+", max: "+r+"]");if(u=(n-e)/a,!Number.isInteger(parseFloat(u.toFixed(6))))throw new Error("MDCSliderFoundation: Slider value must be valid based on the step value ("+a+"). Current value: "+n)}},m.prototype.registerEventHandlers=function(){this.adapter.registerWindowEventHandler("resize",this.resizeListener),m.SUPPORTS_POINTER_EVENTS?(this.adapter.registerEventHandler("pointerdown",this.pointerdownListener),this.adapter.registerEventHandler("pointerup",this.pointerupListener)):(this.adapter.registerEventHandler("mousedown",this.mousedownOrTouchstartListener),this.adapter.registerEventHandler("touchstart",this.mousedownOrTouchstartListener)),this.isRange&&(this.adapter.registerThumbEventHandler(h.Thumb.START,"mouseenter",this.thumbMouseenterListener),this.adapter.registerThumbEventHandler(h.Thumb.START,"mouseleave",this.thumbMouseleaveListener),this.adapter.registerInputEventHandler(h.Thumb.START,"change",this.inputStartChangeListener),this.adapter.registerInputEventHandler(h.Thumb.START,"focus",this.inputStartFocusListener),this.adapter.registerInputEventHandler(h.Thumb.START,"blur",this.inputStartBlurListener)),this.adapter.registerThumbEventHandler(h.Thumb.END,"mouseenter",this.thumbMouseenterListener),this.adapter.registerThumbEventHandler(h.Thumb.END,"mouseleave",this.thumbMouseleaveListener),this.adapter.registerInputEventHandler(h.Thumb.END,"change",this.inputEndChangeListener),this.adapter.registerInputEventHandler(h.Thumb.END,"focus",this.inputEndFocusListener),this.adapter.registerInputEventHandler(h.Thumb.END,"blur",this.inputEndBlurListener)},m.prototype.deregisterEventHandlers=function(){this.adapter.deregisterWindowEventHandler("resize",this.resizeListener),m.SUPPORTS_POINTER_EVENTS?(this.adapter.deregisterEventHandler("pointerdown",this.pointerdownListener),this.adapter.deregisterEventHandler("pointerup",this.pointerupListener)):(this.adapter.deregisterEventHandler("mousedown",this.mousedownOrTouchstartListener),this.adapter.deregisterEventHandler("touchstart",this.mousedownOrTouchstartListener)),this.isRange&&(this.adapter.deregisterThumbEventHandler(h.Thumb.START,"mouseenter",this.thumbMouseenterListener),this.adapter.deregisterThumbEventHandler(h.Thumb.START,"mouseleave",this.thumbMouseleaveListener),this.adapter.deregisterInputEventHandler(h.Thumb.START,"change",this.inputStartChangeListener),this.adapter.deregisterInputEventHandler(h.Thumb.START,"focus",this.inputStartFocusListener),this.adapter.deregisterInputEventHandler(h.Thumb.START,"blur",this.inputStartBlurListener)),this.adapter.deregisterThumbEventHandler(h.Thumb.END,"mouseenter",this.thumbMouseenterListener),this.adapter.deregisterThumbEventHandler(h.Thumb.END,"mouseleave",this.thumbMouseleaveListener),this.adapter.deregisterInputEventHandler(h.Thumb.END,"change",this.inputEndChangeListener),this.adapter.deregisterInputEventHandler(h.Thumb.END,"focus",this.inputEndFocusListener),this.adapter.deregisterInputEventHandler(h.Thumb.END,"blur",this.inputEndBlurListener)},m.prototype.handlePointerup=function(){this.handleUp(),this.adapter.deregisterEventHandler("pointermove",this.moveListener)},m.SUPPORTS_POINTER_EVENTS=f&&Boolean(window.PointerEvent)&&!(["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document),m);function m(t){var e=u.call(this,a(a({},m.defaultAdapter),t))||this;return e.initialStylesRemoved=!1,e.isDisabled=!1,e.isDiscrete=!1,e.step=l.numbers.STEP_SIZE,e.minRange=l.numbers.MIN_RANGE,e.hasTickMarks=!1,e.isRange=!1,e.thumb=null,e.downEventClientX=null,e.startThumbKnobWidth=0,e.endThumbKnobWidth=0,e.animFrame=new o.AnimationFrame,e}function v(t){var e=/(?:\.(\d+))?(?:[eE]([+\-]?\d+))?$/.exec(String(t));if(!e)return 0;var r=e[1]||"",n=e[2]||0;return Math.max(0,("0"===r?0:r.length)-Number(n))}e.MDCSliderFoundation=c},17:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.SafeStyleSheet=e.isStyleSheet=e.unwrapStyle=e.SafeStyle=e.isStyle=e.unwrapScript=e.SafeScript=e.isScript=e.EMPTY_SCRIPT=e.unwrapResourceUrl=e.TrustedResourceUrl=e.isResourceUrl=e.unwrapHtml=e.SafeHtml=e.isHtml=e.EMPTY_HTML=e.unwrapAttributePrefix=e.SafeAttributePrefix=e.safeStyleSheet=e.concatStyleSheets=e.safeStyle=e.concatStyles=e.scriptFromJson=e.safeScriptWithArgs=e.safeScript=e.concatScripts=e.trustedResourceUrl=e.replaceFragment=e.blobUrlFromScript=e.appendParams=e.HtmlSanitizerBuilder=e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.htmlEscape=e.createScriptSrc=e.createScript=e.concatHtmls=e.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(e,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var i=r(22);Object.defineProperty(e,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(e,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(e,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(e,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var a=r(14);Object.defineProperty(e,"sanitizeHtml",{enumerable:!0,get:function(){return a.sanitizeHtml}}),Object.defineProperty(e,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return a.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(e,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return a.sanitizeHtmlToFragment}});var o=r(25);Object.defineProperty(e,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return o.HtmlSanitizerBuilder}});var s=r(26);Object.defineProperty(e,"appendParams",{enumerable:!0,get:function(){return s.appendParams}}),Object.defineProperty(e,"blobUrlFromScript",{enumerable:!0,get:function(){return s.blobUrlFromScript}}),Object.defineProperty(e,"replaceFragment",{enumerable:!0,get:function(){return s.replaceFragment}}),Object.defineProperty(e,"trustedResourceUrl",{enumerable:!0,get:function(){return s.trustedResourceUrl}});var u=r(27);Object.defineProperty(e,"concatScripts",{enumerable:!0,get:function(){return u.concatScripts}}),Object.defineProperty(e,"safeScript",{enumerable:!0,get:function(){return u.safeScript}}),Object.defineProperty(e,"safeScriptWithArgs",{enumerable:!0,get:function(){return u.safeScriptWithArgs}}),Object.defineProperty(e,"scriptFromJson",{enumerable:!0,get:function(){return u.scriptFromJson}});var l=r(28);Object.defineProperty(e,"concatStyles",{enumerable:!0,get:function(){return l.concatStyles}}),Object.defineProperty(e,"safeStyle",{enumerable:!0,get:function(){return l.safeStyle}});var c=r(29);Object.defineProperty(e,"concatStyleSheets",{enumerable:!0,get:function(){return c.concatStyleSheets}}),Object.defineProperty(e,"safeStyleSheet",{enumerable:!0,get:function(){return c.safeStyleSheet}});var d=r(8);Object.defineProperty(e,"SafeAttributePrefix",{enumerable:!0,get:function(){return d.SafeAttributePrefix}}),Object.defineProperty(e,"unwrapAttributePrefix",{enumerable:!0,get:function(){return d.unwrapAttributePrefix}});var p=r(2);Object.defineProperty(e,"EMPTY_HTML",{enumerable:!0,get:function(){return p.EMPTY_HTML}}),Object.defineProperty(e,"isHtml",{enumerable:!0,get:function(){return p.isHtml}}),Object.defineProperty(e,"SafeHtml",{enumerable:!0,get:function(){return p.SafeHtml}}),Object.defineProperty(e,"unwrapHtml",{enumerable:!0,get:function(){return p.unwrapHtml}});var h=r(1);Object.defineProperty(e,"isResourceUrl",{enumerable:!0,get:function(){return h.isResourceUrl}}),Object.defineProperty(e,"TrustedResourceUrl",{enumerable:!0,get:function(){return h.TrustedResourceUrl}}),Object.defineProperty(e,"unwrapResourceUrl",{enumerable:!0,get:function(){return h.unwrapResourceUrl}});var f=r(5);Object.defineProperty(e,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return f.EMPTY_SCRIPT}}),Object.defineProperty(e,"isScript",{enumerable:!0,get:function(){return f.isScript}}),Object.defineProperty(e,"SafeScript",{enumerable:!0,get:function(){return f.SafeScript}}),Object.defineProperty(e,"unwrapScript",{enumerable:!0,get:function(){return f.unwrapScript}});var m=r(10);Object.defineProperty(e,"isStyle",{enumerable:!0,get:function(){return m.isStyle}}),Object.defineProperty(e,"SafeStyle",{enumerable:!0,get:function(){return m.SafeStyle}}),Object.defineProperty(e,"unwrapStyle",{enumerable:!0,get:function(){return m.unwrapStyle}});var v=r(12);Object.defineProperty(e,"isStyleSheet",{enumerable:!0,get:function(){return v.isStyleSheet}}),Object.defineProperty(e,"SafeStyleSheet",{enumerable:!0,get:function(){return v.SafeStyleSheet}}),Object.defineProperty(e,"unwrapStyleSheet",{enumerable:!0,get:function(){return v.unwrapStyleSheet}})},18:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);i&&("get"in i?e.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),a=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return i(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.safeWorker=e.safeWindow=e.safeServiceWorkerContainer=e.safeRange=e.safeLocation=e.safeGlobal=e.safeDomParser=e.safeDocument=e.safeStyleEl=e.safeScriptEl=e.safeObjectEl=e.safeLinkEl=e.safeInputEl=e.safeIframeEl=e.safeFormEl=e.safeEmbedEl=e.safeElement=e.safeButtonEl=e.safeAreaEl=e.safeAnchorEl=void 0,e.safeAnchorEl=a(r(30)),e.safeAreaEl=a(r(31)),e.safeButtonEl=a(r(32)),e.safeElement=a(r(15)),e.safeEmbedEl=a(r(33)),e.safeFormEl=a(r(34)),e.safeIframeEl=a(r(35)),e.safeInputEl=a(r(36)),e.safeLinkEl=a(r(37)),e.safeObjectEl=a(r(38)),e.safeScriptEl=a(r(39)),e.safeStyleEl=a(r(40)),e.safeDocument=a(r(41)),e.safeDomParser=a(r(42)),e.safeGlobal=a(r(43)),e.safeLocation=a(r(44)),e.safeRange=a(r(45)),e.safeServiceWorkerContainer=a(r(46)),e.safeWindow=a(r(47)),e.safeWorker=a(r(48))},19:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);e.safeAttrPrefix=function(t){var e=t[0].toLowerCase();return(0,n.createAttributePrefix)(e)}},2:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapHtml=e.isHtml=e.EMPTY_HTML=e.createHtml=e.SafeHtml=void 0,r(0);var n=r(4),i=r(9),a=(o.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},o);function o(t,e){this.privateDoNotAccessOrElseWrappedHtml=t}function s(t,e){return null!=e?e:new a(t,n.secretToken)}var u=window.TrustedHTML;e.SafeHtml=null!=u?u:a,e.createHtml=function(t){var e,r=t;return s(r,null===(e=(0,i.getTrustedTypesPolicy)())||void 0===e?void 0:e.createHTML(r))},e.EMPTY_HTML=function(){var t;return s("",null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.emptyHTML)}(),e.isHtml=function(t){return t instanceof e.SafeHtml},e.unwrapHtml=function(t){var e;if(null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.isHTML(t))return t;if(t instanceof a)return t.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},20:function(t,e){var r,n,i=t.exports={};function a(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function s(e){if(r===setTimeout)return setTimeout(e,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:a}catch(t){r=a}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(t){n=o}}();var u,l=[],c=!1,d=-1;function p(){c&&u&&(c=!1,u.length?l=u.concat(l):d=-1,l.length&&h())}function h(){if(!c){var t=s(p);c=!0;for(var e=l.length;e;){for(u=l,l=[];++d<e;)u&&u[d].run();d=-1,e=l.length}u=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(t)}}function f(t,e){this.fun=t,this.array=e}function m(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];l.push(new f(t,e)),1!==l.length||c||s(h)},f.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},21:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SECURITY_SENSITIVE_ATTRIBUTES=void 0,e.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},22:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatHtmls=e.createScriptSrc=e.createScript=e.htmlEscape=void 0;var a=r(2),o=r(1),i=r(5);function s(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}e.htmlEscape=function(t,e){void 0===e&&(e={});var r=s(t);return e.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),e.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),e.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,a.createHtml)(r)},e.createScript=function(t,e){void 0===e&&(e={});var r=(0,i.unwrapScript)(t).toString(),n="<script";return e.id&&(n+=' id="'.concat(s(e.id),'"')),e.nonce&&(n+=' nonce="'.concat(s(e.nonce),'"')),e.type&&(n+=' type="'.concat(s(e.type),'"')),n+=">".concat(r,"<\/script>"),(0,a.createHtml)(n)},e.createScriptSrc=function(t,e,r){var n=(0,o.unwrapResourceUrl)(t).toString(),i='<script src="'.concat(s(n),'"');return e&&(i+=" async"),r&&(i+=' nonce="'.concat(s(r),'"')),i+="><\/script>",(0,a.createHtml)(i)},e.concatHtmls=function(t){return(0,a.createHtml)(t.map(a.unwrapHtml).join(""))}},23:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createInertFragment=void 0;var n=r(15),i=r(2);e.createInertFragment=function(t){var e=document.createElement("template"),r=(0,i.createHtml)(t);return(0,n.setInnerHtml)(e,r),e.content}},24:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isElement=e.isText=e.getNodeName=void 0,e.getNodeName=function(t){var e=t.nodeName;return"string"==typeof e?e:"FORM"},e.isText=function(t){return t.nodeType===Node.TEXT_NODE},e.isElement=function(t){var e=t.nodeType;return e===Node.ELEMENT_NODE||"number"!=typeof e}},25:function(t,e,r){"use strict";var E=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},_=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||0<e--)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o};Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlSanitizerBuilder=void 0;var n=r(4),i=r(14),a=r(16),I=r(11),o=(s.prototype.onlyAllowElements=function(t){var e,r,n=new Set,i=new Map;try{for(var a=E(t),o=a.next();!o.done;o=a.next()){var s=o.value;if(s=s.toUpperCase(),!this.sanitizerTable.isAllowedElement(s))throw new Error("Element: ".concat(s,", is not allowed by html5_contract.textpb"));var u=this.sanitizerTable.elementPolicies.get(s);void 0!==u?i.set(s,u):n.add(s)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return this.sanitizerTable=new I.SanitizerTable(n,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},s.prototype.onlyAllowAttributes=function(t){var e,r,n,i,a,o,s=new Set,u=new Map,l=new Map;try{for(var c=E(t),d=c.next();!d.done;d=c.next()){var p=d.value;this.sanitizerTable.allowedGlobalAttributes.has(p)&&s.add(p),this.sanitizerTable.globalAttributePolicies.has(p)&&u.set(p,this.sanitizerTable.globalAttributePolicies.get(p))}}catch(t){e={error:t}}finally{try{d&&!d.done&&(r=c.return)&&r.call(c)}finally{if(e)throw e.error}}try{for(var h=E(this.sanitizerTable.elementPolicies.entries()),f=h.next();!f.done;f=h.next()){var m=_(f.value,2),v=m[0],T=m[1],b=new Map;try{for(var y=(a=void 0,E(T.entries())),A=y.next();!A.done;A=y.next()){var S=_(A.value,2),g=(p=S[0],S[1]);t.has(p)&&b.set(p,g)}}catch(t){a={error:t}}finally{try{A&&!A.done&&(o=y.return)&&o.call(y)}finally{if(a)throw a.error}}l.set(v,b)}}catch(t){n={error:t}}finally{try{f&&!f.done&&(i=h.return)&&i.call(h)}finally{if(n)throw n.error}}return this.sanitizerTable=new I.SanitizerTable(this.sanitizerTable.allowedElements,l,s,u),this},s.prototype.allowDataAttributes=function(t){var e,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=E(t),a=i.next();!a.done;a=i.next()){var o=a.value;if(0!==o.indexOf("data-"))throw new Error("data attribute: ".concat(o,' does not begin with the prefix "data-"'));n.add(o)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return this.sanitizerTable=new I.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},s.prototype.allowStyleAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("style",{policyAction:I.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new I.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowClassAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("class",{policyAction:I.AttributePolicyAction.KEEP}),this.sanitizerTable=new I.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowIdAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("id",{policyAction:I.AttributePolicyAction.KEEP}),this.sanitizerTable=new I.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},s);function s(){this.calledBuild=!1,this.sanitizerTable=a.defaultSanitizerTable}e.HtmlSanitizerBuilder=o},26:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.blobUrlFromScript=e.replaceFragment=e.appendParams=e.trustedResourceUrl=void 0,r(0);var s=r(1),n=r(5);r(6);e.trustedResourceUrl=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(0===e.length)return(0,s.createResourceUrl)(t[0]);t[0].toLowerCase();for(var n=[t[0]],i=0;i<e.length;i++)n.push(encodeURIComponent(e[i])),n.push(t[i+1]);return(0,s.createResourceUrl)(n.join(""))},e.appendParams=function(t,e){var a=(0,s.unwrapResourceUrl)(t).toString();if(/#/.test(a)){throw new Error("")}var o=/\?/.test(a)?"&":"?";return e.forEach(function(t,e){for(var r=t instanceof Array?t:[t],n=0;n<r.length;n++){var i=r[n];null!=i&&(a+=o+encodeURIComponent(e)+"="+encodeURIComponent(String(i)),o="&")}}),(0,s.createResourceUrl)(a)};var i=/[^#]*/;e.replaceFragment=function(t,e){var r=(0,s.unwrapResourceUrl)(t).toString();return(0,s.createResourceUrl)(i.exec(r)[0]+"#"+e)},e.blobUrlFromScript=function(t){var e=(0,n.unwrapScript)(t).toString(),r=new Blob([e],{type:"text/javascript"});return(0,s.createResourceUrl)(URL.createObjectURL(r))}},27:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeScriptWithArgs=e.scriptFromJson=e.concatScripts=e.safeScript=void 0,r(0);var i=r(5);r(6);function a(t){return(0,i.createScript)(JSON.stringify(t).replace(/</g,"\\x3c"))}e.safeScript=function(t){return(0,i.createScript)(t[0])},e.concatScripts=function(t){return(0,i.createScript)(t.map(i.unwrapScript).join(""))},e.scriptFromJson=a,e.safeScriptWithArgs=function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=t.map(function(t){return a(t).toString()});return(0,i.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},276:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),i(r(277),e),i(r(278),e),i(r(109),e),i(r(163),e),i(r(110),e)},277:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},278:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),u=this&&this.__assign||function(){return(u=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCSlider=void 0;var a,o=r(13),l=r(52),c=r(49),d=r(53),p=r(51),h=r(109),s=r(163),f=r(110),m=(a=o.MDCComponent,i(v,a),v.attachTo=function(t,e){return void 0===e&&(e={}),new v(t,void 0,e)},v.prototype.getDefaultFoundation=function(){var n=this,t={hasClass:function(t){return n.root.classList.contains(t)},addClass:function(t){n.root.classList.add(t)},removeClass:function(t){n.root.classList.remove(t)},addThumbClass:function(t,e){n.getThumbEl(e).classList.add(t)},removeThumbClass:function(t,e){n.getThumbEl(e).classList.remove(t)},getAttribute:function(t){return n.root.getAttribute(t)},getInputValue:function(t){return n.getInput(t).value},setInputValue:function(t,e){n.getInput(e).value=t},getInputAttribute:function(t,e){return n.getInput(e).getAttribute(t)},setInputAttribute:function(t,e,r){n.getInput(r).setAttribute(t,e)},removeInputAttribute:function(t,e){n.getInput(e).removeAttribute(t)},focusInput:function(t){n.getInput(t).focus()},isInputFocused:function(t){return n.getInput(t)===document.activeElement},shouldHideFocusStylesForPointerEvents:function(){return!1},getThumbKnobWidth:function(t){return n.getThumbEl(t).querySelector("."+h.cssClasses.THUMB_KNOB).getBoundingClientRect().width},getThumbBoundingClientRect:function(t){return n.getThumbEl(t).getBoundingClientRect()},getBoundingClientRect:function(){return n.root.getBoundingClientRect()},getValueIndicatorContainerWidth:function(t){return n.getThumbEl(t).querySelector("."+h.cssClasses.VALUE_INDICATOR_CONTAINER).getBoundingClientRect().width},isRTL:function(){return"rtl"===getComputedStyle(n.root).direction},setThumbStyleProperty:function(t,e,r){n.getThumbEl(r).style.setProperty(t,e)},removeThumbStyleProperty:function(t,e){n.getThumbEl(e).style.removeProperty(t)},setTrackActiveStyleProperty:function(t,e){n.trackActive.style.setProperty(t,e)},removeTrackActiveStyleProperty:function(t){n.trackActive.style.removeProperty(t)},setValueIndicatorText:function(t,e){n.getThumbEl(e).querySelector("."+h.cssClasses.VALUE_INDICATOR_TEXT).textContent=String(t)},getValueToAriaValueTextFn:function(){return n.valueToAriaValueTextFn},updateTickMarks:function(t){var e=n.root.querySelector("."+h.cssClasses.TICK_MARKS_CONTAINER);if(e||((e=document.createElement("div")).classList.add(h.cssClasses.TICK_MARKS_CONTAINER),n.root.querySelector("."+h.cssClasses.TRACK).appendChild(e)),t.length!==e.children.length){for(;e.firstChild;)e.removeChild(e.firstChild);n.addTickMarks(e,t)}else n.updateTickMarks(e,t)},setPointerCapture:function(t){n.root.setPointerCapture(t)},emitChangeEvent:function(t,e){n.emit(h.events.CHANGE,{value:t,thumb:e})},emitInputEvent:function(t,e){n.emit(h.events.INPUT,{value:t,thumb:e})},emitDragStartEvent:function(t,e){n.getRipple(e).activate()},emitDragEndEvent:function(t,e){n.getRipple(e).deactivate()},registerEventHandler:function(t,e){n.listen(t,e)},deregisterEventHandler:function(t,e){n.unlisten(t,e)},registerThumbEventHandler:function(t,e,r){n.getThumbEl(t).addEventListener(e,r)},deregisterThumbEventHandler:function(t,e,r){n.getThumbEl(t).removeEventListener(e,r)},registerInputEventHandler:function(t,e,r){n.getInput(t).addEventListener(e,r)},deregisterInputEventHandler:function(t,e,r){n.getInput(t).removeEventListener(e,r)},registerBodyEventHandler:function(t,e){document.body.addEventListener(t,e)},deregisterBodyEventHandler:function(t,e){document.body.removeEventListener(t,e)},registerWindowEventHandler:function(t,e){window.addEventListener(t,e)},deregisterWindowEventHandler:function(t,e){window.removeEventListener(t,e)}};return new s.MDCSliderFoundation(t)},v.prototype.initialize=function(t){var e=(void 0===t?{}:t).skipInitialUIUpdate;this.inputs=Array.from(this.root.querySelectorAll("."+h.cssClasses.INPUT)),this.thumbs=Array.from(this.root.querySelectorAll("."+h.cssClasses.THUMB)),this.trackActive=this.root.querySelector("."+h.cssClasses.TRACK_ACTIVE),this.ripples=this.createRipples(),e&&(this.skipInitialUIUpdate=!0)},v.prototype.initialSyncWithDOM=function(){this.foundation.layout({skipUpdateUI:this.skipInitialUIUpdate})},v.prototype.layout=function(){this.foundation.layout()},v.prototype.getValueStart=function(){return this.foundation.getValueStart()},v.prototype.setValueStart=function(t){this.foundation.setValueStart(t)},v.prototype.getValue=function(){return this.foundation.getValue()},v.prototype.setValue=function(t){this.foundation.setValue(t)},v.prototype.getDisabled=function(){return this.foundation.getDisabled()},v.prototype.setDisabled=function(t){this.foundation.setDisabled(t)},v.prototype.setValueToAriaValueTextFn=function(t){this.valueToAriaValueTextFn=t},v.prototype.getThumbEl=function(t){return t===f.Thumb.END?this.thumbs[this.thumbs.length-1]:this.thumbs[0]},v.prototype.getInput=function(t){return t===f.Thumb.END?this.inputs[this.inputs.length-1]:this.inputs[0]},v.prototype.getRipple=function(t){return t===f.Thumb.END?this.ripples[this.ripples.length-1]:this.ripples[0]},v.prototype.addTickMarks=function(t,e){for(var r=document.createDocumentFragment(),n=0;n<e.length;n++){var i=document.createElement("div"),a=e[n]===f.TickMark.ACTIVE?h.cssClasses.TICK_MARK_ACTIVE:h.cssClasses.TICK_MARK_INACTIVE;i.classList.add(a),r.appendChild(i)}t.appendChild(r)},v.prototype.updateTickMarks=function(t,e){for(var r=Array.from(t.children),n=0;n<r.length;n++)e[n]===f.TickMark.ACTIVE?(r[n].classList.add(h.cssClasses.TICK_MARK_ACTIVE),r[n].classList.remove(h.cssClasses.TICK_MARK_INACTIVE)):(r[n].classList.add(h.cssClasses.TICK_MARK_INACTIVE),r[n].classList.remove(h.cssClasses.TICK_MARK_ACTIVE))},v.prototype.createRipples=function(){for(var a=[],o=Array.from(this.root.querySelectorAll("."+h.cssClasses.THUMB)),t=function(t){var r=o[t],n=s.inputs[t],e=u(u({},d.MDCRipple.createAdapter(s)),{addClass:function(t){r.classList.add(t)},computeBoundingRect:function(){return r.getBoundingClientRect()},deregisterInteractionHandler:function(t,e){n.removeEventListener(t,e)},isSurfaceActive:function(){return c.matches(n,":active")},isUnbounded:function(){return!0},registerInteractionHandler:function(t,e){n.addEventListener(t,e,l.applyPassive())},removeClass:function(t){r.classList.remove(t)},updateCssVariable:function(t,e){r.style.setProperty(t,e)}}),i=new d.MDCRipple(r,new p.MDCRippleFoundation(e));i.unbounded=!0,a.push(i)},s=this,e=0;e<o.length;e++)t(e);return a},v);function v(){var t=null!==a&&a.apply(this,arguments)||this;return t.skipInitialUIUpdate=!1,t.valueToAriaValueTextFn=null,t}e.MDCSlider=m},28:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyles=e.safeStyle=void 0,r(0);r(6);var n=r(10);e.safeStyle=function(t){var e=t[0];return(0,n.createStyle)(e)},e.concatStyles=function(t){return(0,n.createStyle)(t.map(n.unwrapStyle).join(""))}},29:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyleSheets=e.safeStyleSheet=void 0,r(0);r(6);var n=r(12);e.safeStyleSheet=function(t){var e=t[0];return(0,n.createStyleSheet)(e)},e.concatStyleSheets=function(t){return(0,n.createStyleSheet)(t.map(n.unwrapStyleSheet).join(""))}},3:function(t,e,r){"use strict";function n(t){var e;try{e=new URL(t)}catch(t){return"https:"}return e.protocol}Object.defineProperty(e,"__esModule",{value:!0}),e.restrictivelySanitizeUrl=e.unwrapUrlOrSanitize=e.sanitizeJavascriptUrl=void 0,r(0);var i=["data:","http:","https:","mailto:","ftp:"];function a(t){if("javascript:"!==n(t))return t}e.sanitizeJavascriptUrl=a,e.unwrapUrlOrSanitize=function(t){return a(t)},e.restrictivelySanitizeUrl=function(t){var e=n(t);return void 0!==e&&-1!==i.indexOf(e.toLowerCase())?t:"about:invalid#zClosurez"}},30:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},31:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},32:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var n=r(3);e.setFormaction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},33:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=void 0;var n=r(1);e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e)}},34:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setAction=void 0;var n=r(3);e.setAction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.action=r)}},35:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrcdoc=e.setSrc=void 0;var n=r(2),i=r(1);e.setSrc=function(t,e){t.src=(0,i.unwrapResourceUrl)(e).toString()},e.setSrcdoc=function(t,e){t.srcdoc=(0,n.unwrapHtml)(e)}},36:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var n=r(3);e.setFormaction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},37:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHrefAndRel=void 0;var i=r(3),a=r(1),o=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];e.setHrefAndRel=function(t,e,r){if(e instanceof a.TrustedResourceUrl)t.href=(0,a.unwrapResourceUrl)(e).toString();else{if(-1===o.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,i.unwrapUrlOrSanitize)(e);if(void 0===n)return;t.href=n}t.rel=r}},38:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setData=void 0;var n=r(1);e.setData=function(t,e){t.data=(0,n.unwrapResourceUrl)(e)}},39:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=e.setTextContent=void 0;var n=r(1),i=r(5);function a(t){var e=function(t){var e,r=t.document,n=null===(e=r.querySelector)||void 0===e?void 0:e.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(t.ownerDocument&&t.ownerDocument.defaultView||window);e&&t.setAttribute("nonce",e)}e.setTextContent=function(t,e){t.textContent=(0,i.unwrapScript)(e),a(t)},e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e),a(t)}},4:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ensureTokenIsValid=e.secretToken=void 0,e.secretToken={},e.ensureTokenIsValid=function(t){if(t!==e.secretToken)throw new Error("Bad secret")}},40:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setTextContent=void 0;var n=r(12);e.setTextContent=function(t,e){t.textContent=(0,n.unwrapStyleSheet)(e)}},41:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.execCommandInsertHtml=e.execCommand=e.write=void 0;var a=r(2);e.write=function(t,e){t.write((0,a.unwrapHtml)(e))},e.execCommand=function(t,e,r){var n=String(e),i=r;return"inserthtml"===n.toLowerCase()&&(i=(0,a.unwrapHtml)(r)),t.execCommand(n,!1,i)},e.execCommandInsertHtml=function(t,e){return t.execCommand("insertHTML",!1,(0,a.unwrapHtml)(e))}},42:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseFromString=e.parseHtml=void 0;var n=r(2);function i(t,e,r){return t.parseFromString((0,n.unwrapHtml)(e),r)}e.parseHtml=function(t,e){return i(t,e,"text/html")},e.parseFromString=i},43:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.globalEval=void 0;var i=r(5);e.globalEval=function(t,e){var r=(0,i.unwrapScript)(e),n=t.eval(r);return n===r&&(n=t.eval(r.toString())),n}},44:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assign=e.replace=e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)},e.replace=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&t.replace(r)},e.assign=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&t.assign(r)}},45:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createContextualFragment=void 0;var n=r(2);e.createContextualFragment=function(t,e){return t.createContextualFragment((0,n.unwrapHtml)(e))}},46:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.register=void 0;var n=r(1);e.register=function(t,e,r){return t.register((0,n.unwrapResourceUrl)(e),r)}},47:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.open=void 0;var a=r(3);e.open=function(t,e,r,n){var i=(0,a.unwrapUrlOrSanitize)(e);return void 0!==i?t.open(i,r,n):null}},48:function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,a=r.call(t),o=[];try{for(;(void 0===e||0<e--)&&!(n=a.next()).done;)o.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(i)throw i.error}}return o},i=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,i=0,a=e.length;i<a;i++)!n&&i in e||((n=n||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.importScripts=e.createShared=e.create=void 0;var a=r(1);e.create=function(t,e){return new Worker((0,a.unwrapResourceUrl)(t),e)},e.createShared=function(t,e){return new SharedWorker((0,a.unwrapResourceUrl)(t),e)},e.importScripts=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.importScripts.apply(t,i([],n(e.map(function(t){return(0,a.unwrapResourceUrl)(t)})),!1))}},49:function(t,e,r){"use strict";function n(t,e){return(t.matches||t.webkitMatchesSelector||t.msMatchesSelector).call(t,e)}Object.defineProperty(e,"__esModule",{value:!0}),e.estimateScrollWidth=e.matches=e.closest=void 0,e.closest=function(t,e){if(t.closest)return t.closest(e);for(var r=t;r;){if(n(r,e))return r;r=r.parentElement}return null},e.matches=n,e.estimateScrollWidth=function(t){var e=t;if(null!==e.offsetParent)return e.scrollWidth;var r=e.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},5:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapScript=e.isScript=e.EMPTY_SCRIPT=e.createScript=e.SafeScript=void 0,r(0);var n=r(4),i=r(9),a=(o.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},o);function o(t,e){this.privateDoNotAccessOrElseWrappedScript=t}function s(t,e){return null!=e?e:new a(t,n.secretToken)}var u=window.TrustedScript;e.SafeScript=null!=u?u:a,e.createScript=function(t){var e,r=t;return s(r,null===(e=(0,i.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScript(r))},e.EMPTY_SCRIPT=function(){var t;return s("",null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.emptyScript)}(),e.isScript=function(t){return t instanceof e.SafeScript},e.unwrapScript=function(t){var e;if(null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.isScript(t))return t;if(t instanceof a)return t.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},50:function(t,e,r){"use strict";var o;Object.defineProperty(e,"__esModule",{value:!0}),e.getNormalizedEventCoords=e.supportsCssVariables=void 0,e.supportsCssVariables=function(t,e){void 0===e&&(e=!1);var r,n=t.CSS;if("boolean"==typeof o&&!e)return o;if(!(n&&"function"==typeof n.supports))return!1;var i=n.supports("--css-vars","yes"),a=n.supports("(--css-vars: yes)")&&n.supports("color","#00000000");return r=i||a,e||(o=r),r},e.getNormalizedEventCoords=function(t,e,r){if(!t)return{x:0,y:0};var n,i,a=e.x,o=e.y,s=a+r.left,u=o+r.top;if("touchstart"===t.type){var l=t;n=l.changedTouches[0].pageX-s,i=l.changedTouches[0].pageY-u}else{var c=t;n=c.pageX-s,i=c.pageY-u}return{x:n,y:i}}},51:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__assign||function(){return(a=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},o=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCRippleFoundation=void 0;var s,u=r(7),l=r(54),c=r(50),d=["touchstart","pointerdown","mousedown","keydown"],p=["touchend","pointerup","mouseup","contextmenu"],h=[],f=(s=u.MDCFoundation,i(m,s),Object.defineProperty(m,"cssClasses",{get:function(){return l.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(m,"strings",{get:function(){return l.strings},enumerable:!1,configurable:!0}),Object.defineProperty(m,"numbers",{get:function(){return l.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(m,"defaultAdapter",{get:function(){return{addClass:function(){},browserSupportsCssVars:function(){return!0},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},containsEventTarget:function(){return!0},deregisterDocumentInteractionHandler:function(){},deregisterInteractionHandler:function(){},deregisterResizeHandler:function(){},getWindowPageOffset:function(){return{x:0,y:0}},isSurfaceActive:function(){return!0},isSurfaceDisabled:function(){return!0},isUnbounded:function(){return!0},registerDocumentInteractionHandler:function(){},registerInteractionHandler:function(){},registerResizeHandler:function(){},removeClass:function(){},updateCssVariable:function(){}}},enumerable:!1,configurable:!0}),m.prototype.init=function(){var t=this,e=this.supportsPressRipple();if(this.registerRootHandlers(e),e){var r=m.cssClasses,n=r.ROOT,i=r.UNBOUNDED;requestAnimationFrame(function(){t.adapter.addClass(n),t.adapter.isUnbounded()&&(t.adapter.addClass(i),t.layoutInternal())})}},m.prototype.destroy=function(){var t=this;if(this.supportsPressRipple()){this.activationTimer&&(clearTimeout(this.activationTimer),this.activationTimer=0,this.adapter.removeClass(m.cssClasses.FG_ACTIVATION)),this.fgDeactivationRemovalTimer&&(clearTimeout(this.fgDeactivationRemovalTimer),this.fgDeactivationRemovalTimer=0,this.adapter.removeClass(m.cssClasses.FG_DEACTIVATION));var e=m.cssClasses,r=e.ROOT,n=e.UNBOUNDED;requestAnimationFrame(function(){t.adapter.removeClass(r),t.adapter.removeClass(n),t.removeCssVars()})}this.deregisterRootHandlers(),this.deregisterDeactivationHandlers()},m.prototype.activate=function(t){this.activateImpl(t)},m.prototype.deactivate=function(){this.deactivateImpl()},m.prototype.layout=function(){var t=this;this.layoutFrame&&cancelAnimationFrame(this.layoutFrame),this.layoutFrame=requestAnimationFrame(function(){t.layoutInternal(),t.layoutFrame=0})},m.prototype.setUnbounded=function(t){var e=m.cssClasses.UNBOUNDED;t?this.adapter.addClass(e):this.adapter.removeClass(e)},m.prototype.handleFocus=function(){var t=this;requestAnimationFrame(function(){t.adapter.addClass(m.cssClasses.BG_FOCUSED)})},m.prototype.handleBlur=function(){var t=this;requestAnimationFrame(function(){t.adapter.removeClass(m.cssClasses.BG_FOCUSED)})},m.prototype.supportsPressRipple=function(){return this.adapter.browserSupportsCssVars()},m.prototype.defaultActivationState=function(){return{activationEvent:void 0,hasDeactivationUXRun:!1,isActivated:!1,isProgrammatic:!1,wasActivatedByPointer:!1,wasElementMadeActive:!1}},m.prototype.registerRootHandlers=function(t){var e,r;if(t){try{for(var n=o(d),i=n.next();!i.done;i=n.next()){var a=i.value;this.adapter.registerInteractionHandler(a,this.activateHandler)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}this.adapter.isUnbounded()&&this.adapter.registerResizeHandler(this.resizeHandler)}this.adapter.registerInteractionHandler("focus",this.focusHandler),this.adapter.registerInteractionHandler("blur",this.blurHandler)},m.prototype.registerDeactivationHandlers=function(t){var e,r;if("keydown"===t.type)this.adapter.registerInteractionHandler("keyup",this.deactivateHandler);else try{for(var n=o(p),i=n.next();!i.done;i=n.next()){var a=i.value;this.adapter.registerDocumentInteractionHandler(a,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},m.prototype.deregisterRootHandlers=function(){var e,t;try{for(var r=o(d),n=r.next();!n.done;n=r.next()){var i=n.value;this.adapter.deregisterInteractionHandler(i,this.activateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}this.adapter.deregisterInteractionHandler("focus",this.focusHandler),this.adapter.deregisterInteractionHandler("blur",this.blurHandler),this.adapter.isUnbounded()&&this.adapter.deregisterResizeHandler(this.resizeHandler)},m.prototype.deregisterDeactivationHandlers=function(){var e,t;this.adapter.deregisterInteractionHandler("keyup",this.deactivateHandler);try{for(var r=o(p),n=r.next();!n.done;n=r.next()){var i=n.value;this.adapter.deregisterDocumentInteractionHandler(i,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},m.prototype.removeCssVars=function(){var e=this,r=m.strings;Object.keys(r).forEach(function(t){0===t.indexOf("VAR_")&&e.adapter.updateCssVariable(r[t],null)})},m.prototype.activateImpl=function(t){var e=this;if(!this.adapter.isSurfaceDisabled()){var r=this.activationState;if(!r.isActivated){var n=this.previousActivationEvent;n&&void 0!==t&&n.type!==t.type||(r.isActivated=!0,r.isProgrammatic=void 0===t,r.activationEvent=t,r.wasActivatedByPointer=!r.isProgrammatic&&void 0!==t&&("mousedown"===t.type||"touchstart"===t.type||"pointerdown"===t.type),void 0!==t&&0<h.length&&h.some(function(t){return e.adapter.containsEventTarget(t)})?this.resetActivationState():(void 0!==t&&(h.push(t.target),this.registerDeactivationHandlers(t)),r.wasElementMadeActive=this.checkElementMadeActive(t),r.wasElementMadeActive&&this.animateActivation(),requestAnimationFrame(function(){h=[],r.wasElementMadeActive||void 0===t||" "!==t.key&&32!==t.keyCode||(r.wasElementMadeActive=e.checkElementMadeActive(t),r.wasElementMadeActive&&e.animateActivation()),r.wasElementMadeActive||(e.activationState=e.defaultActivationState())})))}}},m.prototype.checkElementMadeActive=function(t){return void 0===t||"keydown"!==t.type||this.adapter.isSurfaceActive()},m.prototype.animateActivation=function(){var t=this,e=m.strings,r=e.VAR_FG_TRANSLATE_START,n=e.VAR_FG_TRANSLATE_END,i=m.cssClasses,a=i.FG_DEACTIVATION,o=i.FG_ACTIVATION,s=m.numbers.DEACTIVATION_TIMEOUT_MS;this.layoutInternal();var u="",l="";if(!this.adapter.isUnbounded()){var c=this.getFgTranslationCoordinates(),d=c.startPoint,p=c.endPoint;u=d.x+"px, "+d.y+"px",l=p.x+"px, "+p.y+"px"}this.adapter.updateCssVariable(r,u),this.adapter.updateCssVariable(n,l),clearTimeout(this.activationTimer),clearTimeout(this.fgDeactivationRemovalTimer),this.rmBoundedActivationClasses(),this.adapter.removeClass(a),this.adapter.computeBoundingRect(),this.adapter.addClass(o),this.activationTimer=setTimeout(function(){t.activationTimerCallback()},s)},m.prototype.getFgTranslationCoordinates=function(){var t,e=this.activationState,r=e.activationEvent;return{startPoint:t={x:(t=e.wasActivatedByPointer?c.getNormalizedEventCoords(r,this.adapter.getWindowPageOffset(),this.adapter.computeBoundingRect()):{x:this.frame.width/2,y:this.frame.height/2}).x-this.initialSize/2,y:t.y-this.initialSize/2},endPoint:{x:this.frame.width/2-this.initialSize/2,y:this.frame.height/2-this.initialSize/2}}},m.prototype.runDeactivationUXLogicIfReady=function(){var t=this,e=m.cssClasses.FG_DEACTIVATION,r=this.activationState,n=r.hasDeactivationUXRun,i=r.isActivated;!n&&i||!this.activationAnimationHasEnded||(this.rmBoundedActivationClasses(),this.adapter.addClass(e),this.fgDeactivationRemovalTimer=setTimeout(function(){t.adapter.removeClass(e)},l.numbers.FG_DEACTIVATION_MS))},m.prototype.rmBoundedActivationClasses=function(){var t=m.cssClasses.FG_ACTIVATION;this.adapter.removeClass(t),this.activationAnimationHasEnded=!1,this.adapter.computeBoundingRect()},m.prototype.resetActivationState=function(){var t=this;this.previousActivationEvent=this.activationState.activationEvent,this.activationState=this.defaultActivationState(),setTimeout(function(){return t.previousActivationEvent=void 0},m.numbers.TAP_DELAY_MS)},m.prototype.deactivateImpl=function(){var t=this,e=this.activationState;if(e.isActivated){var r=a({},e);e.isProgrammatic?(requestAnimationFrame(function(){t.animateDeactivation(r)}),this.resetActivationState()):(this.deregisterDeactivationHandlers(),requestAnimationFrame(function(){t.activationState.hasDeactivationUXRun=!0,t.animateDeactivation(r),t.resetActivationState()}))}},m.prototype.animateDeactivation=function(t){var e=t.wasActivatedByPointer,r=t.wasElementMadeActive;(e||r)&&this.runDeactivationUXLogicIfReady()},m.prototype.layoutInternal=function(){var t=this;this.frame=this.adapter.computeBoundingRect();var e=Math.max(this.frame.height,this.frame.width);this.maxRadius=this.adapter.isUnbounded()?e:Math.sqrt(Math.pow(t.frame.width,2)+Math.pow(t.frame.height,2))+m.numbers.PADDING;var r=Math.floor(e*m.numbers.INITIAL_ORIGIN_SCALE);this.adapter.isUnbounded()&&r%2!=0?this.initialSize=r-1:this.initialSize=r,this.fgScale=""+this.maxRadius/this.initialSize,this.updateLayoutCssVars()},m.prototype.updateLayoutCssVars=function(){var t=m.strings,e=t.VAR_FG_SIZE,r=t.VAR_LEFT,n=t.VAR_TOP,i=t.VAR_FG_SCALE;this.adapter.updateCssVariable(e,this.initialSize+"px"),this.adapter.updateCssVariable(i,this.fgScale),this.adapter.isUnbounded()&&(this.unboundedCoords={left:Math.round(this.frame.width/2-this.initialSize/2),top:Math.round(this.frame.height/2-this.initialSize/2)},this.adapter.updateCssVariable(r,this.unboundedCoords.left+"px"),this.adapter.updateCssVariable(n,this.unboundedCoords.top+"px"))},m);function m(t){var e=s.call(this,a(a({},m.defaultAdapter),t))||this;return e.activationAnimationHasEnded=!1,e.activationTimer=0,e.fgDeactivationRemovalTimer=0,e.fgScale="0",e.frame={width:0,height:0},e.initialSize=0,e.layoutFrame=0,e.maxRadius=0,e.unboundedCoords={left:0,top:0},e.activationState=e.defaultActivationState(),e.activationTimerCallback=function(){e.activationAnimationHasEnded=!0,e.runDeactivationUXLogicIfReady()},e.activateHandler=function(t){e.activateImpl(t)},e.deactivateHandler=function(){e.deactivateImpl()},e.focusHandler=function(){e.handleFocus()},e.blurHandler=function(){e.handleBlur()},e.resizeHandler=function(){e.layout()},e}e.MDCRippleFoundation=f,e.default=f},52:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.applyPassive=void 0,e.applyPassive=function(t){return void 0===t&&(t=window),!!function(t){void 0===t&&(t=window);var e=!1;try{var r={get passive(){return!(e=!0)}},n=function(){};t.document.addEventListener("test",n,r),t.document.removeEventListener("test",n,r)}catch(t){e=!1}return e}(t)&&{passive:!0}}},53:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),a=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),s=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&a(e,t,r);return o(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCRipple=void 0;var u,l=r(13),c=r(52),d=r(49),p=r(51),h=s(r(50)),f=(u=l.MDCComponent,i(m,u),m.attachTo=function(t,e){void 0===e&&(e={isUnbounded:void 0});var r=new m(t);return void 0!==e.isUnbounded&&(r.unbounded=e.isUnbounded),r},m.createAdapter=function(r){return{addClass:function(t){r.root.classList.add(t)},browserSupportsCssVars:function(){return h.supportsCssVariables(window)},computeBoundingRect:function(){return r.root.getBoundingClientRect()},containsEventTarget:function(t){return r.root.contains(t)},deregisterDocumentInteractionHandler:function(t,e){document.documentElement.removeEventListener(t,e,c.applyPassive())},deregisterInteractionHandler:function(t,e){r.root.removeEventListener(t,e,c.applyPassive())},deregisterResizeHandler:function(t){window.removeEventListener("resize",t)},getWindowPageOffset:function(){return{x:window.pageXOffset,y:window.pageYOffset}},isSurfaceActive:function(){return d.matches(r.root,":active")},isSurfaceDisabled:function(){return Boolean(r.disabled)},isUnbounded:function(){return Boolean(r.unbounded)},registerDocumentInteractionHandler:function(t,e){document.documentElement.addEventListener(t,e,c.applyPassive())},registerInteractionHandler:function(t,e){r.root.addEventListener(t,e,c.applyPassive())},registerResizeHandler:function(t){window.addEventListener("resize",t)},removeClass:function(t){r.root.classList.remove(t)},updateCssVariable:function(t,e){r.root.style.setProperty(t,e)}}},Object.defineProperty(m.prototype,"unbounded",{get:function(){return Boolean(this.isUnbounded)},set:function(t){this.isUnbounded=Boolean(t),this.setUnbounded()},enumerable:!1,configurable:!0}),m.prototype.activate=function(){this.foundation.activate()},m.prototype.deactivate=function(){this.foundation.deactivate()},m.prototype.layout=function(){this.foundation.layout()},m.prototype.getDefaultFoundation=function(){return new p.MDCRippleFoundation(m.createAdapter(this))},m.prototype.initialSyncWithDOM=function(){var t=this.root;this.isUnbounded="mdcRippleIsUnbounded"in t.dataset},m.prototype.setUnbounded=function(){this.foundation.setUnbounded(Boolean(this.isUnbounded))},m);function m(){var t=null!==u&&u.apply(this,arguments)||this;return t.disabled=!1,t}e.MDCRipple=f},54:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.numbers=e.strings=e.cssClasses=void 0,e.cssClasses={BG_FOCUSED:"mdc-ripple-upgraded--background-focused",FG_ACTIVATION:"mdc-ripple-upgraded--foreground-activation",FG_DEACTIVATION:"mdc-ripple-upgraded--foreground-deactivation",ROOT:"mdc-ripple-upgraded",UNBOUNDED:"mdc-ripple-upgraded--unbounded"},e.strings={VAR_FG_SCALE:"--mdc-ripple-fg-scale",VAR_FG_SIZE:"--mdc-ripple-fg-size",VAR_FG_TRANSLATE_END:"--mdc-ripple-fg-translate-end",VAR_FG_TRANSLATE_START:"--mdc-ripple-fg-translate-start",VAR_LEFT:"--mdc-ripple-left",VAR_TOP:"--mdc-ripple-top"},e.numbers={DEACTIVATION_TIMEOUT_MS:225,FG_DEACTIVATION_MS:150,INITIAL_ORIGIN_SCALE:.6,PADDING:10,TAP_DELAY_MS:300}},57:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getCorrectEventName=e.getCorrectPropertyName=void 0;var o={animation:{prefixed:"-webkit-animation",standard:"animation"},transform:{prefixed:"-webkit-transform",standard:"transform"},transition:{prefixed:"-webkit-transition",standard:"transition"}},s={animationend:{cssProperty:"animation",prefixed:"webkitAnimationEnd",standard:"animationend"},animationiteration:{cssProperty:"animation",prefixed:"webkitAnimationIteration",standard:"animationiteration"},animationstart:{cssProperty:"animation",prefixed:"webkitAnimationStart",standard:"animationstart"},transitionend:{cssProperty:"transition",prefixed:"webkitTransitionEnd",standard:"transitionend"}};function u(t){return Boolean(t.document)&&"function"==typeof t.document.createElement}e.getCorrectPropertyName=function(t,e){if(u(t)&&e in o){var r=t.document.createElement("div"),n=o[e],i=n.standard,a=n.prefixed;return i in r.style?i:a}return e},e.getCorrectEventName=function(t,e){if(u(t)&&e in s){var r=t.document.createElement("div"),n=s[e],i=n.standard,a=n.prefixed;return n.cssProperty in r.style?i:a}return e}},6:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assertIsTemplateObject=void 0,e.assertIsTemplateObject=function(t,e,r){if(!Array.isArray(t)||!Array.isArray(t.raw)||!e&&1!==t.length)throw new TypeError(r)}},63:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AnimationFrame=void 0;var n=(i.prototype.request=function(e,r){var n=this;this.cancel(e);var t=requestAnimationFrame(function(t){n.rafIDs.delete(e),r(t)});this.rafIDs.set(e,t)},i.prototype.cancel=function(t){var e=this.rafIDs.get(t);e&&(cancelAnimationFrame(e),this.rafIDs.delete(t))},i.prototype.cancelAll=function(){var r=this;this.rafIDs.forEach(function(t,e){r.cancel(e)})},i.prototype.getQueue=function(){var r=[];return this.rafIDs.forEach(function(t,e){r.push(e)}),r},i);function i(){this.rafIDs=new Map}e.AnimationFrame=n},7:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MDCFoundation=void 0;var n=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(t){void 0===t&&(t={}),this.adapter=t}e.MDCFoundation=n,e.default=n},8:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapAttributePrefix=e.createAttributePrefix=e.SafeAttributePrefix=void 0,r(0);function a(){}var o=r(4);e.SafeAttributePrefix=a;var s,u=(i(l,s=a),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},l);function l(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=t,r}e.createAttributePrefix=function(t){return new u(t,o.secretToken)},e.unwrapAttributePrefix=function(t){if(t instanceof u)return t.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},9:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEST_ONLY=e.getTrustedTypesPolicy=e.getTrustedTypes=void 0;var n,i="google#safe";function a(){var t;return""!==i&&null!==(t=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==t?t:null}e.getTrustedTypes=a,e.getTrustedTypesPolicy=function(){var t,e;if(void 0===n)try{n=null!==(e=null===(t=a())||void 0===t?void 0:t.createPolicy(i,{createHTML:function(t){return t},createScript:function(t){return t},createScriptURL:function(t){return t}}))&&void 0!==e?e:null}catch(t){n=null}return n},e.TEST_ONLY={resetDefaults:function(){n=void 0,i="google#safe"},setTrustedTypesPolicyName:function(t){i=t}}}},i.c=n,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=276);function i(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}var r,n});