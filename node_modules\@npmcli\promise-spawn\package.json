{"name": "@npmcli/promise-spawn", "version": "7.0.2", "files": ["bin/", "lib/"], "main": "./lib/index.js", "description": "spawn processes the way the npm cli likes to do", "repository": {"type": "git", "url": "git+https://github.com/npm/promise-spawn.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "spawk": "^1.7.1", "tap": "^16.0.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": true}, "dependencies": {"which": "^4.0.0"}}