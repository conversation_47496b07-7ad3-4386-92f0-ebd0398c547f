{"name": "@sigstore/tuf", "version": "2.3.4", "description": "Client for the Sigstore TUF repository", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "shx rm -rf dist *.tsbuildinfo", "build": "tsc --build", "test": "jest"}, "files": ["dist", "seeds.json"], "author": "<EMAIL>", "license": "Apache-2.0", "repository": {"type": "git", "url": "git+https://github.com/sigstore/sigstore-js.git"}, "bugs": {"url": "https://github.com/sigstore/sigstore-js/issues"}, "homepage": "https://github.com/sigstore/sigstore-js/tree/main/packages/tuf#readme", "publishConfig": {"provenance": true}, "devDependencies": {"@sigstore/jest": "^0.0.0", "@tufjs/repo-mock": "^2.0.1", "@types/make-fetch-happen": "^10.0.4"}, "dependencies": {"@sigstore/protobuf-specs": "^0.3.2", "tuf-js": "^2.2.1"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}