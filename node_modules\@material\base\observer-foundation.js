/**
 * @license
 * Copyright 2021 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
import { __extends, __read, __spreadArray, __values } from "tslib";
import { MDCFoundation } from './foundation';
import { observeProperty, setObserversEnabled } from './observer';
// @ts-ignore(go/ts48upgrade) Fix code and remove this comment. Error:
// TS2344: Type 'Adapter' does not satisfy the constraint '{}'.
var MDCObserverFoundation = /** @class */ (function (_super) {
    __extends(MDCObserverFoundation, _super);
    function MDCObserverFoundation(adapter) {
        var _this = _super.call(this, adapter) || this;
        /** A set of cleanup functions to unobserve changes. */
        _this.unobserves = new Set();
        return _this;
    }
    MDCObserverFoundation.prototype.destroy = function () {
        _super.prototype.destroy.call(this);
        this.unobserve();
    };
    /**
     * Observe a target's properties for changes using the provided map of
     * property names and observer functions.
     *
     * @template T The target type.
     * @param target - The target to observe.
     * @param observers - An object whose keys are target properties and values
     *     are observer functions that are called when the associated property
     *     changes.
     * @return A cleanup function that can be called to unobserve the
     *     target.
     */
    MDCObserverFoundation.prototype.observe = function (target, observers) {
        var e_1, _a;
        var _this = this;
        var cleanup = [];
        try {
            for (var _b = __values(Object.keys(observers)), _c = _b.next(); !_c.done; _c = _b.next()) {
                var property = _c.value;
                var observer = observers[property].bind(this);
                cleanup.push(this.observeProperty(target, property, observer));
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_1) throw e_1.error; }
        }
        var unobserve = function () {
            var e_2, _a;
            try {
                for (var cleanup_1 = __values(cleanup), cleanup_1_1 = cleanup_1.next(); !cleanup_1_1.done; cleanup_1_1 = cleanup_1.next()) {
                    var cleanupFn = cleanup_1_1.value;
                    cleanupFn();
                }
            }
            catch (e_2_1) { e_2 = { error: e_2_1 }; }
            finally {
                try {
                    if (cleanup_1_1 && !cleanup_1_1.done && (_a = cleanup_1.return)) _a.call(cleanup_1);
                }
                finally { if (e_2) throw e_2.error; }
            }
            _this.unobserves.delete(unobserve);
        };
        this.unobserves.add(unobserve);
        return unobserve;
    };
    /**
     * Observe a target's property for changes. When a property changes, the
     * provided `Observer` function will be invoked with the properties current
     * and previous values.
     *
     * The returned cleanup function will stop listening to changes for the
     * provided `Observer`.
     *
     * @template T The observed target type.
     * @template K The observed property.
     * @param target - The target to observe.
     * @param property - The property of the target to observe.
     * @param observer - An observer function to invoke each time the property
     *     changes.
     * @return A cleanup function that will stop observing changes for the
     *     provided `Observer`.
     */
    MDCObserverFoundation.prototype.observeProperty = function (target, property, observer) {
        return observeProperty(target, property, observer);
    };
    /**
     * Enables or disables all observers for the provided target. Disabling
     * observers will prevent them from being called until they are re-enabled.
     *
     * @param target - The target to enable or disable observers for.
     * @param enabled - Whether or not observers should be called.
     */
    MDCObserverFoundation.prototype.setObserversEnabled = function (target, enabled) {
        setObserversEnabled(target, enabled);
    };
    /**
     * Clean up all observers and stop listening for property changes.
     */
    MDCObserverFoundation.prototype.unobserve = function () {
        var e_3, _a;
        try {
            // Iterate over a copy since unobserve() will remove themselves from the set
            for (var _b = __values(__spreadArray([], __read(this.unobserves))), _c = _b.next(); !_c.done; _c = _b.next()) {
                var unobserve = _c.value;
                unobserve();
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
            }
            finally { if (e_3) throw e_3.error; }
        }
    };
    return MDCObserverFoundation;
}(MDCFoundation));
export { MDCObserverFoundation };
//# sourceMappingURL=observer-foundation.js.map