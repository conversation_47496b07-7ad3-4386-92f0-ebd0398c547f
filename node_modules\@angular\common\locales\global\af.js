/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['af'] = ["af",[["v","n"],["vm.","nm."],u],u,[["S","M","D","W","D","V","S"],["So.","Ma.","Di.","Wo.","Do.","Vr.","Sa."],["Sondag","Maandag","Dinsdag","Woensdag","<PERSON><PERSON>dag","<PERSON><PERSON>dag","Saterdag"],["So.","Ma.","<PERSON>.","Wo.","<PERSON>.","Vr.","Sa."]],u,[["J","F","M","<PERSON>","M","<PERSON>","<PERSON>","A","S","O","N","D"],["<PERSON>.","<PERSON>.","<PERSON>t.","Apr.","<PERSON>","Jun.","Jul.","Aug.","Sep.","Okt.","Nov.","<PERSON>."],["<PERSON>uarie","<PERSON>ruarie","Maart","April","<PERSON>","Junie","Julie","Augustus","September","Oktober","November","Desember"]],u,[["v.C.","n.C."],u,["voor Christus","na Christus"]],0,[6,0],["y-MM-dd","dd MMM y","dd MMMM y","EEEE dd MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"ZAR","R","Suid-Afrikaanse rand",{"BYN":[u,"р."],"CAD":[u,"$"],"JPY":["JP¥","¥"],"MXN":[u,"$"],"PHP":[u,"₱"],"RON":[u,"leu"],"THB":["฿"],"TWD":["NT$"],"USD":[u,"$"],"ZAR":["R"]},"ltr", plural, [[["mn","o","m","a","n"],["middernag","die oggend","die middag","die aand","die nag"],u],[["mn","o","m","a","n"],["middernag","oggend","middag","aand","nag"],u],["00:00",["05:00","12:00"],["12:00","18:00"],["18:00","24:00"],["00:00","05:00"]]]];
  })(globalThis);
    