!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("@material/tab",[],e):"object"==typeof exports?exports.tab=e():(t.mdc=t.mdc||{},t.mdc.tab=e())}(this,function(){return n={},i.m=r={0:function(t,e,r){"use strict";(function(t){}).call(this,r(20))},1:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapResourceUrl=e.isResourceUrl=e.createResourceUrl=e.TrustedResourceUrl=void 0,r(0);var i=r(4),o=r(9),a=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(t,e){this.privateDoNotAccessOrElseWrappedResourceUrl=t}var s=window.TrustedScriptURL;e.TrustedResourceUrl=null!=s?s:a,e.createResourceUrl=function(t){var e,r=t,n=null===(e=(0,o.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScriptURL(r);return null!=n?n:new a(r,i.secretToken)},e.isResourceUrl=function(t){return t instanceof e.TrustedResourceUrl},e.unwrapResourceUrl=function(t){var e;if(null===(e=(0,o.getTrustedTypes)())||void 0===e?void 0:e.isScriptURL(t))return t;if(t instanceof a)return t.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},10:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyle=e.isStyle=e.createStyle=e.SafeStyle=void 0,r(0);function o(){}var a=r(4);e.SafeStyle=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},u);function u(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=t,r}e.createStyle=function(t){return new c(t,a.secretToken)},e.isStyle=function(t){return t instanceof c},e.unwrapStyle=function(t){if(t instanceof c)return t.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},11:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributePolicyAction=e.SanitizerTable=void 0;var n,i,o=(a.prototype.isAllowedElement=function(t){return"form"!==t.toLowerCase()&&(this.allowedElements.has(t)||this.elementPolicies.has(t))},a.prototype.getAttributePolicy=function(t,e){var r=this.elementPolicies.get(e);return(null==r?void 0:r.has(t))?r.get(t):this.allowedGlobalAttributes.has(t)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(t)||{policyAction:n.DROP}},a);function a(t,e,r,n){this.allowedElements=t,this.elementPolicies=e,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}e.SanitizerTable=o,(i=n=e.AttributePolicyAction||(e.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},112:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTab=void 0;var o,a=r(13),s=r(53),c=r(51),u=r(86),l=r(87),f=(o=a.MDCComponent,i(d,o),d.attachTo=function(t){return new d(t)},d.prototype.initialize=function(t,e){void 0===t&&(t=function(t,e){return new s.MDCRipple(t,e)}),void 0===e&&(e=function(t){return new u.MDCTabIndicator(t)}),this.id=this.root.id;var r=new c.MDCRippleFoundation(s.MDCRipple.createAdapter(this));this.ripple=t(this.root,r);var n=this.root.querySelector(l.MDCTabFoundation.strings.TAB_INDICATOR_SELECTOR);this.tabIndicator=e(n),this.content=this.root.querySelector(l.MDCTabFoundation.strings.CONTENT_SELECTOR)},d.prototype.initialSyncWithDOM=function(){var t=this;this.handleClick=function(){t.foundation.handleClick()},this.listen("click",this.handleClick)},d.prototype.destroy=function(){this.unlisten("click",this.handleClick),this.ripple.destroy(),o.prototype.destroy.call(this)},d.prototype.getDefaultFoundation=function(){var r=this,t={setAttr:function(t,e){r.safeSetAttribute(r.root,t,e)},addClass:function(t){r.root.classList.add(t)},removeClass:function(t){r.root.classList.remove(t)},hasClass:function(t){return r.root.classList.contains(t)},activateIndicator:function(t){r.tabIndicator.activate(t)},deactivateIndicator:function(){r.tabIndicator.deactivate()},notifyInteracted:function(){r.emit(l.MDCTabFoundation.strings.INTERACTED_EVENT,{tabId:r.id},!0)},getOffsetLeft:function(){return r.root.offsetLeft},getOffsetWidth:function(){return r.root.offsetWidth},getContentOffsetLeft:function(){return r.content.offsetLeft},getContentOffsetWidth:function(){return r.content.offsetWidth},focus:function(){r.root.focus()},isFocused:function(){return r.root===document.activeElement}};return new l.MDCTabFoundation(t)},Object.defineProperty(d.prototype,"active",{get:function(){return this.foundation.isActive()},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"focusOnActivate",{set:function(t){this.foundation.setFocusOnActivate(t)},enumerable:!1,configurable:!0}),d.prototype.activate=function(t){this.foundation.activate(t)},d.prototype.deactivate=function(){this.foundation.deactivate()},d.prototype.computeIndicatorClientRect=function(){return this.tabIndicator.computeContentClientRect()},d.prototype.computeDimensions=function(){return this.foundation.computeDimensions()},d.prototype.focus=function(){this.root.focus()},d);function d(){return null!==o&&o.apply(this,arguments)||this}e.MDCTab=f},12:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.isStyleSheet=e.createStyleSheet=e.SafeStyleSheet=void 0,r(0);function o(){}var a=r(4);e.SafeStyleSheet=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},u);function u(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=t,r}e.createStyleSheet=function(t){return new c(t,a.secretToken)},e.isStyleSheet=function(t){return t instanceof c},e.unwrapStyleSheet=function(t){if(t instanceof c)return t.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},13:function(t,e,r){"use strict";var i=this&&this.__makeTemplateObject||function(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__spreadArray||function(t,e){for(var r=0,n=e.length,i=t.length;r<n;r++,i++)t[i]=e[r];return t};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCComponent=void 0;var s=r(17),c=r(18),n=r(7);var u,l,f=(d.attachTo=function(t){return new d(t,new n.MDCFoundation({}))},d.prototype.initialize=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},d.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},d.prototype.initialSyncWithDOM=function(){},d.prototype.destroy=function(){this.foundation.destroy()},d.prototype.listen=function(t,e,r){this.root.addEventListener(t,e,r)},d.prototype.unlisten=function(t,e,r){this.root.removeEventListener(t,e,r)},d.prototype.emit=function(t,e,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(t,{bubbles:r,detail:e}):(n=document.createEvent("CustomEvent")).initCustomEvent(t,r,!1,e),this.root.dispatchEvent(n)},d.prototype.safeSetAttribute=function(t,e,r){if("tabindex"===e.toLowerCase())t.tabIndex=Number(r);else if(0===e.indexOf("data-")){var n=function(t){return String(t).replace(/\-([a-z])/g,function(t,e){return e.toUpperCase()})}(e.replace(/^data-/,""));t.dataset[n]=r}else c.safeElement.setPrefixedAttribute([s.safeAttrPrefix(u=u||i(["aria-"],["aria-"])),s.safeAttrPrefix(l=l||i(["role"],["role"]))],t,e,r)},d);function d(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=t,this.initialize.apply(this,a([],o(r))),this.foundation=void 0===e?this.getDefaultFoundation():e,this.foundation.init(),this.initialSyncWithDOM()}e.MDCComponent=f,e.default=f},14:function(t,e,r){"use strict";var p=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},f=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.HtmlSanitizerImpl=void 0,r(0);var n=r(2),i=r(4),v=r(3),c=r(23),h=r(24),o=r(16),y=r(11),a=(s.prototype.sanitizeAssertUnchanged=function(t){this.changes=[];var e=this.sanitize(t);if(0===this.changes.length)return e;throw new Error("")},s.prototype.sanitize=function(t){var e=document.createElement("span");e.appendChild(this.sanitizeToFragment(t));var r=(new XMLSerializer).serializeToString(e);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},s.prototype.sanitizeToFragment=function(t){for(var e=this,r=(0,c.createInertFragment)(t),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(t){return e.nodeFilter(t)},!1),i=n.nextNode(),o=document.createDocumentFragment(),a=o;null!==i;){var s=void 0;if((0,h.isText)(i))s=this.sanitizeTextNode(i);else{if(!(0,h.isElement)(i))throw new Error("Node is not of type text or element");s=this.sanitizeElementNode(i)}if(a.appendChild(s),i=n.firstChild())a=s;else for(;!(i=n.nextSibling())&&(i=n.parentNode());)a=a.parentNode}return o},s.prototype.sanitizeTextNode=function(t){return document.createTextNode(t.data)},s.prototype.sanitizeElementNode=function(t){var e,r,n=(0,h.getNodeName)(t),i=document.createElement(n),o=t.attributes;try{for(var a=p(o),s=a.next();!s.done;s=a.next()){var c=s.value,u=c.name,l=c.value,f=this.sanitizerTable.getAttributePolicy(u,n);if(this.satisfiesAllConditions(f.conditions,o))switch(f.policyAction){case y.AttributePolicyAction.KEEP:i.setAttribute(u,l);break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var d=(0,v.restrictivelySanitizeUrl)(l);d!==l&&this.recordChange("Url in attribute ".concat(u,' was modified during sanitization. Original url:"').concat(l,'" was sanitized to: "').concat(d,'"')),i.setAttribute(u,d);break;case y.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(u,l.toLowerCase());break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(u,l);break;case y.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(u," was dropped"));break;default:b(f.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(u,"."))}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return i},s.prototype.nodeFilter=function(t){if((0,h.isText)(t))return NodeFilter.FILTER_ACCEPT;if(!(0,h.isElement)(t))return NodeFilter.FILTER_REJECT;var e=(0,h.getNodeName)(t);return null===e?(this.recordChange("Node name was null for node: ".concat(t)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(e)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(e," was dropped")),NodeFilter.FILTER_REJECT)},s.prototype.recordChange=function(t){0===this.changes.length&&this.changes.push("")},s.prototype.satisfiesAllConditions=function(t,e){var r,n,i;if(!t)return!0;try{for(var o=p(t),a=o.next();!a.done;a=o.next()){var s=f(a.value,2),c=s[0],u=s[1],l=null===(i=e.getNamedItem(c))||void 0===i?void 0:i.value;if(l&&!u.has(l))return!1}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0},s);function s(t,e){this.sanitizerTable=t,this.changes=[],(0,i.ensureTokenIsValid)(e)}e.HtmlSanitizerImpl=a;var u=function(){return new a(o.defaultSanitizerTable,i.secretToken)}();function b(t,e){throw void 0===e&&(e="unexpected value ".concat(t,"!")),new Error(e)}e.sanitizeHtml=function(t){return u.sanitize(t)},e.sanitizeHtmlAssertUnchanged=function(t){return u.sanitizeAssertUnchanged(t)},e.sanitizeHtmlToFragment=function(t){return u.sanitizeToFragment(t)}},15:function(t,e,r){"use strict";var i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||((n=n||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.setPrefixedAttribute=e.buildPrefixedAttributeSetter=e.insertAdjacentHtml=e.setCssText=e.setOuterHtml=e.setInnerHtml=void 0;var a=r(8),s=r(2),n=r(10);function c(t,e,r,n){if(0===t.length)throw new Error("No prefixes are provided");var i=t.map(function(t){return(0,a.unwrapAttributePrefix)(t)}),o=r.toLowerCase();if(i.every(function(t){return 0!==o.indexOf(t)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));e.setAttribute(r,n)}function u(t){if("script"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}e.setInnerHtml=function(t,e){!function(t){return void 0!==t.tagName}(t)||u(t),t.innerHTML=(0,s.unwrapHtml)(e)},e.setOuterHtml=function(t,e){var r=t.parentElement;null!==r&&u(r),t.outerHTML=(0,s.unwrapHtml)(e)},e.setCssText=function(t,e){t.style.cssText=(0,n.unwrapStyle)(e)},e.insertAdjacentHtml=function(t,e,r){var n="beforebegin"===e||"afterend"===e?t.parentElement:t;null!==n&&u(n),t.insertAdjacentHTML(e,(0,s.unwrapHtml)(r))},e.buildPrefixedAttributeSetter=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=o([t],i(e),!1);return function(t,e,r){c(n,t,e,r)}},e.setPrefixedAttribute=c},16:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defaultSanitizerTable=void 0;var n=r(11);e.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},17:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.SafeStyleSheet=e.isStyleSheet=e.unwrapStyle=e.SafeStyle=e.isStyle=e.unwrapScript=e.SafeScript=e.isScript=e.EMPTY_SCRIPT=e.unwrapResourceUrl=e.TrustedResourceUrl=e.isResourceUrl=e.unwrapHtml=e.SafeHtml=e.isHtml=e.EMPTY_HTML=e.unwrapAttributePrefix=e.SafeAttributePrefix=e.safeStyleSheet=e.concatStyleSheets=e.safeStyle=e.concatStyles=e.scriptFromJson=e.safeScriptWithArgs=e.safeScript=e.concatScripts=e.trustedResourceUrl=e.replaceFragment=e.blobUrlFromScript=e.appendParams=e.HtmlSanitizerBuilder=e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.htmlEscape=e.createScriptSrc=e.createScript=e.concatHtmls=e.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(e,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var i=r(22);Object.defineProperty(e,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(e,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(e,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(e,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var o=r(14);Object.defineProperty(e,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(e,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(e,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(e,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var s=r(26);Object.defineProperty(e,"appendParams",{enumerable:!0,get:function(){return s.appendParams}}),Object.defineProperty(e,"blobUrlFromScript",{enumerable:!0,get:function(){return s.blobUrlFromScript}}),Object.defineProperty(e,"replaceFragment",{enumerable:!0,get:function(){return s.replaceFragment}}),Object.defineProperty(e,"trustedResourceUrl",{enumerable:!0,get:function(){return s.trustedResourceUrl}});var c=r(27);Object.defineProperty(e,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(e,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(e,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(e,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var u=r(28);Object.defineProperty(e,"concatStyles",{enumerable:!0,get:function(){return u.concatStyles}}),Object.defineProperty(e,"safeStyle",{enumerable:!0,get:function(){return u.safeStyle}});var l=r(29);Object.defineProperty(e,"concatStyleSheets",{enumerable:!0,get:function(){return l.concatStyleSheets}}),Object.defineProperty(e,"safeStyleSheet",{enumerable:!0,get:function(){return l.safeStyleSheet}});var f=r(8);Object.defineProperty(e,"SafeAttributePrefix",{enumerable:!0,get:function(){return f.SafeAttributePrefix}}),Object.defineProperty(e,"unwrapAttributePrefix",{enumerable:!0,get:function(){return f.unwrapAttributePrefix}});var d=r(2);Object.defineProperty(e,"EMPTY_HTML",{enumerable:!0,get:function(){return d.EMPTY_HTML}}),Object.defineProperty(e,"isHtml",{enumerable:!0,get:function(){return d.isHtml}}),Object.defineProperty(e,"SafeHtml",{enumerable:!0,get:function(){return d.SafeHtml}}),Object.defineProperty(e,"unwrapHtml",{enumerable:!0,get:function(){return d.unwrapHtml}});var p=r(1);Object.defineProperty(e,"isResourceUrl",{enumerable:!0,get:function(){return p.isResourceUrl}}),Object.defineProperty(e,"TrustedResourceUrl",{enumerable:!0,get:function(){return p.TrustedResourceUrl}}),Object.defineProperty(e,"unwrapResourceUrl",{enumerable:!0,get:function(){return p.unwrapResourceUrl}});var v=r(5);Object.defineProperty(e,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return v.EMPTY_SCRIPT}}),Object.defineProperty(e,"isScript",{enumerable:!0,get:function(){return v.isScript}}),Object.defineProperty(e,"SafeScript",{enumerable:!0,get:function(){return v.SafeScript}}),Object.defineProperty(e,"unwrapScript",{enumerable:!0,get:function(){return v.unwrapScript}});var h=r(10);Object.defineProperty(e,"isStyle",{enumerable:!0,get:function(){return h.isStyle}}),Object.defineProperty(e,"SafeStyle",{enumerable:!0,get:function(){return h.SafeStyle}}),Object.defineProperty(e,"unwrapStyle",{enumerable:!0,get:function(){return h.unwrapStyle}});var y=r(12);Object.defineProperty(e,"isStyleSheet",{enumerable:!0,get:function(){return y.isStyleSheet}}),Object.defineProperty(e,"SafeStyleSheet",{enumerable:!0,get:function(){return y.SafeStyleSheet}}),Object.defineProperty(e,"unwrapStyleSheet",{enumerable:!0,get:function(){return y.unwrapStyleSheet}})},18:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(e,r);i&&("get"in i?e.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,i)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return i(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.safeWorker=e.safeWindow=e.safeServiceWorkerContainer=e.safeRange=e.safeLocation=e.safeGlobal=e.safeDomParser=e.safeDocument=e.safeStyleEl=e.safeScriptEl=e.safeObjectEl=e.safeLinkEl=e.safeInputEl=e.safeIframeEl=e.safeFormEl=e.safeEmbedEl=e.safeElement=e.safeButtonEl=e.safeAreaEl=e.safeAnchorEl=void 0,e.safeAnchorEl=o(r(30)),e.safeAreaEl=o(r(31)),e.safeButtonEl=o(r(32)),e.safeElement=o(r(15)),e.safeEmbedEl=o(r(33)),e.safeFormEl=o(r(34)),e.safeIframeEl=o(r(35)),e.safeInputEl=o(r(36)),e.safeLinkEl=o(r(37)),e.safeObjectEl=o(r(38)),e.safeScriptEl=o(r(39)),e.safeStyleEl=o(r(40)),e.safeDocument=o(r(41)),e.safeDomParser=o(r(42)),e.safeGlobal=o(r(43)),e.safeLocation=o(r(44)),e.safeRange=o(r(45)),e.safeServiceWorkerContainer=o(r(46)),e.safeWindow=o(r(47)),e.safeWorker=o(r(48))},19:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);e.safeAttrPrefix=function(t){var e=t[0].toLowerCase();return(0,n.createAttributePrefix)(e)}},2:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapHtml=e.isHtml=e.EMPTY_HTML=e.createHtml=e.SafeHtml=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(t,e){this.privateDoNotAccessOrElseWrappedHtml=t}function s(t,e){return null!=e?e:new o(t,n.secretToken)}var c=window.TrustedHTML;e.SafeHtml=null!=c?c:o,e.createHtml=function(t){var e,r=t;return s(r,null===(e=(0,i.getTrustedTypesPolicy)())||void 0===e?void 0:e.createHTML(r))},e.EMPTY_HTML=function(){var t;return s("",null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.emptyHTML)}(),e.isHtml=function(t){return t instanceof e.SafeHtml},e.unwrapHtml=function(t){var e;if(null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.isHTML(t))return t;if(t instanceof o)return t.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},20:function(t,e){var r,n,i=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(t){n=a}}();var c,u=[],l=!1,f=-1;function d(){l&&c&&(l=!1,c.length?u=c.concat(u):f=-1,u.length&&p())}function p(){if(!l){var t=s(d);l=!0;for(var e=u.length;e;){for(c=u,u=[];++f<e;)c&&c[f].run();f=-1,e=u.length}c=null,l=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(t)}}function v(t,e){this.fun=t,this.array=e}function h(){}i.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];u.push(new v(t,e)),1!==u.length||l||s(p)},v.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},21:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SECURITY_SENSITIVE_ATTRIBUTES=void 0,e.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},22:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatHtmls=e.createScriptSrc=e.createScript=e.htmlEscape=void 0;var o=r(2),a=r(1),i=r(5);function s(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}e.htmlEscape=function(t,e){void 0===e&&(e={});var r=s(t);return e.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),e.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),e.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(r)},e.createScript=function(t,e){void 0===e&&(e={});var r=(0,i.unwrapScript)(t).toString(),n="<script";return e.id&&(n+=' id="'.concat(s(e.id),'"')),e.nonce&&(n+=' nonce="'.concat(s(e.nonce),'"')),e.type&&(n+=' type="'.concat(s(e.type),'"')),n+=">".concat(r,"<\/script>"),(0,o.createHtml)(n)},e.createScriptSrc=function(t,e,r){var n=(0,a.unwrapResourceUrl)(t).toString(),i='<script src="'.concat(s(n),'"');return e&&(i+=" async"),r&&(i+=' nonce="'.concat(s(r),'"')),i+="><\/script>",(0,o.createHtml)(i)},e.concatHtmls=function(t){return(0,o.createHtml)(t.map(o.unwrapHtml).join(""))}},23:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createInertFragment=void 0;var n=r(15),i=r(2);e.createInertFragment=function(t){var e=document.createElement("template"),r=(0,i.createHtml)(t);return(0,n.setInnerHtml)(e,r),e.content}},24:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isElement=e.isText=e.getNodeName=void 0,e.getNodeName=function(t){var e=t.nodeName;return"string"==typeof e?e:"FORM"},e.isText=function(t){return t.nodeType===Node.TEXT_NODE},e.isElement=function(t){var e=t.nodeType;return e===Node.ELEMENT_NODE||"number"!=typeof e}},25:function(t,e,r){"use strict";var T=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlSanitizerBuilder=void 0;var n=r(4),i=r(14),o=r(16),w=r(11),a=(s.prototype.onlyAllowElements=function(t){var e,r,n=new Set,i=new Map;try{for(var o=T(t),a=o.next();!a.done;a=o.next()){var s=a.value;if(s=s.toUpperCase(),!this.sanitizerTable.isAllowedElement(s))throw new Error("Element: ".concat(s,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(s);void 0!==c?i.set(s,c):n.add(s)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return this.sanitizerTable=new w.SanitizerTable(n,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},s.prototype.onlyAllowAttributes=function(t){var e,r,n,i,o,a,s=new Set,c=new Map,u=new Map;try{for(var l=T(t),f=l.next();!f.done;f=l.next()){var d=f.value;this.sanitizerTable.allowedGlobalAttributes.has(d)&&s.add(d),this.sanitizerTable.globalAttributePolicies.has(d)&&c.set(d,this.sanitizerTable.globalAttributePolicies.get(d))}}catch(t){e={error:t}}finally{try{f&&!f.done&&(r=l.return)&&r.call(l)}finally{if(e)throw e.error}}try{for(var p=T(this.sanitizerTable.elementPolicies.entries()),v=p.next();!v.done;v=p.next()){var h=E(v.value,2),y=h[0],b=h[1],m=new Map;try{for(var S=(o=void 0,T(b.entries())),_=S.next();!_.done;_=S.next()){var A=E(_.value,2),g=(d=A[0],A[1]);t.has(d)&&m.set(d,g)}}catch(t){o={error:t}}finally{try{_&&!_.done&&(a=S.return)&&a.call(S)}finally{if(o)throw o.error}}u.set(y,m)}}catch(t){n={error:t}}finally{try{v&&!v.done&&(i=p.return)&&i.call(p)}finally{if(n)throw n.error}}return this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,u,s,c),this},s.prototype.allowDataAttributes=function(t){var e,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=T(t),o=i.next();!o.done;o=i.next()){var a=o.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));n.add(a)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},s.prototype.allowStyleAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("style",{policyAction:w.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowClassAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("class",{policyAction:w.AttributePolicyAction.KEEP}),this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowIdAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("id",{policyAction:w.AttributePolicyAction.KEEP}),this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},s);function s(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}e.HtmlSanitizerBuilder=a},26:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.blobUrlFromScript=e.replaceFragment=e.appendParams=e.trustedResourceUrl=void 0,r(0);var s=r(1),n=r(5);r(6);e.trustedResourceUrl=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(0===e.length)return(0,s.createResourceUrl)(t[0]);t[0].toLowerCase();for(var n=[t[0]],i=0;i<e.length;i++)n.push(encodeURIComponent(e[i])),n.push(t[i+1]);return(0,s.createResourceUrl)(n.join(""))},e.appendParams=function(t,e){var o=(0,s.unwrapResourceUrl)(t).toString();if(/#/.test(o)){throw new Error("")}var a=/\?/.test(o)?"&":"?";return e.forEach(function(t,e){for(var r=t instanceof Array?t:[t],n=0;n<r.length;n++){var i=r[n];null!=i&&(o+=a+encodeURIComponent(e)+"="+encodeURIComponent(String(i)),a="&")}}),(0,s.createResourceUrl)(o)};var i=/[^#]*/;e.replaceFragment=function(t,e){var r=(0,s.unwrapResourceUrl)(t).toString();return(0,s.createResourceUrl)(i.exec(r)[0]+"#"+e)},e.blobUrlFromScript=function(t){var e=(0,n.unwrapScript)(t).toString(),r=new Blob([e],{type:"text/javascript"});return(0,s.createResourceUrl)(URL.createObjectURL(r))}},27:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeScriptWithArgs=e.scriptFromJson=e.concatScripts=e.safeScript=void 0,r(0);var i=r(5);r(6);function o(t){return(0,i.createScript)(JSON.stringify(t).replace(/</g,"\\x3c"))}e.safeScript=function(t){return(0,i.createScript)(t[0])},e.concatScripts=function(t){return(0,i.createScript)(t.map(i.unwrapScript).join(""))},e.scriptFromJson=o,e.safeScriptWithArgs=function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=t.map(function(t){return o(t).toString()});return(0,i.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},28:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyles=e.safeStyle=void 0,r(0);r(6);var n=r(10);e.safeStyle=function(t){var e=t[0];return(0,n.createStyle)(e)},e.concatStyles=function(t){return(0,n.createStyle)(t.map(n.unwrapStyle).join(""))}},29:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyleSheets=e.safeStyleSheet=void 0,r(0);r(6);var n=r(12);e.safeStyleSheet=function(t){var e=t[0];return(0,n.createStyleSheet)(e)},e.concatStyleSheets=function(t){return(0,n.createStyleSheet)(t.map(n.unwrapStyleSheet).join(""))}},291:function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),i=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),i(r(292),e),i(r(112),e),i(r(96),e),i(r(87),e),i(r(293),e)},292:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},293:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},3:function(t,e,r){"use strict";function n(t){var e;try{e=new URL(t)}catch(t){return"https:"}return e.protocol}Object.defineProperty(e,"__esModule",{value:!0}),e.restrictivelySanitizeUrl=e.unwrapUrlOrSanitize=e.sanitizeJavascriptUrl=void 0,r(0);var i=["data:","http:","https:","mailto:","ftp:"];function o(t){if("javascript:"!==n(t))return t}e.sanitizeJavascriptUrl=o,e.unwrapUrlOrSanitize=function(t){return o(t)},e.restrictivelySanitizeUrl=function(t){var e=n(t);return void 0!==e&&-1!==i.indexOf(e.toLowerCase())?t:"about:invalid#zClosurez"}},30:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},31:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},32:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var n=r(3);e.setFormaction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},33:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=void 0;var n=r(1);e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e)}},34:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setAction=void 0;var n=r(3);e.setAction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.action=r)}},35:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrcdoc=e.setSrc=void 0;var n=r(2),i=r(1);e.setSrc=function(t,e){t.src=(0,i.unwrapResourceUrl)(e).toString()},e.setSrcdoc=function(t,e){t.srcdoc=(0,n.unwrapHtml)(e)}},36:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var n=r(3);e.setFormaction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},37:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHrefAndRel=void 0;var i=r(3),o=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];e.setHrefAndRel=function(t,e,r){if(e instanceof o.TrustedResourceUrl)t.href=(0,o.unwrapResourceUrl)(e).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,i.unwrapUrlOrSanitize)(e);if(void 0===n)return;t.href=n}t.rel=r}},38:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setData=void 0;var n=r(1);e.setData=function(t,e){t.data=(0,n.unwrapResourceUrl)(e)}},39:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=e.setTextContent=void 0;var n=r(1),i=r(5);function o(t){var e=function(t){var e,r=t.document,n=null===(e=r.querySelector)||void 0===e?void 0:e.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(t.ownerDocument&&t.ownerDocument.defaultView||window);e&&t.setAttribute("nonce",e)}e.setTextContent=function(t,e){t.textContent=(0,i.unwrapScript)(e),o(t)},e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e),o(t)}},4:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ensureTokenIsValid=e.secretToken=void 0,e.secretToken={},e.ensureTokenIsValid=function(t){if(t!==e.secretToken)throw new Error("Bad secret")}},40:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setTextContent=void 0;var n=r(12);e.setTextContent=function(t,e){t.textContent=(0,n.unwrapStyleSheet)(e)}},41:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.execCommandInsertHtml=e.execCommand=e.write=void 0;var o=r(2);e.write=function(t,e){t.write((0,o.unwrapHtml)(e))},e.execCommand=function(t,e,r){var n=String(e),i=r;return"inserthtml"===n.toLowerCase()&&(i=(0,o.unwrapHtml)(r)),t.execCommand(n,!1,i)},e.execCommandInsertHtml=function(t,e){return t.execCommand("insertHTML",!1,(0,o.unwrapHtml)(e))}},42:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseFromString=e.parseHtml=void 0;var n=r(2);function i(t,e,r){return t.parseFromString((0,n.unwrapHtml)(e),r)}e.parseHtml=function(t,e){return i(t,e,"text/html")},e.parseFromString=i},43:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.globalEval=void 0;var i=r(5);e.globalEval=function(t,e){var r=(0,i.unwrapScript)(e),n=t.eval(r);return n===r&&(n=t.eval(r.toString())),n}},44:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assign=e.replace=e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)},e.replace=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&t.replace(r)},e.assign=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&t.assign(r)}},45:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createContextualFragment=void 0;var n=r(2);e.createContextualFragment=function(t,e){return t.createContextualFragment((0,n.unwrapHtml)(e))}},46:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.register=void 0;var n=r(1);e.register=function(t,e,r){return t.register((0,n.unwrapResourceUrl)(e),r)}},47:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.open=void 0;var o=r(3);e.open=function(t,e,r,n){var i=(0,o.unwrapUrlOrSanitize)(e);return void 0!==i?t.open(i,r,n):null}},48:function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=o.next()).done;)a.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||((n=n||Array.prototype.slice.call(e,0,i))[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.importScripts=e.createShared=e.create=void 0;var o=r(1);e.create=function(t,e){return new Worker((0,o.unwrapResourceUrl)(t),e)},e.createShared=function(t,e){return new SharedWorker((0,o.unwrapResourceUrl)(t),e)},e.importScripts=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.importScripts.apply(t,i([],n(e.map(function(t){return(0,o.unwrapResourceUrl)(t)})),!1))}},49:function(t,e,r){"use strict";function n(t,e){return(t.matches||t.webkitMatchesSelector||t.msMatchesSelector).call(t,e)}Object.defineProperty(e,"__esModule",{value:!0}),e.estimateScrollWidth=e.matches=e.closest=void 0,e.closest=function(t,e){if(t.closest)return t.closest(e);for(var r=t;r;){if(n(r,e))return r;r=r.parentElement}return null},e.matches=n,e.estimateScrollWidth=function(t){var e=t;if(null!==e.offsetParent)return e.scrollWidth;var r=e.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},5:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapScript=e.isScript=e.EMPTY_SCRIPT=e.createScript=e.SafeScript=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(t,e){this.privateDoNotAccessOrElseWrappedScript=t}function s(t,e){return null!=e?e:new o(t,n.secretToken)}var c=window.TrustedScript;e.SafeScript=null!=c?c:o,e.createScript=function(t){var e,r=t;return s(r,null===(e=(0,i.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScript(r))},e.EMPTY_SCRIPT=function(){var t;return s("",null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.emptyScript)}(),e.isScript=function(t){return t instanceof e.SafeScript},e.unwrapScript=function(t){var e;if(null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.isScript(t))return t;if(t instanceof o)return t.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},50:function(t,e,r){"use strict";var a;Object.defineProperty(e,"__esModule",{value:!0}),e.getNormalizedEventCoords=e.supportsCssVariables=void 0,e.supportsCssVariables=function(t,e){void 0===e&&(e=!1);var r,n=t.CSS;if("boolean"==typeof a&&!e)return a;if(!(n&&"function"==typeof n.supports))return!1;var i=n.supports("--css-vars","yes"),o=n.supports("(--css-vars: yes)")&&n.supports("color","#00000000");return r=i||o,e||(a=r),r},e.getNormalizedEventCoords=function(t,e,r){if(!t)return{x:0,y:0};var n,i,o=e.x,a=e.y,s=o+r.left,c=a+r.top;if("touchstart"===t.type){var u=t;n=u.changedTouches[0].pageX-s,i=u.changedTouches[0].pageY-c}else{var l=t;n=l.pageX-s,i=l.pageY-c}return{x:n,y:i}}},51:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCRippleFoundation=void 0;var s,c=r(7),u=r(54),l=r(50),f=["touchstart","pointerdown","mousedown","keydown"],d=["touchend","pointerup","mouseup","contextmenu"],p=[],v=(s=c.MDCFoundation,i(h,s),Object.defineProperty(h,"cssClasses",{get:function(){return u.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(h,"strings",{get:function(){return u.strings},enumerable:!1,configurable:!0}),Object.defineProperty(h,"numbers",{get:function(){return u.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(h,"defaultAdapter",{get:function(){return{addClass:function(){},browserSupportsCssVars:function(){return!0},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},containsEventTarget:function(){return!0},deregisterDocumentInteractionHandler:function(){},deregisterInteractionHandler:function(){},deregisterResizeHandler:function(){},getWindowPageOffset:function(){return{x:0,y:0}},isSurfaceActive:function(){return!0},isSurfaceDisabled:function(){return!0},isUnbounded:function(){return!0},registerDocumentInteractionHandler:function(){},registerInteractionHandler:function(){},registerResizeHandler:function(){},removeClass:function(){},updateCssVariable:function(){}}},enumerable:!1,configurable:!0}),h.prototype.init=function(){var t=this,e=this.supportsPressRipple();if(this.registerRootHandlers(e),e){var r=h.cssClasses,n=r.ROOT,i=r.UNBOUNDED;requestAnimationFrame(function(){t.adapter.addClass(n),t.adapter.isUnbounded()&&(t.adapter.addClass(i),t.layoutInternal())})}},h.prototype.destroy=function(){var t=this;if(this.supportsPressRipple()){this.activationTimer&&(clearTimeout(this.activationTimer),this.activationTimer=0,this.adapter.removeClass(h.cssClasses.FG_ACTIVATION)),this.fgDeactivationRemovalTimer&&(clearTimeout(this.fgDeactivationRemovalTimer),this.fgDeactivationRemovalTimer=0,this.adapter.removeClass(h.cssClasses.FG_DEACTIVATION));var e=h.cssClasses,r=e.ROOT,n=e.UNBOUNDED;requestAnimationFrame(function(){t.adapter.removeClass(r),t.adapter.removeClass(n),t.removeCssVars()})}this.deregisterRootHandlers(),this.deregisterDeactivationHandlers()},h.prototype.activate=function(t){this.activateImpl(t)},h.prototype.deactivate=function(){this.deactivateImpl()},h.prototype.layout=function(){var t=this;this.layoutFrame&&cancelAnimationFrame(this.layoutFrame),this.layoutFrame=requestAnimationFrame(function(){t.layoutInternal(),t.layoutFrame=0})},h.prototype.setUnbounded=function(t){var e=h.cssClasses.UNBOUNDED;t?this.adapter.addClass(e):this.adapter.removeClass(e)},h.prototype.handleFocus=function(){var t=this;requestAnimationFrame(function(){t.adapter.addClass(h.cssClasses.BG_FOCUSED)})},h.prototype.handleBlur=function(){var t=this;requestAnimationFrame(function(){t.adapter.removeClass(h.cssClasses.BG_FOCUSED)})},h.prototype.supportsPressRipple=function(){return this.adapter.browserSupportsCssVars()},h.prototype.defaultActivationState=function(){return{activationEvent:void 0,hasDeactivationUXRun:!1,isActivated:!1,isProgrammatic:!1,wasActivatedByPointer:!1,wasElementMadeActive:!1}},h.prototype.registerRootHandlers=function(t){var e,r;if(t){try{for(var n=a(f),i=n.next();!i.done;i=n.next()){var o=i.value;this.adapter.registerInteractionHandler(o,this.activateHandler)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}this.adapter.isUnbounded()&&this.adapter.registerResizeHandler(this.resizeHandler)}this.adapter.registerInteractionHandler("focus",this.focusHandler),this.adapter.registerInteractionHandler("blur",this.blurHandler)},h.prototype.registerDeactivationHandlers=function(t){var e,r;if("keydown"===t.type)this.adapter.registerInteractionHandler("keyup",this.deactivateHandler);else try{for(var n=a(d),i=n.next();!i.done;i=n.next()){var o=i.value;this.adapter.registerDocumentInteractionHandler(o,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},h.prototype.deregisterRootHandlers=function(){var e,t;try{for(var r=a(f),n=r.next();!n.done;n=r.next()){var i=n.value;this.adapter.deregisterInteractionHandler(i,this.activateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}this.adapter.deregisterInteractionHandler("focus",this.focusHandler),this.adapter.deregisterInteractionHandler("blur",this.blurHandler),this.adapter.isUnbounded()&&this.adapter.deregisterResizeHandler(this.resizeHandler)},h.prototype.deregisterDeactivationHandlers=function(){var e,t;this.adapter.deregisterInteractionHandler("keyup",this.deactivateHandler);try{for(var r=a(d),n=r.next();!n.done;n=r.next()){var i=n.value;this.adapter.deregisterDocumentInteractionHandler(i,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},h.prototype.removeCssVars=function(){var e=this,r=h.strings;Object.keys(r).forEach(function(t){0===t.indexOf("VAR_")&&e.adapter.updateCssVariable(r[t],null)})},h.prototype.activateImpl=function(t){var e=this;if(!this.adapter.isSurfaceDisabled()){var r=this.activationState;if(!r.isActivated){var n=this.previousActivationEvent;n&&void 0!==t&&n.type!==t.type||(r.isActivated=!0,r.isProgrammatic=void 0===t,r.activationEvent=t,r.wasActivatedByPointer=!r.isProgrammatic&&void 0!==t&&("mousedown"===t.type||"touchstart"===t.type||"pointerdown"===t.type),void 0!==t&&0<p.length&&p.some(function(t){return e.adapter.containsEventTarget(t)})?this.resetActivationState():(void 0!==t&&(p.push(t.target),this.registerDeactivationHandlers(t)),r.wasElementMadeActive=this.checkElementMadeActive(t),r.wasElementMadeActive&&this.animateActivation(),requestAnimationFrame(function(){p=[],r.wasElementMadeActive||void 0===t||" "!==t.key&&32!==t.keyCode||(r.wasElementMadeActive=e.checkElementMadeActive(t),r.wasElementMadeActive&&e.animateActivation()),r.wasElementMadeActive||(e.activationState=e.defaultActivationState())})))}}},h.prototype.checkElementMadeActive=function(t){return void 0===t||"keydown"!==t.type||this.adapter.isSurfaceActive()},h.prototype.animateActivation=function(){var t=this,e=h.strings,r=e.VAR_FG_TRANSLATE_START,n=e.VAR_FG_TRANSLATE_END,i=h.cssClasses,o=i.FG_DEACTIVATION,a=i.FG_ACTIVATION,s=h.numbers.DEACTIVATION_TIMEOUT_MS;this.layoutInternal();var c="",u="";if(!this.adapter.isUnbounded()){var l=this.getFgTranslationCoordinates(),f=l.startPoint,d=l.endPoint;c=f.x+"px, "+f.y+"px",u=d.x+"px, "+d.y+"px"}this.adapter.updateCssVariable(r,c),this.adapter.updateCssVariable(n,u),clearTimeout(this.activationTimer),clearTimeout(this.fgDeactivationRemovalTimer),this.rmBoundedActivationClasses(),this.adapter.removeClass(o),this.adapter.computeBoundingRect(),this.adapter.addClass(a),this.activationTimer=setTimeout(function(){t.activationTimerCallback()},s)},h.prototype.getFgTranslationCoordinates=function(){var t,e=this.activationState,r=e.activationEvent;return{startPoint:t={x:(t=e.wasActivatedByPointer?l.getNormalizedEventCoords(r,this.adapter.getWindowPageOffset(),this.adapter.computeBoundingRect()):{x:this.frame.width/2,y:this.frame.height/2}).x-this.initialSize/2,y:t.y-this.initialSize/2},endPoint:{x:this.frame.width/2-this.initialSize/2,y:this.frame.height/2-this.initialSize/2}}},h.prototype.runDeactivationUXLogicIfReady=function(){var t=this,e=h.cssClasses.FG_DEACTIVATION,r=this.activationState,n=r.hasDeactivationUXRun,i=r.isActivated;!n&&i||!this.activationAnimationHasEnded||(this.rmBoundedActivationClasses(),this.adapter.addClass(e),this.fgDeactivationRemovalTimer=setTimeout(function(){t.adapter.removeClass(e)},u.numbers.FG_DEACTIVATION_MS))},h.prototype.rmBoundedActivationClasses=function(){var t=h.cssClasses.FG_ACTIVATION;this.adapter.removeClass(t),this.activationAnimationHasEnded=!1,this.adapter.computeBoundingRect()},h.prototype.resetActivationState=function(){var t=this;this.previousActivationEvent=this.activationState.activationEvent,this.activationState=this.defaultActivationState(),setTimeout(function(){return t.previousActivationEvent=void 0},h.numbers.TAP_DELAY_MS)},h.prototype.deactivateImpl=function(){var t=this,e=this.activationState;if(e.isActivated){var r=o({},e);e.isProgrammatic?(requestAnimationFrame(function(){t.animateDeactivation(r)}),this.resetActivationState()):(this.deregisterDeactivationHandlers(),requestAnimationFrame(function(){t.activationState.hasDeactivationUXRun=!0,t.animateDeactivation(r),t.resetActivationState()}))}},h.prototype.animateDeactivation=function(t){var e=t.wasActivatedByPointer,r=t.wasElementMadeActive;(e||r)&&this.runDeactivationUXLogicIfReady()},h.prototype.layoutInternal=function(){var t=this;this.frame=this.adapter.computeBoundingRect();var e=Math.max(this.frame.height,this.frame.width);this.maxRadius=this.adapter.isUnbounded()?e:Math.sqrt(Math.pow(t.frame.width,2)+Math.pow(t.frame.height,2))+h.numbers.PADDING;var r=Math.floor(e*h.numbers.INITIAL_ORIGIN_SCALE);this.adapter.isUnbounded()&&r%2!=0?this.initialSize=r-1:this.initialSize=r,this.fgScale=""+this.maxRadius/this.initialSize,this.updateLayoutCssVars()},h.prototype.updateLayoutCssVars=function(){var t=h.strings,e=t.VAR_FG_SIZE,r=t.VAR_LEFT,n=t.VAR_TOP,i=t.VAR_FG_SCALE;this.adapter.updateCssVariable(e,this.initialSize+"px"),this.adapter.updateCssVariable(i,this.fgScale),this.adapter.isUnbounded()&&(this.unboundedCoords={left:Math.round(this.frame.width/2-this.initialSize/2),top:Math.round(this.frame.height/2-this.initialSize/2)},this.adapter.updateCssVariable(r,this.unboundedCoords.left+"px"),this.adapter.updateCssVariable(n,this.unboundedCoords.top+"px"))},h);function h(t){var e=s.call(this,o(o({},h.defaultAdapter),t))||this;return e.activationAnimationHasEnded=!1,e.activationTimer=0,e.fgDeactivationRemovalTimer=0,e.fgScale="0",e.frame={width:0,height:0},e.initialSize=0,e.layoutFrame=0,e.maxRadius=0,e.unboundedCoords={left:0,top:0},e.activationState=e.defaultActivationState(),e.activationTimerCallback=function(){e.activationAnimationHasEnded=!0,e.runDeactivationUXLogicIfReady()},e.activateHandler=function(t){e.activateImpl(t)},e.deactivateHandler=function(){e.deactivateImpl()},e.focusHandler=function(){e.handleFocus()},e.blurHandler=function(){e.handleBlur()},e.resizeHandler=function(){e.layout()},e}e.MDCRippleFoundation=v,e.default=v},52:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.applyPassive=void 0,e.applyPassive=function(t){return void 0===t&&(t=window),!!function(t){void 0===t&&(t=window);var e=!1;try{var r={get passive(){return!(e=!0)}},n=function(){};t.document.addEventListener("test",n,r),t.document.removeEventListener("test",n,r)}catch(t){e=!1}return e}(t)&&{passive:!0}}},53:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),a=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),s=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&o(e,t,r);return a(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCRipple=void 0;var c,u=r(13),l=r(52),f=r(49),d=r(51),p=s(r(50)),v=(c=u.MDCComponent,i(h,c),h.attachTo=function(t,e){void 0===e&&(e={isUnbounded:void 0});var r=new h(t);return void 0!==e.isUnbounded&&(r.unbounded=e.isUnbounded),r},h.createAdapter=function(r){return{addClass:function(t){r.root.classList.add(t)},browserSupportsCssVars:function(){return p.supportsCssVariables(window)},computeBoundingRect:function(){return r.root.getBoundingClientRect()},containsEventTarget:function(t){return r.root.contains(t)},deregisterDocumentInteractionHandler:function(t,e){document.documentElement.removeEventListener(t,e,l.applyPassive())},deregisterInteractionHandler:function(t,e){r.root.removeEventListener(t,e,l.applyPassive())},deregisterResizeHandler:function(t){window.removeEventListener("resize",t)},getWindowPageOffset:function(){return{x:window.pageXOffset,y:window.pageYOffset}},isSurfaceActive:function(){return f.matches(r.root,":active")},isSurfaceDisabled:function(){return Boolean(r.disabled)},isUnbounded:function(){return Boolean(r.unbounded)},registerDocumentInteractionHandler:function(t,e){document.documentElement.addEventListener(t,e,l.applyPassive())},registerInteractionHandler:function(t,e){r.root.addEventListener(t,e,l.applyPassive())},registerResizeHandler:function(t){window.addEventListener("resize",t)},removeClass:function(t){r.root.classList.remove(t)},updateCssVariable:function(t,e){r.root.style.setProperty(t,e)}}},Object.defineProperty(h.prototype,"unbounded",{get:function(){return Boolean(this.isUnbounded)},set:function(t){this.isUnbounded=Boolean(t),this.setUnbounded()},enumerable:!1,configurable:!0}),h.prototype.activate=function(){this.foundation.activate()},h.prototype.deactivate=function(){this.foundation.deactivate()},h.prototype.layout=function(){this.foundation.layout()},h.prototype.getDefaultFoundation=function(){return new d.MDCRippleFoundation(h.createAdapter(this))},h.prototype.initialSyncWithDOM=function(){var t=this.root;this.isUnbounded="mdcRippleIsUnbounded"in t.dataset},h.prototype.setUnbounded=function(){this.foundation.setUnbounded(Boolean(this.isUnbounded))},h);function h(){var t=null!==c&&c.apply(this,arguments)||this;return t.disabled=!1,t}e.MDCRipple=v},54:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.numbers=e.strings=e.cssClasses=void 0,e.cssClasses={BG_FOCUSED:"mdc-ripple-upgraded--background-focused",FG_ACTIVATION:"mdc-ripple-upgraded--foreground-activation",FG_DEACTIVATION:"mdc-ripple-upgraded--foreground-deactivation",ROOT:"mdc-ripple-upgraded",UNBOUNDED:"mdc-ripple-upgraded--unbounded"},e.strings={VAR_FG_SCALE:"--mdc-ripple-fg-scale",VAR_FG_SIZE:"--mdc-ripple-fg-size",VAR_FG_TRANSLATE_END:"--mdc-ripple-fg-translate-end",VAR_FG_TRANSLATE_START:"--mdc-ripple-fg-translate-start",VAR_LEFT:"--mdc-ripple-left",VAR_TOP:"--mdc-ripple-top"},e.numbers={DEACTIVATION_TIMEOUT_MS:225,FG_DEACTIVATION_MS:150,INITIAL_ORIGIN_SCALE:.6,PADDING:10,TAP_DELAY_MS:300}},59:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabIndicatorFoundation=void 0;var a,s=r(7),c=r(78),u=(a=s.MDCFoundation,i(l,a),Object.defineProperty(l,"cssClasses",{get:function(){return c.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(l,"strings",{get:function(){return c.strings},enumerable:!1,configurable:!0}),Object.defineProperty(l,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},computeContentClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},setContentStyleProperty:function(){}}},enumerable:!1,configurable:!0}),l.prototype.computeContentClientRect=function(){return this.adapter.computeContentClientRect()},l);function l(t){return a.call(this,o(o({},l.defaultAdapter),t))||this}e.MDCTabIndicatorFoundation=u,e.default=u},6:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assertIsTemplateObject=void 0,e.assertIsTemplateObject=function(t,e,r){if(!Array.isArray(t)||!Array.isArray(t.raw)||!e&&1!==t.length)throw new TypeError(r)}},7:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MDCFoundation=void 0;var n=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(t){void 0===t&&(t={}),this.adapter=t}e.MDCFoundation=n,e.default=n},77:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCFadingTabIndicatorFoundation=void 0;var o,a=r(59),s=(o=a.MDCTabIndicatorFoundation,i(c,o),c.prototype.activate=function(){this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c.prototype.deactivate=function(){this.adapter.removeClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c);function c(){return null!==o&&o.apply(this,arguments)||this}e.MDCFadingTabIndicatorFoundation=s,e.default=s},78:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.cssClasses=void 0;e.cssClasses={ACTIVE:"mdc-tab-indicator--active",FADE:"mdc-tab-indicator--fade",NO_TRANSITION:"mdc-tab-indicator--no-transition"};e.strings={CONTENT_SELECTOR:".mdc-tab-indicator__content"}},79:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCSlidingTabIndicatorFoundation=void 0;var o,a=r(59),s=(o=a.MDCTabIndicatorFoundation,i(c,o),c.prototype.activate=function(t){if(t){var e=this.computeContentClientRect(),r=t.width/e.width,n=t.left-e.left;this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.NO_TRANSITION),this.adapter.setContentStyleProperty("transform","translateX("+n+"px) scaleX("+r+")"),this.computeContentClientRect(),this.adapter.removeClass(a.MDCTabIndicatorFoundation.cssClasses.NO_TRANSITION),this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE),this.adapter.setContentStyleProperty("transform","")}else this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c.prototype.deactivate=function(){this.adapter.removeClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c);function c(){return null!==o&&o.apply(this,arguments)||this}e.MDCSlidingTabIndicatorFoundation=s,e.default=s},8:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapAttributePrefix=e.createAttributePrefix=e.SafeAttributePrefix=void 0,r(0);function o(){}var a=r(4);e.SafeAttributePrefix=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},u);function u(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=t,r}e.createAttributePrefix=function(t){return new c(t,a.secretToken)},e.unwrapAttributePrefix=function(t){if(t instanceof c)return t.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},86:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabIndicator=void 0;var o,a=r(13),s=r(77),c=r(59),u=r(79),l=(o=a.MDCComponent,i(f,o),f.attachTo=function(t){return new f(t)},f.prototype.initialize=function(){this.content=this.root.querySelector(c.MDCTabIndicatorFoundation.strings.CONTENT_SELECTOR)},f.prototype.computeContentClientRect=function(){return this.foundation.computeContentClientRect()},f.prototype.getDefaultFoundation=function(){var r=this,t={addClass:function(t){r.root.classList.add(t)},removeClass:function(t){r.root.classList.remove(t)},computeContentClientRect:function(){return r.content.getBoundingClientRect()},setContentStyleProperty:function(t,e){r.content.style.setProperty(t,e)}};return this.root.classList.contains(c.MDCTabIndicatorFoundation.cssClasses.FADE)?new s.MDCFadingTabIndicatorFoundation(t):new u.MDCSlidingTabIndicatorFoundation(t)},f.prototype.activate=function(t){this.foundation.activate(t)},f.prototype.deactivate=function(){this.foundation.deactivate()},f);function f(){return null!==o&&o.apply(this,arguments)||this}e.MDCTabIndicator=l},87:function(t,e,r){"use strict";var n,i=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabFoundation=void 0;var a,s=r(7),c=r(96),u=(a=s.MDCFoundation,i(l,a),Object.defineProperty(l,"cssClasses",{get:function(){return c.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(l,"strings",{get:function(){return c.strings},enumerable:!1,configurable:!0}),Object.defineProperty(l,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},setAttr:function(){},activateIndicator:function(){},deactivateIndicator:function(){},notifyInteracted:function(){},getOffsetLeft:function(){return 0},getOffsetWidth:function(){return 0},getContentOffsetLeft:function(){return 0},getContentOffsetWidth:function(){return 0},focus:function(){},isFocused:function(){return!1}}},enumerable:!1,configurable:!0}),l.prototype.handleClick=function(){this.adapter.notifyInteracted()},l.prototype.isActive=function(){return this.adapter.hasClass(c.cssClasses.ACTIVE)},l.prototype.setFocusOnActivate=function(t){this.focusOnActivate=t},l.prototype.activate=function(t){this.adapter.addClass(c.cssClasses.ACTIVE),this.adapter.setAttr(c.strings.ARIA_SELECTED,"true"),this.adapter.setAttr(c.strings.TABINDEX,"0"),this.adapter.activateIndicator(t),this.focusOnActivate&&!this.adapter.isFocused()&&this.adapter.focus()},l.prototype.deactivate=function(){this.isActive()&&(this.adapter.removeClass(c.cssClasses.ACTIVE),this.adapter.setAttr(c.strings.ARIA_SELECTED,"false"),this.adapter.setAttr(c.strings.TABINDEX,"-1"),this.adapter.deactivateIndicator())},l.prototype.computeDimensions=function(){var t=this.adapter.getOffsetWidth(),e=this.adapter.getOffsetLeft(),r=this.adapter.getContentOffsetWidth(),n=this.adapter.getContentOffsetLeft();return{contentLeft:e+n,contentRight:e+n+r,rootLeft:e,rootRight:e+t}},l);function l(t){var e=a.call(this,o(o({},l.defaultAdapter),t))||this;return e.focusOnActivate=!0,e}e.MDCTabFoundation=u,e.default=u},9:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEST_ONLY=e.getTrustedTypesPolicy=e.getTrustedTypes=void 0;var n,i="google#safe";function o(){var t;return""!==i&&null!==(t=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==t?t:null}e.getTrustedTypes=o,e.getTrustedTypesPolicy=function(){var t,e;if(void 0===n)try{n=null!==(e=null===(t=o())||void 0===t?void 0:t.createPolicy(i,{createHTML:function(t){return t},createScript:function(t){return t},createScriptURL:function(t){return t}}))&&void 0!==e?e:null}catch(t){n=null}return n},e.TEST_ONLY={resetDefaults:function(){n=void 0,i="google#safe"},setTrustedTypesPolicyName:function(t){i=t}}},96:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.cssClasses=void 0;e.cssClasses={ACTIVE:"mdc-tab--active"};e.strings={ARIA_SELECTED:"aria-selected",CONTENT_SELECTOR:".mdc-tab__content",INTERACTED_EVENT:"MDCTab:interacted",RIPPLE_SELECTOR:".mdc-tab__ripple",TABINDEX:"tabIndex",TAB_INDICATOR_SELECTOR:".mdc-tab-indicator"}}},i.c=n,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=291);function i(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}var r,n});