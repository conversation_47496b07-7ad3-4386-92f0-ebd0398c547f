/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://github.com/material-components/material-components-web/blob/master/LICENSE
 */
.mdc-banner__graphic {
  color: #fff;
  /* @alternate */
  color: var(--mdc-theme-surface, #fff);
}

.mdc-banner__graphic {
  background-color: #6200ee;
  /* @alternate */
  background-color: var(--mdc-theme-primary, #6200ee);
}

.mdc-banner__content,
.mdc-banner__fixed {
  min-width: 344px;
}
@media (max-width: 480px), (max-width: 344px) {
  .mdc-banner__content,
.mdc-banner__fixed {
    min-width: 100%;
  }
}

.mdc-banner__content {
  max-width: 720px;
}

.mdc-banner {
  z-index: 1;
  border-bottom-style: solid;
  box-sizing: border-box;
  display: none;
  flex-shrink: 0;
  height: 0;
  position: relative;
  width: 100%;
}
@media (max-width: 480px) {
  .mdc-banner .mdc-banner__fixed {
    left: 0;
    right: 0;
  }
  .mdc-banner .mdc-banner__text {
    /* @noflip */
    /*rtl:ignore*/
    margin-left: 16px;
    /* @noflip */
    /*rtl:ignore*/
    margin-right: 36px;
  }
  [dir=rtl] .mdc-banner .mdc-banner__text, .mdc-banner .mdc-banner__text[dir=rtl] {
    /*rtl:begin:ignore*/
    /* @noflip */
    /*rtl:ignore*/
    margin-left: 36px;
    /* @noflip */
    /*rtl:ignore*/
    margin-right: 16px;
    /*rtl:end:ignore*/
  }
}
@media (max-width: 480px) {
  .mdc-banner.mdc-banner--mobile-stacked .mdc-banner__content {
    flex-wrap: wrap;
  }
  .mdc-banner.mdc-banner--mobile-stacked .mdc-banner__graphic {
    margin-bottom: 12px;
  }
  .mdc-banner.mdc-banner--mobile-stacked .mdc-banner__text {
    /* @noflip */
    /*rtl:ignore*/
    margin-left: 16px;
    /* @noflip */
    /*rtl:ignore*/
    margin-right: 8px;
    padding-bottom: 4px;
  }
  [dir=rtl] .mdc-banner.mdc-banner--mobile-stacked .mdc-banner__text, .mdc-banner.mdc-banner--mobile-stacked .mdc-banner__text[dir=rtl] {
    /*rtl:begin:ignore*/
    /* @noflip */
    /*rtl:ignore*/
    margin-left: 8px;
    /* @noflip */
    /*rtl:ignore*/
    margin-right: 16px;
    /*rtl:end:ignore*/
  }

  .mdc-banner.mdc-banner--mobile-stacked .mdc-banner__actions {
    margin-left: auto;
  }
}

.mdc-banner--opening,
.mdc-banner--open,
.mdc-banner--closing {
  display: flex;
}

.mdc-banner--open {
  transition: height 300ms ease;
}
.mdc-banner--open .mdc-banner__content {
  transition: -webkit-transform 300ms ease;
  transition: transform 300ms ease;
  transition: transform 300ms ease, -webkit-transform 300ms ease;
  -webkit-transform: translateY(0);
          transform: translateY(0);
}

.mdc-banner--closing {
  transition: height 250ms ease;
}
.mdc-banner--closing .mdc-banner__content {
  transition: -webkit-transform 250ms ease;
  transition: transform 250ms ease;
  transition: transform 250ms ease, -webkit-transform 250ms ease;
}

.mdc-banner--centered .mdc-banner__content {
  left: 0;
  margin-left: auto;
  margin-right: auto;
  right: 0;
}

.mdc-banner__fixed {
  border-bottom-style: solid;
  box-sizing: border-box;
  height: inherit;
  position: fixed;
  width: 100%;
}

.mdc-banner__content {
  display: flex;
  min-height: 52px;
  position: absolute;
  -webkit-transform: translateY(-100%);
          transform: translateY(-100%);
  width: 100%;
}

.mdc-banner__graphic-text-wrapper {
  display: flex;
  width: 100%;
}

.mdc-banner__graphic {
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 16px;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 0;
  flex-shrink: 0;
  margin-top: 16px;
  margin-bottom: 16px;
  text-align: center;
}
[dir=rtl] .mdc-banner__graphic, .mdc-banner__graphic[dir=rtl] {
  /*rtl:begin:ignore*/
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 0;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 16px;
  /*rtl:end:ignore*/
}

.mdc-banner__icon {
  position: relative;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.mdc-banner__text {
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 24px;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 90px;
  align-self: center;
  flex-grow: 1;
  padding-top: 16px;
  padding-bottom: 16px;
}
[dir=rtl] .mdc-banner__text, .mdc-banner__text[dir=rtl] {
  /*rtl:begin:ignore*/
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 90px;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 24px;
  /*rtl:end:ignore*/
}

.mdc-banner__actions {
  /* @noflip */
  /*rtl:ignore*/
  padding-left: 0;
  /* @noflip */
  /*rtl:ignore*/
  padding-right: 8px;
  align-self: flex-end;
  display: flex;
  flex-shrink: 0;
  padding-bottom: 8px;
  padding-top: 8px;
}
[dir=rtl] .mdc-banner__actions, .mdc-banner__actions[dir=rtl] {
  /*rtl:begin:ignore*/
  /* @noflip */
  /*rtl:ignore*/
  padding-left: 8px;
  /* @noflip */
  /*rtl:ignore*/
  padding-right: 0;
  /*rtl:end:ignore*/
}

.mdc-banner__secondary-action {
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 0;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 8px;
}
[dir=rtl] .mdc-banner__secondary-action, .mdc-banner__secondary-action[dir=rtl] {
  /*rtl:begin:ignore*/
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 8px;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 0;
  /*rtl:end:ignore*/
}

.mdc-banner {
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-banner-container-color, #fff);
  border-bottom-color: rgba(0, 0, 0, 0.12);
  /* @alternate */
  border-bottom-color: var(--mdc-banner-divider-color, rgba(0, 0, 0, 0.12));
  border-bottom-width: 1px;
  /* @alternate */
  border-bottom-width: var(--mdc-banner-divider-height, 1px);
  border-radius: 0;
  /* @alternate */
  border-radius: var(--mdc-banner-container-shape, 0);
}
.mdc-banner .mdc-banner__text {
  color: #000;
  /* @alternate */
  color: var(--mdc-banner-supporting-text-color, #000);
}
.mdc-banner .mdc-banner__text {
  letter-spacing: 0.0178571429em;
  /* @alternate */
  letter-spacing: var(--mdc-banner-supporting-text-tracking, 0.0178571429em);
  font-size: 0.875rem;
  /* @alternate */
  font-size: var(--mdc-banner-supporting-text-size, 0.875rem);
  font-family: Roboto, sans-serif;
  /* @alternate */
  font-family: var(--mdc-banner-supporting-text-font, Roboto, sans-serif);
  font-weight: 400;
  /* @alternate */
  font-weight: var(--mdc-banner-supporting-text-weight, 400);
  line-height: 1.25rem;
  /* @alternate */
  line-height: var(--mdc-banner-supporting-text-line-height, 1.25rem);
}
.mdc-banner .mdc-banner__graphic {
  border-radius: 50%;
  /* @alternate */
  border-radius: var(--mdc-banner-with-image-image-shape, 50%);
}
.mdc-banner .mdc-banner__graphic {
  height: 40px;
  /* @alternate */
  height: var(--mdc-banner-with-image-image-size, 40px);
  width: 40px;
  /* @alternate */
  width: var(--mdc-banner-with-image-image-size, 40px);
}
.mdc-banner .mdc-banner__fixed {
  background-color: #fff;
  /* @alternate */
  background-color: var(--mdc-banner-container-color, #fff);
}
.mdc-banner .mdc-banner__fixed {
  border-bottom-color: rgba(0, 0, 0, 0.12);
  /* @alternate */
  border-bottom-color: var(--mdc-banner-divider-color, rgba(0, 0, 0, 0.12));
}
.mdc-banner .mdc-banner__fixed {
  border-bottom-width: 1px;
  /* @alternate */
  border-bottom-width: var(--mdc-banner-divider-height, 1px);
}
.mdc-banner .mdc-button:not(:disabled) {
  color: #6200ee;
  /* @alternate */
  color: var(--mdc-text-button-label-text-color, var(--mdc-banner-action-label-text-color, #6200ee));
}
.mdc-banner .mdc-button .mdc-button__ripple::before {
  background-color: #6200ee;
  /* @alternate */
  background-color: var(--mdc-text-button-hover-state-layer-color, var(--mdc-banner-action-hover-state-layer-color, #6200ee));
}
.mdc-banner .mdc-button .mdc-button__ripple::after {
  background-color: #6200ee;
  /* @alternate */
  background-color: var(--mdc-text-button-pressed-state-layer-color, var(--mdc-banner-action-pressed-state-layer-color, #6200ee));
}
.mdc-banner .mdc-button:hover .mdc-button__ripple::before, .mdc-banner .mdc-button.mdc-ripple-surface--hover .mdc-button__ripple::before {
  opacity: 0.04;
  /* @alternate */
  opacity: var(--mdc-text-button-hover-state-layer-opacity, var(--mdc-banner-action-hover-state-layer-opacity, 0.04));
}
.mdc-banner .mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before, .mdc-banner .mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before {
  transition-duration: 75ms;
  opacity: 0.12;
  /* @alternate */
  opacity: var(--mdc-text-button-focus-state-layer-opacity, var(--mdc-banner-action-focus-state-layer-opacity, 0.12));
}
.mdc-banner .mdc-button:not(.mdc-ripple-upgraded) .mdc-button__ripple::after {
  transition: opacity 150ms linear;
}
.mdc-banner .mdc-button:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after {
  transition-duration: 75ms;
  opacity: 0.1;
  /* @alternate */
  opacity: var(--mdc-text-button-pressed-state-layer-opacity, var(--mdc-banner-action-pressed-state-layer-opacity, 0.1));
}
.mdc-banner .mdc-button.mdc-ripple-upgraded {
  --mdc-ripple-fg-opacity: var(--mdc-text-button-pressed-state-layer-opacity, var(--mdc-banner-action-pressed-state-layer-opacity, 0.1));
}
.mdc-banner__secondary-action {
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 0;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 8px;
}
[dir=rtl] .mdc-banner__secondary-action, .mdc-banner__secondary-action[dir=rtl] {
  /*rtl:begin:ignore*/
  /* @noflip */
  /*rtl:ignore*/
  margin-left: 8px;
  /* @noflip */
  /*rtl:ignore*/
  margin-right: 0;
  /*rtl:end:ignore*/
}

/*# sourceMappingURL=mdc.banner.css.map*/