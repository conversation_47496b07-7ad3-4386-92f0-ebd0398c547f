{"version": 3, "file": "service-worker.mjs", "sources": ["../../../../../../packages/service-worker/src/low_level.ts", "../../../../../../packages/service-worker/src/push.ts", "../../../../../../packages/service-worker/src/update.ts", "../../../../../../packages/service-worker/src/provider.ts", "../../../../../../packages/service-worker/src/module.ts", "../../../../../../packages/service-worker/public_api.ts", "../../../../../../packages/service-worker/index.ts", "../../../../../../packages/service-worker/service-worker.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {concat, ConnectableObservable, defer, fromEvent, Observable, of, throwError} from 'rxjs';\nimport {filter, map, publish, switchMap, take, tap} from 'rxjs/operators';\n\nexport const ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\n\n/**\n * An event emitted when the service worker has checked the version of the app on the server and it\n * didn't find a new version that it doesn't have already downloaded.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nexport interface NoNewVersionDetectedEvent {\n  type: 'NO_NEW_VERSION_DETECTED';\n  version: {hash: string; appData?: Object};\n}\n\n/**\n * An event emitted when the service worker has detected a new version of the app on the server and\n * is about to start downloading it.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nexport interface VersionDetectedEvent {\n  type: 'VERSION_DETECTED';\n  version: {hash: string; appData?: object};\n}\n\n/**\n * An event emitted when the installation of a new version failed.\n * It may be used for logging/monitoring purposes.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nexport interface VersionInstallationFailedEvent {\n  type: 'VERSION_INSTALLATION_FAILED';\n  version: {hash: string; appData?: object};\n  error: string;\n}\n\n/**\n * An event emitted when a new version of the app is available.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nexport interface VersionReadyEvent {\n  type: 'VERSION_READY';\n  currentVersion: {hash: string; appData?: object};\n  latestVersion: {hash: string; appData?: object};\n}\n\n/**\n * A union of all event types that can be emitted by\n * {@link api/service-worker/SwUpdate#versionUpdates SwUpdate#versionUpdates}.\n *\n * @publicApi\n */\nexport type VersionEvent =\n  | VersionDetectedEvent\n  | VersionInstallationFailedEvent\n  | VersionReadyEvent\n  | NoNewVersionDetectedEvent;\n\n/**\n * An event emitted when the version of the app used by the service worker to serve this client is\n * in a broken state that cannot be recovered from and a full page reload is required.\n *\n * For example, the service worker may not be able to retrieve a required resource, neither from the\n * cache nor from the server. This could happen if a new version is deployed to the server and the\n * service worker cache has been partially cleaned by the browser, removing some files of a previous\n * app version but not all.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nexport interface UnrecoverableStateEvent {\n  type: 'UNRECOVERABLE_STATE';\n  reason: string;\n}\n\n/**\n * An event emitted when a `PushEvent` is received by the service worker.\n */\nexport interface PushEvent {\n  type: 'PUSH';\n  data: any;\n}\n\nexport type IncomingEvent = UnrecoverableStateEvent | VersionEvent;\n\nexport interface TypedEvent {\n  type: string;\n}\n\ntype OperationCompletedEvent =\n  | {\n      type: 'OPERATION_COMPLETED';\n      nonce: number;\n      result: boolean;\n    }\n  | {\n      type: 'OPERATION_COMPLETED';\n      nonce: number;\n      result?: undefined;\n      error: string;\n    };\n\nfunction errorObservable(message: string): Observable<any> {\n  return defer(() => throwError(new Error(message)));\n}\n\n/**\n * @publicApi\n */\nexport class NgswCommChannel {\n  readonly worker: Observable<ServiceWorker>;\n\n  readonly registration: Observable<ServiceWorkerRegistration>;\n\n  readonly events: Observable<TypedEvent>;\n\n  constructor(private serviceWorker: ServiceWorkerContainer | undefined) {\n    if (!serviceWorker) {\n      this.worker = this.events = this.registration = errorObservable(ERR_SW_NOT_SUPPORTED);\n    } else {\n      const controllerChangeEvents = fromEvent(serviceWorker, 'controllerchange');\n      const controllerChanges = controllerChangeEvents.pipe(map(() => serviceWorker.controller));\n      const currentController = defer(() => of(serviceWorker.controller));\n      const controllerWithChanges = concat(currentController, controllerChanges);\n\n      this.worker = controllerWithChanges.pipe(filter((c): c is ServiceWorker => !!c));\n\n      this.registration = <Observable<ServiceWorkerRegistration>>(\n        this.worker.pipe(switchMap(() => serviceWorker.getRegistration()))\n      );\n\n      const rawEvents = fromEvent<MessageEvent>(serviceWorker, 'message');\n      const rawEventPayload = rawEvents.pipe(map((event) => event.data));\n      const eventsUnconnected = rawEventPayload.pipe(filter((event) => event && event.type));\n      const events = eventsUnconnected.pipe(publish()) as ConnectableObservable<IncomingEvent>;\n      events.connect();\n\n      this.events = events;\n    }\n  }\n\n  postMessage(action: string, payload: Object): Promise<void> {\n    return this.worker\n      .pipe(\n        take(1),\n        tap((sw: ServiceWorker) => {\n          sw.postMessage({\n            action,\n            ...payload,\n          });\n        }),\n      )\n      .toPromise()\n      .then(() => undefined);\n  }\n\n  postMessageWithOperation(\n    type: string,\n    payload: Object,\n    operationNonce: number,\n  ): Promise<boolean> {\n    const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n    const postMessage = this.postMessage(type, payload);\n    return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n  }\n\n  generateNonce(): number {\n    return Math.round(Math.random() * 10000000);\n  }\n\n  eventsOfType<T extends TypedEvent>(type: T['type'] | T['type'][]): Observable<T> {\n    let filterFn: (event: TypedEvent) => event is T;\n    if (typeof type === 'string') {\n      filterFn = (event: TypedEvent): event is T => event.type === type;\n    } else {\n      filterFn = (event: TypedEvent): event is T => type.includes(event.type);\n    }\n    return this.events.pipe(filter(filterFn));\n  }\n\n  nextEventOfType<T extends TypedEvent>(type: T['type']): Observable<T> {\n    return this.eventsOfType(type).pipe(take(1));\n  }\n\n  waitForOperationCompleted(nonce: number): Promise<boolean> {\n    return this.eventsOfType<OperationCompletedEvent>('OPERATION_COMPLETED')\n      .pipe(\n        filter((event) => event.nonce === nonce),\n        take(1),\n        map((event) => {\n          if (event.result !== undefined) {\n            return event.result;\n          }\n          throw new Error(event.error!);\n        }),\n      )\n      .toPromise() as Promise<boolean>;\n  }\n\n  get isEnabled(): boolean {\n    return !!this.serviceWorker;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {merge, NEVER, Observable, Subject} from 'rxjs';\nimport {map, switchMap, take} from 'rxjs/operators';\n\nimport {ERR_SW_NOT_SUPPORTED, NgswCommChannel, PushEvent} from './low_level';\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](guide/service-worker-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\n@Injectable()\nexport class SwPush {\n  /**\n   * Emits the payloads of the received push notification messages.\n   */\n  readonly messages: Observable<object>;\n\n  /**\n   * Emits the payloads of the received push notification messages as well as the action the user\n   * interacted with. If no action was used the `action` property contains an empty string `''`.\n   *\n   * Note that the `notification` property does **not** contain a\n   * [Notification][Mozilla Notification] object but rather a\n   * [NotificationOptions](https://notifications.spec.whatwg.org/#dictdef-notificationoptions)\n   * object that also includes the `title` of the [Notification][Mozilla Notification] object.\n   *\n   * [Mozilla Notification]: https://developer.mozilla.org/en-US/docs/Web/API/Notification\n   */\n  readonly notificationClicks: Observable<{\n    action: string;\n    notification: NotificationOptions & {\n      title: string;\n    };\n  }>;\n\n  /**\n   * Emits the currently active\n   * [PushSubscription](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n   * associated to the Service Worker registration or `null` if there is no subscription.\n   */\n  readonly subscription: Observable<PushSubscription | null>;\n\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled(): boolean {\n    return this.sw.isEnabled;\n  }\n\n  private pushManager: Observable<PushManager> | null = null;\n  private subscriptionChanges = new Subject<PushSubscription | null>();\n\n  constructor(private sw: NgswCommChannel) {\n    if (!sw.isEnabled) {\n      this.messages = NEVER;\n      this.notificationClicks = NEVER;\n      this.subscription = NEVER;\n      return;\n    }\n\n    this.messages = this.sw.eventsOfType<PushEvent>('PUSH').pipe(map((message) => message.data));\n\n    this.notificationClicks = this.sw\n      .eventsOfType('NOTIFICATION_CLICK')\n      .pipe(map((message: any) => message.data));\n\n    this.pushManager = this.sw.registration.pipe(map((registration) => registration.pushManager));\n\n    const workerDrivenSubscriptions = this.pushManager.pipe(\n      switchMap((pm) => pm.getSubscription()),\n    );\n    this.subscription = merge(workerDrivenSubscriptions, this.subscriptionChanges);\n  }\n\n  /**\n   * Subscribes to Web Push Notifications,\n   * after requesting and receiving user permission.\n   *\n   * @param options An object containing the `serverPublicKey` string.\n   * @returns A Promise that resolves to the new subscription object.\n   */\n  requestSubscription(options: {serverPublicKey: string}): Promise<PushSubscription> {\n    if (!this.sw.isEnabled || this.pushManager === null) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const pushOptions: PushSubscriptionOptionsInit = {userVisibleOnly: true};\n    let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n    let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n    for (let i = 0; i < key.length; i++) {\n      applicationServerKey[i] = key.charCodeAt(i);\n    }\n    pushOptions.applicationServerKey = applicationServerKey;\n\n    return this.pushManager\n      .pipe(\n        switchMap((pm) => pm.subscribe(pushOptions)),\n        take(1),\n      )\n      .toPromise()\n      .then((sub) => {\n        this.subscriptionChanges.next(sub!);\n        return sub!;\n      });\n  }\n\n  /**\n   * Unsubscribes from Service Worker push notifications.\n   *\n   * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n   *          active subscription or the unsubscribe operation fails.\n   */\n  unsubscribe(): Promise<void> {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n\n    const doUnsubscribe = (sub: PushSubscription | null) => {\n      if (sub === null) {\n        throw new Error('Not subscribed to push notifications.');\n      }\n\n      return sub.unsubscribe().then((success) => {\n        if (!success) {\n          throw new Error('Unsubscribe failed!');\n        }\n\n        this.subscriptionChanges.next(null);\n      });\n    };\n\n    return this.subscription.pipe(take(1), switchMap(doUnsubscribe)).toPromise();\n  }\n\n  private decodeBase64(input: string): string {\n    return atob(input);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {NEVER, Observable} from 'rxjs';\n\nimport {\n  ERR_SW_NOT_SUPPORTED,\n  NgswCommChannel,\n  UnrecoverableStateEvent,\n  VersionEvent,\n} from './low_level';\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\n@Injectable()\nexport class SwUpdate {\n  /**\n   * Emits a `VersionDetectedEvent` event whenever a new version is detected on the server.\n   *\n   * Emits a `VersionInstallationFailedEvent` event whenever checking for or downloading a new\n   * version fails.\n   *\n   * Emits a `VersionReadyEvent` event whenever a new version has been downloaded and is ready for\n   * activation.\n   */\n  readonly versionUpdates: Observable<VersionEvent>;\n\n  /**\n   * Emits an `UnrecoverableStateEvent` event whenever the version of the app used by the service\n   * worker to serve this client is in a broken state that cannot be recovered from without a full\n   * page reload.\n   */\n  readonly unrecoverable: Observable<UnrecoverableStateEvent>;\n\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled(): boolean {\n    return this.sw.isEnabled;\n  }\n\n  constructor(private sw: NgswCommChannel) {\n    if (!sw.isEnabled) {\n      this.versionUpdates = NEVER;\n      this.unrecoverable = NEVER;\n      return;\n    }\n    this.versionUpdates = this.sw.eventsOfType<VersionEvent>([\n      'VERSION_DETECTED',\n      'VERSION_INSTALLATION_FAILED',\n      'VERSION_READY',\n      'NO_NEW_VERSION_DETECTED',\n    ]);\n    this.unrecoverable = this.sw.eventsOfType<UnrecoverableStateEvent>('UNRECOVERABLE_STATE');\n  }\n\n  /**\n   * Checks for an update and waits until the new version is downloaded from the server and ready\n   * for activation.\n   *\n   * @returns a promise that\n   * - resolves to `true` if a new version was found and is ready to be activated.\n   * - resolves to `false` if no new version was found\n   * - rejects if any error occurs\n   */\n  checkForUpdate(): Promise<boolean> {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', {nonce}, nonce);\n  }\n\n  /**\n   * Updates the current client (i.e. browser tab) to the latest version that is ready for\n   * activation.\n   *\n   * In most cases, you should not use this method and instead should update a client by reloading\n   * the page.\n   *\n   * <div class=\"alert is-important\">\n   *\n   * Updating a client without reloading can easily result in a broken application due to a version\n   * mismatch between the [application shell](guide/glossary#app-shell) and other page resources,\n   * such as [lazy-loaded chunks](guide/glossary#lazy-loading), whose filenames may change between\n   * versions.\n   *\n   * Only use this method, if you are certain it is safe for your specific use case.\n   *\n   * </div>\n   *\n   * @returns a promise that\n   *  - resolves to `true` if an update was activated successfully\n   *  - resolves to `false` if no update was available (for example, the client was already on the\n   *    latest version).\n   *  - rejects if any error occurs\n   */\n  activateUpdate(): Promise<boolean> {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', {nonce}, nonce);\n  }\n}\n", "/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {isPlatformBrowser} from '@angular/common';\nimport {\n  APP_INITIALIZER,\n  ApplicationRef,\n  EnvironmentProviders,\n  InjectionToken,\n  Injector,\n  makeEnvironmentProviders,\n  NgZone,\n  PLATFORM_ID,\n} from '@angular/core';\nimport {merge, Observable, of} from 'rxjs';\nimport {delay, filter, take} from 'rxjs/operators';\n\nimport {NgswCommChannel} from './low_level';\nimport {SwPush} from './push';\nimport {SwUpdate} from './update';\n\nexport const SCRIPT = new InjectionToken<string>(ngDevMode ? 'NGSW_REGISTER_SCRIPT' : '');\n\nexport function ngswAppInitializer(\n  injector: Injector,\n  script: string,\n  options: SwRegistrationOptions,\n  platformId: string,\n): Function {\n  return () => {\n    if (\n      !(isPlatformBrowser(platformId) && 'serviceWorker' in navigator && options.enabled !== false)\n    ) {\n      return;\n    }\n\n    // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n    // becomes active. This allows the SW to initialize itself even if there is no application\n    // traffic.\n    navigator.serviceWorker.addEventListener('controllerchange', () => {\n      if (navigator.serviceWorker.controller !== null) {\n        navigator.serviceWorker.controller.postMessage({action: 'INITIALIZE'});\n      }\n    });\n\n    let readyToRegister$: Observable<unknown>;\n\n    if (typeof options.registrationStrategy === 'function') {\n      readyToRegister$ = options.registrationStrategy();\n    } else {\n      const [strategy, ...args] = (\n        options.registrationStrategy || 'registerWhenStable:30000'\n      ).split(':');\n\n      switch (strategy) {\n        case 'registerImmediately':\n          readyToRegister$ = of(null);\n          break;\n        case 'registerWithDelay':\n          readyToRegister$ = delayWithTimeout(+args[0] || 0);\n          break;\n        case 'registerWhenStable':\n          readyToRegister$ = !args[0]\n            ? whenStable(injector)\n            : merge(whenStable(injector), delayWithTimeout(+args[0]));\n          break;\n        default:\n          // Unknown strategy.\n          throw new Error(\n            `Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`,\n          );\n      }\n    }\n\n    // Don't return anything to avoid blocking the application until the SW is registered.\n    // Also, run outside the Angular zone to avoid preventing the app from stabilizing (especially\n    // given that some registration strategies wait for the app to stabilize).\n    // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n    const ngZone = injector.get(NgZone);\n    ngZone.runOutsideAngular(() =>\n      readyToRegister$\n        .pipe(take(1))\n        .subscribe(() =>\n          navigator.serviceWorker\n            .register(script, {scope: options.scope})\n            .catch((err) => console.error('Service worker registration failed with:', err)),\n        ),\n    );\n  };\n}\n\nfunction delayWithTimeout(timeout: number): Observable<unknown> {\n  return of(null).pipe(delay(timeout));\n}\n\nfunction whenStable(injector: Injector): Observable<unknown> {\n  const appRef = injector.get(ApplicationRef);\n  return appRef.isStable.pipe(filter((stable) => stable));\n}\n\nexport function ngswCommChannelFactory(\n  opts: SwRegistrationOptions,\n  platformId: string,\n): NgswCommChannel {\n  return new NgswCommChannel(\n    isPlatformBrowser(platformId) && opts.enabled !== false ? navigator.serviceWorker : undefined,\n  );\n}\n\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nexport abstract class SwRegistrationOptions {\n  /**\n   * Whether the ServiceWorker will be registered and the related services (such as `SwPush` and\n   * `SwUpdate`) will attempt to communicate and interact with it.\n   *\n   * Default: true\n   */\n  enabled?: boolean;\n\n  /**\n   * A URL that defines the ServiceWorker's registration scope; that is, what range of URLs it can\n   * control. It will be used when calling\n   * [ServiceWorkerContainer#register()](https://developer.mozilla.org/en-US/docs/Web/API/ServiceWorkerContainer/register).\n   */\n  scope?: string;\n\n  /**\n   * Defines the ServiceWorker registration strategy, which determines when it will be registered\n   * with the browser.\n   *\n   * The default behavior of registering once the application stabilizes (i.e. as soon as there are\n   * no pending micro- and macro-tasks) is designed to register the ServiceWorker as soon as\n   * possible but without affecting the application's first time load.\n   *\n   * Still, there might be cases where you want more control over when the ServiceWorker is\n   * registered (for example, there might be a long-running timeout or polling interval, preventing\n   * the app from stabilizing). The available option are:\n   *\n   * - `registerWhenStable:<timeout>`: Register as soon as the application stabilizes (no pending\n   *     micro-/macro-tasks) but no later than `<timeout>` milliseconds. If the app hasn't\n   *     stabilized after `<timeout>` milliseconds (for example, due to a recurrent asynchronous\n   *     task), the ServiceWorker will be registered anyway.\n   *     If `<timeout>` is omitted, the ServiceWorker will only be registered once the app\n   *     stabilizes.\n   * - `registerImmediately`: Register immediately.\n   * - `registerWithDelay:<timeout>`: Register with a delay of `<timeout>` milliseconds. For\n   *     example, use `registerWithDelay:5000` to register the ServiceWorker after 5 seconds. If\n   *     `<timeout>` is omitted, is defaults to `0`, which will register the ServiceWorker as soon\n   *     as possible but still asynchronously, once all pending micro-tasks are completed.\n   * - An [Observable](guide/observables) factory function: A function that returns an `Observable`.\n   *     The function will be used at runtime to obtain and subscribe to the `Observable` and the\n   *     ServiceWorker will be registered as soon as the first value is emitted.\n   *\n   * Default: 'registerWhenStable:30000'\n   */\n  registrationStrategy?: string | (() => Observable<unknown>);\n}\n\n/**\n * @publicApi\n *\n * Sets up providers to register the given Angular Service Worker script.\n *\n * If `enabled` is set to `false` in the given options, the module will behave as if service\n * workers are not supported by the browser, and the service worker will not be registered.\n *\n * Example usage:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideServiceWorker('ngsw-worker.js')\n *   ],\n * });\n * ```\n */\nexport function provideServiceWorker(\n  script: string,\n  options: SwRegistrationOptions = {},\n): EnvironmentProviders {\n  return makeEnvironmentProviders([\n    SwPush,\n    SwUpdate,\n    {provide: SCRIPT, useValue: script},\n    {provide: SwRegistrationOptions, useValue: options},\n    {\n      provide: NgswCommChannel,\n      useFactory: ngswCommChannelFactory,\n      deps: [SwRegistrationOptions, PLATFORM_ID],\n    },\n    {\n      provide: APP_INITIALIZER,\n      useFactory: ngswAppInitializer,\n      deps: [Injector, SCRIPT, SwRegistrationOptions, PLATFORM_ID],\n      multi: true,\n    },\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ModuleWithProviders, NgModule} from '@angular/core';\n\nimport {provideServiceWorker, SwRegistrationOptions} from './provider';\nimport {SwPush} from './push';\nimport {SwUpdate} from './update';\n\n/**\n * @publicApi\n */\n@NgModule({providers: [SwPush, SwUpdate]})\nexport class ServiceWorkerModule {\n  /**\n   * Register the given Angular Service Worker script.\n   *\n   * If `enabled` is set to `false` in the given options, the module will behave as if service\n   * workers are not supported by the browser, and the service worker will not be registered.\n   */\n  static register(\n    script: string,\n    options: SwRegistrationOptions = {},\n  ): ModuleWithProviders<ServiceWorkerModule> {\n    return {\n      ngModule: ServiceWorkerModule,\n      providers: [provideServiceWorker(script, options)],\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/index';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.NgswCommChannel"], "mappings": ";;;;;;;;;;;;AAWO,MAAM,oBAAoB,GAAG,+DAA+D,CAAC;AAgHpG,SAAS,eAAe,CAAC,OAAe,EAAA;AACtC,IAAA,OAAO,KAAK,CAAC,MAAM,UAAU,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC;AAED;;AAEG;MACU,eAAe,CAAA;AAO1B,IAAA,WAAA,CAAoB,aAAiD,EAAA;QAAjD,IAAa,CAAA,aAAA,GAAb,aAAa,CAAoC;QACnE,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,eAAe,CAAC,oBAAoB,CAAC,CAAC;SACvF;aAAM;YACL,MAAM,sBAAsB,GAAG,SAAS,CAAC,aAAa,EAAE,kBAAkB,CAAC,CAAC;AAC5E,YAAA,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;AAC3F,YAAA,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;YACpE,MAAM,qBAAqB,GAAG,MAAM,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;AAE3E,YAAA,IAAI,CAAC,MAAM,GAAG,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEjF,IAAI,CAAC,YAAY,IACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,CACnE,CAAC;YAEF,MAAM,SAAS,GAAG,SAAS,CAAe,aAAa,EAAE,SAAS,CAAC,CAAC;AACpE,YAAA,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YACnE,MAAM,iBAAiB,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YACvF,MAAM,MAAM,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,CAAyC,CAAC;YACzF,MAAM,CAAC,OAAO,EAAE,CAAC;AAEjB,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;KACF;IAED,WAAW,CAAC,MAAc,EAAE,OAAe,EAAA;QACzC,OAAO,IAAI,CAAC,MAAM;aACf,IAAI,CACH,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,CAAC,EAAiB,KAAI;YACxB,EAAE,CAAC,WAAW,CAAC;gBACb,MAAM;AACN,gBAAA,GAAG,OAAO;AACX,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CACH;AACA,aAAA,SAAS,EAAE;AACX,aAAA,IAAI,CAAC,MAAM,SAAS,CAAC,CAAC;KAC1B;AAED,IAAA,wBAAwB,CACtB,IAAY,EACZ,OAAe,EACf,cAAsB,EAAA;QAEtB,MAAM,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,CAAC;QACjF,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC;KAC3F;IAED,aAAa,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;KAC7C;AAED,IAAA,YAAY,CAAuB,IAA6B,EAAA;AAC9D,QAAA,IAAI,QAA2C,CAAC;AAChD,QAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,QAAQ,GAAG,CAAC,KAAiB,KAAiB,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC;SACnE;aAAM;AACL,YAAA,QAAQ,GAAG,CAAC,KAAiB,KAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACzE;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;KAC3C;AAED,IAAA,eAAe,CAAuB,IAAe,EAAA;AACnD,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9C;AAED,IAAA,yBAAyB,CAAC,KAAa,EAAA;AACrC,QAAA,OAAO,IAAI,CAAC,YAAY,CAA0B,qBAAqB,CAAC;aACrE,IAAI,CACH,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EACxC,IAAI,CAAC,CAAC,CAAC,EACP,GAAG,CAAC,CAAC,KAAK,KAAI;AACZ,YAAA,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE;gBAC9B,OAAO,KAAK,CAAC,MAAM,CAAC;aACrB;AACD,YAAA,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,KAAM,CAAC,CAAC;AAChC,SAAC,CAAC,CACH;AACA,aAAA,SAAS,EAAsB,CAAC;KACpC;AAED,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;KAC7B;AACF;;ACjND;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EG;MAEU,MAAM,CAAA;AA+BjB;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;KAC1B;AAKD,IAAA,WAAA,CAAoB,EAAmB,EAAA;QAAnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAiB;QAH/B,IAAW,CAAA,WAAA,GAAmC,IAAI,CAAC;AACnD,QAAA,IAAA,CAAA,mBAAmB,GAAG,IAAI,OAAO,EAA2B,CAAC;AAGnE,QAAA,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;AACtB,YAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AAChC,YAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,OAAO;SACR;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAY,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAE7F,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE;aAC9B,YAAY,CAAC,oBAAoB,CAAC;AAClC,aAAA,IAAI,CAAC,GAAG,CAAC,CAAC,OAAY,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;QAE7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;QAE9F,MAAM,yBAAyB,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CACrD,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,eAAe,EAAE,CAAC,CACxC,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,yBAAyB,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;KAChF;AAED;;;;;;AAMG;AACH,IAAA,mBAAmB,CAAC,OAAkC,EAAA;AACpD,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,IAAI,EAAE;YACnD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACxD;AACD,QAAA,MAAM,WAAW,GAAgC,EAAC,eAAe,EAAE,IAAI,EAAC,CAAC;QACzE,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3F,QAAA,IAAI,oBAAoB,GAAG,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AACvE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnC,oBAAoB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SAC7C;AACD,QAAA,WAAW,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;QAExD,OAAO,IAAI,CAAC,WAAW;aACpB,IAAI,CACH,SAAS,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,EAC5C,IAAI,CAAC,CAAC,CAAC,CACR;AACA,aAAA,SAAS,EAAE;AACX,aAAA,IAAI,CAAC,CAAC,GAAG,KAAI;AACZ,YAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAI,CAAC,CAAC;AACpC,YAAA,OAAO,GAAI,CAAC;AACd,SAAC,CAAC,CAAC;KACN;AAED;;;;;AAKG;IACH,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACxD;AAED,QAAA,MAAM,aAAa,GAAG,CAAC,GAA4B,KAAI;AACrD,YAAA,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,gBAAA,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;aAC1D;YAED,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,KAAI;gBACxC,IAAI,CAAC,OAAO,EAAE;AACZ,oBAAA,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;iBACxC;AAED,gBAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACtC,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;AAEF,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;KAC9E;AAEO,IAAA,YAAY,CAAC,KAAa,EAAA;AAChC,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;KACpB;yHA7HU,MAAM,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAN,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAAN,MAAM,EAAA,UAAA,EAAA,CAAA;kBADlB,UAAU;;;AC3EX;;;;;;;AAOG;MAEU,QAAQ,CAAA;AAmBnB;;;AAGG;AACH,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;KAC1B;AAED,IAAA,WAAA,CAAoB,EAAmB,EAAA;QAAnB,IAAE,CAAA,EAAA,GAAF,EAAE,CAAiB;AACrC,QAAA,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;AACjB,YAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC5B,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,OAAO;SACR;QACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAe;YACvD,kBAAkB;YAClB,6BAA6B;YAC7B,eAAe;YACf,yBAAyB;AAC1B,SAAA,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAA0B,qBAAqB,CAAC,CAAC;KAC3F;AAED;;;;;;;;AAQG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACxD;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AACtC,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,wBAAwB,CAAC,mBAAmB,EAAE,EAAC,KAAK,EAAC,EAAE,KAAK,CAAC,CAAC;KAC9E;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBG;IACH,cAAc,GAAA;AACZ,QAAA,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE;YACtB,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACxD;QACD,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC;AACtC,QAAA,OAAO,IAAI,CAAC,EAAE,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,EAAC,KAAK,EAAC,EAAE,KAAK,CAAC,CAAC;KAC5E;yHAzFU,QAAQ,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,eAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAR,QAAQ,EAAA,CAAA,CAAA,EAAA;;sGAAR,QAAQ,EAAA,UAAA,EAAA,CAAA;kBADpB,UAAU;;;AC1BX;;;;;;AAMG;AAoBI,MAAM,MAAM,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC,CAAC;AAEpF,SAAU,kBAAkB,CAChC,QAAkB,EAClB,MAAc,EACd,OAA8B,EAC9B,UAAkB,EAAA;AAElB,IAAA,OAAO,MAAK;AACV,QAAA,IACE,EAAE,iBAAiB,CAAC,UAAU,CAAC,IAAI,eAAe,IAAI,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,EAC7F;YACA,OAAO;SACR;;;;QAKD,SAAS,CAAC,aAAa,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAK;YAChE,IAAI,SAAS,CAAC,aAAa,CAAC,UAAU,KAAK,IAAI,EAAE;AAC/C,gBAAA,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,EAAC,MAAM,EAAE,YAAY,EAAC,CAAC,CAAC;aACxE;AACH,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,gBAAqC,CAAC;AAE1C,QAAA,IAAI,OAAO,OAAO,CAAC,oBAAoB,KAAK,UAAU,EAAE;AACtD,YAAA,gBAAgB,GAAG,OAAO,CAAC,oBAAoB,EAAE,CAAC;SACnD;aAAM;AACL,YAAA,MAAM,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,CAC1B,OAAO,CAAC,oBAAoB,IAAI,0BAA0B,EAC1D,KAAK,CAAC,GAAG,CAAC,CAAC;YAEb,QAAQ,QAAQ;AACd,gBAAA,KAAK,qBAAqB;AACxB,oBAAA,gBAAgB,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC;oBAC5B,MAAM;AACR,gBAAA,KAAK,mBAAmB;oBACtB,gBAAgB,GAAG,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;oBACnD,MAAM;AACR,gBAAA,KAAK,oBAAoB;AACvB,oBAAA,gBAAgB,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACzB,0BAAE,UAAU,CAAC,QAAQ,CAAC;AACtB,0BAAE,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,MAAM;AACR,gBAAA;;oBAEE,MAAM,IAAI,KAAK,CACb,CAAA,6CAAA,EAAgD,OAAO,CAAC,oBAAoB,CAAE,CAAA,CAC/E,CAAC;aACL;SACF;;;;;QAMD,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACpC,QAAA,MAAM,CAAC,iBAAiB,CAAC,MACvB,gBAAgB;AACb,aAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACb,aAAA,SAAS,CAAC,MACT,SAAS,CAAC,aAAa;aACpB,QAAQ,CAAC,MAAM,EAAE,EAAC,KAAK,EAAE,OAAO,CAAC,KAAK,EAAC,CAAC;AACxC,aAAA,KAAK,CAAC,CAAC,GAAG,KAAK,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,GAAG,CAAC,CAAC,CAClF,CACJ,CAAC;AACJ,KAAC,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,OAAe,EAAA;AACvC,IAAA,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,UAAU,CAAC,QAAkB,EAAA;IACpC,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC5C,IAAA,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC;AAC1D,CAAC;AAEe,SAAA,sBAAsB,CACpC,IAA2B,EAC3B,UAAkB,EAAA;IAElB,OAAO,IAAI,eAAe,CACxB,iBAAiB,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,GAAG,SAAS,CAAC,aAAa,GAAG,SAAS,CAC9F,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;AAWG;MACmB,qBAAqB,CAAA;AA8C1C,CAAA;AAED;;;;;;;;;;;;;;;;AAgBG;SACa,oBAAoB,CAClC,MAAc,EACd,UAAiC,EAAE,EAAA;AAEnC,IAAA,OAAO,wBAAwB,CAAC;QAC9B,MAAM;QACN,QAAQ;AACR,QAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAC;AACnC,QAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,OAAO,EAAC;AACnD,QAAA;AACE,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,UAAU,EAAE,sBAAsB;AAClC,YAAA,IAAI,EAAE,CAAC,qBAAqB,EAAE,WAAW,CAAC;AAC3C,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,eAAe;AACxB,YAAA,UAAU,EAAE,kBAAkB;YAC9B,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,qBAAqB,EAAE,WAAW,CAAC;AAC5D,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;AACF,KAAA,CAAC,CAAC;AACL;;ACtMA;;AAEG;MAEU,mBAAmB,CAAA;AAC9B;;;;;AAKG;AACH,IAAA,OAAO,QAAQ,CACb,MAAc,EACd,UAAiC,EAAE,EAAA;QAEnC,OAAO;AACL,YAAA,QAAQ,EAAE,mBAAmB;YAC7B,SAAS,EAAE,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;SACnD,CAAC;KACH;yHAfU,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HAAnB,mBAAmB,EAAA,CAAA,CAAA,EAAA;AAAnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EADV,SAAA,EAAA,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAA,CAAA,CAAA,EAAA;;sGAC3B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAC,CAAA;;;ACTzC;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}