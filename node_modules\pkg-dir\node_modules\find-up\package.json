{"name": "find-up", "version": "6.3.0", "description": "Find a file or directory by walking up parent directories", "license": "MIT", "repository": "sindresorhus/find-up", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"locate-path": "^7.1.0", "path-exists": "^5.0.0"}, "devDependencies": {"ava": "^3.15.0", "is-path-inside": "^4.0.0", "tempy": "^2.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}