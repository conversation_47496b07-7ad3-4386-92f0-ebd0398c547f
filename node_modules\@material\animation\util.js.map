{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;AAIH,IAAM,kBAAkB,GAAyB;IAC/C,SAAS,EAAE;QACT,QAAQ,EAAE,mBAAmB;QAC7B,QAAQ,EAAE,WAAW;KACtB;IACD,SAAS,EAAE;QACT,QAAQ,EAAE,mBAAmB;QAC7B,QAAQ,EAAE,WAAW;KACtB;IACD,UAAU,EAAE;QACV,QAAQ,EAAE,oBAAoB;QAC9B,QAAQ,EAAE,YAAY;KACvB;CACF,CAAC;AAEF,IAAM,cAAc,GAAwB;IAC1C,YAAY,EAAE;QACZ,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,oBAAoB;QAC9B,QAAQ,EAAE,cAAc;KACzB;IACD,kBAAkB,EAAE;QAClB,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,0BAA0B;QACpC,QAAQ,EAAE,oBAAoB;KAC/B;IACD,cAAc,EAAE;QACd,WAAW,EAAE,WAAW;QACxB,QAAQ,EAAE,sBAAsB;QAChC,QAAQ,EAAE,gBAAgB;KAC3B;IACD,aAAa,EAAE;QACb,WAAW,EAAE,YAAY;QACzB,QAAQ,EAAE,qBAAqB;QAC/B,QAAQ,EAAE,eAAe;KAC1B;CACF,CAAC;AAEF,SAAS,QAAQ,CAAC,SAAiB;IACjC,OAAO,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC;QAC9B,OAAO,SAAS,CAAC,QAAQ,CAAC,aAAa,KAAK,UAAU,CAAC;AAC7D,CAAC;AAED,MAAM,UAAU,sBAAsB,CAClC,SAAiB,EAAE,WAAoC;IAEzD,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,WAAW,IAAI,kBAAkB,EAAE;QAC5D,IAAM,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAA,KAAuB,kBAAkB,CAAC,WAAW,CAAC,EAArD,QAAQ,cAAA,EAAE,QAAQ,cAAmC,CAAC;QAC7D,IAAM,UAAU,GAAG,QAAQ,IAAI,EAAE,CAAC,KAAK,CAAC;QACxC,OAAO,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;KACzC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,mBAAmB,CAC/B,SAAiB,EAAE,SAA8B;IAEnD,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,IAAI,cAAc,EAAE;QACtD,IAAM,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAA,KAAoC,cAAc,CAAC,SAAS,CAAC,EAA5D,QAAQ,cAAA,EAAE,QAAQ,cAAA,EAAE,WAAW,iBAA6B,CAAC;QACpE,IAAM,UAAU,GAAG,WAAW,IAAI,EAAE,CAAC,KAAK,CAAC;QAC3C,OAAO,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;KACzC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC"}