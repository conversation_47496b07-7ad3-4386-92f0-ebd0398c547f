//
// !!! THIS FILE WAS AUTOMATICALLY GENERATED !!!
// !!! DO NOT MODIFY IT BY HAND !!!
// Design system display name: Google Material 3
// Design system version: v0.132
// User-configured context group "Audience": "3P"
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-shape';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-shape': md-sys-shape.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'active-indicator-color': map.get($deps, 'md-sys-color', 'primary'),
    'active-indicator-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'active-indicator-width': if($exclude-hardcoded-values, null, 4px),
    'four-color-active-indicator-four-color':
      map.get($deps, 'md-sys-color', 'tertiary-container'),
    'four-color-active-indicator-one-color':
      map.get($deps, 'md-sys-color', 'primary'),
    'four-color-active-indicator-three-color':
      map.get($deps, 'md-sys-color', 'tertiary'),
    'four-color-active-indicator-two-color':
      map.get($deps, 'md-sys-color', 'primary-container'),
    'size': if($exclude-hardcoded-values, null, 48px)
  );
}
