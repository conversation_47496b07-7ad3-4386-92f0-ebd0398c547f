{"name": "watchpack", "version": "2.4.0", "description": "", "main": "./lib/watchpack.js", "directories": {"test": "test"}, "files": ["lib/"], "scripts": {"pretest": "yarn lint", "test": "mocha", "lint": "eslint lib", "precover": "yarn lint", "pretty-files": "prettier \"lib/**.*\" \"test/**/*.js\" --write", "cover": "istanbul cover node_modules/mocha/bin/_mocha"}, "repository": {"type": "git", "url": "https://github.com/webpack/watchpack.git"}, "author": "<PERSON> @sokra", "license": "MIT", "bugs": {"url": "https://github.com/webpack/watchpack/issues"}, "homepage": "https://github.com/webpack/watchpack", "devDependencies": {"coveralls": "^3.0.0", "eslint": "^5.11.1", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "istanbul": "^0.4.3", "mocha": "^5.0.1", "prettier": "^1.11.0", "rimraf": "^2.6.2", "should": "^8.3.1", "write-file-atomic": "^3.0.1"}, "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}