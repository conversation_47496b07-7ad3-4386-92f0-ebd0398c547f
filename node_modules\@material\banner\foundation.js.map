{"version": 3, "file": "foundation.js", "sourceRoot": "", "sources": ["foundation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,2BAA2B,CAAC;AAGxD,OAAO,EAAC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAC,MAAM,aAAa,CAAC;AAE9D,IAAA,OAAO,GAAmB,UAAU,QAA7B,EAAE,IAAI,GAAa,UAAU,KAAvB,EAAE,OAAO,GAAI,UAAU,QAAd,CAAe;AAE5C;;;GAGG;AACH;IAAyC,uCAA+B;IAyBtE,6BAAY,OAAmC;QAA/C,YACE,wCAAU,mBAAmB,CAAC,cAAc,GAAK,OAAO,EAAE,SAC3D;QAVO,cAAQ,GAAG,KAAK,CAAC;QACzB,qEAAqE;QACrE,oCAAoC;QAC5B,oBAAc,GAAG,CAAC,CAAC;QAC3B,qEAAqE;QACrE,yBAAyB;QACjB,oBAAc,GAAG,CAAC,CAAC;;IAI3B,CAAC;IA1BD,sBAAoB,qCAAc;aAAlC;YACE,OAAO;gBACL,QAAQ,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACzB,gBAAgB,EAAE,cAAM,OAAA,CAAC,EAAD,CAAC;gBACzB,YAAY,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC7B,aAAa,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC9B,YAAY,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC7B,aAAa,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC9B,mBAAmB,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACpC,YAAY,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC7B,WAAW,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC5B,gBAAgB,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACjC,SAAS,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;aAC3B,CAAC;QACJ,CAAC;;;OAAA;IAcQ,qCAAO,GAAhB;QACE,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,kCAAI,GAAJ;QAAA,iBAiBC;QAhBC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAE7B,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC;YAC1C,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC5B,KAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAK,aAAa,OAAI,CAAC,CAAC;YAE9D,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC;gBAC/B,KAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,KAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBACzB,KAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC9B,CAAC,EAAE,OAAO,CAAC,6BAA6B,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,mCAAK,GAAL,UAAM,MAAmB;QAAzB,iBAuBC;QAtBC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,0EAA0E;YAC1E,iCAAiC;YACjC,OAAO;SACR;QAED,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAElC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;YAC/B,KAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC5B,KAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,EAAE,OAAO,CAAC,8BAA8B,CAAC,CAAC;IAC7C,CAAC;IAED,oCAAM,GAAN;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,sDAAwB,GAAxB,UAAyB,gBAAwB;QAAxB,iCAAA,EAAA,wBAAwB;QAC/C,IAAI,gBAAgB,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAClD;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACjC;IACH,CAAC;IAED,wDAA0B,GAA1B,UAA2B,gBAAwB;QAAxB,iCAAA,EAAA,wBAAwB;QACjD,IAAI,gBAAgB,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;SAEpD;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;SACnC;IACH,CAAC;IAED,oCAAM,GAAN;QACE,IAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACtD,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAK,aAAa,OAAI,CAAC,CAAC;IAChE,CAAC;IAEO,qDAAuB,GAA/B;QACE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IACH,0BAAC;AAAD,CAAC,AAtHD,CAAyC,aAAa,GAsHrD"}