import 'dotenv/config';
import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { ApolloServer } from 'apollo-server-express';

import { prisma, utils, redis } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';
import categoryRoutes from './routes/categories';
import productRoutes from './routes/products';
import stockRoutes from './routes/stock';
import { typeDefs, resolvers } from './graphql';

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Authentication middleware for protected routes
const authenticateRequest = (req: AuthenticatedRequest, _res: Response, next: NextFunction): void => {
  try {
    const userId = req.headers['x-user-id'] as string;
    const roles = req.headers['x-user-roles'] ? JSON.parse(req.headers['x-user-roles'] as string) : [];
    const permissions = req.headers['x-user-permissions'] ? JSON.parse(req.headers['x-user-permissions'] as string) : [];

    if (userId) {
      req.user = {
        userId,
        roles,
        permissions,
      };
    }
    next();
  } catch (error) {
    next();
  }
};

app.use(authenticateRequest);

// Health check
app.get('/health', (_req: Request, res: Response) => {
  res.json({
    service: 'inventory-service',
    status: 'healthy',
    timestamp: new Date().toISOString(),
  });
});

// Routes
app.use('/categories', categoryRoutes);
app.use('/products', productRoutes);
app.use('/stock', stockRoutes);

// GraphQL Server
const createApolloServer = async (): Promise<ApolloServer> => {
  const server = new ApolloServer({
    typeDefs,
    resolvers,
    context: ({ req }: { req: AuthenticatedRequest }) => ({
      user: req.user,
      prisma,
      redis: redis.getRedisClient(),
    }),
    introspection: process.env.NODE_ENV !== 'production',
    playground: process.env.NODE_ENV !== 'production',
  });

  await server.start();
  server.applyMiddleware({ app, path: '/graphql' });
  
  return server;
};

// Error handling middleware
app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
  console.error('Inventory service error:', err);
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal server error';
  
  utils.sendError(res, message, statusCode);
});

// 404 handler
app.use('*', (_req: Request, res: Response) => {
  utils.sendError(res, 'Route not found', 404);
});

// Initialize services
const initializeServices = async (): Promise<void> => {
  try {
    await redis.connectRedis();
    console.log('✅ Redis connected');
  } catch (error: any) {
    console.error('❌ Redis connection failed:', error.message);
    console.log('⚠️  Continuing without Redis cache');
  }
};

// Start server
const startServer = async (): Promise<void> => {
  await initializeServices();
  
  // Initialize GraphQL server
  const apolloServer = await createApolloServer();
  
  app.listen(PORT, () => {
    console.log(`📦 Inventory Service running on port ${PORT}`);
    console.log(`🚀 GraphQL endpoint: http://localhost:${PORT}${apolloServer.graphqlPath}`);
  });
};

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  await redis.disconnectRedis();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  await redis.disconnectRedis();
  process.exit(0);
});

startServer().catch((error) => {
  console.error('❌ Failed to start Inventory Service:', error);
  process.exit(1);
});
