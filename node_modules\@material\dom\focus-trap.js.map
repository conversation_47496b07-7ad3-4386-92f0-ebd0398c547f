{"version": 3, "file": "focus-trap.js", "sourceRoot": "", "sources": ["focus-trap.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;AAEH,IAAM,oBAAoB,GAAG,wBAAwB,CAAC;AAEtD;;;;;;GAMG;AACH;IAIE,mBACqB,IAAiB,EACjB,OAA0B;QAA1B,wBAAA,EAAA,YAA0B;QAD1B,SAAI,GAAJ,IAAI,CAAa;QACjB,YAAO,GAAP,OAAO,CAAmB;QAL/C,oDAAoD;QAC5C,6BAAwB,GAAqB,IAAI,CAAC;IAIR,CAAC;IAEnD;;;OAGG;IACH,6BAAS,GAAT;QACE,IAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7B,MAAM,IAAI,KAAK,CACX,4DAA4D,CAAC,CAAC;SACnE;QAED,IAAI,CAAC,wBAAwB;YACzB,QAAQ,CAAC,aAAa,YAAY,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,IAAI,CAAC;QACzD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SACrE;IACH,CAAC;IAED;;;OAGG;IACH,gCAAY,GAAZ;QACE,KAAK;aACA,IAAI,CACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAc,MAAI,oBAAsB,CAAC,CAAC;aACvE,OAAO,CAAC,UAAC,UAAuB;YAC/B,UAAU,CAAC,aAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACpD,CAAC,CAAC,CAAC;QAEP,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACnE,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;SACvC;IACH,CAAC;IAED;;;;;;OAMG;IACK,gCAAY,GAApB,UAAqB,EAAe;QAApC,iBAmBC;QAlBC,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5C,IAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAE1C,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE;YACtC,IAAM,YAAY,GAAG,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACnD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;aAC/C;QACH,CAAC,CAAC,CAAC;QACH,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE;YACpC,IAAM,YAAY,GAAG,KAAI,CAAC,oBAAoB,CAAC,EAAE,CAAC,CAAC;YACnD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC3B,YAAY,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;aACzB;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,YAAY,CAAC,aAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/C,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACK,uCAAmB,GAA3B,UACI,YAA2B,EAAE,cAA4B;QAC3D,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,cAAc,EAAE;YAClB,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;SAChE;QACD,YAAY,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,CAAC;IACnC,CAAC;IAEO,wCAAoB,GAA5B,UAA6B,IAAiB;QAC5C,IAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACjD,6DAA6D,CAAC,CAAC,CAAC;QACpE,OAAO,YAAY,CAAC,MAAM,CAAC,UAAC,EAAE;YAC5B,IAAM,kBAAkB,GAAG,EAAE,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;gBAClE,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI;gBACnC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,IAAI;gBACjC,EAAE,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,CAAC;YAC9C,IAAM,oBAAoB,GAAG,EAAE,CAAC,QAAQ,IAAI,CAAC;gBACzC,EAAE,CAAC,qBAAqB,EAAE,CAAC,KAAK,GAAG,CAAC;gBACpC,CAAC,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,oBAAoB,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAExE,IAAI,wBAAwB,GAAG,KAAK,CAAC;YACrC,IAAI,oBAAoB,EAAE;gBACxB,IAAM,KAAK,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;gBACnC,wBAAwB;oBACpB,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC;aAC/D;YACD,OAAO,oBAAoB,IAAI,CAAC,wBAAwB,CAAC;QAC3D,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,kCAAc,GAAtB;QACE,IAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC/C,QAAQ,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;QACvC,oCAAoC;QACpC,QAAQ,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;QAC7C,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAC7C,OAAO,QAAQ,CAAC;IAClB,CAAC;IACH,gBAAC;AAAD,CAAC,AArHD,IAqHC"}