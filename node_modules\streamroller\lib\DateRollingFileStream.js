const RollingFileWriteStream = require('./RollingFileWriteStream');

// just to adapt the previous version
class DateRollingFileStream extends RollingFileWriteStream {
  constructor(filename, pattern, options) {
    if (pattern && typeof(pattern) === 'object') {
      options = pattern;
      pattern = null;
    }
    if (!options) {
      options = {};
    }
    if (!pattern) {
      pattern = 'yyyy-MM-dd';
    }
    options.pattern = pattern;
    if (!options.numBackups && options.numBackups !== 0) {
      if (!options.daysToKeep && options.daysToKeep !== 0) {
        options.daysToKeep = 1;
      } else {
        process.emitWarning(
          "options.daysToKeep is deprecated due to the confusion it causes when used " + 
          "together with file size rolling. Please use options.numBackups instead.",
          "DeprecationWarning", "streamroller-DEP0001"
        );
      }
      options.numBackups = options.daysToKeep;
    } else {
      options.daysToKeep = options.numBackups;
    }
    super(filename, options);
    this.mode = this.options.mode;
  }

  get theStream() {
    return this.currentFileStream;
  }

}

module.exports = DateRollingFileStream;
