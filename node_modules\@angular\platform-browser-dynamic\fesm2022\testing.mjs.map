{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/platform-browser-dynamic/testing/src/dom_test_component_renderer.ts", "../../../../../../packages/platform-browser-dynamic/testing/src/platform_core_dynamic_testing.ts", "../../../../../../packages/platform-browser-dynamic/testing/src/testing.ts", "../../../../../../packages/platform-browser-dynamic/testing/public_api.ts", "../../../../../../packages/platform-browser-dynamic/testing/index.ts", "../../../../../../packages/platform-browser-dynamic/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\n@Injectable()\nexport class DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor(@Inject(DOCUMENT) private _doc: any) {\n    super();\n  }\n\n  override insertRootElement(rootElId: string) {\n    this.removeAllRootElements();\n    const rootElement = getDOM().getDefaultDocument().createElement('div');\n    rootElement.setAttribute('id', rootElId);\n    this._doc.body.appendChild(rootElement);\n  }\n\n  override removeAllRootElements() {\n    // TODO(juliemr): can/should this be optional?\n    const oldRoots = this._doc.querySelectorAll('[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      getDOM().remove(oldRoots[i]);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {createPlatformFactory, PlatformRef} from '@angular/core';\nimport {ɵplatformCoreDynamic as platformCoreDynamic} from '@angular/platform-browser-dynamic';\n\n/**\n * Platform for dynamic tests\n *\n * @publicApi\n */\nexport const platformCoreDynamicTesting: (extraProviders?: any[]) => PlatformRef =\n    createPlatformFactory(\n        platformCoreDynamic,\n        'coreDynamicTesting',\n        [],\n    );\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {createPlatformFactory, NgModule, PlatformRef, StaticProvider} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\nimport {ɵINTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS as INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS} from '@angular/platform-browser-dynamic';\nimport {BrowserTestingModule} from '@angular/platform-browser/testing';\n\nimport {DOMTestComponentRenderer} from './dom_test_component_renderer';\nimport {platformCoreDynamicTesting} from './platform_core_dynamic_testing';\n\nexport * from './private_export_testing';\n\n/**\n * @publicApi\n */\nexport const platformBrowserDynamicTesting = createPlatformFactory(\n    platformCoreDynamicTesting, 'browserDynamicTesting',\n    INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS);\n\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserTestingModule],\n  providers: [\n    {provide: TestComponentRenderer, useClass: DOMTestComponentRenderer},\n  ]\n})\nexport class BrowserDynamicTestingModule {\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["getDOM", "platformCoreDynamic", "INTERNAL_BROWSER_DYNAMIC_PLATFORM_PROVIDERS"], "mappings": ";;;;;;;;;;;;;AAYA;;AAEG;AAEG,MAAO,wBAAyB,SAAQ,qBAAqB,CAAA;AACjE,IAAA,WAAA,CAAsC,IAAS,EAAA;AAC7C,QAAA,KAAK,EAAE,CAAC;QAD4B,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAK;KAE9C;AAEQ,IAAA,iBAAiB,CAAC,QAAgB,EAAA;QACzC,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAC7B,QAAA,MAAM,WAAW,GAAGA,OAAM,EAAE,CAAC,kBAAkB,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACvE,QAAA,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;KACzC;IAEQ,qBAAqB,GAAA;;QAE5B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC1D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxCA,OAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;SAC9B;KACF;AAlBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,kBACf,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HADjB,wBAAwB,EAAA,CAAA,CAAA,EAAA;;sGAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC,UAAU;;0BAEI,MAAM;2BAAC,QAAQ,CAAA;;;ACN9B;;;;AAIG;AACI,MAAM,0BAA0B,GACnC,qBAAqB,CACjBC,oBAAmB,EACnB,oBAAoB,EACpB,EAAE;;ACFV;;AAEG;AACI,MAAM,6BAA6B,GAAG,qBAAqB,CAC9D,0BAA0B,EAAE,uBAAuB,EACnDC,4CAA2C,EAAE;AAEjD;;;;AAIG;MAOU,2BAA2B,CAAA;yHAA3B,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAA3B,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,2BAA2B,YAL5B,oBAAoB,CAAA,EAAA,CAAA,CAAA,EAAA;AAKnB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,2BAA2B,EAJ3B,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAC;AACrE,SAAA,EAAA,OAAA,EAAA,CAHS,oBAAoB,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAKnB,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBANvC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,oBAAoB,CAAC;AAC/B,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAC;AACrE,qBAAA;AACF,iBAAA,CAAA;;;AC3BD;;;;AAIG;;ACJH;;ACRA;;AAEG;;;;"}