import express, { Response, NextFunction } from 'express';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

/**
 * @swagger
 * /products:
 *   get:
 *     summary: Get all products
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: string
 *       - in: query
 *         name: lowStock
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Products retrieved successfully
 */
router.get('/', requireAuth, requirePermission('inventory:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { page, limit, search, categoryId, lowStock } = req.query;
  const skip = ((page as number) - 1) * (limit as number);

  const where: any = { isActive: true };

  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
      { sku: { contains: search as string, mode: 'insensitive' } },
      { barcode: { contains: search as string, mode: 'insensitive' } },
    ];
  }

  if (categoryId) {
    where.categoryId = categoryId as string;
  }

  if (lowStock === 'true') {
    where.stock = {
      quantity: {
        lte: (prisma as any).raw('stock.min_threshold')
      }
    };
  }

  const [products, total] = await Promise.all([
    prisma.product.findMany({
      where,
      skip,
      take: limit as number,
      include: {
        category: true,
        stock: true,
      },
      orderBy: { name: 'asc' }
    }),
    prisma.product.count({ where })
  ]);

  utils.sendSuccess(res, {
    products,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / (limit as number)),
    }
  });
}));

/**
 * @swagger
 * /products/{id}:
 *   get:
 *     summary: Get product by ID
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product retrieved successfully
 *       404:
 *         description: Product not found
 */
router.get('/:id', requireAuth, requirePermission('inventory:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      category: true,
      stock: true,
    }
  });

  if (!product) {
    utils.sendError(res, 'Product not found', 404);
    return;
  }

  utils.sendSuccess(res, { product });
}));

/**
 * @swagger
 * /products:
 *   post:
 *     summary: Create new product
 *     tags: [Products]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - sku
 *               - price
 *               - categoryId
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               sku:
 *                 type: string
 *               barcode:
 *                 type: string
 *               price:
 *                 type: number
 *               cost:
 *                 type: number
 *               categoryId:
 *                 type: string
 *               initialStock:
 *                 type: integer
 *                 default: 0
 *               minThreshold:
 *                 type: integer
 *                 default: 10
 *     responses:
 *       201:
 *         description: Product created successfully
 */
router.post('/', requireAuth, requirePermission('inventory:create'), validation.validateBody(validation.productCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { initialStock = 0, minThreshold = 10, ...productData } = req.body;

  // Check if SKU already exists
  const existingProduct = await prisma.product.findUnique({
    where: { sku: productData.sku }
  });

  if (existingProduct) {
    utils.sendError(res, 'Product with this SKU already exists', 400);
    return;
  }

  // Check if barcode already exists (if provided)
  if (productData.barcode) {
    const existingBarcode = await prisma.product.findUnique({
      where: { barcode: productData.barcode }
    });

    if (existingBarcode) {
      utils.sendError(res, 'Product with this barcode already exists', 400);
      return;
    }
  }

  // Create product with stock
  const product = await prisma.product.create({
    data: {
      ...productData,
      stock: {
        create: {
          quantity: initialStock,
          minThreshold,
        }
      }
    },
    include: {
      category: true,
      stock: true,
    }
  });

  utils.sendSuccess(res, { product }, 'Product created successfully', 201);
}));

/**
 * @swagger
 * /products/{id}:
 *   put:
 *     summary: Update product
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               sku:
 *                 type: string
 *               barcode:
 *                 type: string
 *               price:
 *                 type: number
 *               cost:
 *                 type: number
 *               categoryId:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Product updated successfully
 */
router.put('/:id', requireAuth, requirePermission('inventory:update'), validation.validateParams(validation.idSchema), validation.validateBody(validation.productUpdateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if product exists
  const existingProduct = await prisma.product.findUnique({
    where: { id }
  });

  if (!existingProduct) {
    utils.sendError(res, 'Product not found', 404);
    return;
  }

  // Check for SKU conflicts
  if (updateData.sku) {
    const conflictProduct = await prisma.product.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { sku: updateData.sku }
        ]
      }
    });

    if (conflictProduct) {
      utils.sendError(res, 'SKU already exists', 400);
      return;
    }
  }

  // Check for barcode conflicts
  if (updateData.barcode) {
    const conflictProduct = await prisma.product.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { barcode: updateData.barcode }
        ]
      }
    });

    if (conflictProduct) {
      utils.sendError(res, 'Barcode already exists', 400);
      return;
    }
  }

  // Update product
  const product = await prisma.product.update({
    where: { id },
    data: updateData,
    include: {
      category: true,
      stock: true,
    }
  });

  utils.sendSuccess(res, { product }, 'Product updated successfully');
}));

/**
 * @swagger
 * /products/{id}:
 *   delete:
 *     summary: Delete product
 *     tags: [Products]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Product deleted successfully
 */
router.delete('/:id', requireAuth, requirePermission('inventory:delete'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if product exists
  const product = await prisma.product.findUnique({
    where: { id }
  });

  if (!product) {
    utils.sendError(res, 'Product not found', 404);
    return;
  }

  // Soft delete by deactivating
  await prisma.product.update({
    where: { id },
    data: { isActive: false },
  });

  utils.sendSuccess(res, null, 'Product deleted successfully');
}));

/**
 * @swagger
 * /products/search:
 *   get:
 *     summary: Search products by barcode or SKU
 *     tags: [Products]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query (barcode or SKU)
 *     responses:
 *       200:
 *         description: Product found
 *       404:
 *         description: Product not found
 */
router.get('/search', requireAuth, requirePermission('inventory:read'), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { q } = req.query;

  if (!q) {
    utils.sendError(res, 'Search query required', 400);
    return;
  }

  const product = await prisma.product.findFirst({
    where: {
      AND: [
        { isActive: true },
        {
          OR: [
            { sku: { equals: q as string, mode: 'insensitive' } },
            { barcode: { equals: q as string, mode: 'insensitive' } },
          ]
        }
      ]
    },
    include: {
      category: true,
      stock: true,
    }
  });

  if (!product) {
    utils.sendError(res, 'Product not found', 404);
    return;
  }

  utils.sendSuccess(res, { product });
}));

export default router;
