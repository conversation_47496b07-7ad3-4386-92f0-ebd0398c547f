@use '../mixins' as typography;
@use '@material/feature-targeting/feature-targeting';

@mixin test($query) {
  .test {
    @include typography.core-styles($query: $query);
    @include typography.base($query: $query);
    @include typography.typography(button, $query: $query);
    @include typography.overflow-ellipsis($query: $query);
    @include typography.baseline($top: 0, $bottom: 0, $query: $query);
    @include typography.text-baseline($top: 0, $bottom: 0, $query: $query);
  }
}

// This shouldn't output any CSS.
@include test(feature-targeting.any());
