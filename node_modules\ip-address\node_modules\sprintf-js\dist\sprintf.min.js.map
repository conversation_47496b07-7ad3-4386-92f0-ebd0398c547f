{"version": 3, "sources": ["sprintf.js"], "names": ["re", "not_string", "not_bool", "not_type", "not_primitive", "number", "numeric_arg", "json", "not_json", "text", "modulo", "placeholder", "key", "key_access", "index_access", "sign", "sprintf", "parse_tree", "argv", "arg", "i", "k", "ph", "pad", "pad_character", "pad_length", "is_positive", "cursor", "tree_length", "length", "output", "keys", "undefined", "Error", "param_no", "test", "type", "Function", "isNaN", "TypeError", "parseInt", "toString", "String", "fromCharCode", "JSON", "stringify", "width", "precision", "parseFloat", "toExponential", "toFixed", "Number", "toPrecision", "substring", "Object", "prototype", "call", "slice", "toLowerCase", "valueOf", "toUpperCase", "replace", "pad_char", "char<PERSON>t", "repeat", "align", "sprintf_format", "fmt", "sprintf_cache", "match", "_fmt", "arg_names", "exec", "push", "SyntaxError", "field_list", "replacement_field", "field_match", "sprintf_parse", "arguments", "vsprintf", "apply", "concat", "create", "exports", "window", "define"], "mappings": ";CAEC,WACG,aAEA,IAAIA,EAAK,CACLC,WAAY,OACZC,SAAU,OACVC,SAAU,OACVC,cAAe,OACfC,OAAQ,UACRC,YAAa,eACbC,KAAM,MACNC,SAAU,OACVC,KAAM,YACNC,OAAQ,WACRC,YAAa,2FACbC,IAAK,sBACLC,WAAY,wBACZC,aAAc,aACdC,KAAM,SAGV,SAASC,EAAQJ,GAEb,OAOJ,SAAwBK,EAAYC,GAChC,IAAiDC,EAAkBC,EAAGC,EAAGC,EAAIC,EAAKC,EAAeC,EAAYC,EAAaX,EAAtHY,EAAS,EAAGC,EAAcX,EAAWY,OAAaC,EAAS,GAC/D,IAAKV,EAAI,EAAGA,EAAIQ,EAAaR,IACzB,GAA6B,iBAAlBH,EAAWG,GAClBU,GAAUb,EAAWG,QAEpB,GAA6B,iBAAlBH,EAAWG,GAAiB,CAExC,IADAE,EAAKL,EAAWG,IACTW,KAEH,IADAZ,EAAMD,EAAKS,GACNN,EAAI,EAAGA,EAAIC,EAAGS,KAAKF,OAAQR,IAAK,CACjC,GAAWW,MAAPb,EACA,MAAM,IAAIc,MAAMjB,EAAQ,gEAAiEM,EAAGS,KAAKV,GAAIC,EAAGS,KAAKV,EAAE,KAEnHF,EAAMA,EAAIG,EAAGS,KAAKV,SAItBF,EADKG,EAAGY,SACFhB,EAAKI,EAAGY,UAGRhB,EAAKS,KAOf,GAJI3B,EAAGG,SAASgC,KAAKb,EAAGc,OAASpC,EAAGI,cAAc+B,KAAKb,EAAGc,OAASjB,aAAekB,WAC9ElB,EAAMA,KAGNnB,EAAGM,YAAY6B,KAAKb,EAAGc,OAAyB,iBAARjB,GAAoBmB,MAAMnB,GAClE,MAAM,IAAIoB,UAAUvB,EAAQ,0CAA2CG,IAO3E,OAJInB,EAAGK,OAAO8B,KAAKb,EAAGc,QAClBV,EAAqB,GAAPP,GAGVG,EAAGc,MACP,IAAK,IACDjB,EAAMqB,SAASrB,EAAK,IAAIsB,SAAS,GACjC,MACJ,IAAK,IACDtB,EAAMuB,OAAOC,aAAaH,SAASrB,EAAK,KACxC,MACJ,IAAK,IACL,IAAK,IACDA,EAAMqB,SAASrB,EAAK,IACpB,MACJ,IAAK,IACDA,EAAMyB,KAAKC,UAAU1B,EAAK,KAAMG,EAAGwB,MAAQN,SAASlB,EAAGwB,OAAS,GAChE,MACJ,IAAK,IACD3B,EAAMG,EAAGyB,UAAYC,WAAW7B,GAAK8B,cAAc3B,EAAGyB,WAAaC,WAAW7B,GAAK8B,gBACnF,MACJ,IAAK,IACD9B,EAAMG,EAAGyB,UAAYC,WAAW7B,GAAK+B,QAAQ5B,EAAGyB,WAAaC,WAAW7B,GACxE,MACJ,IAAK,IACDA,EAAMG,EAAGyB,UAAYL,OAAOS,OAAOhC,EAAIiC,YAAY9B,EAAGyB,aAAeC,WAAW7B,GAChF,MACJ,IAAK,IACDA,GAAOqB,SAASrB,EAAK,MAAQ,GAAGsB,SAAS,GACzC,MACJ,IAAK,IACDtB,EAAMuB,OAAOvB,GACbA,EAAOG,EAAGyB,UAAY5B,EAAIkC,UAAU,EAAG/B,EAAGyB,WAAa5B,EACvD,MACJ,IAAK,IACDA,EAAMuB,SAASvB,GACfA,EAAOG,EAAGyB,UAAY5B,EAAIkC,UAAU,EAAG/B,EAAGyB,WAAa5B,EACvD,MACJ,IAAK,IACDA,EAAMmC,OAAOC,UAAUd,SAASe,KAAKrC,GAAKsC,MAAM,GAAI,GAAGC,cACvDvC,EAAOG,EAAGyB,UAAY5B,EAAIkC,UAAU,EAAG/B,EAAGyB,WAAa5B,EACvD,MACJ,IAAK,IACDA,EAAMqB,SAASrB,EAAK,MAAQ,EAC5B,MACJ,IAAK,IACDA,EAAMA,EAAIwC,UACVxC,EAAOG,EAAGyB,UAAY5B,EAAIkC,UAAU,EAAG/B,EAAGyB,WAAa5B,EACvD,MACJ,IAAK,IACDA,GAAOqB,SAASrB,EAAK,MAAQ,GAAGsB,SAAS,IACzC,MACJ,IAAK,IACDtB,GAAOqB,SAASrB,EAAK,MAAQ,GAAGsB,SAAS,IAAImB,cAGjD5D,EAAGO,KAAK4B,KAAKb,EAAGc,MAChBN,GAAUX,IAGNnB,EAAGK,OAAO8B,KAAKb,EAAGc,OAAWV,IAAeJ,EAAGP,KAK/CA,EAAO,IAJPA,EAAOW,EAAc,IAAM,IAC3BP,EAAMA,EAAIsB,WAAWoB,QAAQ7D,EAAGe,KAAM,KAK1CS,EAAgBF,EAAGwC,SAA2B,MAAhBxC,EAAGwC,SAAmB,IAAMxC,EAAGwC,SAASC,OAAO,GAAK,IAClFtC,EAAaH,EAAGwB,OAAS/B,EAAOI,GAAKU,OACrCN,EAAMD,EAAGwB,OAAsB,EAAbrB,EAAiBD,EAAcwC,OAAOvC,GAAoB,GAC5EK,GAAUR,EAAG2C,MAAQlD,EAAOI,EAAMI,EAAyB,MAAlBC,EAAwBT,EAAOQ,EAAMJ,EAAMI,EAAMR,EAAOI,GAI7G,OAAOW,EAjHAoC,CAsHX,SAAuBC,GACnB,GAAIC,EAAcD,GACd,OAAOC,EAAcD,GAGzB,IAAgBE,EAAZC,EAAOH,EAAYlD,EAAa,GAAIsD,EAAY,EACpD,KAAOD,GAAM,CACT,GAAqC,QAAhCD,EAAQrE,EAAGS,KAAK+D,KAAKF,IACtBrD,EAAWwD,KAAKJ,EAAM,SAErB,GAAuC,QAAlCA,EAAQrE,EAAGU,OAAO8D,KAAKF,IAC7BrD,EAAWwD,KAAK,SAEf,CAAA,GAA4C,QAAvCJ,EAAQrE,EAAGW,YAAY6D,KAAKF,IA6ClC,MAAM,IAAII,YAAY,oCA5CtB,GAAIL,EAAM,GAAI,CACVE,GAAa,EACb,IAAII,EAAa,GAAIC,EAAoBP,EAAM,GAAIQ,EAAc,GACjE,GAAuD,QAAlDA,EAAc7E,EAAGY,IAAI4D,KAAKI,IAe3B,MAAM,IAAIF,YAAY,gDAbtB,IADAC,EAAWF,KAAKI,EAAY,IACwD,MAA5ED,EAAoBA,EAAkBvB,UAAUwB,EAAY,GAAGhD,UACnE,GAA8D,QAAzDgD,EAAc7E,EAAGa,WAAW2D,KAAKI,IAClCD,EAAWF,KAAKI,EAAY,QAE3B,CAAA,GAAgE,QAA3DA,EAAc7E,EAAGc,aAAa0D,KAAKI,IAIzC,MAAM,IAAIF,YAAY,gDAHtBC,EAAWF,KAAKI,EAAY,IAUxCR,EAAM,GAAKM,OAGXJ,GAAa,EAEjB,GAAkB,IAAdA,EACA,MAAM,IAAItC,MAAM,6EAGpBhB,EAAWwD,KACP,CACI9D,YAAa0D,EAAM,GACnBnC,SAAamC,EAAM,GACnBtC,KAAasC,EAAM,GACnBtD,KAAasD,EAAM,GACnBP,SAAaO,EAAM,GACnBJ,MAAaI,EAAM,GACnBvB,MAAauB,EAAM,GACnBtB,UAAasB,EAAM,GACnBjC,KAAaiC,EAAM,KAO/BC,EAAOA,EAAKjB,UAAUgB,EAAM,GAAGxC,QAEnC,OAAOuC,EAAcD,GAAOlD,EApLN6D,CAAclE,GAAMmE,WAG9C,SAASC,EAASb,EAAKjD,GACnB,OAAOF,EAAQiE,MAAM,KAAM,CAACd,GAAKe,OAAOhE,GAAQ,KAgHpD,IAAIkD,EAAgBd,OAAO6B,OAAO,MAuEX,oBAAZC,UACPA,QAAiB,QAAIpE,EACrBoE,QAAkB,SAAIJ,GAEJ,oBAAXK,SACPA,OAAgB,QAAIrE,EACpBqE,OAAiB,SAAIL,EAEC,mBAAXM,QAAyBA,OAAY,KAC5CA,OAAO,WACH,MAAO,CACHtE,QAAWA,EACXgE,SAAYA,MA9N/B", "file": "sprintf.min.js", "sourcesContent": ["/* global window, exports, define */\n\n!function() {\n    'use strict'\n\n    var re = {\n        not_string: /[^s]/,\n        not_bool: /[^t]/,\n        not_type: /[^T]/,\n        not_primitive: /[^v]/,\n        number: /[diefg]/,\n        numeric_arg: /[bcdiefguxX]/,\n        json: /[j]/,\n        not_json: /[^j]/,\n        text: /^[^\\x25]+/,\n        modulo: /^\\x25{2}/,\n        placeholder: /^\\x25(?:([1-9]\\d*)\\$|\\(([^)]+)\\))?(\\+)?(0|'[^$])?(-)?(\\d+)?(?:\\.(\\d+))?([b-gijostTuvxX])/,\n        key: /^([a-z_][a-z_\\d]*)/i,\n        key_access: /^\\.([a-z_][a-z_\\d]*)/i,\n        index_access: /^\\[(\\d+)\\]/,\n        sign: /^[+-]/\n    }\n\n    function sprintf(key) {\n        // `arguments` is not an array, but should be fine for this call\n        return sprintf_format(sprintf_parse(key), arguments)\n    }\n\n    function vsprintf(fmt, argv) {\n        return sprintf.apply(null, [fmt].concat(argv || []))\n    }\n\n    function sprintf_format(parse_tree, argv) {\n        var cursor = 1, tree_length = parse_tree.length, arg, output = '', i, k, ph, pad, pad_character, pad_length, is_positive, sign\n        for (i = 0; i < tree_length; i++) {\n            if (typeof parse_tree[i] === 'string') {\n                output += parse_tree[i]\n            }\n            else if (typeof parse_tree[i] === 'object') {\n                ph = parse_tree[i] // convenience purposes only\n                if (ph.keys) { // keyword argument\n                    arg = argv[cursor]\n                    for (k = 0; k < ph.keys.length; k++) {\n                        if (arg == undefined) {\n                            throw new Error(sprintf('[sprintf] Cannot access property \"%s\" of undefined value \"%s\"', ph.keys[k], ph.keys[k-1]))\n                        }\n                        arg = arg[ph.keys[k]]\n                    }\n                }\n                else if (ph.param_no) { // positional argument (explicit)\n                    arg = argv[ph.param_no]\n                }\n                else { // positional argument (implicit)\n                    arg = argv[cursor++]\n                }\n\n                if (re.not_type.test(ph.type) && re.not_primitive.test(ph.type) && arg instanceof Function) {\n                    arg = arg()\n                }\n\n                if (re.numeric_arg.test(ph.type) && (typeof arg !== 'number' && isNaN(arg))) {\n                    throw new TypeError(sprintf('[sprintf] expecting number but found %T', arg))\n                }\n\n                if (re.number.test(ph.type)) {\n                    is_positive = arg >= 0\n                }\n\n                switch (ph.type) {\n                    case 'b':\n                        arg = parseInt(arg, 10).toString(2)\n                        break\n                    case 'c':\n                        arg = String.fromCharCode(parseInt(arg, 10))\n                        break\n                    case 'd':\n                    case 'i':\n                        arg = parseInt(arg, 10)\n                        break\n                    case 'j':\n                        arg = JSON.stringify(arg, null, ph.width ? parseInt(ph.width) : 0)\n                        break\n                    case 'e':\n                        arg = ph.precision ? parseFloat(arg).toExponential(ph.precision) : parseFloat(arg).toExponential()\n                        break\n                    case 'f':\n                        arg = ph.precision ? parseFloat(arg).toFixed(ph.precision) : parseFloat(arg)\n                        break\n                    case 'g':\n                        arg = ph.precision ? String(Number(arg.toPrecision(ph.precision))) : parseFloat(arg)\n                        break\n                    case 'o':\n                        arg = (parseInt(arg, 10) >>> 0).toString(8)\n                        break\n                    case 's':\n                        arg = String(arg)\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 't':\n                        arg = String(!!arg)\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 'T':\n                        arg = Object.prototype.toString.call(arg).slice(8, -1).toLowerCase()\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 'u':\n                        arg = parseInt(arg, 10) >>> 0\n                        break\n                    case 'v':\n                        arg = arg.valueOf()\n                        arg = (ph.precision ? arg.substring(0, ph.precision) : arg)\n                        break\n                    case 'x':\n                        arg = (parseInt(arg, 10) >>> 0).toString(16)\n                        break\n                    case 'X':\n                        arg = (parseInt(arg, 10) >>> 0).toString(16).toUpperCase()\n                        break\n                }\n                if (re.json.test(ph.type)) {\n                    output += arg\n                }\n                else {\n                    if (re.number.test(ph.type) && (!is_positive || ph.sign)) {\n                        sign = is_positive ? '+' : '-'\n                        arg = arg.toString().replace(re.sign, '')\n                    }\n                    else {\n                        sign = ''\n                    }\n                    pad_character = ph.pad_char ? ph.pad_char === '0' ? '0' : ph.pad_char.charAt(1) : ' '\n                    pad_length = ph.width - (sign + arg).length\n                    pad = ph.width ? (pad_length > 0 ? pad_character.repeat(pad_length) : '') : ''\n                    output += ph.align ? sign + arg + pad : (pad_character === '0' ? sign + pad + arg : pad + sign + arg)\n                }\n            }\n        }\n        return output\n    }\n\n    var sprintf_cache = Object.create(null)\n\n    function sprintf_parse(fmt) {\n        if (sprintf_cache[fmt]) {\n            return sprintf_cache[fmt]\n        }\n\n        var _fmt = fmt, match, parse_tree = [], arg_names = 0\n        while (_fmt) {\n            if ((match = re.text.exec(_fmt)) !== null) {\n                parse_tree.push(match[0])\n            }\n            else if ((match = re.modulo.exec(_fmt)) !== null) {\n                parse_tree.push('%')\n            }\n            else if ((match = re.placeholder.exec(_fmt)) !== null) {\n                if (match[2]) {\n                    arg_names |= 1\n                    var field_list = [], replacement_field = match[2], field_match = []\n                    if ((field_match = re.key.exec(replacement_field)) !== null) {\n                        field_list.push(field_match[1])\n                        while ((replacement_field = replacement_field.substring(field_match[0].length)) !== '') {\n                            if ((field_match = re.key_access.exec(replacement_field)) !== null) {\n                                field_list.push(field_match[1])\n                            }\n                            else if ((field_match = re.index_access.exec(replacement_field)) !== null) {\n                                field_list.push(field_match[1])\n                            }\n                            else {\n                                throw new SyntaxError('[sprintf] failed to parse named argument key')\n                            }\n                        }\n                    }\n                    else {\n                        throw new SyntaxError('[sprintf] failed to parse named argument key')\n                    }\n                    match[2] = field_list\n                }\n                else {\n                    arg_names |= 2\n                }\n                if (arg_names === 3) {\n                    throw new Error('[sprintf] mixing positional and named placeholders is not (yet) supported')\n                }\n\n                parse_tree.push(\n                    {\n                        placeholder: match[0],\n                        param_no:    match[1],\n                        keys:        match[2],\n                        sign:        match[3],\n                        pad_char:    match[4],\n                        align:       match[5],\n                        width:       match[6],\n                        precision:   match[7],\n                        type:        match[8]\n                    }\n                )\n            }\n            else {\n                throw new SyntaxError('[sprintf] unexpected placeholder')\n            }\n            _fmt = _fmt.substring(match[0].length)\n        }\n        return sprintf_cache[fmt] = parse_tree\n    }\n\n    /**\n     * export to either browser or node.js\n     */\n    /* eslint-disable quote-props */\n    if (typeof exports !== 'undefined') {\n        exports['sprintf'] = sprintf\n        exports['vsprintf'] = vsprintf\n    }\n    if (typeof window !== 'undefined') {\n        window['sprintf'] = sprintf\n        window['vsprintf'] = vsprintf\n\n        if (typeof define === 'function' && define['amd']) {\n            define(function() {\n                return {\n                    'sprintf': sprintf,\n                    'vsprintf': vsprintf\n                }\n            })\n        }\n    }\n    /* eslint-enable quote-props */\n}(); // eslint-disable-line\n"]}