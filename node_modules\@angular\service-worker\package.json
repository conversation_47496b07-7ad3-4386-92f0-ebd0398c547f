{"name": "@angular/service-worker", "version": "17.3.12", "description": "Angular - service worker tooling!", "author": "angular", "license": "MIT", "engines": {"node": "^18.13.0 || >=20.9.0"}, "exports": {"./ngsw-worker.js": {"default": "./ngsw-worker.js"}, "./safety-worker.js": {"default": "./safety-worker.js"}, "./config/schema.json": {"default": "./config/schema.json"}, "./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/service-worker.mjs", "esm": "./esm2022/service-worker.mjs", "default": "./fesm2022/service-worker.mjs"}, "./config": {"types": "./config/index.d.ts", "esm2022": "./esm2022/config/config.mjs", "esm": "./esm2022/config/config.mjs", "default": "./fesm2022/config.mjs"}}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "17.3.12", "@angular/common": "17.3.12"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/service-worker"}, "bin": {"ngsw-config": "./ngsw-config.js"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/service-worker.mjs", "typings": "./index.d.ts", "type": "module"}