{"version": 3, "sources": ["webpack://mdc.[name]/webpack/universalModuleDefinition", "webpack://mdc.[name]/webpack/bootstrap", "webpack://mdc.[name]/./packages/mdc-animation/animationframe.ts", "webpack://mdc.[name]/./packages/mdc-animation/index.ts", "webpack://mdc.[name]/./packages/mdc-animation/types.ts", "webpack://mdc.[name]/./packages/mdc-animation/util.ts"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,kDAA0C,gCAAgC;AAC1E;AACA;;AAEA;AACA;AACA;AACA,gEAAwD,kBAAkB;AAC1E;AACA,yDAAiD,cAAc;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAyC,iCAAiC;AAC1E,wHAAgH,mBAAmB,EAAE;AACrI;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;;AAGA;AACA;;;;;;;;;;;;;;AC7DG;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;;;;AACH;AAAA;AACmB,aAAM,SAAG,IAuD5B;AAAC;AAjDI;;;;;AACH,6BAAO,UAAP,UAAmB,KAAgC;AAAnD,oBASC;AARK,aAAO,OAAM;AACjB,YAAa,gCAAyB,UAAM;AACtC,kBAAO,OAAO,OAAM;AACgD;AAC3B;AACrC,qBACV;AAAG,SALkC;AAMjC,aAAO,OAAI,IAAI,KACrB;AAAC;AAKE;;;;AACH,6BAAM,SAAN,UAAkB;AAChB,YAAW,QAAO,KAAO,OAAI,IAAM;AACnC,YAAS,OAAE;AACW,iCAAQ;AACxB,iBAAO,OAAO,OAAM;AAE5B;AAAC;AAIE;;;AACH,6BAAS,YAAT;AAAA,oBAOC;AANwE;AACV;AACd;AAC3C,aAAO,OAAQ,QAAC,UAAE,GAAK;AACrB,kBAAO,OACb;AACF;AAAC;AAIE;;;AACH,6BAAQ,WAAR;AACE,YAAW,QAAgB;AAC4C;AACV;AACd;AAC3C,aAAO,OAAQ,QAAC,UAAE,GAAK;AACpB,kBAAK,KACZ;AAAG;AACH,eACF;AAAC;AACH,WAAC;AAAA;AAxDY,yBAAc,e;;;;;;;;;;;;;ACNxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,4CAA+B;AAEvB,eAAI;AACZ,wGAAiC;AACjC,sFAAwB;AACxB,oFAAuB,UAA6C,2C;;;;;;;;;;;;;ACPjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACAA;;;;;;;;;;;;;;;;;;;;;;;;;AAIH,IAAwB;AACb;AACC,kBAAqB;AACrB,kBACT;AAHU;AAIF;AACC,kBAAqB;AACrB,kBACT;AAHU;AAID;AACA,kBAAsB;AACtB,kBAEV;AAJY;AATmC;AAejD,IAAoB;AACN;AACC,qBAAa;AAChB,kBAAsB;AACtB,kBACT;AAJa;AAKI;AACL,qBAAa;AAChB,kBAA4B;AAC5B,kBACT;AAJmB;AAKN;AACD,qBAAa;AAChB,kBAAwB;AACxB,kBACT;AAJe;AAKH;AACA,qBAAc;AACjB,kBAAuB;AACvB,kBAEV;AALe;AAhB2B;AAuB5C,SAAiB,SAAkB;AACjC,WAAc,QAAU,UAAU,aAC9B,OAAgB,UAAS,SAAc,kBAC7C;AAAC;AAED,SAAsC,uBACjB,WAAsC;AAEzD,QAAY,SAAW,cAAe,eAAsB,oBAAE;AAC5D,YAAQ,KAAY,UAAS,SAAc,cAAQ;AAC7C,iBAAyC,mBAAa;YAA7C;YAAU,cAAoC;AAC7D,YAAgB,aAAW,YAAM,GAAO;AACxC,eAAmB,aAAW,WAAU;AACzC;AACD,WACF;AAAC;AAVD,iCAUC;AAED,SAAmC,oBACd,WAAgC;AAEnD,QAAY,SAAW,cAAa,aAAkB,gBAAE;AACtD,YAAQ,KAAY,UAAS,SAAc,cAAQ;AAC7C,iBAAkD,eAAW;YAApD;YAAU;YAAa,iBAA8B;AACpE,YAAgB,aAAc,eAAM,GAAO;AAC3C,eAAmB,aAAW,WAAU;AACzC;AACD,WACF;AAAC;AAVD,8BAUC,oB", "file": "mdc.animation.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"@material/animation\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"animation\"] = factory();\n\telse\n\t\troot[\"mdc\"] = root[\"mdc\"] || {}, root[\"mdc\"][\"animation\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./packages/mdc-animation/index.ts\");\n", "/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * AnimationFrame provides a user-friendly abstraction around requesting\n * and canceling animation frames.\n */\nexport class AnimationFrame {\n  private readonly rafIDs = new Map<string, number>();\n\n  /**\n   * Requests an animation frame. Cancels any existing frame with the same key.\n   * @param {string} key The key for this callback.\n   * @param {FrameRequestCallback} callback The callback to be executed.\n   */\n  request(key: string, callback: FrameRequestCallback) {\n    this.cancel(key);\n    const frameID = requestAnimationFrame((frame) => {\n      this.rafIDs.delete(key);\n      // Callback must come *after* the key is deleted so that nested calls to\n      // request with the same key are not deleted.\n      callback(frame);\n    });\n    this.rafIDs.set(key, frameID);\n  }\n\n  /**\n   * Cancels a queued callback with the given key.\n   * @param {string} key The key for this callback.\n   */\n  cancel(key: string) {\n    const rafID = this.rafIDs.get(key);\n    if (rafID) {\n      cancelAnimationFrame(rafID);\n      this.rafIDs.delete(key);\n    }\n  }\n\n  /**\n   * Cancels all queued callback.\n   */\n  cancelAll() {\n    // Need to use forEach because it's the only iteration method supported\n    // by IE11. Suppress the underscore because we don't need it.\n    // tslint:disable-next-line:enforce-name-casing\n    this.rafIDs.forEach((_, key) => {\n      this.cancel(key);\n    });\n  }\n\n  /**\n   * Returns the queue of unexecuted callback keys.\n   */\n  getQueue(): string[] {\n    const queue: string[] = [];\n    // Need to use forEach because it's the only iteration method supported\n    // by IE11. Suppress the underscore because we don't need it.\n    // tslint:disable-next-line:enforce-name-casing\n    this.rafIDs.forEach((_, key) => {\n      queue.push(key);\n    });\n    return queue;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nimport * as util from './util';\n\nexport {util};  // New namespace\nexport * from './animationframe';\nexport * from './types';\nexport * from './util';  // Old namespace for backward compatibility\n", "/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nexport type StandardCssPropertyName = 'animation'|'transform'|'transition';\n\nexport type PrefixedCssPropertyName =\n    '-webkit-animation'|'-webkit-transform'|'-webkit-transition';\n\nexport type StandardJsEventType =\n    'animationend'|'animationiteration'|'animationstart'|'transitionend';\n\nexport type PrefixedJsEventType = 'webkitAnimationEnd'|\n    'webkitAnimationIteration'|'webkitAnimationStart'|'webkitTransitionEnd';\n\nexport interface CssVendorProperty {\n  prefixed: PrefixedCssPropertyName;\n  standard: StandardCssPropertyName;\n}\n\nexport interface JsVendorProperty {\n  cssProperty: StandardCssPropertyName;\n  prefixed: PrefixedJsEventType;\n  standard: StandardJsEventType;\n}\n\nexport type CssVendorPropertyMap = {\n  [K in StandardCssPropertyName]: CssVendorProperty\n};\nexport type JsVendorPropertyMap = {\n  [K in StandardJsEventType]: JsVendorProperty\n};\n", "/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nimport {CssVendorPropertyMap, JsVendorPropertyMap, PrefixedCssPropertyName, PrefixedJsEventType, StandardCssPropertyName, StandardJsEventType} from './types';\n\nconst cssPropertyNameMap: CssVendorPropertyMap = {\n  animation: {\n    prefixed: '-webkit-animation',\n    standard: 'animation',\n  },\n  transform: {\n    prefixed: '-webkit-transform',\n    standard: 'transform',\n  },\n  transition: {\n    prefixed: '-webkit-transition',\n    standard: 'transition',\n  },\n};\n\nconst jsEventTypeMap: JsVendorPropertyMap = {\n  animationend: {\n    cssProperty: 'animation',\n    prefixed: 'webkitAnimationEnd',\n    standard: 'animationend',\n  },\n  animationiteration: {\n    cssProperty: 'animation',\n    prefixed: 'webkitAnimationIteration',\n    standard: 'animationiteration',\n  },\n  animationstart: {\n    cssProperty: 'animation',\n    prefixed: 'webkitAnimationStart',\n    standard: 'animationstart',\n  },\n  transitionend: {\n    cssProperty: 'transition',\n    prefixed: 'webkitTransitionEnd',\n    standard: 'transitionend',\n  },\n};\n\nfunction isWindow(windowObj: Window): boolean {\n  return Boolean(windowObj.document) &&\n      typeof windowObj.document.createElement === 'function';\n}\n\nexport function getCorrectPropertyName(\n    windowObj: Window, cssProperty: StandardCssPropertyName):\n    StandardCssPropertyName|PrefixedCssPropertyName {\n  if (isWindow(windowObj) && cssProperty in cssPropertyNameMap) {\n    const el = windowObj.document.createElement('div');\n    const {standard, prefixed} = cssPropertyNameMap[cssProperty];\n    const isStandard = standard in el.style;\n    return isStandard ? standard : prefixed;\n  }\n  return cssProperty;\n}\n\nexport function getCorrectEventName(\n    windowObj: Window, eventType: StandardJsEventType): StandardJsEventType|\n    PrefixedJsEventType {\n  if (isWindow(windowObj) && eventType in jsEventTypeMap) {\n    const el = windowObj.document.createElement('div');\n    const {standard, prefixed, cssProperty} = jsEventTypeMap[eventType];\n    const isStandard = cssProperty in el.style;\n    return isStandard ? standard : prefixed;\n  }\n  return eventType;\n}\n"], "sourceRoot": ""}