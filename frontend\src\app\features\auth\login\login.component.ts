import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { AuthService } from '../../../core/services/auth.service';
import { LoginRequest } from '../../../core/models/user.model';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  template: `
    <div class="login-container">
      <div class="login-card-container">
        <mat-card class="login-card">
          <mat-card-header>
            <mat-card-title>
              <div class="logo">
                <mat-icon>store</mat-icon>
                <span>Shop Management</span>
              </div>
            </mat-card-title>
            <mat-card-subtitle>Sign in to your account</mat-card-subtitle>
          </mat-card-header>

          <mat-card-content>
            <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput 
                       type="email" 
                       formControlName="email"
                       placeholder="Enter your email"
                       autocomplete="email">
                <mat-icon matSuffix>email</mat-icon>
                <mat-error *ngIf="loginForm.get('email')?.hasError('required')">
                  Email is required
                </mat-error>
                <mat-error *ngIf="loginForm.get('email')?.hasError('email')">
                  Please enter a valid email
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Password</mat-label>
                <input matInput 
                       [type]="hidePassword ? 'password' : 'text'"
                       formControlName="password"
                       placeholder="Enter your password"
                       autocomplete="current-password">
                <button mat-icon-button 
                        matSuffix 
                        type="button"
                        (click)="hidePassword = !hidePassword">
                  <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                  Password is required
                </mat-error>
                <mat-error *ngIf="loginForm.get('password')?.hasError('minlength')">
                  Password must be at least 6 characters
                </mat-error>
              </mat-form-field>

              <div class="form-actions">
                <button mat-raised-button 
                        color="primary" 
                        type="submit"
                        class="full-width"
                        [disabled]="loginForm.invalid || loading">
                  <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
                  <span *ngIf="!loading">Sign In</span>
                  <span *ngIf="loading">Signing In...</span>
                </button>
              </div>

              <div class="form-footer">
                <p>
                  Don't have an account? 
                  <a routerLink="/register" class="register-link">Sign up here</a>
                </p>
              </div>
            </form>
          </mat-card-content>
        </mat-card>

        <!-- Demo Credentials -->
        <mat-card class="demo-card">
          <mat-card-header>
            <mat-card-title>Demo Credentials</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="demo-credentials">
              <div class="credential-item">
                <strong>Admin:</strong>
                <span><EMAIL> / admin123</span>
                <button mat-icon-button (click)="fillDemoCredentials('admin')">
                  <mat-icon>content_copy</mat-icon>
                </button>
              </div>
              <div class="credential-item">
                <strong>Manager:</strong>
                <span><EMAIL> / manager123</span>
                <button mat-icon-button (click)="fillDemoCredentials('manager')">
                  <mat-icon>content_copy</mat-icon>
                </button>
              </div>
              <div class="credential-item">
                <strong>Cashier:</strong>
                <span><EMAIL> / cashier123</span>
                <button mat-icon-button (click)="fillDemoCredentials('cashier')">
                  <mat-icon>content_copy</mat-icon>
                </button>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 20px;
    }

    .login-card-container {
      display: flex;
      flex-direction: column;
      gap: 20px;
      width: 100%;
      max-width: 400px;
    }

    .login-card {
      padding: 20px;
    }

    .demo-card {
      padding: 15px;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 1.5em;
    }

    .logo mat-icon {
      font-size: 2em;
      color: #667eea;
    }

    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }

    .form-actions {
      margin: 20px 0;
    }

    .form-actions button {
      height: 48px;
      font-size: 16px;
    }

    .form-footer {
      text-align: center;
      margin-top: 20px;
    }

    .register-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
    }

    .register-link:hover {
      text-decoration: underline;
    }

    .demo-credentials {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .credential-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .credential-item strong {
      min-width: 70px;
    }

    .credential-item span {
      flex: 1;
      margin: 0 10px;
      font-family: monospace;
    }

    @media (max-width: 480px) {
      .login-container {
        padding: 10px;
      }
      
      .login-card {
        padding: 15px;
      }
    }
  `]
})
export class LoginComponent {
  private fb = inject(FormBuilder);
  private authService = inject(AuthService);
  private router = inject(Router);
  private snackBar = inject(MatSnackBar);

  loginForm: FormGroup;
  loading = false;
  hidePassword = true;

  constructor() {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid) {
      this.loading = true;
      const credentials: LoginRequest = this.loginForm.value;

      this.authService.login(credentials).subscribe({
        next: () => {
          this.snackBar.open('Login successful!', 'Close', { duration: 3000 });
        },
        error: (error) => {
          this.loading = false;
          const message = error.error?.message || 'Login failed. Please try again.';
          this.snackBar.open(message, 'Close', { duration: 5000 });
        }
      });
    }
  }

  fillDemoCredentials(role: string): void {
    const credentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      manager: { email: '<EMAIL>', password: 'manager123' },
      cashier: { email: '<EMAIL>', password: 'cashier123' }
    };

    const cred = credentials[role as keyof typeof credentials];
    if (cred) {
      this.loginForm.patchValue(cred);
      this.snackBar.open(`${role} credentials filled`, 'Close', { duration: 2000 });
    }
  }
}
