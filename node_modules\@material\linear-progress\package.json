{"name": "@material/linear-progress", "description": "The Material Components for the web linear progress indicator component", "version": "15.0.0-canary.7f224ddd4.0", "license": "MIT", "main": "dist/mdc.linearProgress.js", "module": "index.js", "sideEffects": false, "keywords": ["material components", "material design", "linear progress"], "repository": {"type": "git", "url": "https://github.com/material-components/material-components-web.git", "directory": "packages/mdc-linear-progress"}, "dependencies": {"@material/animation": "15.0.0-canary.7f224ddd4.0", "@material/base": "15.0.0-canary.7f224ddd4.0", "@material/dom": "15.0.0-canary.7f224ddd4.0", "@material/feature-targeting": "15.0.0-canary.7f224ddd4.0", "@material/progress-indicator": "15.0.0-canary.7f224ddd4.0", "@material/rtl": "15.0.0-canary.7f224ddd4.0", "@material/theme": "15.0.0-canary.7f224ddd4.0", "tslib": "^2.1.0"}, "publishConfig": {"access": "public"}, "gitHead": "ff8ff3ce455054f1532d1ae0983f40634a524886"}