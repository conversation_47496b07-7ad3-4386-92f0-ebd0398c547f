{"name": "unique-filename", "version": "3.0.0", "description": "Generate a unique filename for use in temporary directories or caches.", "main": "lib/index.js", "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "https://github.com/npm/unique-filename.git"}, "keywords": [], "author": "GitHub Inc.", "license": "ISC", "bugs": {"url": "https://github.com/iarna/unique-filename/issues"}, "homepage": "https://github.com/iarna/unique-filename", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.5.1", "tap": "^16.3.0"}, "dependencies": {"unique-slug": "^4.0.0"}, "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}