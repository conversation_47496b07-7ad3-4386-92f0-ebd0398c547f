import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar } from '@angular/material/snack-bar';

import { ApiService } from '../../../core/services/api.service';
import { Product, ProductWithStock } from '../../../core/models/product.model';
import { Cart, CartItem, PaymentMethod } from '../../../core/models/sale.model';

@Component({
  selector: 'app-pos',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatSelectModule
  ],
  template: `
    <div class="pos-container">
      <!-- Product Search -->
      <div class="search-section">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Product Search</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <form [formGroup]="searchForm">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Search by SKU, Barcode, or Name</mat-label>
                <input matInput 
                       formControlName="search"
                       placeholder="Scan or type product code"
                       (keyup.enter)="searchProduct()">
                <button mat-icon-button matSuffix (click)="searchProduct()">
                  <mat-icon>search</mat-icon>
                </button>
              </mat-form-field>
            </form>
          </mat-card-content>
        </mat-card>
      </div>

      <div class="pos-main">
        <!-- Cart Section -->
        <div class="cart-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Shopping Cart</mat-card-title>
              <button mat-icon-button (click)="clearCart()" [disabled]="cart.items.length === 0">
                <mat-icon>clear_all</mat-icon>
              </button>
            </mat-card-header>
            <mat-card-content>
              <div class="cart-items" *ngIf="cart.items.length > 0; else emptyCart">
                <table mat-table [dataSource]="cart.items" class="cart-table">
                  <ng-container matColumnDef="product">
                    <th mat-header-cell *matHeaderCellDef>Product</th>
                    <td mat-cell *matCellDef="let item">
                      <div class="product-info">
                        <div class="product-name">{{ item.product.name }}</div>
                        <div class="product-sku">{{ item.product.sku }}</div>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="quantity">
                    <th mat-header-cell *matHeaderCellDef>Qty</th>
                    <td mat-cell *matCellDef="let item; let i = index">
                      <div class="quantity-controls">
                        <button mat-icon-button (click)="updateQuantity(i, item.quantity - 1)">
                          <mat-icon>remove</mat-icon>
                        </button>
                        <span class="quantity">{{ item.quantity }}</span>
                        <button mat-icon-button (click)="updateQuantity(i, item.quantity + 1)">
                          <mat-icon>add</mat-icon>
                        </button>
                      </div>
                    </td>
                  </ng-container>

                  <ng-container matColumnDef="price">
                    <th mat-header-cell *matHeaderCellDef>Price</th>
                    <td mat-cell *matCellDef="let item">{{ item.unitPrice | currency }}</td>
                  </ng-container>

                  <ng-container matColumnDef="total">
                    <th mat-header-cell *matHeaderCellDef>Total</th>
                    <td mat-cell *matCellDef="let item">{{ item.total | currency }}</td>
                  </ng-container>

                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef></th>
                    <td mat-cell *matCellDef="let item; let i = index">
                      <button mat-icon-button color="warn" (click)="removeItem(i)">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
                </table>
              </div>

              <ng-template #emptyCart>
                <div class="empty-cart">
                  <mat-icon>shopping_cart</mat-icon>
                  <p>Cart is empty</p>
                  <p>Scan or search for products to add them</p>
                </div>
              </ng-template>
            </mat-card-content>
          </mat-card>
        </div>

        <!-- Checkout Section -->
        <div class="checkout-section">
          <mat-card>
            <mat-card-header>
              <mat-card-title>Checkout</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <!-- Order Summary -->
              <div class="order-summary">
                <div class="summary-line">
                  <span>Subtotal:</span>
                  <span>{{ cart.subtotal | currency }}</span>
                </div>
                <div class="summary-line">
                  <span>Tax:</span>
                  <span>{{ cart.tax | currency }}</span>
                </div>
                <div class="summary-line total">
                  <span>Total:</span>
                  <span>{{ cart.total | currency }}</span>
                </div>
              </div>

              <!-- Payment Method -->
              <form [formGroup]="paymentForm">
                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Payment Method</mat-label>
                  <mat-select formControlName="method">
                    <mat-option value="CASH">Cash</mat-option>
                    <mat-option value="CARD">Card</mat-option>
                    <mat-option value="DIGITAL">Digital Payment</mat-option>
                  </mat-select>
                </mat-form-field>

                <mat-form-field appearance="outline" class="full-width">
                  <mat-label>Amount Received</mat-label>
                  <input matInput 
                         type="number" 
                         formControlName="amount"
                         [placeholder]="cart.total.toString()">
                  <span matSuffix>{{ currency }}</span>
                </mat-form-field>

                <div class="change-amount" *ngIf="getChange() > 0">
                  <strong>Change: {{ getChange() | currency }}</strong>
                </div>
              </form>

              <!-- Checkout Actions -->
              <div class="checkout-actions">
                <button mat-raised-button 
                        color="primary" 
                        class="full-width"
                        [disabled]="cart.items.length === 0 || processing"
                        (click)="processSale()">
                  <mat-icon>payment</mat-icon>
                  {{ processing ? 'Processing...' : 'Complete Sale' }}
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .pos-container {
      padding: 20px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .search-section {
      margin-bottom: 20px;
    }

    .pos-main {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 20px;
    }

    .full-width {
      width: 100%;
    }

    .cart-table {
      width: 100%;
    }

    .product-info {
      display: flex;
      flex-direction: column;
    }

    .product-name {
      font-weight: 500;
    }

    .product-sku {
      font-size: 0.8em;
      color: #666;
    }

    .quantity-controls {
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .quantity {
      min-width: 30px;
      text-align: center;
      font-weight: 500;
    }

    .empty-cart {
      text-align: center;
      padding: 40px;
      color: #666;
    }

    .empty-cart mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 20px;
    }

    .order-summary {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .summary-line {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }

    .summary-line.total {
      font-weight: bold;
      font-size: 1.2em;
      border-top: 1px solid #ddd;
      padding-top: 10px;
      margin-top: 10px;
    }

    .change-amount {
      text-align: center;
      padding: 10px;
      background: #e8f5e8;
      border-radius: 4px;
      margin: 10px 0;
      color: #2e7d32;
    }

    .checkout-actions {
      margin-top: 20px;
    }

    .checkout-actions button {
      height: 56px;
      font-size: 16px;
    }

    @media (max-width: 768px) {
      .pos-main {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class PosComponent implements OnInit {
  private fb = inject(FormBuilder);
  private apiService = inject(ApiService);
  private snackBar = inject(MatSnackBar);

  searchForm: FormGroup;
  paymentForm: FormGroup;
  
  cart: Cart = {
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0
  };

  displayedColumns = ['product', 'quantity', 'price', 'total', 'actions'];
  processing = false;
  currency = '$';

  constructor() {
    this.searchForm = this.fb.group({
      search: ['']
    });

    this.paymentForm = this.fb.group({
      method: ['CASH'],
      amount: [0]
    });
  }

  ngOnInit(): void {
    this.updateCartTotals();
  }

  searchProduct(): void {
    const searchTerm = this.searchForm.get('search')?.value?.trim();
    if (!searchTerm) return;

    this.apiService.search<ProductWithStock>('/inventory/products', searchTerm).subscribe({
      next: (response) => {
        if (response.data) {
          this.addToCart(response.data as any);
          this.searchForm.reset();
        }
      },
      error: () => {
        this.snackBar.open('Product not found', 'Close', { duration: 3000 });
      }
    });
  }

  addToCart(product: ProductWithStock): void {
    const existingIndex = this.cart.items.findIndex(item => item.product.id === product.id);
    
    if (existingIndex >= 0) {
      this.cart.items[existingIndex].quantity++;
      this.cart.items[existingIndex].total = this.cart.items[existingIndex].quantity * this.cart.items[existingIndex].unitPrice;
    } else {
      const cartItem: CartItem = {
        product,
        quantity: 1,
        unitPrice: product.price,
        discount: 0,
        total: product.price
      };
      this.cart.items.push(cartItem);
    }
    
    this.updateCartTotals();
    this.snackBar.open(`${product.name} added to cart`, 'Close', { duration: 2000 });
  }

  updateQuantity(index: number, newQuantity: number): void {
    if (newQuantity <= 0) {
      this.removeItem(index);
      return;
    }

    this.cart.items[index].quantity = newQuantity;
    this.cart.items[index].total = newQuantity * this.cart.items[index].unitPrice;
    this.updateCartTotals();
  }

  removeItem(index: number): void {
    this.cart.items.splice(index, 1);
    this.updateCartTotals();
  }

  clearCart(): void {
    this.cart.items = [];
    this.updateCartTotals();
  }

  updateCartTotals(): void {
    this.cart.subtotal = this.cart.items.reduce((sum, item) => sum + item.total, 0);
    this.cart.tax = this.cart.subtotal * 0.1; // 10% tax
    this.cart.total = this.cart.subtotal + this.cart.tax - this.cart.discount;
    
    // Update payment amount to match total
    this.paymentForm.patchValue({ amount: this.cart.total });
  }

  getChange(): number {
    const amountReceived = this.paymentForm.get('amount')?.value || 0;
    return Math.max(0, amountReceived - this.cart.total);
  }

  processSale(): void {
    if (this.cart.items.length === 0) return;

    this.processing = true;
    
    const saleData = {
      items: this.cart.items.map(item => ({
        productId: item.product.id,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discount: item.discount
      })),
      payments: [{
        amount: this.cart.total,
        method: this.paymentForm.get('method')?.value as PaymentMethod
      }],
      discount: this.cart.discount
    };

    this.apiService.post('/sales', saleData).subscribe({
      next: (response) => {
        this.snackBar.open('Sale completed successfully!', 'Close', { duration: 3000 });
        this.clearCart();
        this.processing = false;
      },
      error: (error) => {
        this.snackBar.open('Failed to process sale', 'Close', { duration: 3000 });
        this.processing = false;
      }
    });
  }
}
