{"name": "npm-install-checks", "version": "6.3.0", "description": "Check the engines and platform fields in package.json", "main": "lib/index.js", "dependencies": {"semver": "^7.1.1"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.19.0", "tap": "^16.0.1"}, "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "https://github.com/npm/npm-install-checks.git"}, "keywords": ["npm,", "install"], "license": "BSD-2-<PERSON><PERSON>", "files": ["bin/", "lib/"], "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "author": "GitHub Inc.", "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.19.0", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}