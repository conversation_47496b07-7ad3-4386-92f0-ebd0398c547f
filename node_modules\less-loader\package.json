{"name": "less-loader", "version": "11.1.0", "description": "A Less loader for webpack. Compiles Less to CSS.", "license": "MIT", "repository": "webpack-contrib/less-loader", "author": "<PERSON> @jhnns", "homepage": "https://github.com/webpack-contrib/less-loader", "bugs": "https://github.com/webpack-contrib/less-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 14.15.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "dependencies": {"klona": "^2.0.4"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.3", "@babel/preset-env": "^7.19.3", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.1.2", "cross-env": "^7.0.3", "del": "^6.1.1", "del-cli": "^4.0.1", "eslint": "^8.24.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.24.1", "husky": "^8.0.1", "jest": "^29.1.2", "less": "^4.1.3", "less-plugin-glob": "^2.0.2", "lint-staged": "^12.5.0", "memfs": "^3.4.7", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "standard-version": "^9.3.1", "strip-ansi": "^7.0.0", "webpack": "^5.74.0"}, "keywords": ["webpack", "loader", "less", "lesscss", "less.js", "css", "preprocessor"]}