/// <reference types="node" />
import type { BundleWithDsseEnvelope, BundleWithMessageSignature } from './bundle';
type VerificationMaterialOptions = {
    certificate?: Buffer;
    keyHint?: string;
    singleCertificate?: boolean;
};
type MessageSignatureBundleOptions = {
    digest: Buffer;
    signature: <PERSON><PERSON><PERSON>;
} & VerificationMaterialOptions;
type DSSEBundleOptions = {
    artifact: Buffer;
    artifactType: string;
    signature: <PERSON><PERSON><PERSON>;
} & VerificationMaterialOptions;
export declare function toMessageSignatureBundle(options: MessageSignatureBundleOptions): BundleWithMessageSignature;
export declare function toDSSEBundle(options: DSSEBundleOptions): BundleWithDsseEnvelope;
export {};
