!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/banner",[],t):"object"==typeof exports?exports.banner=t():(e.mdc=e.mdc||{},e.mdc.banner=t())}(this,function(){return n={},i.m=r=[function(e,t,r){"use strict";(function(e){}).call(this,r(20))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapResourceUrl=t.isResourceUrl=t.createResourceUrl=t.TrustedResourceUrl=void 0,r(0);var i=r(4),o=r(9),a=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(e,t){this.privateDoNotAccessOrElseWrappedResourceUrl=e}var c=window.TrustedScriptURL;t.TrustedResourceUrl=null!=c?c:a,t.createResourceUrl=function(e){var t,r=e,n=null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScriptURL(r);return null!=n?n:new a(r,i.secretToken)},t.isResourceUrl=function(e){return e instanceof t.TrustedResourceUrl},t.unwrapResourceUrl=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isScriptURL(e))return e;if(e instanceof a)return e.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapHtml=t.isHtml=t.EMPTY_HTML=t.createHtml=t.SafeHtml=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedHtml=e}function c(e,t){return null!=t?t:new o(e,n.secretToken)}var s=window.TrustedHTML;t.SafeHtml=null!=s?s:o,t.createHtml=function(e){var t,r=e;return c(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createHTML(r))},t.EMPTY_HTML=function(){var e;return c("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyHTML)}(),t.isHtml=function(e){return e instanceof t.SafeHtml},t.unwrapHtml=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isHTML(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},function(e,t,r){"use strict";function n(e){var t;try{t=new URL(e)}catch(e){return"https:"}return t.protocol}Object.defineProperty(t,"__esModule",{value:!0}),t.restrictivelySanitizeUrl=t.unwrapUrlOrSanitize=t.sanitizeJavascriptUrl=void 0,r(0);var i=["data:","http:","https:","mailto:","ftp:"];function o(e){if("javascript:"!==n(e))return e}t.sanitizeJavascriptUrl=o,t.unwrapUrlOrSanitize=function(e){return o(e)},t.restrictivelySanitizeUrl=function(e){var t=n(e);return void 0!==t&&-1!==i.indexOf(t.toLowerCase())?e:"about:invalid#zClosurez"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureTokenIsValid=t.secretToken=void 0,t.secretToken={},t.ensureTokenIsValid=function(e){if(e!==t.secretToken)throw new Error("Bad secret")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapScript=t.isScript=t.EMPTY_SCRIPT=t.createScript=t.SafeScript=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedScript=e}function c(e,t){return null!=t?t:new o(e,n.secretToken)}var s=window.TrustedScript;t.SafeScript=null!=s?s:o,t.createScript=function(e){var t,r=e;return c(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScript(r))},t.EMPTY_SCRIPT=function(){var e;return c("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyScript)}(),t.isScript=function(e){return e instanceof t.SafeScript},t.unwrapScript=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isScript(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertIsTemplateObject=void 0,t.assertIsTemplateObject=function(e,t,r){if(!Array.isArray(e)||!Array.isArray(e.raw)||!t&&1!==e.length)throw new TypeError(r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCFoundation=void 0;var n=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(e){void 0===e&&(e={}),this.adapter=e}t.MDCFoundation=n,t.default=n},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapAttributePrefix=t.createAttributePrefix=t.SafeAttributePrefix=void 0,r(0);function o(){}var a=r(4);t.SafeAttributePrefix=o;var c,s=(i(l,c=o),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},l);function l(e,t){var r=c.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=e,r}t.createAttributePrefix=function(e){return new s(e,a.secretToken)},t.unwrapAttributePrefix=function(e){if(e instanceof s)return e.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEST_ONLY=t.getTrustedTypesPolicy=t.getTrustedTypes=void 0;var n,i="google#safe";function o(){var e;return""!==i&&null!==(e=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==e?e:null}t.getTrustedTypes=o,t.getTrustedTypesPolicy=function(){var e,t;if(void 0===n)try{n=null!==(t=null===(e=o())||void 0===e?void 0:e.createPolicy(i,{createHTML:function(e){return e},createScript:function(e){return e},createScriptURL:function(e){return e}}))&&void 0!==t?t:null}catch(e){n=null}return n},t.TEST_ONLY={resetDefaults:function(){n=void 0,i="google#safe"},setTrustedTypesPolicyName:function(e){i=e}}},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyle=t.isStyle=t.createStyle=t.SafeStyle=void 0,r(0);function o(){}var a=r(4);t.SafeStyle=o;var c,s=(i(l,c=o),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},l);function l(e,t){var r=c.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=e,r}t.createStyle=function(e){return new s(e,a.secretToken)},t.isStyle=function(e){return e instanceof s},t.unwrapStyle=function(e){if(e instanceof s)return e.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AttributePolicyAction=t.SanitizerTable=void 0;var n,i,o=(a.prototype.isAllowedElement=function(e){return"form"!==e.toLowerCase()&&(this.allowedElements.has(e)||this.elementPolicies.has(e))},a.prototype.getAttributePolicy=function(e,t){var r=this.elementPolicies.get(t);return(null==r?void 0:r.has(e))?r.get(e):this.allowedGlobalAttributes.has(e)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(e)||{policyAction:n.DROP}},a);function a(e,t,r,n){this.allowedElements=e,this.elementPolicies=t,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}t.SanitizerTable=o,(i=n=t.AttributePolicyAction||(t.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.isStyleSheet=t.createStyleSheet=t.SafeStyleSheet=void 0,r(0);function o(){}var a=r(4);t.SafeStyleSheet=o;var c,s=(i(l,c=o),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},l);function l(e,t){var r=c.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=e,r}t.createStyleSheet=function(e){return new s(e,a.secretToken)},t.isStyleSheet=function(e){return e instanceof s},t.unwrapStyleSheet=function(e){if(e instanceof s)return e.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},function(e,t,r){"use strict";var i=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__spreadArray||function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCComponent=void 0;var c=r(17),s=r(18),n=r(7);var l,u,f=(p.attachTo=function(e){return new p(e,new n.MDCFoundation({}))},p.prototype.initialize=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},p.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},p.prototype.initialSyncWithDOM=function(){},p.prototype.destroy=function(){this.foundation.destroy()},p.prototype.listen=function(e,t,r){this.root.addEventListener(e,t,r)},p.prototype.unlisten=function(e,t,r){this.root.removeEventListener(e,t,r)},p.prototype.emit=function(e,t,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(e,{bubbles:r,detail:t}):(n=document.createEvent("CustomEvent")).initCustomEvent(e,r,!1,t),this.root.dispatchEvent(n)},p.prototype.safeSetAttribute=function(e,t,r){if("tabindex"===t.toLowerCase())e.tabIndex=Number(r);else if(0===t.indexOf("data-")){var n=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})}(t.replace(/^data-/,""));e.dataset[n]=r}else s.safeElement.setPrefixedAttribute([c.safeAttrPrefix(l=l||i(["aria-"],["aria-"])),c.safeAttrPrefix(u=u||i(["role"],["role"]))],e,t,r)},p);function p(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=e,this.initialize.apply(this,a([],o(r))),this.foundation=void 0===t?this.getDefaultFoundation():t,this.foundation.init(),this.initialSyncWithDOM()}t.MDCComponent=f,t.default=f},function(e,t,r){"use strict";var d=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},f=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.HtmlSanitizerImpl=void 0,r(0);var n=r(2),i=r(4),y=r(3),s=r(23),h=r(24),o=r(16),v=r(11),a=(c.prototype.sanitizeAssertUnchanged=function(e){this.changes=[];var t=this.sanitize(e);if(0===this.changes.length)return t;throw new Error("")},c.prototype.sanitize=function(e){var t=document.createElement("span");t.appendChild(this.sanitizeToFragment(e));var r=(new XMLSerializer).serializeToString(t);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},c.prototype.sanitizeToFragment=function(e){for(var t=this,r=(0,s.createInertFragment)(e),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(e){return t.nodeFilter(e)},!1),i=n.nextNode(),o=document.createDocumentFragment(),a=o;null!==i;){var c=void 0;if((0,h.isText)(i))c=this.sanitizeTextNode(i);else{if(!(0,h.isElement)(i))throw new Error("Node is not of type text or element");c=this.sanitizeElementNode(i)}if(a.appendChild(c),i=n.firstChild())a=c;else for(;!(i=n.nextSibling())&&(i=n.parentNode());)a=a.parentNode}return o},c.prototype.sanitizeTextNode=function(e){return document.createTextNode(e.data)},c.prototype.sanitizeElementNode=function(e){var t,r,n=(0,h.getNodeName)(e),i=document.createElement(n),o=e.attributes;try{for(var a=d(o),c=a.next();!c.done;c=a.next()){var s=c.value,l=s.name,u=s.value,f=this.sanitizerTable.getAttributePolicy(l,n);if(this.satisfiesAllConditions(f.conditions,o))switch(f.policyAction){case v.AttributePolicyAction.KEEP:i.setAttribute(l,u);break;case v.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var p=(0,y.restrictivelySanitizeUrl)(u);p!==u&&this.recordChange("Url in attribute ".concat(l,' was modified during sanitization. Original url:"').concat(u,'" was sanitized to: "').concat(p,'"')),i.setAttribute(l,p);break;case v.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(l,u.toLowerCase());break;case v.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(l,u);break;case v.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(l," was dropped"));break;default:m(f.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(l,"."))}}catch(e){t={error:e}}finally{try{c&&!c.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return i},c.prototype.nodeFilter=function(e){if((0,h.isText)(e))return NodeFilter.FILTER_ACCEPT;if(!(0,h.isElement)(e))return NodeFilter.FILTER_REJECT;var t=(0,h.getNodeName)(e);return null===t?(this.recordChange("Node name was null for node: ".concat(e)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(t)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(t," was dropped")),NodeFilter.FILTER_REJECT)},c.prototype.recordChange=function(e){0===this.changes.length&&this.changes.push("")},c.prototype.satisfiesAllConditions=function(e,t){var r,n,i;if(!e)return!0;try{for(var o=d(e),a=o.next();!a.done;a=o.next()){var c=f(a.value,2),s=c[0],l=c[1],u=null===(i=t.getNamedItem(s))||void 0===i?void 0:i.value;if(u&&!l.has(u))return!1}}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0},c);function c(e,t){this.sanitizerTable=e,this.changes=[],(0,i.ensureTokenIsValid)(t)}t.HtmlSanitizerImpl=a;var l=function(){return new a(o.defaultSanitizerTable,i.secretToken)}();function m(e,t){throw void 0===t&&(t="unexpected value ".concat(e,"!")),new Error(t)}t.sanitizeHtml=function(e){return l.sanitize(e)},t.sanitizeHtmlAssertUnchanged=function(e){return l.sanitizeAssertUnchanged(e)},t.sanitizeHtmlToFragment=function(e){return l.sanitizeToFragment(e)}},function(e,t,r){"use strict";var i=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.setPrefixedAttribute=t.buildPrefixedAttributeSetter=t.insertAdjacentHtml=t.setCssText=t.setOuterHtml=t.setInnerHtml=void 0;var a=r(8),c=r(2),n=r(10);function s(e,t,r,n){if(0===e.length)throw new Error("No prefixes are provided");var i=e.map(function(e){return(0,a.unwrapAttributePrefix)(e)}),o=r.toLowerCase();if(i.every(function(e){return 0!==o.indexOf(e)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));t.setAttribute(r,n)}function l(e){if("script"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}t.setInnerHtml=function(e,t){!function(e){return void 0!==e.tagName}(e)||l(e),e.innerHTML=(0,c.unwrapHtml)(t)},t.setOuterHtml=function(e,t){var r=e.parentElement;null!==r&&l(r),e.outerHTML=(0,c.unwrapHtml)(t)},t.setCssText=function(e,t){e.style.cssText=(0,n.unwrapStyle)(t)},t.insertAdjacentHtml=function(e,t,r){var n="beforebegin"===t||"afterend"===t?e.parentElement:e;null!==n&&l(n),e.insertAdjacentHTML(t,(0,c.unwrapHtml)(r))},t.buildPrefixedAttributeSetter=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=o([e],i(t),!1);return function(e,t,r){s(n,e,t,r)}},t.setPrefixedAttribute=s},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSanitizerTable=void 0;var n=r(11);t.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.SafeStyleSheet=t.isStyleSheet=t.unwrapStyle=t.SafeStyle=t.isStyle=t.unwrapScript=t.SafeScript=t.isScript=t.EMPTY_SCRIPT=t.unwrapResourceUrl=t.TrustedResourceUrl=t.isResourceUrl=t.unwrapHtml=t.SafeHtml=t.isHtml=t.EMPTY_HTML=t.unwrapAttributePrefix=t.SafeAttributePrefix=t.safeStyleSheet=t.concatStyleSheets=t.safeStyle=t.concatStyles=t.scriptFromJson=t.safeScriptWithArgs=t.safeScript=t.concatScripts=t.trustedResourceUrl=t.replaceFragment=t.blobUrlFromScript=t.appendParams=t.HtmlSanitizerBuilder=t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.htmlEscape=t.createScriptSrc=t.createScript=t.concatHtmls=t.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(t,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var i=r(22);Object.defineProperty(t,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(t,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(t,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(t,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var o=r(14);Object.defineProperty(t,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(t,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(t,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(t,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var c=r(26);Object.defineProperty(t,"appendParams",{enumerable:!0,get:function(){return c.appendParams}}),Object.defineProperty(t,"blobUrlFromScript",{enumerable:!0,get:function(){return c.blobUrlFromScript}}),Object.defineProperty(t,"replaceFragment",{enumerable:!0,get:function(){return c.replaceFragment}}),Object.defineProperty(t,"trustedResourceUrl",{enumerable:!0,get:function(){return c.trustedResourceUrl}});var s=r(27);Object.defineProperty(t,"concatScripts",{enumerable:!0,get:function(){return s.concatScripts}}),Object.defineProperty(t,"safeScript",{enumerable:!0,get:function(){return s.safeScript}}),Object.defineProperty(t,"safeScriptWithArgs",{enumerable:!0,get:function(){return s.safeScriptWithArgs}}),Object.defineProperty(t,"scriptFromJson",{enumerable:!0,get:function(){return s.scriptFromJson}});var l=r(28);Object.defineProperty(t,"concatStyles",{enumerable:!0,get:function(){return l.concatStyles}}),Object.defineProperty(t,"safeStyle",{enumerable:!0,get:function(){return l.safeStyle}});var u=r(29);Object.defineProperty(t,"concatStyleSheets",{enumerable:!0,get:function(){return u.concatStyleSheets}}),Object.defineProperty(t,"safeStyleSheet",{enumerable:!0,get:function(){return u.safeStyleSheet}});var f=r(8);Object.defineProperty(t,"SafeAttributePrefix",{enumerable:!0,get:function(){return f.SafeAttributePrefix}}),Object.defineProperty(t,"unwrapAttributePrefix",{enumerable:!0,get:function(){return f.unwrapAttributePrefix}});var p=r(2);Object.defineProperty(t,"EMPTY_HTML",{enumerable:!0,get:function(){return p.EMPTY_HTML}}),Object.defineProperty(t,"isHtml",{enumerable:!0,get:function(){return p.isHtml}}),Object.defineProperty(t,"SafeHtml",{enumerable:!0,get:function(){return p.SafeHtml}}),Object.defineProperty(t,"unwrapHtml",{enumerable:!0,get:function(){return p.unwrapHtml}});var d=r(1);Object.defineProperty(t,"isResourceUrl",{enumerable:!0,get:function(){return d.isResourceUrl}}),Object.defineProperty(t,"TrustedResourceUrl",{enumerable:!0,get:function(){return d.TrustedResourceUrl}}),Object.defineProperty(t,"unwrapResourceUrl",{enumerable:!0,get:function(){return d.unwrapResourceUrl}});var y=r(5);Object.defineProperty(t,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return y.EMPTY_SCRIPT}}),Object.defineProperty(t,"isScript",{enumerable:!0,get:function(){return y.isScript}}),Object.defineProperty(t,"SafeScript",{enumerable:!0,get:function(){return y.SafeScript}}),Object.defineProperty(t,"unwrapScript",{enumerable:!0,get:function(){return y.unwrapScript}});var h=r(10);Object.defineProperty(t,"isStyle",{enumerable:!0,get:function(){return h.isStyle}}),Object.defineProperty(t,"SafeStyle",{enumerable:!0,get:function(){return h.SafeStyle}}),Object.defineProperty(t,"unwrapStyle",{enumerable:!0,get:function(){return h.unwrapStyle}});var v=r(12);Object.defineProperty(t,"isStyleSheet",{enumerable:!0,get:function(){return v.isStyleSheet}}),Object.defineProperty(t,"SafeStyleSheet",{enumerable:!0,get:function(){return v.SafeStyleSheet}}),Object.defineProperty(t,"unwrapStyleSheet",{enumerable:!0,get:function(){return v.unwrapStyleSheet}})},function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&("get"in i?t.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.safeWorker=t.safeWindow=t.safeServiceWorkerContainer=t.safeRange=t.safeLocation=t.safeGlobal=t.safeDomParser=t.safeDocument=t.safeStyleEl=t.safeScriptEl=t.safeObjectEl=t.safeLinkEl=t.safeInputEl=t.safeIframeEl=t.safeFormEl=t.safeEmbedEl=t.safeElement=t.safeButtonEl=t.safeAreaEl=t.safeAnchorEl=void 0,t.safeAnchorEl=o(r(30)),t.safeAreaEl=o(r(31)),t.safeButtonEl=o(r(32)),t.safeElement=o(r(15)),t.safeEmbedEl=o(r(33)),t.safeFormEl=o(r(34)),t.safeIframeEl=o(r(35)),t.safeInputEl=o(r(36)),t.safeLinkEl=o(r(37)),t.safeObjectEl=o(r(38)),t.safeScriptEl=o(r(39)),t.safeStyleEl=o(r(40)),t.safeDocument=o(r(41)),t.safeDomParser=o(r(42)),t.safeGlobal=o(r(43)),t.safeLocation=o(r(44)),t.safeRange=o(r(45)),t.safeServiceWorkerContainer=o(r(46)),t.safeWindow=o(r(47)),t.safeWorker=o(r(48))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);t.safeAttrPrefix=function(e){var t=e[0].toLowerCase();return(0,n.createAttributePrefix)(t)}},function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var s,l=[],u=!1,f=-1;function p(){u&&s&&(u=!1,s.length?l=s.concat(l):f=-1,l.length&&d())}function d(){if(!u){var e=c(p);u=!0;for(var t=l.length;t;){for(s=l,l=[];++f<t;)s&&s[f].run();f=-1,t=l.length}s=null,u=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(e)}}function y(e,t){this.fun=e,this.array=t}function h(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new y(e,t)),1!==l.length||u||c(d)},y.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SECURITY_SENSITIVE_ATTRIBUTES=void 0,t.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatHtmls=t.createScriptSrc=t.createScript=t.htmlEscape=void 0;var o=r(2),a=r(1),i=r(5);function c(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}t.htmlEscape=function(e,t){void 0===t&&(t={});var r=c(e);return t.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),t.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),t.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(r)},t.createScript=function(e,t){void 0===t&&(t={});var r=(0,i.unwrapScript)(e).toString(),n="<script";return t.id&&(n+=' id="'.concat(c(t.id),'"')),t.nonce&&(n+=' nonce="'.concat(c(t.nonce),'"')),t.type&&(n+=' type="'.concat(c(t.type),'"')),n+=">".concat(r,"<\/script>"),(0,o.createHtml)(n)},t.createScriptSrc=function(e,t,r){var n=(0,a.unwrapResourceUrl)(e).toString(),i='<script src="'.concat(c(n),'"');return t&&(i+=" async"),r&&(i+=' nonce="'.concat(c(r),'"')),i+="><\/script>",(0,o.createHtml)(i)},t.concatHtmls=function(e){return(0,o.createHtml)(e.map(o.unwrapHtml).join(""))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInertFragment=void 0;var n=r(15),i=r(2);t.createInertFragment=function(e){var t=document.createElement("template"),r=(0,i.createHtml)(e);return(0,n.setInnerHtml)(t,r),t.content}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElement=t.isText=t.getNodeName=void 0,t.getNodeName=function(e){var t=e.nodeName;return"string"==typeof t?t:"FORM"},t.isText=function(e){return e.nodeType===Node.TEXT_NODE},t.isElement=function(e){var t=e.nodeType;return t===Node.ELEMENT_NODE||"number"!=typeof t}},function(e,t,r){"use strict";var _=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},g=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlSanitizerBuilder=void 0;var n=r(4),i=r(14),o=r(16),P=r(11),a=(c.prototype.onlyAllowElements=function(e){var t,r,n=new Set,i=new Map;try{for(var o=_(e),a=o.next();!a.done;a=o.next()){var c=a.value;if(c=c.toUpperCase(),!this.sanitizerTable.isAllowedElement(c))throw new Error("Element: ".concat(c,", is not allowed by html5_contract.textpb"));var s=this.sanitizerTable.elementPolicies.get(c);void 0!==s?i.set(c,s):n.add(c)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return this.sanitizerTable=new P.SanitizerTable(n,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},c.prototype.onlyAllowAttributes=function(e){var t,r,n,i,o,a,c=new Set,s=new Map,l=new Map;try{for(var u=_(e),f=u.next();!f.done;f=u.next()){var p=f.value;this.sanitizerTable.allowedGlobalAttributes.has(p)&&c.add(p),this.sanitizerTable.globalAttributePolicies.has(p)&&s.set(p,this.sanitizerTable.globalAttributePolicies.get(p))}}catch(e){t={error:e}}finally{try{f&&!f.done&&(r=u.return)&&r.call(u)}finally{if(t)throw t.error}}try{for(var d=_(this.sanitizerTable.elementPolicies.entries()),y=d.next();!y.done;y=d.next()){var h=g(y.value,2),v=h[0],m=h[1],b=new Map;try{for(var S=(o=void 0,_(m.entries())),A=S.next();!A.done;A=S.next()){var E=g(A.value,2),T=(p=E[0],E[1]);e.has(p)&&b.set(p,T)}}catch(e){o={error:e}}finally{try{A&&!A.done&&(a=S.return)&&a.call(S)}finally{if(o)throw o.error}}l.set(v,b)}}catch(e){n={error:e}}finally{try{y&&!y.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}return this.sanitizerTable=new P.SanitizerTable(this.sanitizerTable.allowedElements,l,c,s),this},c.prototype.allowDataAttributes=function(e){var t,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=_(e),o=i.next();!o.done;o=i.next()){var a=o.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));n.add(a)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return this.sanitizerTable=new P.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},c.prototype.allowStyleAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("style",{policyAction:P.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new P.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},c.prototype.allowClassAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("class",{policyAction:P.AttributePolicyAction.KEEP}),this.sanitizerTable=new P.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},c.prototype.allowIdAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("id",{policyAction:P.AttributePolicyAction.KEEP}),this.sanitizerTable=new P.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},c.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},c);function c(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}t.HtmlSanitizerBuilder=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.blobUrlFromScript=t.replaceFragment=t.appendParams=t.trustedResourceUrl=void 0,r(0);var c=r(1),n=r(5);r(6);t.trustedResourceUrl=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(0===t.length)return(0,c.createResourceUrl)(e[0]);e[0].toLowerCase();for(var n=[e[0]],i=0;i<t.length;i++)n.push(encodeURIComponent(t[i])),n.push(e[i+1]);return(0,c.createResourceUrl)(n.join(""))},t.appendParams=function(e,t){var o=(0,c.unwrapResourceUrl)(e).toString();if(/#/.test(o)){throw new Error("")}var a=/\?/.test(o)?"&":"?";return t.forEach(function(e,t){for(var r=e instanceof Array?e:[e],n=0;n<r.length;n++){var i=r[n];null!=i&&(o+=a+encodeURIComponent(t)+"="+encodeURIComponent(String(i)),a="&")}}),(0,c.createResourceUrl)(o)};var i=/[^#]*/;t.replaceFragment=function(e,t){var r=(0,c.unwrapResourceUrl)(e).toString();return(0,c.createResourceUrl)(i.exec(r)[0]+"#"+t)},t.blobUrlFromScript=function(e){var t=(0,n.unwrapScript)(e).toString(),r=new Blob([t],{type:"text/javascript"});return(0,c.createResourceUrl)(URL.createObjectURL(r))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeScriptWithArgs=t.scriptFromJson=t.concatScripts=t.safeScript=void 0,r(0);var i=r(5);r(6);function o(e){return(0,i.createScript)(JSON.stringify(e).replace(/</g,"\\x3c"))}t.safeScript=function(e){return(0,i.createScript)(e[0])},t.concatScripts=function(e){return(0,i.createScript)(e.map(i.unwrapScript).join(""))},t.scriptFromJson=o,t.safeScriptWithArgs=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.map(function(e){return o(e).toString()});return(0,i.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyles=t.safeStyle=void 0,r(0);r(6);var n=r(10);t.safeStyle=function(e){var t=e[0];return(0,n.createStyle)(t)},t.concatStyles=function(e){return(0,n.createStyle)(e.map(n.unwrapStyle).join(""))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyleSheets=t.safeStyleSheet=void 0,r(0);r(6);var n=r(12);t.safeStyleSheet=function(e){var t=e[0];return(0,n.createStyleSheet)(t)},t.concatStyleSheets=function(e){return(0,n.createStyleSheet)(e.map(n.unwrapStyleSheet).join(""))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=void 0;var n=r(1);t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setAction=void 0;var n=r(3);t.setAction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.action=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrcdoc=t.setSrc=void 0;var n=r(2),i=r(1);t.setSrc=function(e,t){e.src=(0,i.unwrapResourceUrl)(t).toString()},t.setSrcdoc=function(e,t){e.srcdoc=(0,n.unwrapHtml)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHrefAndRel=void 0;var i=r(3),o=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];t.setHrefAndRel=function(e,t,r){if(t instanceof o.TrustedResourceUrl)e.href=(0,o.unwrapResourceUrl)(t).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,i.unwrapUrlOrSanitize)(t);if(void 0===n)return;e.href=n}e.rel=r}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setData=void 0;var n=r(1);t.setData=function(e,t){e.data=(0,n.unwrapResourceUrl)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=t.setTextContent=void 0;var n=r(1),i=r(5);function o(e){var t=function(e){var t,r=e.document,n=null===(t=r.querySelector)||void 0===t?void 0:t.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(e.ownerDocument&&e.ownerDocument.defaultView||window);t&&e.setAttribute("nonce",t)}t.setTextContent=function(e,t){e.textContent=(0,i.unwrapScript)(t),o(e)},t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t),o(e)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setTextContent=void 0;var n=r(12);t.setTextContent=function(e,t){e.textContent=(0,n.unwrapStyleSheet)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execCommandInsertHtml=t.execCommand=t.write=void 0;var o=r(2);t.write=function(e,t){e.write((0,o.unwrapHtml)(t))},t.execCommand=function(e,t,r){var n=String(t),i=r;return"inserthtml"===n.toLowerCase()&&(i=(0,o.unwrapHtml)(r)),e.execCommand(n,!1,i)},t.execCommandInsertHtml=function(e,t){return e.execCommand("insertHTML",!1,(0,o.unwrapHtml)(t))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseFromString=t.parseHtml=void 0;var n=r(2);function i(e,t,r){return e.parseFromString((0,n.unwrapHtml)(t),r)}t.parseHtml=function(e,t){return i(e,t,"text/html")},t.parseFromString=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.globalEval=void 0;var i=r(5);t.globalEval=function(e,t){var r=(0,i.unwrapScript)(t),n=e.eval(r);return n===r&&(n=e.eval(r.toString())),n}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assign=t.replace=t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)},t.replace=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.replace(r)},t.assign=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.assign(r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createContextualFragment=void 0;var n=r(2);t.createContextualFragment=function(e,t){return e.createContextualFragment((0,n.unwrapHtml)(t))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.register=void 0;var n=r(1);t.register=function(e,t,r){return e.register((0,n.unwrapResourceUrl)(t),r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.open=void 0;var o=r(3);t.open=function(e,t,r,n){var i=(0,o.unwrapUrlOrSanitize)(t);return void 0!==i?e.open(i,r,n):null}},function(e,t,r){"use strict";var n=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.importScripts=t.createShared=t.create=void 0;var o=r(1);t.create=function(e,t){return new Worker((0,o.unwrapResourceUrl)(e),t)},t.createShared=function(e,t){return new SharedWorker((0,o.unwrapResourceUrl)(e),t)},t.importScripts=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];e.importScripts.apply(e,i([],n(t.map(function(e){return(0,o.unwrapResourceUrl)(e)})),!1))}},function(e,t,r){"use strict";function n(e,t){return(e.matches||e.webkitMatchesSelector||e.msMatchesSelector).call(e,t)}Object.defineProperty(t,"__esModule",{value:!0}),t.estimateScrollWidth=t.matches=t.closest=void 0,t.closest=function(e,t){if(e.closest)return e.closest(t);for(var r=e;r;){if(n(r,t))return r;r=r.parentElement}return null},t.matches=n,t.estimateScrollWidth=function(e){var t=e;if(null!==t.offsetParent)return t.scrollWidth;var r=t.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FocusTrap=void 0;var o="mdc-dom-focus-sentinel",n=(i.prototype.trapFocus=function(){var e=this.getFocusableElements(this.root);if(0===e.length)throw new Error("FocusTrap: Element must have at least one focusable child.");this.elFocusedBeforeTrapFocus=document.activeElement instanceof HTMLElement?document.activeElement:null,this.wrapTabFocus(this.root),this.options.skipInitialFocus||this.focusInitialElement(e,this.options.initialFocusEl)},i.prototype.releaseFocus=function(){Array.from(this.root.querySelectorAll("."+o)).forEach(function(e){e.parentElement.removeChild(e)}),!this.options.skipRestoreFocus&&this.elFocusedBeforeTrapFocus&&this.elFocusedBeforeTrapFocus.focus()},i.prototype.wrapTabFocus=function(t){var r=this,e=this.createSentinel(),n=this.createSentinel();e.addEventListener("focus",function(){var e=r.getFocusableElements(t);0<e.length&&e[e.length-1].focus()}),n.addEventListener("focus",function(){var e=r.getFocusableElements(t);0<e.length&&e[0].focus()}),t.insertBefore(e,t.children[0]),t.appendChild(n)},i.prototype.focusInitialElement=function(e,t){var r=0;t&&(r=Math.max(e.indexOf(t),0)),e[r].focus()},i.prototype.getFocusableElements=function(e){return Array.from(e.querySelectorAll("[autofocus], [tabindex], a, input, textarea, select, button")).filter(function(e){var t="true"===e.getAttribute("aria-disabled")||null!=e.getAttribute("disabled")||null!=e.getAttribute("hidden")||"true"===e.getAttribute("aria-hidden"),r=0<=e.tabIndex&&0<e.getBoundingClientRect().width&&!e.classList.contains(o)&&!t,n=!1;if(r){var i=getComputedStyle(e);n="none"===i.display||"hidden"===i.visibility}return r&&!n})},i.prototype.createSentinel=function(){var e=document.createElement("div");return e.setAttribute("tabindex","0"),e.setAttribute("aria-hidden","true"),e.classList.add(o),e},i);function i(e,t){void 0===t&&(t={}),this.root=e,this.options=t,this.elFocusedBeforeTrapFocus=null}t.FocusTrap=n},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";var n,i;Object.defineProperty(t,"__esModule",{value:!0}),t.Action=t.CloseReason=t.selectors=t.events=t.numbers=t.cssClasses=void 0,t.cssClasses={CLOSING:"mdc-banner--closing",OPEN:"mdc-banner--open",OPENING:"mdc-banner--opening"},t.numbers={BANNER_ANIMATION_CLOSE_TIME_MS:250,BANNER_ANIMATION_OPEN_TIME_MS:300},t.events={CLOSED:"MDCBanner:closed",CLOSING:"MDCBanner:closing",OPENED:"MDCBanner:opened",OPENING:"MDCBanner:opening",ACTION_CLICKED:"MDCBanner:actionClicked"},t.selectors={CONTENT:".mdc-banner__content",PRIMARY_ACTION:".mdc-banner__primary-action",SECONDARY_ACTION:".mdc-banner__secondary-action",TEXT:".mdc-banner__text"},(n=t.CloseReason||(t.CloseReason={}))[n.PRIMARY=0]="PRIMARY",n[n.SECONDARY=1]="SECONDARY",n[n.UNSPECIFIED=2]="UNSPECIFIED",(i=t.Action||(t.Action={}))[i.PRIMARY=0]="PRIMARY",i[i.SECONDARY=1]="SECONDARY",i[i.UNKNOWN=2]="UNKNOWN"},,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCBannerFoundation=void 0;var a,c=r(7),s=r(99),l=s.cssClasses.OPENING,u=s.cssClasses.OPEN,f=s.cssClasses.CLOSING,p=(a=c.MDCFoundation,i(d,a),Object.defineProperty(d,"defaultAdapter",{get:function(){return{addClass:function(){},getContentHeight:function(){return 0},notifyClosed:function(){},notifyClosing:function(){},notifyOpened:function(){},notifyOpening:function(){},notifyActionClicked:function(){},releaseFocus:function(){},removeClass:function(){},setStyleProperty:function(){},trapFocus:function(){}}},enumerable:!1,configurable:!0}),d.prototype.destroy=function(){cancelAnimationFrame(this.animationFrame),this.animationFrame=0,clearTimeout(this.animationTimer),this.animationTimer=0},d.prototype.open=function(){var e=this;this.isOpened=!0,this.adapter.removeClass(f),this.adapter.addClass(l),this.adapter.notifyOpening();var t=this.adapter.getContentHeight();this.animationFrame=requestAnimationFrame(function(){e.adapter.addClass(u),e.adapter.setStyleProperty("height",t+"px"),e.animationTimer=setTimeout(function(){e.handleAnimationTimerEnd(),e.adapter.trapFocus(),e.adapter.notifyOpened()},s.numbers.BANNER_ANIMATION_OPEN_TIME_MS)})},d.prototype.close=function(e){var t=this;this.isOpened&&(cancelAnimationFrame(this.animationFrame),this.animationFrame=0,this.isOpened=!1,this.adapter.notifyClosing(e),this.adapter.addClass(f),this.adapter.setStyleProperty("height","0"),this.adapter.removeClass(u),this.adapter.removeClass(l),clearTimeout(this.animationTimer),this.animationTimer=setTimeout(function(){t.adapter.releaseFocus(),t.handleAnimationTimerEnd(),t.adapter.notifyClosed(e)},s.numbers.BANNER_ANIMATION_CLOSE_TIME_MS))},d.prototype.isOpen=function(){return this.isOpened},d.prototype.handlePrimaryActionClick=function(e){void 0===e&&(e=!1),e?this.adapter.notifyActionClicked(s.Action.PRIMARY):this.close(s.CloseReason.PRIMARY)},d.prototype.handleSecondaryActionClick=function(e){void 0===e&&(e=!1),e?this.adapter.notifyActionClicked(s.Action.SECONDARY):this.close(s.CloseReason.SECONDARY)},d.prototype.layout=function(){var e=this.adapter.getContentHeight();this.adapter.setStyleProperty("height",e+"px")},d.prototype.handleAnimationTimerEnd=function(){this.animationTimer=0,this.adapter.removeClass(l),this.adapter.removeClass(f)},d);function d(e){var t=a.call(this,o(o({},d.defaultAdapter),e))||this;return t.isOpened=!1,t.animationFrame=0,t.animationTimer=0,t}t.MDCBannerFoundation=p},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(187),t),i(r(188),t),i(r(99),t),i(r(123),t)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCBanner=void 0;var o,a=r(13),c=r(68),s=r(49),l=r(99),u=r(123),f=(o=a.MDCComponent,i(p,o),p.attachTo=function(e){return new p(e)},p.prototype.initialize=function(e){var r=this;void 0===e&&(e=function(e,t){return new c.FocusTrap(e,t)}),this.contentEl=this.root.querySelector(l.selectors.CONTENT),this.textEl=this.root.querySelector(l.selectors.TEXT),this.primaryActionEl=this.root.querySelector(l.selectors.PRIMARY_ACTION),this.secondaryActionEl=this.root.querySelector(l.selectors.SECONDARY_ACTION),this.focusTrapFactory=e,this.handleContentClick=function(e){var t=e.target;s.closest(t,l.selectors.PRIMARY_ACTION)?r.foundation.handlePrimaryActionClick():s.closest(t,l.selectors.SECONDARY_ACTION)&&r.foundation.handleSecondaryActionClick()}},p.prototype.initialSyncWithDOM=function(){this.registerContentClickHandler(this.handleContentClick),this.focusTrap=this.focusTrapFactory(this.root,{initialFocusEl:this.primaryActionEl})},p.prototype.destroy=function(){o.prototype.destroy.call(this),this.deregisterContentClickHandler(this.handleContentClick)},p.prototype.layout=function(){this.foundation.layout()},p.prototype.open=function(){this.foundation.open()},p.prototype.close=function(e){this.foundation.close(e)},p.prototype.getDefaultFoundation=function(){var r=this,e={addClass:function(e){r.root.classList.add(e)},getContentHeight:function(){return r.contentEl.offsetHeight},notifyClosed:function(e){r.emit(l.events.CLOSED,{reason:e})},notifyClosing:function(e){r.emit(l.events.CLOSING,{reason:e})},notifyOpened:function(){r.emit(l.events.OPENED,{})},notifyOpening:function(){r.emit(l.events.OPENING,{})},notifyActionClicked:function(e){r.emit(l.events.ACTION_CLICKED,{action:e})},releaseFocus:function(){r.focusTrap.releaseFocus()},removeClass:function(e){r.root.classList.remove(e)},setStyleProperty:function(e,t){r.root.style.setProperty(e,t)},trapFocus:function(){r.focusTrap.trapFocus()}};return new u.MDCBannerFoundation(e)},Object.defineProperty(p.prototype,"isOpen",{get:function(){return this.foundation.isOpen()},enumerable:!1,configurable:!0}),p.prototype.getText=function(){return this.textEl.textContent||""},p.prototype.setText=function(e){this.textEl.textContent=e},p.prototype.getPrimaryActionText=function(){return this.primaryActionEl.textContent||""},p.prototype.setPrimaryActionText=function(e){this.primaryActionEl.textContent=e},p.prototype.getSecondaryActionText=function(){return this.secondaryActionEl?this.secondaryActionEl.textContent||"":null},p.prototype.setSecondaryActionText=function(e){this.secondaryActionEl&&(this.secondaryActionEl.textContent=e)},p.prototype.registerContentClickHandler=function(e){this.contentEl.addEventListener("click",e)},p.prototype.deregisterContentClickHandler=function(e){this.contentEl.removeEventListener("click",e)},p);function p(){return null!==o&&o.apply(this,arguments)||this}t.MDCBanner=f}],i.c=n,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=186);function i(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}var r,n});