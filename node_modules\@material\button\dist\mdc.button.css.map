{"version": 3, "sources": ["webpack:///./packages/mdc-button/mdc-button.scss", "webpack:///mdc-button.scss", "webpack:///./packages/material-components-web/node_modules/@material/elevation/_elevation.scss", "webpack:///./packages/material-components-web/node_modules/@material/theme/_css.scss", "webpack:///./packages/material-components-web/node_modules/@material/theme/_gss.scss", "webpack:///./packages/mdc-button/_button-base.scss", "webpack:///./packages/material-components-web/node_modules/@material/elevation/_elevation-theme.scss", "webpack:///./packages/material-components-web/node_modules/@material/rtl/_rtl.scss", "webpack:///./packages/material-components-web/node_modules/@material/focus-ring/_focus-ring.scss", "webpack:///./packages/material-components-web/node_modules/@material/dom/_dom.scss", "webpack:///./packages/material-components-web/node_modules/@material/typography/_typography.scss", "webpack:///./packages/mdc-button/_button-text.scss", "webpack:///./packages/mdc-button/_button-shared-theme.scss", "webpack:///./packages/mdc-button/_button-filled.scss", "webpack:///./packages/mdc-button/_button-protected.scss", "webpack:///./packages/mdc-button/_button-outlined.scss", "webpack:///./packages/material-components-web/node_modules/@material/ripple/_ripple.scss", "webpack:///./packages/material-components-web/node_modules/@material/animation/_animation.scss", "webpack:///./packages/mdc-button/_button-ripple.scss", "webpack:///./packages/mdc-button/_button.scss", "webpack:///./packages/material-components-web/node_modules/@material/ripple/_ripple-theme.scss", "webpack:///./packages/mdc-button/_button-outlined-theme.scss"], "names": [], "mappings": ";;;;;;;AAwCE;EAOM;AC7CR;;AC0DE;EAGM;EACA;EACA;ECCF;ECZF;EDwBA;EDFI;ECVF;ECZF;EDwBA;AF/DJ;;AIkCE;ECiOE;EACA;EDpGA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;KAAA;MAAA;UAAA;EACA;EAGA;EACA;EACA;AJ7JJ;AKoQE;EHxNE;EAAA;EG8NI;EFjPJ;EAAc;EEmPV;ALpQR;AIwJE;EAEI;EACA;AJvJN;AI4JE;EAEI;AJ3JN;AI+JE;EAEI;AJ9JN;AIkKE;EAEI;EACA;AJjKN;AIqKE;EAEI;AJpKN;AIJI;EDIA;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EE8JF;EACA;EACA;AJxKF;AMUM;EACE;EHtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EIKI;ANHR;;AInBI;EAEI;EACA;EDLJ;EAAc;ECOV;UAAA;EACA;EDRJ;EAAc;ECUV;EACA;AJuBR;AInBI;EAKI;AJiBR;AIbI;EGrBA;EACA;EACA;EACA;EACA;EACA;EJRA;EAAc;EIUd;EJVA;EAAc;EIYd;UAAA;EAmFA,wBAd0B;EAe1B,uBAT4B;EH5DxB;AJ2BR;AQhBI;EJdA;IGTE;EP2CJ;AACF;AOzCI;EACE;EACA;EACA,kBAtDgB;EAuDhB;EACA;EACA;EJxBF;EAAc;EI0BZ;EJ1BF;EAAc;EI4BZ;UAAA;EACA,wBA9BgB;EA+BhB,uBA/BgB;AP4EtB;AQrCI;EDpBA;IAeI;EP8CN;AACF;AQ1CI;EJNE;IAGM;EJiDV;AACF;AI5CI;EL/BA;EACA;EACA,YAtCK;EAmDH;EACA;EACA;UAAA;ACkEN;;AI/CE;ED9CE;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AFmFJ;AMjFM;EACE;EHtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EIKI;ANwFR;;AI5DE;EA+IA;AJ/EF;;AI1DE;ELgBI,eAJwB;EAKxB,kBALwB;ACmD9B;;AInDE;EKwME;EACA;EPxQE;ECZF;EDwBA;EAZE;ECZF;EDwBA;UAAA;AFiHJ;;AU9IE;ECgeE;AX9UJ;;AY7IE;ERuNE;EOoQA;AXzUJ;AW6UI;EAII;AX9UR;AWkVI;EAII;AXnVR;;AaxJE;ETuNE;EOoQA;AX9TJ;AWkUI;EAII;AXnUR;AWuUI;EAII;AXxUR;;ActKE;EAUE;EAMA;Ad2JJ;AcxJE;EAEI;EACA;AdyJN;;Ae+EE;EACE;IACE,+DC5R2B;YD4R3B,uDC5R2B;IboC7B;IAAc;IY8PZ;YAAA;Ef/EJ;EekFE;IZjQA;IAAc;IYmQZ;YAAA;Ef/EJ;AACF;;AeiEE;EACE;IACE,+DC5R2B;YD4R3B,uDC5R2B;IboC7B;IAAc;IY8PZ;YAAA;Ef/EJ;EekFE;IZjQA;IAAc;IYmQZ;YAAA;Ef/EJ;AACF;AemFE;EACE;IACE;YAAA;IACA;EfjFJ;EeoFE;IACE;EflFJ;AACF;Ae0EE;EACE;IACE;YAAA;IACA;EfjFJ;EeoFE;IACE;EflFJ;AACF;AeqFE;EACE;IACE;YAAA;IACA;EfnFJ;EesFE;IACE;EfpFJ;AACF;Ae4EE;EACE;IACE;YAAA;IACA;EfnFJ;EesFE;IACE;EfpFJ;AACF;AiB/NE;EF+EE;EACA;EACA;EACA;EACA;EACA;EAEA;EAGE;AfgJN;Ae5IE;;EAGI;EACA;EACA;EACA;EACA;Af6IN;AezIE;EAGI;Eb5EA;ECZF;EDwBA;AF4MJ;Ae/HE;EbzFI;ECZF;EDwBA;AFiNJ;AexHI;EAEI;UAAA;AfyHR;AerHI;EAEI;EZpHJ;EAAc;EYsHV;EACA;UAAA;EACA;UAAA;AfuHR;AejHI;EAEI;EZhIJ;EAAc;EYkIV;AfmHR;Ae7GI;EAEI;UAAA;Af8GR;AetGI;EAEI;UAAA;EZpJJ;EAAc;EY0JV;UAAA;AfoGR;AetFE;;EAGI;EZ3KF;EAAc;EY6KZ;EACA;EACA;AfwFN;AenFI;EAEI;EACA;AfoFR;;AiB3RE;EAEI;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;AjB2RN;;AkB9RE;EhBeI;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AF2RJ;AWeE;ETtTI;ECZF;EDwBA;AFgSJ;AW6BE;ETzUI;ECZF;EDwBA;AFqSJ;AW6PI;ET9iBE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AFgTJ;AmByME;EjBrgBI;ECZF;EDwBA;AFqTJ;AmB0ME;EjB3gBI;ECZF;EDwBA;AF0TJ;AmB+LE;EjBrgBI;ECZF;EDwBA;AF+TJ;AmB0LE;EApRI;EjBjPA;ECZF;EDwBA;AFqUJ;AmB0LE;EA/PQ;AnBwEV;AmBuLE;EAxPU,yBA7SO;EjB0Bb;ECZF;EDwBA;AF8UJ;AmB5DI;EjBlRA;AFiVJ;AWsFE;ETnbI;ECZF;EDwBA;AFsVJ;AkB3WE;EhBSI;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AF6WJ;AWxLE;ETjMI;ECZF;EDwBA;AFkXJ;AWjKE;ET7NI;ECZF;EDwBA;AFuXJ;AW7EE;ETtTI;ECZF;EDwBA;AF4XJ;AW/DE;ETzUI;ECZF;EDwBA;AFiYJ;AWiKI;ET9iBE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AF4YJ;AmB6GE;EjBrgBI;ECZF;EDwBA;AFiZJ;AmB8GE;EjB3gBI;ECZF;EDwBA;AFsZJ;AmBmGE;EjBrgBI;ECZF;EDwBA;AF2ZJ;AmB8FE;EApRI;EjBjPA;ECZF;EDwBA;AFiaJ;AmB8FE;EA/PQ;AnBoKV;AmB2FE;EAxPU,yBA7SO;EjB0Bb;ECZF;EDwBA;AF0aJ;AmBxJI;EjBlRA;AF6aJ;AWNE;ETnbI;ECZF;EDwBA;AFkbJ;AkBjcE;EhBGI;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AF4cJ;AWvRE;ETjMI;ECZF;EDwBA;AFidJ;AWhQE;ET7NI;ECZF;EDwBA;AFsdJ;AW5KE;ETtTI;ECZF;EDwBA;AF2dJ;AW9JE;ETzUI;ECZF;EDwBA;AFgeJ;AWkEI;ET9iBE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AF2eJ;AmBcE;EjBrgBI;ECZF;EDwBA;AFgfJ;AmBeE;EjB3gBI;ECZF;EDwBA;AFqfJ;AmBIE;EjBrgBI;ECZF;EDwBA;AF0fJ;AmBDE;EApRI;EjBjPA;ECZF;EDwBA;AFggBJ;AmBDE;EA/PQ;AnBmQV;AmBJE;EAxPU,yBA7SO;EjB0Bb;ECZF;EDwBA;AFygBJ;AmBvPI;EjBlRA;AF4gBJ;AWrGE;ETnbI;ECZF;EDwBA;AFihBJ;AmB7FE;EjBhcI;ECZF;EDwBA;AFshBJ;AW2FM;ET7nBA;ECZF;EDwBA;AF2hBJ;AmBhGE;EjBvcI;ECZF;EDwBA;AFgiBJ;AWyGM;ETrpBA;ECZF;EDwBA;AFqiBJ;AkB9iBE;EhBHI;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;ESmcA;ET/cE;ECZF;EDwBA;AFgkBJ;AWtRE;ETtTI;ECZF;EDwBA;AFqkBJ;AWxQE;ETzUI;ECZF;EDwBA;AF0kBJ;AWxCI;ET9iBE;ECZF;EDwBA;EAZE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AFqlBJ;AmB5FE;EjBrgBI;ECZF;EDwBA;AF0lBJ;AmB3FE;EjB3gBI;ECZF;EDwBA;AF+lBJ;AmBtGE;EjBrgBI;ECZF;EDwBA;AFomBJ;AmB3GE;EApRI;EjBjPA;ECZF;EDwBA;AF0mBJ;AmB3GE;EA/PQ;AnB6WV;AmB9GE;EAxPU,yBA7SO;EjB0Bb;ECZF;EDwBA;AFmnBJ;AmBjWI;EjBlRA;AFsnBJ;AW/ME;ETnbI;ECZF;EDwBA;AF2nBJ;AoBljBE;ElBrFI;ECZF;EDwBA;AFgoBJ;AoBzhBE;ElBnHI;ECZF;EDwBA;AFqoBJ;AW9LI;EAII;AX6LR;AWzLI;EAII;AXwLR;AoB/eI;ElB5JA;EAAA;EAAA;EAAA;EAZE;ECZF;EDwBA;AFopBJ;AoB/eI;ElBjLE;ECZF;EDwBA;EAZE;ECZF;EDwBA;AF4pBJ;;AkBlnBI;;;Ef7DA;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AFuqBJ;AMrqBM;EACE;EHtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EIKI;AN4qBR;;AkBhoBI;;;EfpEA;EAAc;EDmBd;ECnBA;EAAc;EDmBd;AF4rBJ;AM1rBM;EACE;EHtBJ;EAAc;EDmBd;ECnBA;EAAc;EDmBd;EIKI;ANisBR,C", "file": "mdc.button.css", "sourcesContent": ["//\n// Copyright 2019 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:math';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/theme/theme';\n@use '@material/theme/keys';\n@use '@material/theme/custom-properties';\n\n$height: 48px !default;\n$width: $height !default;\n\n/// Styles applied to the component's touch target wrapper element.\n@mixin wrapper($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-touch-target-wrapper {\n    @include feature-targeting.targets($feat-structure) {\n      // Ensure that styles are only emitted once across all components that\n      // have increased touch targets.\n      @include base-mixins.emit-once('mdc-touch-target/wrapper') {\n        // NOTE: Will change to `inline-block` in the future, but keeping as is\n        // temporarily for backwards-compatibility.\n        display: inline;\n      }\n    }\n  }\n}\n\n/// Styles applied to the component's inner touch target element.\n/// By default, only sets the inner element height to the minimum touch target\n/// height ($mdc-touch-target-height).\n/// @param {Boolean} $set-width [false] - Sets the inner element width to the\n///     minimum touch target width ($mdc-touch-target-width).\n/// @param $height [$mdc-touch-target-height] - Touch target height.\n/// @param $width [$mdc-touch-target-width] - Touch target width.\n@mixin touch-target(\n  $set-width: false,\n  $query: feature-targeting.all(),\n  $height: $height,\n  $width: $width\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    position: absolute;\n    top: 50%;\n    height: $height;\n  }\n\n  @if $set-width {\n    @include feature-targeting.targets($feat-structure) {\n      @include rtl.ignore-next-line();\n      left: 50%;\n      width: $width;\n      @include rtl.ignore-next-line();\n      transform: translate(-50%, -50%);\n    }\n  } @else {\n    @include feature-targeting.targets($feat-structure) {\n      left: 0;\n      right: 0;\n      transform: translateY(-50%);\n    }\n  }\n}\n\n/// Applies margin to the component with the increased touch target,\n/// to compensate for the touch target.\n@mixin margin(\n  $component-height,\n  $component-width: null,\n  $touch-target-height: $height,\n  $touch-target-width: $width,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    @if keys.is-key($touch-target-height) or\n      keys.is-key($component-height) or\n      custom-properties.is-custom-prop($touch-target-height) or\n      custom-properties.is-custom-prop($component-height) or\n      custom-properties.is-custom-prop-string($touch-target-height) or\n      custom-properties.is-custom-prop-string($component-height)\n    {\n      // Custom properties\n      @include theme.property(\n        margin-top,\n        'max((touch-target-height - component-height) / 2, 0px)',\n        $replace: (\n          component-height: $component-height,\n          touch-target-height: $touch-target-height\n        )\n      );\n      @include theme.property(\n        margin-bottom,\n        'max((touch-target-height - component-height) / 2, 0px)',\n        $replace: (\n          component-height: $component-height,\n          touch-target-height: $touch-target-height\n        )\n      );\n    } @else {\n      // Static values\n      $vertical-margin-value: math.div(\n        $touch-target-height - $component-height,\n        2\n      );\n      margin-top: $vertical-margin-value;\n      margin-bottom: $vertical-margin-value;\n    }\n  }\n\n  @if $component-width {\n    @include feature-targeting.targets($feat-structure) {\n      @if keys.is-key($touch-target-width) or\n        keys.is-key($component-width) or\n        custom-properties.is-custom-prop($touch-target-width) or\n        custom-properties.is-custom-prop($component-width) or\n        custom-properties.is-custom-prop-string($touch-target-width) or\n        custom-properties.is-custom-prop-string($component-width)\n      {\n        // Custom properties\n        @include theme.property(\n          margin-right,\n          'max((touch-target-width - component-width) / 2, 0px)',\n          $replace: (\n            component-width: $component-width,\n            touch-target-width: $touch-target-width\n          )\n        );\n        @include theme.property(\n          margin-left,\n          'max((touch-target-width - component-width) / 2), 0px',\n          $replace: (\n            component-width: $component-width,\n            touch-target-width: $touch-target-width\n          )\n        );\n      } @else {\n        // Static values\n        $horizontal-margin-value: math.div(\n          $touch-target-width - $component-width,\n          2\n        );\n        margin-right: $horizontal-margin-value;\n        margin-left: $horizontal-margin-value;\n      }\n    }\n  }\n}\n", ".mdc-touch-target-wrapper {\n  display: inline;\n}\n\n.mdc-elevation-overlay {\n  position: absolute;\n  border-radius: inherit;\n  pointer-events: none;\n  opacity: 0;\n  /* @alternate */\n  opacity: var(--mdc-elevation-overlay-opacity, 0);\n  transition: opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-elevation-overlay-color, #fff);\n}\n\n.mdc-button {\n  /* @alternate */\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  box-sizing: border-box;\n  min-width: 64px;\n  border: none;\n  outline: none;\n  /* @alternate */\n  line-height: inherit;\n  user-select: none;\n  -webkit-appearance: none;\n  overflow: visible;\n  vertical-align: middle;\n  background: transparent;\n}\n.mdc-button .mdc-elevation-overlay {\n  width: 100%;\n  height: 100%;\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n}\n.mdc-button::-moz-focus-inner {\n  padding: 0;\n  border: 0;\n}\n.mdc-button:active {\n  outline: none;\n}\n.mdc-button:hover {\n  cursor: pointer;\n}\n.mdc-button:disabled {\n  cursor: default;\n  pointer-events: none;\n}\n.mdc-button[hidden] {\n  display: none;\n}\n.mdc-button .mdc-button__icon {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 8px;\n  display: inline-block;\n  position: relative;\n  vertical-align: top;\n}\n[dir=rtl] .mdc-button .mdc-button__icon, .mdc-button .mdc-button__icon[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 8px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 0;\n  /*rtl:end:ignore*/\n}\n\n.mdc-button .mdc-button__progress-indicator {\n  font-size: 0;\n  position: absolute;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(-50%, -50%);\n  top: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 50%;\n  line-height: initial;\n}\n.mdc-button .mdc-button__label {\n  position: relative;\n}\n.mdc-button .mdc-button__focus-ring {\n  pointer-events: none;\n  border: 2px solid transparent;\n  border-radius: 6px;\n  box-sizing: content-box;\n  position: absolute;\n  top: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(-50%, -50%);\n  height: calc(100% + 4px);\n  width: calc(100% + 4px);\n  display: none;\n}\n@media screen and (forced-colors: active) {\n  .mdc-button .mdc-button__focus-ring {\n    border-color: CanvasText;\n  }\n}\n.mdc-button .mdc-button__focus-ring::after {\n  content: \"\";\n  border: 2px solid transparent;\n  border-radius: 8px;\n  display: block;\n  position: absolute;\n  top: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 50%;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(-50%, -50%);\n  height: calc(100% + 4px);\n  width: calc(100% + 4px);\n}\n@media screen and (forced-colors: active) {\n  .mdc-button .mdc-button__focus-ring::after {\n    border-color: CanvasText;\n  }\n}\n@media screen and (forced-colors: active) {\n  .mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring, .mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring {\n    display: block;\n  }\n}\n.mdc-button .mdc-button__touch {\n  position: absolute;\n  top: 50%;\n  height: 48px;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n}\n\n.mdc-button__label + .mdc-button__icon {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 8px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 0;\n}\n[dir=rtl] .mdc-button__label + .mdc-button__icon, .mdc-button__label + .mdc-button__icon[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 8px;\n  /*rtl:end:ignore*/\n}\n\nsvg.mdc-button__icon {\n  fill: currentColor;\n}\n\n.mdc-button--touch {\n  margin-top: 6px;\n  margin-bottom: 6px;\n}\n\n.mdc-button {\n  -moz-osx-font-smoothing: grayscale;\n  -webkit-font-smoothing: antialiased;\n  font-family: Roboto, sans-serif;\n  /* @alternate */\n  font-family: var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif));\n  text-decoration: none;\n  /* @alternate */\n  text-decoration: var(--mdc-typography-button-text-decoration, none);\n}\n\n.mdc-button {\n  padding: 0 8px 0 8px;\n}\n\n.mdc-button--unelevated {\n  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);\n  padding: 0 16px 0 16px;\n}\n.mdc-button--unelevated.mdc-button--icon-trailing {\n  padding: 0 12px 0 16px;\n}\n.mdc-button--unelevated.mdc-button--icon-leading {\n  padding: 0 16px 0 12px;\n}\n\n.mdc-button--raised {\n  transition: box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);\n  padding: 0 16px 0 16px;\n}\n.mdc-button--raised.mdc-button--icon-trailing {\n  padding: 0 12px 0 16px;\n}\n.mdc-button--raised.mdc-button--icon-leading {\n  padding: 0 16px 0 12px;\n}\n\n.mdc-button--outlined {\n  border-style: solid;\n  transition: border 280ms cubic-bezier(0.4, 0, 0.2, 1);\n}\n.mdc-button--outlined .mdc-button__ripple {\n  border-style: solid;\n  border-color: transparent;\n}\n\n@keyframes mdc-ripple-fg-radius-in {\n  from {\n    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    /* @noflip */\n    /*rtl:ignore*/\n    transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);\n  }\n  to {\n    /* @noflip */\n    /*rtl:ignore*/\n    transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));\n  }\n}\n@keyframes mdc-ripple-fg-opacity-in {\n  from {\n    animation-timing-function: linear;\n    opacity: 0;\n  }\n  to {\n    opacity: var(--mdc-ripple-fg-opacity, 0);\n  }\n}\n@keyframes mdc-ripple-fg-opacity-out {\n  from {\n    animation-timing-function: linear;\n    opacity: var(--mdc-ripple-fg-opacity, 0);\n  }\n  to {\n    opacity: 0;\n  }\n}\n.mdc-button {\n  --mdc-ripple-fg-size: 0;\n  --mdc-ripple-left: 0;\n  --mdc-ripple-top: 0;\n  --mdc-ripple-fg-scale: 1;\n  --mdc-ripple-fg-translate-end: 0;\n  --mdc-ripple-fg-translate-start: 0;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n  will-change: transform, opacity;\n}\n.mdc-button .mdc-button__ripple::before,\n.mdc-button .mdc-button__ripple::after {\n  position: absolute;\n  border-radius: 50%;\n  opacity: 0;\n  pointer-events: none;\n  content: \"\";\n}\n.mdc-button .mdc-button__ripple::before {\n  transition: opacity 15ms linear, background-color 15ms linear;\n  z-index: 1;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, 1);\n}\n.mdc-button .mdc-button__ripple::after {\n  z-index: 0;\n  /* @alternate */\n  z-index: var(--mdc-ripple-z-index, 0);\n}\n.mdc-button.mdc-ripple-upgraded .mdc-button__ripple::before {\n  transform: scale(var(--mdc-ripple-fg-scale, 1));\n}\n.mdc-button.mdc-ripple-upgraded .mdc-button__ripple::after {\n  top: 0;\n  /* @noflip */\n  /*rtl:ignore*/\n  left: 0;\n  transform: scale(0);\n  transform-origin: center center;\n}\n.mdc-button.mdc-ripple-upgraded--unbounded .mdc-button__ripple::after {\n  top: var(--mdc-ripple-top, 0);\n  /* @noflip */\n  /*rtl:ignore*/\n  left: var(--mdc-ripple-left, 0);\n}\n.mdc-button.mdc-ripple-upgraded--foreground-activation .mdc-button__ripple::after {\n  animation: mdc-ripple-fg-radius-in 225ms forwards, mdc-ripple-fg-opacity-in 75ms forwards;\n}\n.mdc-button.mdc-ripple-upgraded--foreground-deactivation .mdc-button__ripple::after {\n  animation: mdc-ripple-fg-opacity-out 150ms;\n  /* @noflip */\n  /*rtl:ignore*/\n  transform: translate(var(--mdc-ripple-fg-translate-end, 0)) scale(var(--mdc-ripple-fg-scale, 1));\n}\n.mdc-button .mdc-button__ripple::before,\n.mdc-button .mdc-button__ripple::after {\n  top: calc(50% - 100%);\n  /* @noflip */\n  /*rtl:ignore*/\n  left: calc(50% - 100%);\n  width: 200%;\n  height: 200%;\n}\n.mdc-button.mdc-ripple-upgraded .mdc-button__ripple::after {\n  width: var(--mdc-ripple-fg-size, 100%);\n  height: var(--mdc-ripple-fg-size, 100%);\n}\n\n.mdc-button__ripple {\n  position: absolute;\n  box-sizing: content-box;\n  overflow: hidden;\n  z-index: 0;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n}\n\n.mdc-button {\n  font-family: Roboto, sans-serif;\n  /* @alternate */\n  font-family: var(--mdc-text-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));\n  font-size: 0.875rem;\n  /* @alternate */\n  font-size: var(--mdc-text-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));\n  letter-spacing: 0.0892857143em;\n  /* @alternate */\n  letter-spacing: var(--mdc-text-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));\n  font-weight: 500;\n  /* @alternate */\n  font-weight: var(--mdc-text-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));\n  text-transform: uppercase;\n  /* @alternate */\n  text-transform: var(--mdc-text-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));\n  height: 36px;\n  /* @alternate */\n  height: var(--mdc-text-button-container-height, 36px);\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px));\n}\n.mdc-button:not(:disabled) {\n  color: #6200ee;\n  /* @alternate */\n  color: var(--mdc-text-button-label-text-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button:disabled {\n  color: rgba(0, 0, 0, 0.38);\n  /* @alternate */\n  color: var(--mdc-text-button-disabled-label-text-color, rgba(0, 0, 0, 0.38));\n}\n.mdc-button .mdc-button__icon {\n  font-size: 1.125rem;\n  /* @alternate */\n  font-size: var(--mdc-text-button-with-icon-icon-size, 1.125rem);\n  width: 1.125rem;\n  /* @alternate */\n  width: var(--mdc-text-button-with-icon-icon-size, 1.125rem);\n  height: 1.125rem;\n  /* @alternate */\n  height: var(--mdc-text-button-with-icon-icon-size, 1.125rem);\n}\n.mdc-button .mdc-button__ripple::before {\n  background-color: #6200ee;\n  /* @alternate */\n  background-color: var(--mdc-text-button-hover-state-layer-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button .mdc-button__ripple::after {\n  background-color: #6200ee;\n  /* @alternate */\n  background-color: var(--mdc-text-button-pressed-state-layer-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button:hover .mdc-button__ripple::before, .mdc-button.mdc-ripple-surface--hover .mdc-button__ripple::before {\n  opacity: 0.04;\n  /* @alternate */\n  opacity: var(--mdc-text-button-hover-state-layer-opacity, 0.04);\n}\n.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before, .mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before {\n  transition-duration: 75ms;\n  opacity: 0.12;\n  /* @alternate */\n  opacity: var(--mdc-text-button-focus-state-layer-opacity, 0.12);\n}\n.mdc-button:not(.mdc-ripple-upgraded) .mdc-button__ripple::after {\n  transition: opacity 150ms linear;\n}\n.mdc-button:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after {\n  transition-duration: 75ms;\n  opacity: 0.12;\n  /* @alternate */\n  opacity: var(--mdc-text-button-pressed-state-layer-opacity, 0.12);\n}\n.mdc-button.mdc-ripple-upgraded {\n  --mdc-ripple-fg-opacity: var(--mdc-text-button-pressed-state-layer-opacity, 0.12);\n}\n.mdc-button .mdc-button__ripple {\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-text-button-container-shape, var(--mdc-shape-small, 4px));\n}\n.mdc-button--unelevated {\n  font-family: Roboto, sans-serif;\n  /* @alternate */\n  font-family: var(--mdc-filled-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));\n  font-size: 0.875rem;\n  /* @alternate */\n  font-size: var(--mdc-filled-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));\n  letter-spacing: 0.0892857143em;\n  /* @alternate */\n  letter-spacing: var(--mdc-filled-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));\n  font-weight: 500;\n  /* @alternate */\n  font-weight: var(--mdc-filled-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));\n  text-transform: uppercase;\n  /* @alternate */\n  text-transform: var(--mdc-filled-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));\n  height: 36px;\n  /* @alternate */\n  height: var(--mdc-filled-button-container-height, 36px);\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px));\n}\n.mdc-button--unelevated:not(:disabled) {\n  background-color: #6200ee;\n  /* @alternate */\n  background-color: var(--mdc-filled-button-container-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button--unelevated:disabled {\n  background-color: rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  background-color: var(--mdc-filled-button-disabled-container-color, rgba(0, 0, 0, 0.12));\n}\n.mdc-button--unelevated:not(:disabled) {\n  color: #fff;\n  /* @alternate */\n  color: var(--mdc-filled-button-label-text-color, var(--mdc-theme-on-primary, #fff));\n}\n.mdc-button--unelevated:disabled {\n  color: rgba(0, 0, 0, 0.38);\n  /* @alternate */\n  color: var(--mdc-filled-button-disabled-label-text-color, rgba(0, 0, 0, 0.38));\n}\n.mdc-button--unelevated .mdc-button__icon {\n  font-size: 1.125rem;\n  /* @alternate */\n  font-size: var(--mdc-filled-button-with-icon-icon-size, 1.125rem);\n  width: 1.125rem;\n  /* @alternate */\n  width: var(--mdc-filled-button-with-icon-icon-size, 1.125rem);\n  height: 1.125rem;\n  /* @alternate */\n  height: var(--mdc-filled-button-with-icon-icon-size, 1.125rem);\n}\n.mdc-button--unelevated .mdc-button__ripple::before {\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-filled-button-hover-state-layer-color, var(--mdc-theme-on-primary, #fff));\n}\n.mdc-button--unelevated .mdc-button__ripple::after {\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-filled-button-pressed-state-layer-color, var(--mdc-theme-on-primary, #fff));\n}\n.mdc-button--unelevated:hover .mdc-button__ripple::before, .mdc-button--unelevated.mdc-ripple-surface--hover .mdc-button__ripple::before {\n  opacity: 0.08;\n  /* @alternate */\n  opacity: var(--mdc-filled-button-hover-state-layer-opacity, 0.08);\n}\n.mdc-button--unelevated.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before, .mdc-button--unelevated:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before {\n  transition-duration: 75ms;\n  opacity: 0.24;\n  /* @alternate */\n  opacity: var(--mdc-filled-button-focus-state-layer-opacity, 0.24);\n}\n.mdc-button--unelevated:not(.mdc-ripple-upgraded) .mdc-button__ripple::after {\n  transition: opacity 150ms linear;\n}\n.mdc-button--unelevated:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after {\n  transition-duration: 75ms;\n  opacity: 0.24;\n  /* @alternate */\n  opacity: var(--mdc-filled-button-pressed-state-layer-opacity, 0.24);\n}\n.mdc-button--unelevated.mdc-ripple-upgraded {\n  --mdc-ripple-fg-opacity: var(--mdc-filled-button-pressed-state-layer-opacity, 0.24);\n}\n.mdc-button--unelevated .mdc-button__ripple {\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-filled-button-container-shape, var(--mdc-shape-small, 4px));\n}\n.mdc-button--raised {\n  font-family: Roboto, sans-serif;\n  /* @alternate */\n  font-family: var(--mdc-protected-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));\n  font-size: 0.875rem;\n  /* @alternate */\n  font-size: var(--mdc-protected-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));\n  letter-spacing: 0.0892857143em;\n  /* @alternate */\n  letter-spacing: var(--mdc-protected-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));\n  font-weight: 500;\n  /* @alternate */\n  font-weight: var(--mdc-protected-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));\n  text-transform: uppercase;\n  /* @alternate */\n  text-transform: var(--mdc-protected-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));\n  height: 36px;\n  /* @alternate */\n  height: var(--mdc-protected-button-container-height, 36px);\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px));\n  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  box-shadow: var(--mdc-protected-button-container-elevation, 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12));\n}\n.mdc-button--raised:not(:disabled) {\n  background-color: #6200ee;\n  /* @alternate */\n  background-color: var(--mdc-protected-button-container-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button--raised:disabled {\n  background-color: rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  background-color: var(--mdc-protected-button-disabled-container-color, rgba(0, 0, 0, 0.12));\n}\n.mdc-button--raised:not(:disabled) {\n  color: #fff;\n  /* @alternate */\n  color: var(--mdc-protected-button-label-text-color, var(--mdc-theme-on-primary, #fff));\n}\n.mdc-button--raised:disabled {\n  color: rgba(0, 0, 0, 0.38);\n  /* @alternate */\n  color: var(--mdc-protected-button-disabled-label-text-color, rgba(0, 0, 0, 0.38));\n}\n.mdc-button--raised .mdc-button__icon {\n  font-size: 1.125rem;\n  /* @alternate */\n  font-size: var(--mdc-protected-button-with-icon-icon-size, 1.125rem);\n  width: 1.125rem;\n  /* @alternate */\n  width: var(--mdc-protected-button-with-icon-icon-size, 1.125rem);\n  height: 1.125rem;\n  /* @alternate */\n  height: var(--mdc-protected-button-with-icon-icon-size, 1.125rem);\n}\n.mdc-button--raised .mdc-button__ripple::before {\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-protected-button-hover-state-layer-color, var(--mdc-theme-on-primary, #fff));\n}\n.mdc-button--raised .mdc-button__ripple::after {\n  background-color: #fff;\n  /* @alternate */\n  background-color: var(--mdc-protected-button-pressed-state-layer-color, var(--mdc-theme-on-primary, #fff));\n}\n.mdc-button--raised:hover .mdc-button__ripple::before, .mdc-button--raised.mdc-ripple-surface--hover .mdc-button__ripple::before {\n  opacity: 0.08;\n  /* @alternate */\n  opacity: var(--mdc-protected-button-hover-state-layer-opacity, 0.08);\n}\n.mdc-button--raised.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before, .mdc-button--raised:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before {\n  transition-duration: 75ms;\n  opacity: 0.24;\n  /* @alternate */\n  opacity: var(--mdc-protected-button-focus-state-layer-opacity, 0.24);\n}\n.mdc-button--raised:not(.mdc-ripple-upgraded) .mdc-button__ripple::after {\n  transition: opacity 150ms linear;\n}\n.mdc-button--raised:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after {\n  transition-duration: 75ms;\n  opacity: 0.24;\n  /* @alternate */\n  opacity: var(--mdc-protected-button-pressed-state-layer-opacity, 0.24);\n}\n.mdc-button--raised.mdc-ripple-upgraded {\n  --mdc-ripple-fg-opacity: var(--mdc-protected-button-pressed-state-layer-opacity, 0.24);\n}\n.mdc-button--raised .mdc-button__ripple {\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-protected-button-container-shape, var(--mdc-shape-small, 4px));\n}\n.mdc-button--raised.mdc-ripple-upgraded--background-focused, .mdc-button--raised:not(.mdc-ripple-upgraded):focus {\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  box-shadow: var(--mdc-protected-button-focus-container-elevation, 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12));\n}\n.mdc-button--raised:hover {\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  box-shadow: var(--mdc-protected-button-hover-container-elevation, 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12));\n}\n.mdc-button--raised:not(:disabled):active {\n  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  box-shadow: var(--mdc-protected-button-pressed-container-elevation, 0px 5px 5px -3px rgba(0, 0, 0, 0.2), 0px 8px 10px 1px rgba(0, 0, 0, 0.14), 0px 3px 14px 2px rgba(0, 0, 0, 0.12));\n}\n.mdc-button--raised:disabled {\n  box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  box-shadow: var(--mdc-protected-button-disabled-container-elevation, 0px 0px 0px 0px rgba(0, 0, 0, 0.2), 0px 0px 0px 0px rgba(0, 0, 0, 0.14), 0px 0px 0px 0px rgba(0, 0, 0, 0.12));\n}\n.mdc-button--outlined {\n  font-family: Roboto, sans-serif;\n  /* @alternate */\n  font-family: var(--mdc-outlined-button-label-text-font, var(--mdc-typography-button-font-family, var(--mdc-typography-font-family, Roboto, sans-serif)));\n  font-size: 0.875rem;\n  /* @alternate */\n  font-size: var(--mdc-outlined-button-label-text-size, var(--mdc-typography-button-font-size, 0.875rem));\n  letter-spacing: 0.0892857143em;\n  /* @alternate */\n  letter-spacing: var(--mdc-outlined-button-label-text-tracking, var(--mdc-typography-button-letter-spacing, 0.0892857143em));\n  font-weight: 500;\n  /* @alternate */\n  font-weight: var(--mdc-outlined-button-label-text-weight, var(--mdc-typography-button-font-weight, 500));\n  text-transform: uppercase;\n  /* @alternate */\n  text-transform: var(--mdc-outlined-button-label-text-transform, var(--mdc-typography-button-text-transform, uppercase));\n  height: 36px;\n  /* @alternate */\n  height: var(--mdc-outlined-button-container-height, 36px);\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px));\n  padding: 0 15px 0 15px;\n  border-width: 1px;\n  /* @alternate */\n  border-width: var(--mdc-outlined-button-outline-width, 1px);\n}\n.mdc-button--outlined:not(:disabled) {\n  color: #6200ee;\n  /* @alternate */\n  color: var(--mdc-outlined-button-label-text-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button--outlined:disabled {\n  color: rgba(0, 0, 0, 0.38);\n  /* @alternate */\n  color: var(--mdc-outlined-button-disabled-label-text-color, rgba(0, 0, 0, 0.38));\n}\n.mdc-button--outlined .mdc-button__icon {\n  font-size: 1.125rem;\n  /* @alternate */\n  font-size: var(--mdc-outlined-button-with-icon-icon-size, 1.125rem);\n  width: 1.125rem;\n  /* @alternate */\n  width: var(--mdc-outlined-button-with-icon-icon-size, 1.125rem);\n  height: 1.125rem;\n  /* @alternate */\n  height: var(--mdc-outlined-button-with-icon-icon-size, 1.125rem);\n}\n.mdc-button--outlined .mdc-button__ripple::before {\n  background-color: #6200ee;\n  /* @alternate */\n  background-color: var(--mdc-outlined-button-hover-state-layer-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button--outlined .mdc-button__ripple::after {\n  background-color: #6200ee;\n  /* @alternate */\n  background-color: var(--mdc-outlined-button-pressed-state-layer-color, var(--mdc-theme-primary, #6200ee));\n}\n.mdc-button--outlined:hover .mdc-button__ripple::before, .mdc-button--outlined.mdc-ripple-surface--hover .mdc-button__ripple::before {\n  opacity: 0.04;\n  /* @alternate */\n  opacity: var(--mdc-outlined-button-hover-state-layer-opacity, 0.04);\n}\n.mdc-button--outlined.mdc-ripple-upgraded--background-focused .mdc-button__ripple::before, .mdc-button--outlined:not(.mdc-ripple-upgraded):focus .mdc-button__ripple::before {\n  transition-duration: 75ms;\n  opacity: 0.12;\n  /* @alternate */\n  opacity: var(--mdc-outlined-button-focus-state-layer-opacity, 0.12);\n}\n.mdc-button--outlined:not(.mdc-ripple-upgraded) .mdc-button__ripple::after {\n  transition: opacity 150ms linear;\n}\n.mdc-button--outlined:not(.mdc-ripple-upgraded):active .mdc-button__ripple::after {\n  transition-duration: 75ms;\n  opacity: 0.12;\n  /* @alternate */\n  opacity: var(--mdc-outlined-button-pressed-state-layer-opacity, 0.12);\n}\n.mdc-button--outlined.mdc-ripple-upgraded {\n  --mdc-ripple-fg-opacity: var(--mdc-outlined-button-pressed-state-layer-opacity, 0.12);\n}\n.mdc-button--outlined .mdc-button__ripple {\n  border-radius: 4px;\n  /* @alternate */\n  border-radius: var(--mdc-outlined-button-container-shape, var(--mdc-shape-small, 4px));\n}\n.mdc-button--outlined:not(:disabled) {\n  border-color: rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  border-color: var(--mdc-outlined-button-outline-color, rgba(0, 0, 0, 0.12));\n}\n.mdc-button--outlined:disabled {\n  border-color: rgba(0, 0, 0, 0.12);\n  /* @alternate */\n  border-color: var(--mdc-outlined-button-disabled-outline-color, rgba(0, 0, 0, 0.12));\n}\n.mdc-button--outlined.mdc-button--icon-trailing {\n  padding: 0 11px 0 15px;\n}\n.mdc-button--outlined.mdc-button--icon-leading {\n  padding: 0 15px 0 11px;\n}\n.mdc-button--outlined .mdc-button__ripple {\n  top: -1px;\n  left: -1px;\n  bottom: -1px;\n  right: -1px;\n  border-width: 1px;\n  /* @alternate */\n  border-width: var(--mdc-outlined-button-outline-width, 1px);\n}\n.mdc-button--outlined .mdc-button__touch {\n  left: calc(-1 * 1px);\n  /* @alternate */\n  left: calc(-1 * var(--mdc-outlined-button-outline-width, 1px));\n  width: calc(100% + 2 * 1px);\n  /* @alternate */\n  width: calc(100% + 2 * var(--mdc-outlined-button-outline-width, 1px));\n}\n\n.mdc-button--raised .mdc-button__icon,\n.mdc-button--unelevated .mdc-button__icon,\n.mdc-button--outlined .mdc-button__icon {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: -4px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 8px;\n}\n[dir=rtl] .mdc-button--raised .mdc-button__icon, [dir=rtl] .mdc-button--unelevated .mdc-button__icon, [dir=rtl] .mdc-button--outlined .mdc-button__icon, .mdc-button--raised .mdc-button__icon[dir=rtl], .mdc-button--unelevated .mdc-button__icon[dir=rtl], .mdc-button--outlined .mdc-button__icon[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 8px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: -4px;\n  /*rtl:end:ignore*/\n}\n\n.mdc-button--raised .mdc-button__label + .mdc-button__icon,\n.mdc-button--unelevated .mdc-button__label + .mdc-button__icon,\n.mdc-button--outlined .mdc-button__label + .mdc-button__icon {\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: 8px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: -4px;\n}\n[dir=rtl] .mdc-button--raised .mdc-button__label + .mdc-button__icon, [dir=rtl] .mdc-button--unelevated .mdc-button__label + .mdc-button__icon, [dir=rtl] .mdc-button--outlined .mdc-button__label + .mdc-button__icon, .mdc-button--raised .mdc-button__label + .mdc-button__icon[dir=rtl], .mdc-button--unelevated .mdc-button__label + .mdc-button__icon[dir=rtl], .mdc-button--outlined .mdc-button__label + .mdc-button__icon[dir=rtl] {\n  /*rtl:begin:ignore*/\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-left: -4px;\n  /* @noflip */\n  /*rtl:ignore*/\n  margin-right: 8px;\n  /*rtl:end:ignore*/\n}", "//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/theme/custom-properties';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/theme';\n@use '@material/theme/theme-color';\n@use './elevation-theme';\n\n@mixin core-styles($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @for $z-value from 0 through 24 {\n    .mdc-elevation--z#{$z-value} {\n      @include elevation-theme.elevation($z-value, $query: $query);\n    }\n  }\n\n  .mdc-elevation-transition {\n    @include feature-targeting.targets($feat-animation) {\n      transition: elevation-theme.transition-value();\n    }\n\n    @include feature-targeting.targets($feat-structure) {\n      will-change: elevation-theme.$property;\n    }\n  }\n}\n\n///\n/// Called once per application to set up the global default elevation styles.\n///\n@mixin overlay-common($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-structure) {\n      @include base-mixins.emit-once('mdc-elevation/common/structure') {\n        position: absolute;\n        border-radius: inherit;\n        pointer-events: none;\n\n        @include theme.property(\n          opacity,\n          custom-properties.create(--mdc-elevation-overlay-opacity, 0)\n        );\n      }\n    }\n\n    @include feature-targeting.targets($feat-animation) {\n      @include base-mixins.emit-once('mdc-elevation/common/animation') {\n        transition: elevation-theme.overlay-transition-value();\n      }\n    }\n\n    @include base-mixins.emit-once('mdc-elevation/common/color') {\n      $fill-color: custom-properties.create(\n        --mdc-elevation-overlay-color,\n        elevation-theme.$overlay-color\n      );\n      @include elevation-theme.overlay-fill-color($fill-color, $query: $query);\n    }\n  }\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use './gss';\n\n/// When true, add an additional property/value declaration before declarations\n/// that use advanced features such as custom properties or CSS functions. This\n/// adds fallback support for older browsers such as IE11 that do not support\n/// these features at the cost of additional CSS. Set this variable to false to\n/// disable generating fallback declarations.\n$enable-fallback-declarations: true !default;\n\n/// Writes a CSS property/value declaration. This mixin is used throughout the\n/// theme package for consistency for dynamically setting CSS property values.\n///\n/// This mixin may optionally take a fallback value. For advanced features such\n/// as custom properties or CSS functions like min and max, a fallback value is\n/// recommended to support older browsers.\n///\n/// @param {String} $property - The CSS property of the declaration.\n/// @param {*} $value - The value of the CSS declaration. The value should be\n///     resolved by other theme functions first (i.e. custom property Maps and\n///     Material theme keys are not supported in this mixin). If the value is\n///     null, no declarations will be emitted.\n/// @param {*} $fallback - An optional fallback value for older browsers. If\n///     provided, a second property/value declaration will be added before the\n///     main property/value declaration.\n/// @param {Map} $gss - An optional Map of GSS annotations to add.\n/// @param {Bool} $important - If true, add `!important` to the declaration.\n@mixin declaration(\n  $property,\n  $value,\n  $fallback-value: null,\n  $gss: (),\n  $important: false\n) {\n  // Normally setting a null value to a property will not emit CSS, so mixins\n  // wouldn't need to check this. However, Sass will throw an error if the\n  // interpolated property is a custom property.\n  @if $value != null {\n    $important-rule: if($important, ' !important', '');\n\n    @if $fallback-value and $enable-fallback-declarations {\n      @include gss.annotate($gss);\n      #{$property}: #{$fallback-value} #{$important-rule};\n\n      // Add @alternate to annotations.\n      $gss: map.merge(\n        $gss,\n        (\n          alternate: true,\n        )\n      );\n    }\n\n    @include gss.annotate($gss);\n    #{$property}: #{$value}#{$important-rule};\n  }\n}\n\n/// Unpacks shorthand values for CSS properties (i.e. lists of 1-3 values).\n/// If a list of 4 values is given, it is returned as-is.\n///\n/// Examples:\n///\n/// unpack-value(4px) => 4px 4px 4px 4px\n/// unpack-value(4px 2px) => 4px 2px 4px 2px\n/// unpack-value(4px 2px 2px) => 4px 2px 2px 2px\n/// unpack-value(4px 2px 0 2px) => 4px 2px 0 2px\n///\n/// @param {Number | Map | List} $value - List of 1 to 4 value numbers.\n/// @return {List} a List of 4 value numbers.\n@function unpack-value($value) {\n  @if meta.type-of($value) == 'map' or list.length($value) == 1 {\n    @return $value $value $value $value;\n  } @else if list.length($value) == 4 {\n    @return $value;\n  } @else if list.length($value) == 3 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 3)\n      list.nth($value, 2);\n  } @else if list.length($value) == 2 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 1)\n      list.nth($value, 2);\n  }\n\n  @error \"Invalid CSS property value: '#{$value}' is more than 4 values\";\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n\n/// Adds optional GSS annotation comments. Useful for theme mixins where one or\n/// more properties are set indirectly.\n///\n/// Annotations may be provided as a Map of annotations or as named arguments.\n///\n/// @example - scss\n///   @include annotate((noflip: true));\n///   left: 0;\n///\n/// @example - scss\n///   @include annotate($noflip: true);\n///   left: 0;\n///\n/// @example - css\n///   /* @noflip */ /*rtl:ignore*/\n///   left: 0;\n///\n/// @param {Map} $annotations - Map of annotations. Values must be set to `true`\n///     for an annotation to be added.\n@mixin annotate($annotations...) {\n  $keywords: meta.keywords($annotations);\n  @if list.length($annotations) > 0 {\n    $annotations: list.nth($annotations, 1);\n  } @else {\n    $annotations: $keywords;\n  }\n\n  @if (map.get($annotations, alternate) == true) {\n    /* @alternate */\n  }\n\n  // noflip must be the last tag right before the property\n  @if (map.get($annotations, noflip) == true) {\n    /* @noflip */ /*rtl:ignore*/\n  }\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/elevation/elevation';\n@use '@material/elevation/elevation-theme';\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/ripple/ripple-theme';\n@use '@material/rtl/rtl';\n@use '@material/dom/dom';\n@use '@material/touch-target/touch-target';\n@use '@material/focus-ring/focus-ring';\n@use '@material/typography/typography';\n@use './button-shared-theme';\n\n@mixin static-styles($query: feature-targeting.all()) {\n  @include _static-styles-base($query: $query);\n  @include _typography-styles($query: $query);\n}\n\n@mixin _static-styles-base($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include touch-target.wrapper($query); // COPYBARA_COMMENT_THIS_LINE\n  // prettier-ignore\n  @include elevation.overlay-common($query); // COPYBARA_COMMENT_THIS_LINE\n\n  // postcss-bem-linter: define button\n  .mdc-button {\n    @include _root-structure($query);\n    // The icon CSS class overrides styles defined in the .material-icons CSS\n    // class, which is loaded separately so the order of CSS definitions is not\n    // guaranteed. Therefore, increase specifity by nesting this class to ensure\n    // overrides apply.\n    .mdc-button__icon {\n      @include feature-targeting.targets($feat-structure) {\n        @include _icon-structure;\n      }\n    }\n\n    .mdc-button__progress-indicator {\n      @include feature-targeting.targets($feat-structure) {\n        font-size: 0;\n        position: absolute;\n        @include rtl.ignore-next-line();\n        transform: translate(-50%, -50%);\n        top: 50%;\n        @include rtl.ignore-next-line();\n        left: 50%;\n        line-height: initial;\n      }\n    }\n\n    .mdc-button__label {\n      @include feature-targeting.targets($feat-structure) {\n        // Necessary such that label is stacked on top of ripple\n        // (since ripple is a positioned element, non-positioned elements\n        // appear under it).\n        position: relative;\n      }\n    }\n\n    .mdc-button__focus-ring {\n      @include focus-ring.focus-ring($query: $query);\n      @include feature-targeting.targets($feat-structure) {\n        display: none;\n      }\n    }\n\n    @include ripple-theme.focus {\n      .mdc-button__focus-ring {\n        @include dom.forced-colors-mode($exclude-ie11: true) {\n          @include feature-targeting.targets($feat-structure) {\n            display: block;\n          }\n        }\n      }\n    }\n\n    .mdc-button__touch {\n      @include touch-target.touch-target($query: $query);\n    }\n  }\n\n  .mdc-button__label + .mdc-button__icon {\n    @include feature-targeting.targets($feat-structure) {\n      @include icon-trailing;\n    }\n  }\n\n  svg.mdc-button__icon {\n    @include feature-targeting.targets($feat-structure) {\n      @include icon-svg;\n    }\n  }\n\n  .mdc-button--touch {\n    // Touch target doesn't change with height. It simply gets removed if\n    // density (height) changes. Therefore, it is a static style.\n    @include touch-target.margin(\n      $component-height: button-shared-theme.$height,\n      $query: $query\n    );\n  }\n  // postcss-bem-linter: end\n}\n\n@mixin _typography-styles($query) {\n  .mdc-button {\n    // Exclude properties declared in theme styles.\n    @include typography.typography(\n      button,\n      $exclude-props: (\n        font-size,\n        line-height,\n        font-weight,\n        letter-spacing,\n        text-transform\n      ),\n      $query: $query\n    );\n  }\n}\n\n/// @deprecated Contains typography declarations now part of theme-styles.\n@mixin deprecated-static-styles-without-ripple(\n  $query: feature-targeting.all()\n) {\n  @include _deprecated-typography-styles($query: $query);\n  @include _static-styles-base($query: $query);\n}\n\n@mixin _deprecated-typography-styles($query) {\n  .mdc-button {\n    @include typography.typography(button, $query);\n  }\n}\n\n@mixin deprecated-base($query) {\n  @include base($query);\n}\n\n@mixin base($query) {\n  @include typography.typography(button, $query);\n  @include _root-structure($query: $query);\n}\n\n@mixin _root-structure($query) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include elevation-theme.overlay-surface-position($query: $query);\n  @include elevation-theme.overlay-dimensions(100%, $query: $query);\n\n  @include feature-targeting.targets($feat-structure) {\n    display: inline-flex;\n    // position: relative; already set in mdc-elevation-overlay-surface-position\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    min-width: 64px;\n    border: none;\n    outline: none;\n    /* @alternate */\n    line-height: inherit;\n    user-select: none;\n    -webkit-appearance: none;\n    // Even though `visible` is the default, IE 11 computes the property as\n    // `hidden` in some cases, unless it's explicitly defined here.\n    overflow: visible;\n    vertical-align: middle;\n    background: transparent;\n  }\n\n  &::-moz-focus-inner {\n    @include feature-targeting.targets($feat-structure) {\n      padding: 0;\n      border: 0;\n    }\n  }\n\n  // postcss-bem-linter: ignore\n  &:active {\n    @include feature-targeting.targets($feat-structure) {\n      outline: none;\n    }\n  }\n\n  &:hover {\n    @include feature-targeting.targets($feat-structure) {\n      cursor: pointer;\n    }\n  }\n\n  &:disabled {\n    @include feature-targeting.targets($feat-structure) {\n      cursor: default;\n      pointer-events: none;\n    }\n  }\n\n  &[hidden] {\n    @include feature-targeting.targets($feat-structure) {\n      display: none;\n    }\n  }\n}\n\n@mixin icon {\n  @include _icon-structure;\n  @include _icon-size(18px);\n}\n\n@mixin _icon-structure {\n  @include rtl.reflexive-box(margin, right, 8px);\n\n  display: inline-block;\n  position: relative;\n  vertical-align: top;\n}\n\n@mixin _icon-size($size-px) {\n  $icon-size: typography.px-to-rem($size-px);\n\n  font-size: $icon-size;\n  height: $icon-size;\n  width: $icon-size;\n}\n\n@mixin icon-trailing {\n  @include rtl.reflexive-box(margin, left, 8px);\n}\n\n@mixin icon-svg {\n  fill: currentColor;\n}\n\n@mixin icon-contained {\n  @include rtl.reflexive-property(margin, -4px, 8px);\n}\n\n@mixin icon-contained-trailing {\n  @include rtl.reflexive-property(margin, 8px, -4px);\n}\n\n@mixin raised-transition($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n\n  @include feature-targeting.targets($feat-animation) {\n    transition: elevation-theme.transition-value();\n  }\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-icon {\n  @include icon;\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-icon-trailing {\n  @include icon-trailing;\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-icon-svg {\n  @include icon-svg;\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-icon-contained {\n  @include icon-contained;\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-icon-contained-trailing {\n  @include icon-contained-trailing;\n}\n", "//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:map';\n@use 'sass:math';\n@use 'sass:meta';\n@use '@material/animation/variables' as animation-variables;\n@use '@material/theme/custom-properties';\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/theme/css';\n@use '@material/theme/theme';\n@use '@material/theme/validate';\n@use '@material/theme/theme-color';\n\n$baseline-color: black !default;\n$umbra-opacity: 0.2 !default;\n$penumbra-opacity: 0.14 !default;\n$ambient-opacity: 0.12 !default;\n\n$umbra-map: (\n  0: '0px 0px 0px 0px',\n  1: '0px 2px 1px -1px',\n  2: '0px 3px 1px -2px',\n  3: '0px 3px 3px -2px',\n  4: '0px 2px 4px -1px',\n  5: '0px 3px 5px -1px',\n  6: '0px 3px 5px -1px',\n  7: '0px 4px 5px -2px',\n  8: '0px 5px 5px -3px',\n  9: '0px 5px 6px -3px',\n  10: '0px 6px 6px -3px',\n  11: '0px 6px 7px -4px',\n  12: '0px 7px 8px -4px',\n  13: '0px 7px 8px -4px',\n  14: '0px 7px 9px -4px',\n  15: '0px 8px 9px -5px',\n  16: '0px 8px 10px -5px',\n  17: '0px 8px 11px -5px',\n  18: '0px 9px 11px -5px',\n  19: '0px 9px 12px -6px',\n  20: '0px 10px 13px -6px',\n  21: '0px 10px 13px -6px',\n  22: '0px 10px 14px -6px',\n  23: '0px 11px 14px -7px',\n  24: '0px 11px 15px -7px',\n) !default;\n\n$penumbra-map: (\n  0: '0px 0px 0px 0px',\n  1: '0px 1px 1px 0px',\n  2: '0px 2px 2px 0px',\n  3: '0px 3px 4px 0px',\n  4: '0px 4px 5px 0px',\n  5: '0px 5px 8px 0px',\n  6: '0px 6px 10px 0px',\n  7: '0px 7px 10px 1px',\n  8: '0px 8px 10px 1px',\n  9: '0px 9px 12px 1px',\n  10: '0px 10px 14px 1px',\n  11: '0px 11px 15px 1px',\n  12: '0px 12px 17px 2px',\n  13: '0px 13px 19px 2px',\n  14: '0px 14px 21px 2px',\n  15: '0px 15px 22px 2px',\n  16: '0px 16px 24px 2px',\n  17: '0px 17px 26px 2px',\n  18: '0px 18px 28px 2px',\n  19: '0px 19px 29px 2px',\n  20: '0px 20px 31px 3px',\n  21: '0px 21px 33px 3px',\n  22: '0px 22px 35px 3px',\n  23: '0px 23px 36px 3px',\n  24: '0px 24px 38px 3px',\n) !default;\n\n$ambient-map: (\n  0: '0px 0px 0px 0px',\n  1: '0px 1px 3px 0px',\n  2: '0px 1px 5px 0px',\n  3: '0px 1px 8px 0px',\n  4: '0px 1px 10px 0px',\n  5: '0px 1px 14px 0px',\n  6: '0px 1px 18px 0px',\n  7: '0px 2px 16px 1px',\n  8: '0px 3px 14px 2px',\n  9: '0px 3px 16px 2px',\n  10: '0px 4px 18px 3px',\n  11: '0px 4px 20px 3px',\n  12: '0px 5px 22px 4px',\n  13: '0px 5px 24px 4px',\n  14: '0px 5px 26px 4px',\n  15: '0px 6px 28px 5px',\n  16: '0px 6px 30px 5px',\n  17: '0px 6px 32px 5px',\n  18: '0px 7px 34px 6px',\n  19: '0px 7px 36px 6px',\n  20: '0px 8px 38px 7px',\n  21: '0px 8px 40px 7px',\n  22: '0px 8px 42px 7px',\n  23: '0px 9px 44px 8px',\n  24: '0px 9px 46px 8px',\n) !default;\n\n// The css property used for elevation. In most cases this should not be changed. It is exposed\n// as a variable for abstraction / easy use when needing to reference the property directly, for\n// example in a `will-change` rule.\n$property: box-shadow !default;\n\n// The default color for the elevation overlay.\n$overlay-color: #fff;\n\n// The css property used for elevation overlay transitions. In most cases this should not be changed. It is exposed\n// as a variable for abstraction / easy use when needing to reference the property directly, for\n// example in a `will-change` rule.\n$overlay-property: opacity !default;\n\n// The default duration value for elevation transitions.\n$transition-duration: 280ms !default;\n\n// The default easing value for elevation transitions.\n$transition-timing-function: animation-variables.$standard-curve-timing-function !default;\n\n///\n/// Sets the elevation transition value.\n///\n/// @param {String} $duration - The duration of the transition.\n/// @param {String} $easing - The easing function for the transition.\n/// @return {String}\n///\n@function transition-value(\n  $duration: $transition-duration,\n  $easing: $transition-timing-function\n) {\n  @return #{$property} #{$duration} #{$easing};\n}\n\n///\n/// Sets the elevation overlay transition value.\n///\n/// @param {String} $duration - The duration of the transition.\n/// @param {String} $easing - The easing function for the transition.\n/// @return {String}\n///\n@function overlay-transition-value(\n  $duration: $transition-duration,\n  $easing: $transition-timing-function\n) {\n  @return #{$overlay-property} #{$duration} #{$easing};\n}\n\n///\n/// Creates a box-shadow from the Material elevation system.\n/// @param {Number} $level - the level of the Material elevation system.\n/// @param {String} $color - the color of the shadow.\n/// @param {Number} $opacity-boost [0] - optional opacity boost for the shadow.\n/// @return {List} the complete box shadow.\n///\n@function _box-shadow($level, $color, $opacity-boost: 0) {\n  $color: theme-color.prop-value($color);\n  $umbra-z-value: map.get($umbra-map, $level);\n  $penumbra-z-value: map.get($penumbra-map, $level);\n  $ambient-z-value: map.get($ambient-map, $level);\n\n  $umbra-color: rgba($color, $umbra-opacity + $opacity-boost);\n  $penumbra-color: rgba($color, $penumbra-opacity + $opacity-boost);\n  $ambient-color: rgba($color, $ambient-opacity + $opacity-boost);\n\n  @return (\n    #{'#{$umbra-z-value} #{$umbra-color}'},\n    #{'#{$penumbra-z-value} #{$penumbra-color}'},\n    #{$ambient-z-value} $ambient-color\n  );\n}\n\n// Returns the correct box-shadow specified by $z-value.\n// The $z-value must be between 0 and 24.\n// If $color has an alpha channel, it will be ignored and overridden. To increase the opacity of the shadow, use\n// $opacity-boost.\n@function elevation-box-shadow(\n  $z-value,\n  $color: $baseline-color,\n  $opacity-boost: 0\n) {\n  @if $z-value == null {\n    @return null;\n  }\n\n  @if meta.type-of($z-value) != number or not math.is-unitless($z-value) {\n    @error \"$z-value must be a unitless number, but received '#{$z-value}'\";\n  }\n\n  @if $z-value < 0 or $z-value > 24 {\n    @error \"$z-value must be between 0 and 24, but received '#{$z-value}'\";\n  }\n\n  @return _box-shadow($z-value, $color, $opacity-boost);\n}\n\n///\n/// Returns a shadow or null if params are invalid.\n/// @param {Number} $level - the level of the Material elevation system.\n/// @param {String} $color - the color of the shadow.\n/// @return {List|null} the complete box shadow or null.\n///\n@function _shadow($level, $color) {\n  @if $level == null and $color == null {\n    // Do not emit a warning if both are null, which means the user did not\n    // provide tokens.\n    @return null;\n  }\n\n  @if $level == null or $color == null {\n    // If one of the tokens is null, emit a warning: the user may not realize\n    // that both are required.\n    @warn \"both $level and $color are required; received $level: '#{$level}', $color: '#{$color}'\";\n    @return null;\n  }\n\n  @if $level < 0 or $level > 24 {\n    @warn \"$level must be between 0 and 24; received '#{$level}'\";\n    @return null;\n  }\n\n  @return _box-shadow($level, $color);\n}\n\n@function get-elevation($level) {\n  @return (box-shadow: elevation-box-shadow($level));\n}\n\n///\n/// Sets the shadow of the element.\n///\n/// @param {String} $box-shadow - The shadow to apply to the element.\n///\n@mixin shadow($box-shadow, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @include feature-targeting.targets($feat-color) {\n    @include theme.property(box-shadow, $box-shadow);\n  }\n}\n\n///\n/// Sets the elevation overlay surface required positioning.\n///\n@mixin overlay-surface-position($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    /* @alternate */\n    position: relative;\n  }\n}\n\n///\n/// Sets the dimensions of the elevation overlay, including positioning and sizing.\n///\n/// @param {Number} $width - The width of the elevation overlay\n/// @param {Number} [$height] - The height of the elevation overlay\n/// @param {Boolean} [$has-content-sizing] - Set to false if the container has no content sizing\n///\n@mixin overlay-dimensions(\n  $width,\n  $height: $width,\n  $has-content-sizing: true,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-structure) {\n      @include theme.property(width, $width);\n      @include theme.property(height, $height);\n\n      @if $has-content-sizing {\n        top: 0;\n        @include rtl.ignore-next-line();\n        left: 0;\n      } @else {\n        top: 50%;\n        @include rtl.ignore-next-line();\n        left: 50%;\n        @include rtl.ignore-next-line();\n        transform: translate(-50%, -50%);\n      }\n    }\n  }\n}\n\n///\n/// Sets the elevation overlay fill color.\n/// Expected to be called directly on the elevation overlay element.\n///\n/// @param {Color} $color - The color of the elevation overlay.\n///\n@mixin overlay-fill-color($color, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @include feature-targeting.targets($feat-color) {\n    @include theme.property(background-color, $color);\n  }\n}\n\n///\n/// Applies the given color to the container of the overlay.\n/// @param {color} $color - the color of the overlay container\n///\n@mixin overlay-container-color($color, $query: feature-targeting.all()) {\n  .mdc-elevation-overlay {\n    @include overlay-fill-color($color, $query: $query);\n  }\n}\n\n///\n/// Sets the elevation overlay opacity.\n/// Expected to be called from a parent element.\n///\n/// @param {Number} $opacity - The opacity of the elevation overlay.\n///\n@mixin overlay-opacity($opacity, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  .mdc-elevation-overlay {\n    @include feature-targeting.targets($feat-color) {\n      @include theme.property(opacity, $opacity);\n    }\n  }\n}\n\n// Applies the correct CSS rules to an element to give it the elevation specified by $z-value.\n// The $z-value must be between 0 and 24.\n// If $color has an alpha channel, it will be ignored and overridden. To increase the opacity of the shadow, use\n// $opacity-boost.\n@mixin elevation(\n  $z-value,\n  $color: $baseline-color,\n  $opacity-boost: 0,\n  $query: feature-targeting.all()\n) {\n  $box-shadow: elevation-box-shadow(\n    $z-value,\n    $color: $color,\n    $opacity-boost: $opacity-boost\n  );\n\n  @include shadow($box-shadow, $query: $query);\n}\n\n///\n/// Represents the configurable values of the elevation theme.\n///\n$_theme-values: (\n  shadow: null,\n  overlay-opacity: null,\n  overlay-color: null,\n);\n\n///\n/// Applies the shadow theme with the given $resolver function.\n/// @param {Function} $resolver - a function that returns a valid theme config.\n///   @see resolver for an example and expected arguments and return value.\n/// Accepts the following optional keyword args:\n/// @param {Number} $elevation - the level in the elevation system.\n/// @param {String} $shadow-color - the color used for the shadow.\n///\n@mixin with-resolver($resolver, $query: feature-targeting.all(), $args...) {\n  @if $resolver {\n    @include _theme(meta.call($resolver, $args...), $query: $query);\n  }\n}\n\n///\n/// Applies the given theme with validation.\n/// @param {Map} $theme - @see $_theme-values for accepted theme properties.\n///\n@mixin theme-styles($theme: (), $query: feature-targeting.all()) {\n  $theme: validate.theme-styles($_theme-values, $theme, $require-all: false);\n  @include _theme($theme, $query: $query);\n}\n\n///\n/// Applies the given theme.\n/// @param {Map} $theme - @see $_theme-values for accepted theme properties.\n///\n@mixin _theme($theme: (), $query: feature-targeting.all()) {\n  @include shadow(map.get($theme, shadow), $query: $query);\n  @include overlay-opacity(map.get($theme, overlay-opacity), $query: $query);\n  @include overlay-container-color(\n    map.get($theme, overlay-color),\n    $query: $query\n  );\n}\n\n///\n/// Transforms the following optional parameters into a theme config.\n/// @param {Number} $elevation - the level of the elevation system in Material.\n/// @param {String} $shadow-color - the color to be used by the shadow.\n/// @return {Map} @see $_theme-values for accepted theme properties.\n///\n@function resolver($args...) {\n  $opts: meta.keywords($args);\n  $elevation: map.get($opts, elevation);\n  $shadow-color: map.get($opts, shadow-color);\n  @if custom-properties.is-custom-prop($elevation) {\n    @return _resolve-custom-props($elevation, $shadow-color);\n  }\n\n  @return (shadow: _shadow($elevation, $shadow-color));\n}\n\n@function _resolve-custom-props($elevation, $shadow-color) {\n  $fallback-dp: custom-properties.get-fallback($elevation);\n  $fallback-shadow-color: custom-properties.get-fallback($shadow-color);\n  $shadow: custom-properties.set-fallback(\n    $elevation,\n    _shadow($fallback-dp, $fallback-shadow-color)\n  );\n  @return (shadow: $shadow);\n}\n", "//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:meta';\n@use 'sass:selector';\n@use '@material/theme/gss';\n@use '@material/theme/selector-ext';\n@use '@material/theme/theme';\n\n$include: true !default;\n\n/// Creates a rule that will be applied when a component is within the context\n/// of an RTL layout.\n///\n/// @example - scss\n/// .mdc-foo {\n///   padding-left: 4px;\n///\n///   @include rtl {\n///     padding-left: auto;\n///     padding-right: 4px;\n///   }\n/// }\n///\n/// @example - css\n///   .mdc-foo {\n///     padding-left: 4px;\n///   }\n///\n///   [dir=\"rtl\"] .mdc-foo,\n///   .mdc-foo[dir=\"rtl\"] {\n///     padding-left: auto;\n///     padding-right: 4px;\n///   }\n///\n/// Note that this mixin works by checking for an ancestor element with\n/// `[dir=\"rtl\"]`. As a result, nested `dir` values are not supported:\n///\n/// @example - html\n/// <html dir=\"rtl\">\n///   <!-- ... -->\n///   <div dir=\"ltr\">\n///     <div class=\"mdc-foo\">Styled incorrectly as RTL!</div>\n///   </div>\n/// </html>\n///\n/// In the future, selectors such as the `:dir` pseudo-class\n/// (http://mdn.io/css/:dir) will help us mitigate this.\n///\n/// @content Content to be styled in an RTL context.\n@mixin rtl() {\n  @if ($include) {\n    $dir-rtl: '[dir=rtl]';\n\n    $rtl-selectors: list.join(\n      selector.nest($dir-rtl, &),\n      selector-ext.append-strict(&, $dir-rtl)\n    );\n\n    @at-root {\n      #{$rtl-selectors} {\n        /*rtl:begin:ignore*/\n        @content;\n        /*rtl:end:ignore*/\n      }\n    }\n  }\n}\n\n// Takes a base box-model property name (`margin`, `border`, `padding`, etc.) along with a\n// default direction (`left` or `right`) and value, and emits rules which apply the given value to the\n// specified direction by default and the opposite direction in RTL.\n//\n// For example:\n//\n// ```scss\n// .mdc-foo {\n//   @include rtl-reflexive-box(margin, left, 8px);\n// }\n// ```\n//\n// is equivalent to:\n//\n// ```scss\n// .mdc-foo {\n//   margin-left: 8px;\n//   margin-right: 0;\n//\n//   @include rtl {\n//     margin-left: 0;\n//     margin-right: 8px;\n//   }\n// }\n// ```\n//\n// whereas:\n//\n// ```scss\n// .mdc-foo {\n//   @include rtl-reflexive-box(margin, right, 8px);\n// }\n// ```\n//\n// is equivalent to:\n//\n// ```scss\n// .mdc-foo {\n//   margin-left: 0;\n//   margin-right: 8px;\n//\n//   @include rtl {\n//     margin-left: 8px;\n//     margin-right: 0;\n//   }\n// }\n// ```\n//\n// You can also pass an optional 4th `$root-selector` argument which will be forwarded to `mdc-rtl`,\n// e.g. `@include rtl-reflexive-box(margin, left, 8px, '.mdc-component')`.\n//\n// Note that this function will always zero out the original value in an RTL context.\n// If you're trying to flip the values, use `mdc-rtl-reflexive-property()` instead.\n@mixin reflexive-box(\n  $base-property,\n  $default-direction,\n  $value,\n  $replace: null\n) {\n  @if (list.index((right, left), $default-direction) == null) {\n    @error \"Invalid default direction: '#{$default-direction}'. Please specifiy either 'right' or 'left'.\";\n  }\n\n  $left-value: $value;\n  $right-value: 0;\n\n  @if ($default-direction == right) {\n    $left-value: 0;\n    $right-value: $value;\n  }\n\n  @include reflexive-property(\n    $base-property,\n    $left-value,\n    $right-value,\n    $replace: $replace\n  );\n}\n\n// Takes a base property and emits rules that assign <base-property>-left to <left-value> and\n// <base-property>-right to <right-value> in a LTR context, and vice versa in a RTL context.\n// For example:\n//\n// ```scss\n// .mdc-foo {\n//   @include rtl-reflexive-property(margin, auto, 12px);\n// }\n// ```\n//\n// is equivalent to:\n//\n// ```scss\n// .mdc-foo {\n//   margin-left: auto;\n//   margin-right: 12px;\n//\n//   @include rtl {\n//     margin-left: 12px;\n//     margin-right: auto;\n//   }\n// }\n// ```\n//\n// An optional 4th `$root-selector` argument can be given, which will be passed to `mdc-rtl`.\n@mixin reflexive-property(\n  $base-property,\n  $left-value,\n  $right-value,\n  $replace: null\n) {\n  $prop-left: #{$base-property}-left;\n  $prop-right: #{$base-property}-right;\n\n  @include reflexive(\n    $prop-left,\n    $left-value,\n    $prop-right,\n    $right-value,\n    $replace: $replace\n  );\n}\n\n// Takes an argument specifying a horizontal position property (either 'left' or 'right') as well\n// as a value, and applies that value to the specified position in a LTR context, and flips it in a\n// RTL context. For example:\n//\n// ```scss\n// .mdc-foo {\n//   @include rtl-reflexive-position(left, 0);\n// }\n// ```\n//\n// is equivalent to:\n//\n// ```scss\n// .mdc-foo {\n//   left: 0;\n//   right: initial;\n//\n//   @include rtl {\n//     left: initial;\n//     right: 0;\n//   }\n// }\n// ```\n//\n// An optional third $root-selector argument may also be given, which is passed to `mdc-rtl`.\n@mixin reflexive-position($position-property, $value, $replace: null) {\n  @if (list.index((right, left), $position-property) == null) {\n    @error \"Invalid position #{position-property}. Please specifiy either right or left\";\n  }\n\n  // TODO: 'initial' is not supported in IE 11. https://caniuse.com/#feat=css-initial-value\n  $left-value: $value;\n  $right-value: initial;\n\n  @if ($position-property == right) {\n    $right-value: $value;\n    $left-value: initial;\n  }\n\n  @include reflexive(\n    left,\n    $left-value,\n    right,\n    $right-value,\n    $replace: $replace\n  );\n}\n\n// Takes pair of properties with values as arguments and flips it in RTL context.\n// For example:\n//\n// ```scss\n// .mdc-foo {\n//   @include rtl-reflexive(left, 2px, right, 5px);\n// }\n// ```\n//\n// is equivalent to:\n//\n// ```scss\n// .mdc-foo {\n//   left: 2px;\n//   right: 5px;\n//\n//   @include rtl {\n//     right: 2px;\n//     left: 5px;\n//   }\n// }\n// ```\n//\n// An optional fifth `$root-selector` argument may also be given, which is passed to `mdc-rtl`.\n@mixin reflexive(\n  $left-property,\n  $left-value,\n  $right-property,\n  $right-value,\n  $replace: null\n) {\n  $left-replace: null;\n  $right-replace: null;\n  @if $replace {\n    @if meta.type-of($left-value) == 'string' {\n      $left-replace: $replace;\n    }\n\n    @if meta.type-of($right-value) == 'string' {\n      $right-replace: $replace;\n    }\n\n    @if $left-replace == null and $right-replace == null {\n      @error 'mdc-rtl: $replace may only be used with strings but neither left nor right values are strings.';\n    }\n\n    // If any replacements are null, treat the entire value as null (do not\n    // emit anything).\n    @each $name, $replacement in $replace {\n      @if $replacement == null {\n        $left-value: null;\n        $right-value: null;\n      }\n    }\n  }\n\n  // Do not emit if either value are null\n  @if $left-value and $right-value {\n    @include _property($left-property, $left-value, $replace: $left-replace);\n    @include _property($right-property, $right-value, $replace: $right-replace);\n\n    @include rtl {\n      @include _property(\n        $left-property,\n        $right-value,\n        $replace: $right-replace\n      );\n      @include _property($right-property, $left-value, $replace: $left-replace);\n    }\n  }\n}\n\n///\n/// Adds RTL ignore annotation when `$mdc-rtl-include` is true.\n///\n@mixin ignore-next-line() {\n  @include gss.annotate(\n    (\n      noflip: $include,\n    )\n  );\n}\n\n///\n/// Adds `@noflip` annotation when `$mdc-rtl-include` is true.\n///\n/// @param {String} $property\n/// @param {String} $value\n/// @param {Map} $replace\n///\n@mixin _property($property, $value, $replace: null) {\n  @include theme.property(\n    $property,\n    $value,\n    $replace: $replace,\n    $gss: (noflip: $include)\n  );\n}\n", "//\n// Copyright 2021 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/dom/dom';\n\n$ring-radius-default: 8px !default;\n$inner-ring-width-default: 2px !default;\n$inner-ring-color-default: transparent !default;\n$outer-ring-width-default: 2px !default;\n$outer-ring-color-default: transparent !default;\n$container-outer-padding-default: 2px !default;\n\n/// Styles applied to the component's inner focus ring element.\n///\n/// @param $ring-radius [$ring-radius-default] - Focus ring radius.\n/// @param $inner-ring-width [$inner-ring-width-default] - Inner focus ring width.\n/// @param $inner-ring-color [$inner-ring-color-default] - Inner focus ring color.\n/// @param $outer-ring-width [$outer-ring-width-default] - Outer focus ring width.\n/// @param $outer-ring-color [$outer-ring-color-default] - Outer focus ring color.\n/// @param $container-outer-padding-vertical [$container-outer-padding-default]\n////    - The vertical distance between the focus ring and the container.\n/// @param $container-outer-padding-horizontal [$container-outer-padding-default]\n////    - The horizontal distance between the focus ring and the container.\n@mixin focus-ring(\n  $query: feature-targeting.all(),\n  $ring-radius: $ring-radius-default,\n  $inner-ring-width: $inner-ring-width-default,\n  $inner-ring-color: $inner-ring-color-default,\n  $outer-ring-width: $outer-ring-width-default,\n  $outer-ring-color: $outer-ring-color-default,\n  $container-outer-padding-vertical: $container-outer-padding-default,\n  $container-outer-padding-horizontal: $container-outer-padding-default\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  $outer-ring-size: 100%;\n  @if $outer-ring-width > 0 {\n    $outer-ring-size: calc(100% + #{$outer-ring-width * 2});\n  }\n\n  @include feature-targeting.targets($feat-structure) {\n    pointer-events: none;\n    border: $inner-ring-width solid $inner-ring-color;\n    border-radius: calc($ring-radius - $outer-ring-width);\n    box-sizing: content-box;\n    position: absolute;\n    top: 50%;\n    @include rtl.ignore-next-line();\n    left: 50%;\n    @include rtl.ignore-next-line();\n    transform: translate(-50%, -50%);\n\n    @include dom.forced-colors-mode($exclude-ie11: true) {\n      border-color: CanvasText;\n    }\n\n    &::after {\n      content: '';\n      border: $outer-ring-width solid $outer-ring-color;\n      border-radius: $ring-radius;\n      display: block;\n      position: absolute;\n      top: 50%;\n      @include rtl.ignore-next-line();\n      left: 50%;\n      @include rtl.ignore-next-line();\n      transform: translate(-50%, -50%);\n      height: $outer-ring-size;\n      width: $outer-ring-size;\n\n      @include dom.forced-colors-mode($exclude-ie11: true) {\n        border-color: CanvasText;\n      }\n    }\n  }\n\n  @include focus-ring-offset(\n    $container-outer-padding-vertical,\n    $container-outer-padding-horizontal,\n    $query: $query\n  );\n}\n\n/// Customizes the color of the focus ring.\n///\n/// @param $inner-ring-color [$inner-ring-color-default] - Inner focus ring color.\n/// @param $outer-ring-width [$outer-ring-width-default] - Outer focus ring width.\n@mixin focus-ring-color(\n  $inner-ring-color: $inner-ring-color-default,\n  $outer-ring-color: $outer-ring-color-default,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    border-color: $inner-ring-color;\n\n    &::after {\n      border-color: $outer-ring-color;\n    }\n  }\n}\n\n/// Customizes the offset of the focus ring.\n///\n/// @param $container-outer-padding-vertical [$container-outer-padding-default]\n////    - The vertical distance between the focus ring and the container.\n/// @param $container-outer-padding-horizontal [$container-outer-padding-default]\n////    - The horizontal distance between the focus ring and the container.\n@mixin focus-ring-offset(\n  $container-outer-padding-vertical: $container-outer-padding-default,\n  $container-outer-padding-horizontal: $container-outer-padding-default,\n  $offset: 0,\n  $query: feature-targeting.all()\n) {\n  $container-size-vertical: 100%;\n  $container-size-vertical-offset: $container-outer-padding-vertical + $offset *\n    2;\n  @if $container-size-vertical-offset != 0 {\n    $container-size-vertical: calc(100% + $container-size-vertical-offset * 2);\n  }\n  $container-size-horizontal: 100%;\n  $container-size-horizontal-offset: $container-outer-padding-horizontal +\n    $offset * 2;\n  @if $container-size-horizontal-offset != 0 {\n    $container-size-horizontal: calc(\n      100% + $container-size-horizontal-offset * 2\n    );\n  }\n\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    height: $container-size-vertical;\n    width: $container-size-horizontal;\n  }\n}\n\n/// Customizes the border radius of the button focus ring.\n///\n/// @param {Number} $ring-radius - The border radius of the focus ring.\n/// @param {Number} $outer-ring-width [$outer-ring-width] - Width of the outer\n///     ring, required to compute the radius for the inner ring.\n@mixin focus-ring-radius(\n  $ring-radius,\n  $outer-ring-width: $outer-ring-width-default,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    border-radius: calc($ring-radius - $outer-ring-width);\n\n    &::after {\n      border-radius: $ring-radius;\n    }\n  }\n}\n", "// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n\n///\n/// Emits necessary layout styles to set a transparent border around an element\n/// without interfering with the rest of its component layout. The border is\n/// only visible in high-contrast mode. The target element should be a child of\n/// a relatively positioned top-level element (i.e. a ::before pseudo-element).\n///\n/// @param {number} $border-width - The width of the transparent border.\n/// @param {string} $border-style - The style of the transparent border.\n///\n@mixin transparent-border(\n  $border-width: 1px,\n  $border-style: solid,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    position: absolute;\n    box-sizing: border-box;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    @include rtl.ignore-next-line();\n    left: 0;\n    border: $border-width $border-style transparent;\n    border-radius: inherit;\n    content: '';\n    pointer-events: none;\n  }\n\n  // Used to satisfy Firefox v94 which does not render transparent borders in HCM (b/206440838).\n  @include forced-colors-mode($exclude-ie11: true) {\n    @include feature-targeting.targets($feat-structure) {\n      border-color: CanvasText;\n    }\n  }\n}\n\n///\n/// Visually hides text content for accessibility. This text should only be\n/// visible to screen reader users.\n/// See https://a11yproject.com/posts/how-to-hide-content/\n///\n@mixin visually-hidden($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    clip: rect(1px, 1px, 1px, 1px);\n    height: 1px;\n    overflow: hidden;\n    position: absolute;\n    white-space: nowrap; /* added line */\n    width: 1px;\n  }\n}\n\n/// Selects for IE11 support.\n///\n/// @content styles to emit for IE11 support\n@mixin ie11-support {\n  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n    @content;\n  }\n}\n\n/// Selects for `forced-colors` high contrast mode.\n///\n/// While in `forced-colors` mode, only system colors should be used.\n///\n/// @link https://developer.mozilla.org/en-US/docs/Web/CSS/color_value#system_colors\n/// @link https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors\n/// @content styles to emit in `forced-colors` mode\n@mixin forced-colors-mode($exclude-ie11: false) {\n  @if $exclude-ie11 {\n    @media screen and (forced-colors: active) {\n      @content;\n    }\n  } @else {\n    @media screen and (forced-colors: active), (-ms-high-contrast: active) {\n      @content;\n    }\n  }\n}\n", "//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:math';\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use 'sass:string';\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/custom-properties';\n@use '@material/theme/keys';\n@use '@material/theme/theme';\n\n/// @deprecated Avoid calling this function directly. Instead, configure the\n/// `$styles-<style>` variable Maps.\n@function set-styles_($base-styles, $scale-styles, $override-styles) {\n  $options: (\n    custom-property-prefix: typography,\n  );\n\n  $base-styles: keys.set-values($base-styles, $options: $options);\n\n  @each $style, $style-props in $scale-styles {\n    @each $base-key in map.keys($base-styles) {\n      // Ignore the return result, it's not needed\n      $unused: keys.add-link(keys.combine($style, $base-key), $base-key);\n    }\n\n    // Merge base properties for all styles.\n    $style-props: map.merge($base-styles, $style-props);\n\n    // Merge overrides onto each style.\n    $style-props: map.merge($style-props, map.get($override-styles, $style));\n\n    // Register keys for this style\n    @each $property, $value in $style-props {\n      $unused: keys.set-value(\n        keys.combine($style, $property),\n        $value,\n        $options: $options\n      );\n    }\n\n    // Override original styles with new styles.\n    $scale-styles: map.merge($scale-styles, (#{$style}: $style-props));\n  }\n\n  @return $scale-styles;\n}\n\n@function get-letter-spacing_($tracking, $font-size) {\n  @return math.div($tracking, $font-size * 16) * 1em;\n}\n\n@function px-to-rem($px) {\n  @if custom-properties.is-custom-prop($px) {\n    @return custom-properties.set-fallback(\n      $px,\n      _px-to-rem(custom-properties.get-fallback($px))\n    );\n  }\n  @return _px-to-rem($px);\n}\n\n@function _px-to-rem($px) {\n  @if $px == null {\n    @return null;\n  }\n  @return math.div($px, 16px) * 1rem;\n}\n\n$font-family: string.unquote('Roboto, sans-serif') !default;\n\n// Override styles\n$styles-headline1: () !default;\n$styles-headline2: () !default;\n$styles-headline3: () !default;\n$styles-headline4: () !default;\n$styles-headline5: () !default;\n$styles-headline6: () !default;\n$styles-subtitle1: () !default;\n$styles-subtitle2: () !default;\n$styles-body1: () !default;\n$styles-body2: () !default;\n$styles-caption: () !default;\n$styles-button: () !default;\n$styles-overline: () !default;\n\n/// @deprecated Do not override this variable. Use the $styles-<style> override\n/// Map variables instead, or $font-family to set the base font family.\n$base: (\n  font-family: $font-family,\n) !default;\n\n$font-weight-values: (\n  thin: 100,\n  light: 300,\n  regular: 400,\n  medium: 500,\n  bold: 700,\n  black: 900,\n) !default;\n\n/// @deprecated Do not override this variable. Use the $styles-<style> override\n/// Map variables instead.\n$styles: set-styles_(\n  $base,\n  (\n    headline1: (\n      font-size: px-to-rem(96px),\n      line-height: px-to-rem(96px),\n      font-weight: map.get($font-weight-values, light),\n      letter-spacing: get-letter-spacing_(-1.5, 6),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    headline2: (\n      font-size: px-to-rem(60px),\n      line-height: px-to-rem(60px),\n      font-weight: map.get($font-weight-values, light),\n      letter-spacing: get-letter-spacing_(-0.5, 3.75),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    headline3: (\n      font-size: px-to-rem(48px),\n      line-height: px-to-rem(50px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: normal,\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    headline4: (\n      font-size: px-to-rem(34px),\n      line-height: px-to-rem(40px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: get-letter-spacing_(0.25, 2.125),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    headline5: (\n      font-size: px-to-rem(24px),\n      line-height: px-to-rem(32px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: normal,\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    headline6: (\n      font-size: px-to-rem(20px),\n      line-height: px-to-rem(32px),\n      font-weight: map.get($font-weight-values, medium),\n      letter-spacing: get-letter-spacing_(0.25, 1.25),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    subtitle1: (\n      font-size: px-to-rem(16px),\n      line-height: px-to-rem(28px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: get-letter-spacing_(0.15, 1),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    subtitle2: (\n      font-size: px-to-rem(14px),\n      line-height: px-to-rem(22px),\n      font-weight: map.get($font-weight-values, medium),\n      letter-spacing: get-letter-spacing_(0.1, 0.875),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    body1: (\n      font-size: px-to-rem(16px),\n      line-height: px-to-rem(24px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: get-letter-spacing_(0.5, 1),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    body2: (\n      font-size: px-to-rem(14px),\n      line-height: px-to-rem(20px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: get-letter-spacing_(0.25, 0.875),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    caption: (\n      font-size: px-to-rem(12px),\n      line-height: px-to-rem(20px),\n      font-weight: map.get($font-weight-values, regular),\n      letter-spacing: get-letter-spacing_(0.4, 0.75),\n      text-decoration: inherit,\n      text-transform: inherit,\n    ),\n    button: (\n      font-size: px-to-rem(14px),\n      line-height: px-to-rem(36px),\n      font-weight: map.get($font-weight-values, medium),\n      letter-spacing: get-letter-spacing_(1.25, 0.875),\n      text-decoration: none,\n      text-transform: uppercase,\n    ),\n    overline: (\n      font-size: px-to-rem(12px),\n      line-height: px-to-rem(32px),\n      font-weight: map.get($font-weight-values, medium),\n      letter-spacing: get-letter-spacing_(2, 0.75),\n      text-decoration: none,\n      text-transform: uppercase,\n    ),\n  ),\n  (\n    headline1: $styles-headline1,\n    headline2: $styles-headline2,\n    headline3: $styles-headline3,\n    headline4: $styles-headline4,\n    headline5: $styles-headline5,\n    headline6: $styles-headline6,\n    subtitle1: $styles-subtitle1,\n    subtitle2: $styles-subtitle2,\n    body1: $styles-body1,\n    body2: $styles-body2,\n    caption: $styles-caption,\n    button: $styles-button,\n    overline: $styles-overline,\n  )\n) !default;\n\n// A copy of the styles Map that is used to detect compile-time changes for\n// Angular support.\n$_styles-copy: $styles;\n\n@function is-typography-style($style) {\n  @return map.has-key($styles, $style);\n}\n\n@function get-typography-styles() {\n  @return map.keys($styles);\n}\n\n@mixin core-styles($query: feature-targeting.all()) {\n  .mdc-typography {\n    @include base($query: $query);\n  }\n\n  @each $style in get-typography-styles() {\n    .mdc-typography--#{$style} {\n      @include typography($style, $query: $query);\n    }\n  }\n}\n\n@mixin base($query: feature-targeting.all()) {\n  $feat-typography: feature-targeting.create-target($query, typography);\n\n  @include smooth-font($query: $query);\n  @include feature-targeting.targets($feat-typography) {\n    @include theme.property(font-family, font-family);\n  }\n}\n\n@mixin typography($style, $query: feature-targeting.all(), $exclude-props: ()) {\n  $feat-typography: feature-targeting.create-target($query, typography);\n\n  @if not is-typography-style($style) {\n    @error \"Invalid style specified! #{$style} doesn't exist. Choose one of #{get-typography-styles()}\";\n  }\n\n  @include smooth-font($query: $query);\n  @include feature-targeting.targets($feat-typography) {\n    @each $key in keys.get-keys($style) {\n      // <style>-<property>: headline1-font-size\n      // Slice the string past the first key separator to retrieve the\n      // property name\n      $property: string.slice($key, string.index($key, '-') + 1);\n      @if list.index($exclude-props, $property) == null {\n        $current-global-value: map.get($styles, $style, $property);\n        $configured-global-value: map.get($_styles-copy, $style, $property);\n        @if $current-global-value != $configured-global-value {\n          // A compile time change was made to $mdc-typography-styles. To\n          // support Angular, use this value instead of the key's value.\n          @if $current-global-value {\n            // Only emit if the overridden value exists\n            $custom-prop: keys.create-custom-property($key);\n            $custom-prop: custom-properties.set-fallback(\n              $custom-prop,\n              $current-global-value\n            );\n            @include theme.property($property, $custom-prop);\n          }\n        } @else {\n          // Otherwise, use the key, which may be different from the original\n          // configured global value.\n          @include theme.property($property, $key);\n        }\n      }\n    }\n  }\n}\n\n/// Applies antialiasing via font-smoothing to text.\n@mixin smooth-font($query: feature-targeting.all()) {\n  $feat-typography: feature-targeting.create-target($query, typography);\n\n  @include feature-targeting.targets($feat-typography) {\n    -moz-osx-font-smoothing: grayscale;\n    -webkit-font-smoothing: antialiased;\n  }\n}\n\n// Element must be `display: block` or `display: inline-block` for this to work.\n@mixin overflow-ellipsis($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    overflow: hidden;\n  }\n}\n\n/// Sets a container's baseline that text content will align to.\n///\n/// If the `$display` is set to a flexbox display, only `$top` baseline may be\n/// set. A separate element must be added as a child of the container with a\n/// `$bottom` baseline.\n///\n/// @param {Number} $top - the distance from the top of the container to the\n///     text's baseline.\n/// @param {Number} $bottom - the distance from the text's baseline to the\n///     bottom of the container.\n/// @param {String} $display - the display type of the container. May be `flex`,\n///     `inline-flex`, `block`, or `inline-block`.\n@mixin baseline(\n  $top: 0,\n  $bottom: 0,\n  $display: block,\n  $query: feature-targeting.all()\n) {\n  $validDisplayTypes: (flex, inline-flex, block, inline-block);\n\n  @if list.index($validDisplayTypes, $display) == null {\n    @error \"mdc-typography: invalid display specified! #{$display} must be one of #{$validDisplayTypes}\";\n  }\n\n  $isFlexbox: $display == 'flex' or $display == 'inline-flex';\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    display: $display;\n\n    @if $isFlexbox {\n      align-items: baseline;\n    }\n  }\n\n  @if $top > 0 {\n    @include baseline-top($top, $query: $query);\n  }\n\n  @if $bottom > 0 {\n    @if $isFlexbox {\n      @error \"mdc-typography: invalid baseline with display type. #{$display} cannot specifiy $bottom. Add a separate child element with its own $bottom.\";\n    }\n\n    @include baseline-bottom($bottom, $query: $query);\n  }\n}\n\n/// Sets the baseline of flow text content.\n///\n/// Separate `$top` and `$bottom` baselines may be specified. You should ensure\n/// that the `$top` baseline matches the previous text content's $bottom\n/// baseline to ensure text is positioned appropriately.\n///\n/// See go/css-baseline for reference on how this mixin works.\n///\n/// This is intended for text flow content only (e.g. `<h1>`, `<p>`, `<span>`,\n/// or `<div>` with only text content). Use `baseline()` to set the baseline of\n/// containers that are flexbox or have non-flow content children.\n///\n/// @param {Number} $top - the distance from the top of the container to the\n///     text's baseline.\n/// @param {Number} $bottom - the distance from the text's baseline to the\n///     bottom of the container.\n/// @param {Boolean} $lineHeight - the line-height to use for the text. This\n///     is the distance between baselines of multiple lines of text.\n/// @param {String} $display - the display type of the container. May be `block`\n///     or `inline-block`.\n@mixin text-baseline(\n  $top: 0,\n  $bottom: 0,\n  $display: block,\n  $lineHeight: normal,\n  $query: feature-targeting.all()\n) {\n  $validDisplayTypes: (block, inline-block);\n\n  @if list.index($validDisplayTypes, $display) == null {\n    @error \"mdc-typography: invalid display specified! #{$display} must be one of #{$validDisplayTypes}\";\n  }\n\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include baseline(\n    $display: $display,\n    $top: $top,\n    $bottom: $bottom,\n    $query: $query\n  );\n  @include feature-targeting.targets($feat-structure) {\n    @if $top > 0 {\n      margin-top: 0;\n      /* @alternate */\n      line-height: #{$lineHeight};\n    }\n\n    @if $bottom > 0 {\n      margin-bottom: -1 * $bottom;\n    }\n  }\n}\n\n/// Creates a baseline strut from the top of a container. This mixin is for\n/// advanced users, prefer `baseline()`.\n///\n/// @param {Number} $distance - The distance from the top of the container to\n///     the text's baseline.\n@mixin baseline-top($distance, $query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  &::before {\n    @include feature-targeting.targets($feat-structure) {\n      @include baseline-strut_($distance);\n\n      vertical-align: 0;\n    }\n  }\n}\n\n/// Creates a baseline strut from the baseline to the bottom of a container.\n/// This mixin is for advanced users, prefer `baseline()`.\n///\n/// @param {Number} $distance - The distance from the text's baseline to the\n///     bottom of the container.\n@mixin baseline-bottom($distance, $query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  &::after {\n    @include feature-targeting.targets($feat-structure) {\n      @include baseline-strut_($distance);\n\n      vertical-align: -1 * $distance;\n    }\n  }\n}\n\n/// Adds an invisible, zero-width prefix to a container's text.\n/// This ensures that the baseline is always where the text would be, instead\n/// of defaulting to the container bottom when text is empty. Do not use this\n/// mixin if the `baseline` mixin is already applied.\n@mixin zero-width-prefix($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  &::before {\n    @include feature-targeting.targets($feat-structure) {\n      content: '\\200b';\n    }\n  }\n}\n\n@mixin baseline-strut_($distance) {\n  display: inline-block;\n  width: 0;\n  height: $distance;\n  content: '';\n}\n\n@function get-font($typography) {\n  @return map.get($styles, $typography, font-family);\n}\n\n@function get-line-height($typography) {\n  @return map.get($styles, $typography, line-height);\n}\n\n@function get-size($typography) {\n  @return map.get($styles, $typography, font-size);\n}\n\n@function get-weight($typography) {\n  @return map.get($styles, $typography, font-weight);\n}\n\n@function get-tracking($typography) {\n  @return map.get($styles, $typography, letter-spacing);\n}\n\n$_typography-theme: (\n  font: null,\n  line-height: null,\n  size: null,\n  weight: null,\n  tracking: null,\n);\n\n@mixin theme-styles($theme) {\n  @include theme.validate-theme-styles($_typography-theme, $theme);\n\n  @include theme.property(font-family, map.get($theme, font));\n  @include theme.property(line-height, map.get($theme, line-height));\n  @include theme.property(font-size, map.get($theme, size));\n  @include theme.property(font-weight, map.get($theme, weight));\n  @include theme.property(letter-spacing, map.get($theme, tracking));\n}\n\n/// Resolves a theme's typography tokens for the given prefix.\n///\n/// @example - scss\n///   // $theme has the following tokens:\n///   // - label-text-font\n///   // - label-text-line-height\n///   // - label-text-size\n///   // - label-text-tracking\n///   // - label-text-weight\n///   $theme: resolve-theme($theme, map.get($resolvers, typography), label-text);\n///\n/// @param {Map} $theme - The theme to resolve tokens for.\n/// @param {Function} $resolver - The typography resolver to use.\n/// @param {String...} $token-prefixes - The prefix(es) of a typography token\n///     set.\n/// @return {Map} The theme with resolved typography tokens.\n@function resolve-theme($theme, $resolver, $token-prefixes...) {\n  @if $resolver == null {\n    @return $theme;\n  }\n\n  @each $token-prefix in $token-prefixes {\n    $typography-theme: meta.call(\n      $resolver,\n      $font: map.get($theme, '#{$token-prefix}-font'),\n      $line-height: map.get($theme, '#{$token-prefix}-line-height'),\n      $size: map.get($theme, '#{$token-prefix}-size'),\n      $tracking: map.get($theme, '#{$token-prefix}-tracking'),\n      $weight: map.get($theme, '#{$token-prefix}-weight')\n    );\n\n    $theme: map.merge(\n      $theme,\n      (\n        '#{$token-prefix}-font': map.get($typography-theme, font),\n        '#{$token-prefix}-line-height': map.get($typography-theme, line-height),\n        '#{$token-prefix}-size': map.get($typography-theme, size),\n        '#{$token-prefix}-tracking': map.get($typography-theme, tracking),\n        '#{$token-prefix}-weight': map.get($typography-theme, weight),\n      )\n    );\n  }\n\n  @return $theme;\n}\n", "//\n// Copyright 2021 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/tokens/resolvers';\n@use './button-ripple';\n@use './button-shared-theme';\n@use './button-text-theme';\n\n@mixin styles(\n  $theme: button-text-theme.$light-theme,\n  $resolver: resolvers.$material,\n  $query: feature-targeting.all()\n) {\n  @include button-base.static-styles($query: $query);\n  @include static-styles($query: $query);\n  .mdc-button {\n    @include button-text-theme.theme-styles($theme, $query: $query);\n  }\n}\n\n@mixin static-styles($query: feature-targeting.all()) {\n  @include static-styles-without-ripple($query: $query);\n  @include button-ripple.static-styles($query: $query);\n}\n\n@mixin static-styles-without-ripple($query: feature-targeting.all()) {\n  .mdc-button {\n    // TODO(b/179402677): move this into theme config\n    @include button-shared-theme.horizontal-padding(\n      button-shared-theme.$horizontal-padding,\n      $query: $query\n    );\n  }\n}\n", "//\n// Copyright 2021 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:map';\n@use 'sass:math';\n@use 'sass:meta';\n@use '@material/density/functions' as density-functions;\n@use '@material/density/variables' as density-variables;\n@use '@material/dom/mixins' as dom-mixins;\n@use '@material/elevation/elevation-theme';\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/focus-ring/focus-ring';\n@use '@material/ripple/ripple-theme';\n@use '@material/shape/mixins' as shape-mixins;\n@use '@material/theme/custom-properties';\n@use '@material/theme/state';\n@use '@material/theme/theme';\n@use '@material/theme/theme-color';\n@use '@material/typography/typography';\n@use './button-ripple';\n\n$height: 36px !default;\n$horizontal-padding: 8px !default;\n$contained-horizontal-padding: 16px !default;\n// For a contained button with an icon, the padding on the side of the\n// button with the icon.\n$contained-horizontal-padding-icon: 12px !default;\n\n$minimum-height: 24px !default;\n$maximum-height: $height !default;\n$density-scale: density-variables.$default-scale !default;\n$density-config: (\n  height: (\n    default: $height,\n    maximum: $maximum-height,\n    minimum: $minimum-height,\n  ),\n) !default;\n\n$shape-radius: small !default;\n\n$disabled-ink-color: rgba(theme-color.prop-value(on-surface), 0.38) !default;\n$disabled-container-color: rgba(\n  theme-color.prop-value(on-surface),\n  0.12\n) !default;\n\n@mixin theme-styles($theme, $resolver, $query: feature-targeting.all()) {\n  @include _label-text-typography(\n    (\n      family: map.get($theme, label-text-font),\n      size: map.get($theme, label-text-size),\n      tracking: map.get($theme, label-text-tracking),\n      weight: map.get($theme, label-text-weight),\n      transform: map.get($theme, label-text-transform),\n    ),\n    $query: $query\n  );\n\n  @include container-fill-color(\n    (\n      default: map.get($theme, container-color),\n      disabled: map.get($theme, disabled-container-color),\n    ),\n    $query: $query\n  );\n\n  @include ink-color(\n    (\n      default: map.get($theme, label-text-color),\n      hover: map.get($theme, hover-label-text-color),\n      focus: map.get($theme, focus-label-text-color),\n      pressed: map.get($theme, pressed-label-text-color),\n      disabled: map.get($theme, disabled-label-text-color),\n    ),\n    $query: $query\n  );\n\n  @include icon-color(\n    (\n      default: map.get($theme, with-icon-icon-color),\n      hover: map.get($theme, with-icon-hover-icon-color),\n      focus: map.get($theme, with-icon-focus-icon-color),\n      pressed: map.get($theme, with-icon-pressed-icon-color),\n      disabled: map.get($theme, with-icon-disabled-icon-color),\n    ),\n    $query: $query\n  );\n\n  $icon-size: map.get($theme, with-icon-icon-size);\n  @include _icon-size($icon-size, $query: $query);\n\n  @include ripple-theme.states-colors(\n    $color-map: (\n      hover: map.get($theme, hover-state-layer-color),\n      press: map.get($theme, pressed-state-layer-color),\n    ),\n    $ripple-target: button-ripple.$ripple-target,\n    $query: $query\n  );\n\n  $hover-state-layer-opacity: map.get($theme, hover-state-layer-opacity);\n  $focus-state-layer-opacity: map.get($theme, focus-state-layer-opacity);\n  $pressed-state-layer-opacity: map.get($theme, pressed-state-layer-opacity);\n  @include ripple-theme.states-opacities(\n    $opacity-map: (\n      focus: $focus-state-layer-opacity,\n      hover: $hover-state-layer-opacity,\n      press: $pressed-state-layer-opacity,\n    ),\n    $ripple-target: button-ripple.$ripple-target,\n    $query: $query\n  );\n\n  $container-height: map.get($theme, container-height);\n  @include height($container-height, $query: $query);\n\n  $container-height-value: if(\n    custom-properties.is-custom-prop($container-height),\n    custom-properties.get-fallback($container-height),\n    $container-height\n  );\n\n  /// Token \"keep-touch-target\":\n  /// Prevent the increased touch target from being reseted if the\n  /// container-height differs from the default (36px)\n  $keep-touch-target: map.get($theme, keep-touch-target);\n  @if (not $keep-touch-target) and\n    ($container-height-value != null) and\n    ($container-height-value != $height)\n  {\n    @include _touch-target-reset($query: $query);\n  }\n\n  $shape: map.get($theme, container-shape);\n  @if $shape {\n    $container-height: if(\n      $container-height != null,\n      $container-height,\n      $height\n    );\n    @include _shape-radius-with-height(\n      $shape,\n      $height: $container-height,\n      $query: $query\n    );\n  }\n\n  @include _elevation(\n    $resolver,\n    $elevation-map: (\n      default: map.get($theme, container-elevation),\n      disabled: map.get($theme, disabled-container-elevation),\n      focus: map.get($theme, focus-container-elevation),\n      hover: map.get($theme, hover-container-elevation),\n      pressed: map.get($theme, pressed-container-elevation)\n    ),\n    $shadow-color: map.get($theme, container-shadow-color),\n    $query: $query\n  );\n\n  @include _focus-ring-color(map.get($theme, focus-ring-color), $query);\n  @include _focus-ring-offset(map.get($theme, focus-ring-offset), $query);\n}\n\n@function resolve-theme-elevation-keys($theme, $resolver) {\n  $elevation-resolver: map.get($resolver, elevation);\n  $shadow-color: map.get($theme, container-shadow-color);\n  @if $elevation-resolver == null or $shadow-color == null {\n    @return $theme;\n  }\n\n  $elevation-keys: (\n    container-elevation,\n    hover-container-elevation,\n    focus-container-elevation,\n    pressed-container-elevation,\n    disabled-container-elevation\n  );\n\n  @each $key in $elevation-keys {\n    $elevation: map.get($theme, $key);\n    @if $elevation != null {\n      $resolved-value: meta.call(\n        $elevation-resolver,\n        $elevation: $elevation,\n        $shadow-color: $shadow-color\n      );\n      // Update the key with the resolved value.\n      $theme: map.set($theme, $key, $resolved-value);\n    }\n  }\n  @return $theme;\n}\n\n///\n/// Sets ripple color for button.\n///\n@mixin ripple-states(\n  $color,\n  $opacity-map: null,\n  $query: feature-targeting.all()\n) {\n  @include ripple-theme.states(\n    $color: $color,\n    $opacity-map: $opacity-map,\n    $query: $query,\n    $ripple-target: button-ripple.$ripple-target\n  );\n}\n\n@mixin filled-accessible(\n  $container-fill-color,\n  $query: feature-targeting.all()\n) {\n  $fill-tone: theme-color.tone($container-fill-color);\n\n  @include container-fill-color($container-fill-color, $query);\n\n  @if ($fill-tone == 'dark') {\n    @include ink-color(text-primary-on-dark, $query);\n    @include ripple-states($color: text-primary-on-dark, $query: $query);\n  } @else {\n    @include ink-color(text-primary-on-light, $query);\n    @include ripple-states($color: text-primary-on-light, $query: $query);\n  }\n}\n\n///\n/// Sets the container fill color to the given color for an enabled button.\n/// @param {Color|map} $color-or-map - The desired container fill color,\n///     specified either as a flat value or a map of colors with states\n///     {default, hover, focus, pressed, disabled} as keys.\n///\n@mixin container-fill-color($color-or-map, $query: feature-targeting.all()) {\n  // :not(:disabled) is used to support link styled as button\n  // as link does not support :enabled style\n  &:not(:disabled) {\n    @include _container-fill-color(\n      state.get-default-state($color-or-map),\n      $query: $query\n    );\n\n    &:hover {\n      @include _container-fill-color(\n        state.get-hover-state($color-or-map),\n        $query: $query\n      );\n    }\n\n    @include ripple-theme.focus() {\n      @include _container-fill-color(\n        state.get-focus-state($color-or-map),\n        $query: $query\n      );\n    }\n\n    @include ripple-theme.active {\n      @include _container-fill-color(\n        state.get-pressed-state($color-or-map),\n        $query: $query\n      );\n    }\n  }\n\n  &:disabled {\n    @include _container-fill-color(\n      state.get-disabled-state($color-or-map),\n      $query: $query\n    );\n  }\n}\n\n///\n/// Sets the container fill color to the given color for a disabled button.\n/// @param {Color} $color - The desired container fill color.\n/// @deprecated - call `container-fill-color` instead with `disabled` as a map\n///     key.\n///\n@mixin disabled-container-fill-color($color, $query: feature-targeting.all()) {\n  @include container-fill-color(\n    (\n      disabled: $color,\n    ),\n    $query: $query\n  );\n}\n\n///\n/// Sets the icon color to the given color for an enabled button.\n/// @param {Color} $color-or-map - The desired icon color, specified either\n///     as a flat value or a map of colors with states\n///     {default, hover, focus, pressed, disabled} as keys.\n///\n@mixin icon-color($color-or-map, $query: feature-targeting.all()) {\n  &:not(:disabled) {\n    @include _icon-color(\n      state.get-default-state($color-or-map),\n      $query: $query\n    );\n\n    &:hover {\n      @include _icon-color(\n        state.get-hover-state($color-or-map),\n        $query: $query\n      );\n    }\n\n    @include ripple-theme.focus() {\n      @include _icon-color(\n        state.get-focus-state($color-or-map),\n        $query: $query\n      );\n    }\n\n    @include ripple-theme.active {\n      @include _icon-color(\n        state.get-pressed-state($color-or-map),\n        $query: $query\n      );\n    }\n  }\n\n  &:disabled {\n    @include _icon-color(\n      state.get-disabled-state($color-or-map),\n      $query: $query\n    );\n  }\n}\n\n///\n/// Sets the icon color to the given color for a disabled button.\n/// @param {Color} $color - The desired icon color.\n/// @deprecated - call `icon-color` instead with `disabled` as a map key.\n///\n@mixin disabled-icon-color($color, $query: feature-targeting.all()) {\n  @include icon-color(\n    (\n      disabled: $color,\n    ),\n    $query: $query\n  );\n}\n\n///\n/// Sets the ink color to the given color for an enabled button,\n/// and sets the icon color to the given color unless `mdc-button-icon-color`\n/// is also used.\n/// @param {Color} $color-or-map - The desired ink color, specified either\n///     as a flat value or a map of colors with states\n///     {default, hover, focus, pressed, disabled} as keys.\n///\n@mixin ink-color($color-or-map, $query: feature-targeting.all()) {\n  &:not(:disabled) {\n    @include _ink-color(state.get-default-state($color-or-map), $query: $query);\n\n    &:hover {\n      @include _ink-color(state.get-hover-state($color-or-map), $query: $query);\n    }\n\n    @include ripple-theme.focus() {\n      @include _ink-color(state.get-focus-state($color-or-map), $query: $query);\n    }\n\n    @include ripple-theme.active {\n      @include _ink-color(\n        state.get-pressed-state($color-or-map),\n        $query: $query\n      );\n    }\n  }\n\n  &:disabled {\n    @include _ink-color(\n      state.get-disabled-state($color-or-map),\n      $query: $query\n    );\n  }\n}\n\n///\n/// Sets the ink color to the given color for a disabled button,\n/// and sets the icon color to the given color unless `mdc-button-icon-color`\n/// is also used.\n/// @param {Color} $color - The desired ink color.\n/// @deprecated - call `ink-color` instead with `disabled` as a map key.\n///\n@mixin disabled-ink-color($color, $query: feature-targeting.all()) {\n  @include ink-color(\n    (\n      disabled: $color,\n    ),\n    $query: $query\n  );\n}\n\n///\n/// Sets density scale for button.\n///\n/// @param {Number | String} $density-scale - Density scale value for component. Supported density scale values `-3`,\n///     `-2`, `-1`, `0`.\n///\n@mixin density($density-scale, $query: feature-targeting.all()) {\n  $height: density-functions.prop-value(\n    $density-config: $density-config,\n    $density-scale: $density-scale,\n    $property-name: height,\n  );\n\n  @include height($height, $query: $query);\n\n  @if $density-scale != 0 {\n    @include _touch-target-reset($query: $query);\n  }\n}\n\n///\n/// Resets touch target-related styles. This is called from the density mixin to\n/// automatically remove the increased touch target, since dense components\n/// don't have the same default a11y requirements.\n/// @access private\n///\n@mixin _touch-target-reset($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    margin-top: 0;\n    margin-bottom: 0;\n  }\n\n  .mdc-button__touch {\n    @include feature-targeting.targets($feat-structure) {\n      // Do not set display: none in case the touch target is <a> element.\n      height: 100%;\n    }\n  }\n}\n\n///\n/// Sets custom height for button.\n/// @param {Number} $height - Height of button in `px`.\n///\n@mixin height($height, $query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    @include theme.property(height, $height);\n  }\n}\n\n@mixin shape-radius(\n  $radius,\n  $rtl-reflexive: false,\n  $density-scale: $density-scale,\n  $query: feature-targeting.all()\n) {\n  $height: density-functions.prop-value(\n    $density-config: $density-config,\n    $density-scale: $density-scale,\n    $property-name: height,\n  );\n\n  @include _shape-radius-with-height($radius, $rtl-reflexive, $height, $query);\n}\n\n@mixin _shape-radius-with-height(\n  $radius,\n  $rtl-reflexive: false,\n  $height: $height,\n  $query: feature-targeting.all()\n) {\n  @include shape-mixins.radius(\n    $radius,\n    $rtl-reflexive,\n    $component-height: $height,\n    $query: $query\n  );\n\n  #{button-ripple.$ripple-target} {\n    @include shape-mixins.radius(\n      $radius,\n      $rtl-reflexive,\n      $component-height: $height,\n      $query: $query\n    );\n  }\n\n  @include _focus-ring-shape($radius, $query);\n}\n\n///\n/// Sets horizontal padding to the given number.\n/// @param {Number} $padding\n/// @param {Number} $padding-icon [null] For buttons with an icon, the\n///     horizontal padding on the side with the icon, if different from\n///     $padding.\n///\n@mixin horizontal-padding(\n  $padding,\n  $padding-icon: null,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    // $padding should be a single value; enforce it by specifying all 4 sides in the output\n    padding: 0 $padding 0 $padding;\n  }\n\n  @if $padding-icon != null {\n    &.mdc-button--icon-trailing {\n      @include feature-targeting.targets($feat-structure) {\n        // $padding should be a single value; enforce it by specifying all 4\n        // sides in the output.\n        padding: 0 $padding-icon 0 $padding;\n      }\n    }\n\n    &.mdc-button--icon-leading {\n      @include feature-targeting.targets($feat-structure) {\n        // $padding should be a single value; enforce it by specifying all 4\n        // sides in the output.\n        padding: 0 $padding 0 $padding-icon;\n      }\n    }\n  }\n}\n\n///\n/// Sets the button label to overflow as ellipsis\n///\n@mixin label-overflow-ellipsis($query: feature-targeting.all()) {\n  .mdc-button__label {\n    @include typography.overflow-ellipsis($query: $query);\n  }\n}\n\n///\n/// Add a visible outline to the button in high contrast mode.\n///\n@mixin outline-hcm-shim($query: feature-targeting.all()) {\n  &::before {\n    @include dom-mixins.transparent-border($query: $query);\n  }\n}\n\n///\n/// Includes ad-hoc high contrast mode support.\n/// @deprecated Use `outline-hcm-shim` for the outline button. The focus ring\n///     is provided by default.\n///\n@mixin high-contrast-mode-shim($query: feature-targeting.all()) {\n  @include outline-hcm-shim($query: $query);\n\n  // Link buttons apply focus to the contained link. Focus is indicated via the\n  // link since focus-within isn't supported by IE.\n  & .mdc-button__link:focus,\n  &:focus {\n    &::before {\n      @include focus-ring.focus-ring($query: $query);\n    }\n  }\n}\n\n///\n/// Sets the container fill color to the given color. This mixin should be\n/// wrapped in a selector that qualifies button state.\n/// @access private\n///\n@mixin _container-fill-color($color, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $color {\n    @include feature-targeting.targets($feat-color) {\n      @include theme.property(background-color, $color);\n    }\n  }\n}\n\n///\n/// Sets the icon color to the given color. This mixin should be\n/// wrapped in a selector that qualifies button state.\n/// @access private\n///\n@mixin _icon-color($color, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $color {\n    .mdc-button__icon {\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(color, $color);\n      }\n    }\n  }\n}\n\n@mixin _icon-size($size-px, $query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @if $size-px != null {\n    $size-rem: typography.px-to-rem($size-px);\n    .mdc-button__icon {\n      @include feature-targeting.targets($feat-structure) {\n        @include theme.property(font-size, $size-rem);\n        @include theme.property(width, $size-rem);\n        @include theme.property(height, $size-rem);\n      }\n    }\n  }\n}\n\n///\n/// Sets the ink color to the given color. This mixin should be\n/// wrapped in a selector that qualifies button state.\n/// @access private\n///\n@mixin _ink-color($color, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $color {\n    @include feature-targeting.targets($feat-color) {\n      @include theme.property(color, $color);\n    }\n  }\n}\n\n@mixin _label-text-typography(\n  $typography-map,\n  $query: feature-targeting.all()\n) {\n  $feat-typography: feature-targeting.create-target($query, typography);\n\n  $family: map.get($typography-map, family);\n  $size: map.get($typography-map, size);\n  $tracking: map.get($typography-map, tracking);\n  $weight: map.get($typography-map, weight);\n  $transform: map.get($typography-map, transform);\n\n  @include feature-targeting.targets($feat-typography) {\n    @include theme.property(font-family, $family);\n    @include theme.property(font-size, $size);\n    @include theme.property(letter-spacing, $tracking);\n    @include theme.property(font-weight, $weight);\n    @include theme.property(text-transform, $transform);\n  }\n}\n\n@mixin _elevation(\n  $resolver,\n  $elevation-map,\n  $shadow-color,\n  $query: feature-targeting.all()\n) {\n  $elevation-resolver: map.get($resolver, elevation);\n\n  @if $shadow-color {\n    $default: state.get-default-state($elevation-map);\n    @if $default != null {\n      @include elevation-theme.with-resolver(\n        $elevation-resolver,\n        $elevation: $default,\n        $shadow-color: $shadow-color,\n        $query: $query\n      );\n    }\n\n    $focus: state.get-focus-state($elevation-map);\n    @if $focus != null {\n      @include ripple-theme.focus {\n        @include elevation-theme.with-resolver(\n          $elevation-resolver,\n          $elevation: $focus,\n          $shadow-color: $shadow-color,\n          $query: $query\n        );\n      }\n    }\n\n    $hover: state.get-hover-state($elevation-map);\n    @if $hover != null {\n      &:hover {\n        @include elevation-theme.with-resolver(\n          $elevation-resolver,\n          $elevation: $hover,\n          $shadow-color: $shadow-color,\n          $query: $query\n        );\n      }\n    }\n\n    $pressed: state.get-pressed-state($elevation-map);\n    @if $pressed != null {\n      @include ripple-theme.active {\n        @include elevation-theme.with-resolver(\n          $elevation-resolver,\n          $elevation: $pressed,\n          $shadow-color: $shadow-color,\n          $query: $query\n        );\n      }\n    }\n\n    $disabled: state.get-disabled-state($elevation-map);\n    @if $disabled != null {\n      &:disabled {\n        @include elevation-theme.with-resolver(\n          $elevation-resolver,\n          $elevation: $disabled,\n          $shadow-color: $shadow-color,\n          $query: $query\n        );\n      }\n    }\n  }\n}\n\n@mixin _focus-ring-shape($radius, $query: feature-targeting.all()) {\n  $radius-value: if(\n    custom-properties.is-custom-prop($radius),\n    custom-properties.get-declaration-value($radius),\n    $radius\n  );\n\n  .mdc-button__focus-ring {\n    @if $radius-value != 0 and type-of($radius-value) == 'number' {\n      @include focus-ring.focus-ring-radius(\n        $ring-radius: $radius-value,\n        $query: $query\n      );\n    }\n  }\n}\n\n@mixin _focus-ring-color($color, $query: feature-targeting.all()) {\n  $color-value: if(\n    custom-properties.is-custom-prop($color),\n    custom-properties.get-declaration-value($color),\n    $color\n  );\n\n  .mdc-button__focus-ring {\n    @if $color != null {\n      @include focus-ring.focus-ring-color(\n        $inner-ring-color: $color-value,\n        $query: $query\n      );\n    }\n  }\n}\n\n@mixin _focus-ring-offset($offset, $query: feature-targeting.all()) {\n  $offset-value: if(\n    custom-properties.is-custom-prop($offset),\n    custom-properties.get-declaration-value($offset),\n    $offset\n  );\n\n  .mdc-button__focus-ring {\n    @if $offset-value != 0 and type-of($offset-value) == 'number' {\n      @include focus-ring.focus-ring-offset(\n        $offset: $offset-value,\n        $query: $query\n      );\n    }\n  }\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/tokens/resolvers';\n@use './button-base';\n@use './button-filled-theme';\n@use './button-ripple';\n@use './button-shared-theme';\n\n@mixin styles(\n  $theme: button-filled-theme.$light-theme,\n  $resolver: resolvers.$material,\n  $query: feature-targeting.all()\n) {\n  @include button-base.static-styles($query: $query);\n  @include static-styles($query: $query);\n  .mdc-button--unelevated {\n    @include button-filled-theme.theme-styles(\n      $theme,\n      $resolver,\n      $query: $query\n    );\n  }\n}\n\n@mixin static-styles($query: feature-targeting.all()) {\n  @include static-styles-without-ripple($query: $query);\n  @include button-ripple.static-styles($query: $query);\n}\n\n@mixin static-styles-without-ripple($query: feature-targeting.all()) {\n  .mdc-button--unelevated {\n    @include button-base.raised-transition($query: $query);\n    // TODO(b/179402677): move into theme config\n    @include button-shared-theme.horizontal-padding(\n      $padding: button-shared-theme.$contained-horizontal-padding,\n      $padding-icon: button-shared-theme.$contained-horizontal-padding-icon,\n      $query: $query\n    );\n  }\n}\n\n// Legacy mixins\n\n@mixin filled($query: feature-targeting.all()) {\n  // TODO(b/179402677): move into theme config\n  @include button-shared-theme.horizontal-padding(\n    $padding: button-shared-theme.$contained-horizontal-padding,\n    $padding-icon: button-shared-theme.$contained-horizontal-padding-icon,\n    $query: $query\n  );\n  @include button-filled-theme.theme-styles(\n    button-filled-theme.$light-theme,\n    $query: $query\n  );\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-filled($query) {\n  @include filled($query);\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/elevation/elevation-theme';\n@use '@material/feature-targeting/feature-targeting';\n@use './button-base';\n@use './button-protected-theme';\n@use './button-ripple';\n@use './button-shared-theme';\n\n@mixin styles(\n  $theme: button-protected-theme.$light-theme,\n  $resolver: resolvers.$material,\n  $query: feature-targeting.all()\n) {\n  @include button-base.static-styles($query: $query);\n  @include static-styles($query: $query);\n  .mdc-button--raised {\n    @include button-protected-theme.theme-styles(\n      $theme,\n      $resolver: $resolver,\n      $query: $query\n    );\n  }\n}\n\n@mixin static-styles($query: feature-targeting.all()) {\n  @include static-styles-without-ripple($query: $query);\n  @include button-ripple.static-styles($query: $query);\n}\n\n@mixin static-styles-without-ripple($query: feature-targeting.all()) {\n  .mdc-button--raised {\n    @include button-base.raised-transition($query);\n    // TODO(b/179402677): move into theme config\n    @include button-shared-theme.horizontal-padding(\n      $padding: button-shared-theme.$contained-horizontal-padding,\n      $padding-icon: button-shared-theme.$contained-horizontal-padding-icon,\n      $query: $query\n    );\n  }\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-raised($query) {\n  @include raised($query);\n}\n\n@mixin raised($query) {\n  @include elevation-theme.elevation(2, $query: $query);\n\n  &:hover,\n  &:focus {\n    @include elevation-theme.elevation(4, $query: $query);\n  }\n\n  &:active {\n    @include elevation-theme.elevation(8, $query: $query);\n  }\n\n  &:disabled {\n    @include elevation-theme.elevation(0, $query: $query);\n  }\n\n  @include button-base.raised-transition($query);\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:map';\n@use '@material/elevation/elevation-theme';\n@use '@material/feature-targeting/feature-targeting';\n@use './button-base';\n@use './button-outlined-theme';\n@use './button-ripple';\n@use './button-shared-theme';\n\n@mixin styles(\n  $theme: button-outlined-theme.$light-theme,\n  $resolver: resolvers.$material,\n  $query: feature-targeting.all()\n) {\n  @include button-base.static-styles($query: $query);\n  @include static-styles($query: $query);\n  .mdc-button--outlined {\n    @include theme-styles($theme, $resolver, $query: $query);\n  }\n}\n\n@mixin static-styles($query: feature-targeting.all()) {\n  @include static-styles-without-ripple($query: $query);\n  @include button-ripple.static-styles($query: $query);\n}\n\n@mixin static-styles-without-ripple($query: feature-targeting.all()) {\n  .mdc-button--outlined {\n    @include _static-styles($query: $query);\n  }\n}\n\n@mixin _static-styles($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    border-style: solid;\n  }\n\n  @include feature-targeting.targets($feat-animation) {\n    $duration: elevation-theme.$transition-duration;\n    $easing: elevation-theme.$transition-timing-function;\n    transition: border #{$duration} #{$easing};\n  }\n\n  #{button-ripple.$ripple-target} {\n    @include feature-targeting.targets($feat-structure) {\n      border-style: solid;\n      border-color: transparent;\n    }\n  }\n}\n\n@mixin outlined($query: feature-targeting.all()) {\n  @include _static-styles($query: $query);\n  @include button-outlined-theme.theme-styles(\n    button-outlined-theme.$light-theme,\n    $query: $query\n  );\n}\n\n/// @deprecated Private style mixin for partners; not available for public use.\n@mixin deprecated-outlined($query) {\n  @include outlined($query);\n}\n", "//\n// Copyright 2016 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:math';\n@use 'sass:color';\n@use 'sass:map';\n@use '@material/animation/functions' as functions2;\n@use '@material/animation/variables' as variables2;\n@use '@material/base/mixins' as base-mixins;\n@use '@material/theme/custom-properties';\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/theme/theme';\n@use './ripple-theme';\n\n@mixin core-styles($query: feature-targeting.all()) {\n  @include static-styles($query: $query);\n\n  .mdc-ripple-surface {\n    @include ripple-theme.states($query: $query);\n  }\n}\n\n@mixin static-styles($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  .mdc-ripple-surface {\n    @include surface($query: $query);\n    @include radius-bounded($query: $query);\n    @include surface-styles($query: $query);\n  }\n\n  .mdc-ripple-surface[data-mdc-ripple-is-unbounded],\n  .mdc-ripple-upgraded--unbounded {\n    @include radius-unbounded($query: $query);\n    @include unbounded-styles($query: $query);\n  }\n}\n\n/// Sets all states (including hover, focus, press, activated and selected) with\n/// given color as base color.\n///\n/// This mixin is for internal use only. Use `ripple-theme.states($color)` mixin\n/// to set interactive states (hover, focus & press) color.\n///\n/// @param {Color|String} $color - Target base color. Can be valid CSS color or\n///     a color string literal (i.e., `primary`, `secondary`, etc).\n@mixin states-for-color($color, $query: feature-targeting.all()) {\n  @include ripple-theme.states($color, $query: $query);\n  @include ripple-theme.states-activated($color, $query: $query);\n  @include ripple-theme.states-selected($color, $query: $query);\n}\n\n@mixin surface-styles($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    position: relative;\n    outline: none;\n    overflow: hidden;\n  }\n}\n\n@mixin unbounded-styles($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n  @include feature-targeting.targets($feat-structure) {\n    overflow: visible;\n  }\n}\n\n@mixin common($query: feature-targeting.all()) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n\n  // Ensure that styles needed by any component using MDC Ripple are emitted, but only once.\n  // (Every component using MDC Ripple imports these mixins, but doesn't necessarily import\n  // mdc-ripple.scss.)\n  @include feature-targeting.targets($feat-animation) {\n    @include base-mixins.emit-once('mdc-ripple/common/animation') {\n      @include keyframes_;\n    }\n  }\n}\n\n@mixin surface(\n  $query: feature-targeting.all(),\n  $ripple-target: '&',\n  $include-will-change: true // TODO(b/151931961): Remove once resolved\n) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    --mdc-ripple-fg-size: 0;\n    --mdc-ripple-left: 0;\n    --mdc-ripple-top: 0;\n    --mdc-ripple-fg-scale: 1;\n    --mdc-ripple-fg-translate-end: 0;\n    --mdc-ripple-fg-translate-start: 0;\n\n    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n    // TODO(b/151931961): Remove the following block once resolved\n    @if $include-will-change {\n      will-change: transform, opacity;\n    }\n  }\n\n  #{$ripple-target}::before,\n  #{$ripple-target}::after {\n    @include feature-targeting.targets($feat-structure) {\n      position: absolute;\n      border-radius: 50%;\n      opacity: 0;\n      pointer-events: none;\n      content: '';\n    }\n  }\n\n  #{$ripple-target}::before {\n    @include feature-targeting.targets($feat-animation) {\n      // Also transition background-color to avoid unnatural color flashes when toggling activated/selected state\n      transition: opacity ripple-theme.$states-wash-duration linear,\n        background-color ripple-theme.$states-wash-duration linear;\n    }\n\n    @include feature-targeting.targets($feat-structure) {\n      // Ensure that the ripple wash for hover/focus states is displayed on top of positioned child elements\n      @include theme.property(\n        z-index,\n        custom-properties.create(--mdc-ripple-z-index, 1)\n      );\n    }\n  }\n\n  #{$ripple-target}::after {\n    @include feature-targeting.targets($feat-structure) {\n      @include theme.property(\n        z-index,\n        custom-properties.create(--mdc-ripple-z-index, 0)\n      );\n    }\n  }\n\n  // Common styles for upgraded surfaces (some of these depend on custom properties set via JS or other mixins)\n\n  &.mdc-ripple-upgraded {\n    #{$ripple-target}::before {\n      @include feature-targeting.targets($feat-structure) {\n        transform: scale(var(--mdc-ripple-fg-scale, 1));\n      }\n    }\n\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-structure) {\n        top: 0;\n        @include rtl.ignore-next-line();\n        left: 0;\n        transform: scale(0);\n        transform-origin: center center;\n      }\n    }\n  }\n\n  &.mdc-ripple-upgraded--unbounded {\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-structure) {\n        top: var(--mdc-ripple-top, 0);\n        @include rtl.ignore-next-line();\n        left: var(--mdc-ripple-left, 0);\n      }\n    }\n  }\n\n  &.mdc-ripple-upgraded--foreground-activation {\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-animation) {\n        animation: mdc-ripple-fg-radius-in ripple-theme.$translate-duration\n            forwards,\n          mdc-ripple-fg-opacity-in ripple-theme.$fade-in-duration forwards;\n      }\n    }\n  }\n\n  &.mdc-ripple-upgraded--foreground-deactivation {\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-animation) {\n        animation: mdc-ripple-fg-opacity-out ripple-theme.$fade-out-duration;\n      }\n\n      @include feature-targeting.targets($feat-structure) {\n        // Retain transform from mdc-ripple-fg-radius-in activation\n        @include rtl.ignore-next-line();\n        transform: translate(var(--mdc-ripple-fg-translate-end, 0))\n          scale(var(--mdc-ripple-fg-scale, 1));\n      }\n    }\n  }\n}\n\n@mixin radius-bounded(\n  $radius: 100%,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-struture: feature-targeting.create-target($query, structure);\n\n  #{$ripple-target}::before,\n  #{$ripple-target}::after {\n    @include feature-targeting.targets($feat-struture) {\n      top: calc(50% - #{$radius});\n      @include rtl.ignore-next-line();\n      left: calc(50% - #{$radius});\n      width: $radius * 2;\n      height: $radius * 2;\n    }\n  }\n\n  &.mdc-ripple-upgraded {\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-struture) {\n        width: var(--mdc-ripple-fg-size, $radius);\n        height: var(--mdc-ripple-fg-size, $radius);\n      }\n    }\n  }\n}\n\n@mixin radius-unbounded(\n  $radius: 100%,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-struture: feature-targeting.create-target($query, structure);\n\n  #{$ripple-target}::before,\n  #{$ripple-target}::after {\n    @include feature-targeting.targets($feat-struture) {\n      top: calc(50% - #{math.div($radius, 2)});\n      @include rtl.ignore-next-line();\n      left: calc(50% - #{math.div($radius, 2)});\n      width: $radius;\n      height: $radius;\n    }\n  }\n\n  &.mdc-ripple-upgraded {\n    #{$ripple-target}::before,\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-struture) {\n        top: var(--mdc-ripple-top, calc(50% - #{math.div($radius, 2)}));\n        @include rtl.ignore-next-line();\n        left: var(--mdc-ripple-left, calc(50% - #{math.div($radius, 2)}));\n        width: var(--mdc-ripple-fg-size, $radius);\n        height: var(--mdc-ripple-fg-size, $radius);\n      }\n    }\n\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-struture) {\n        width: var(--mdc-ripple-fg-size, $radius);\n        height: var(--mdc-ripple-fg-size, $radius);\n      }\n    }\n  }\n}\n\n// Common styles for a ripple target element.\n// Used for components which have an inner ripple target element.\n@mixin target-common($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include feature-targeting.targets($feat-structure) {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    // Necessary for clicks on other inner elements (e.g. close icon in chip)\n    // to go through.\n    pointer-events: none;\n  }\n}\n\n@mixin keyframes_ {\n  @keyframes mdc-ripple-fg-radius-in {\n    from {\n      animation-timing-function: variables2.$standard-curve-timing-function;\n      // NOTE: For these keyframes, we do not need custom property fallbacks because they are only\n      // used in conjunction with `.mdc-ripple-upgraded`. Since MDCRippleFoundation checks to ensure\n      // that custom properties are supported within the browser before adding this class, we can\n      // safely use them without a fallback.\n      @include rtl.ignore-next-line();\n      transform: translate(var(--mdc-ripple-fg-translate-start, 0)) scale(1);\n    }\n\n    to {\n      @include rtl.ignore-next-line();\n      transform: translate(var(--mdc-ripple-fg-translate-end, 0))\n        scale(var(--mdc-ripple-fg-scale, 1));\n    }\n  }\n\n  @keyframes mdc-ripple-fg-opacity-in {\n    from {\n      animation-timing-function: linear;\n      opacity: 0;\n    }\n\n    to {\n      opacity: var(--mdc-ripple-fg-opacity, 0);\n    }\n  }\n\n  @keyframes mdc-ripple-fg-opacity-out {\n    from {\n      animation-timing-function: linear;\n      opacity: var(--mdc-ripple-fg-opacity, 0);\n    }\n\n    to {\n      opacity: 0;\n    }\n  }\n}\n", "//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n$deceleration-curve-timing-function: cubic-bezier(0, 0, 0.2, 1) !default;\n$standard-curve-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !default;\n$acceleration-curve-timing-function: cubic-bezier(0.4, 0, 1, 1) !default;\n$sharp-curve-timing-function: cubic-bezier(0.4, 0, 0.6, 1) !default;\n\n@function enter($name, $duration, $delay: 0ms) {\n  @return $name $duration $delay $deceleration-curve-timing-function;\n}\n\n@function exit-permanent($name, $duration, $delay: 0ms) {\n  @return $name $duration $delay $acceleration-curve-timing-function;\n}\n\n@function exit-temporary($name, $duration, $delay: 0ms) {\n  @return $name $duration $delay $sharp-curve-timing-function;\n}\n\n@function standard($name, $duration, $delay: 0ms) {\n  @return $name $duration $delay $standard-curve-timing-function;\n}\n\n@function linear($name, $duration, $delay: 0ms) {\n  @return $name $duration $delay linear;\n}\n", "//\n// Copyright 2016 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/ripple/ripple';\n\n$ripple-target: '.mdc-button__ripple';\n\n@mixin static-styles($query: feature-targeting.all()) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @include ripple.common($query); // COPYBARA_COMMENT_THIS_LINE\n\n  .mdc-button {\n    @include ripple.surface($query: $query, $ripple-target: $ripple-target);\n    @include ripple.radius-bounded(\n      $query: $query,\n      $ripple-target: $ripple-target\n    );\n  }\n\n  #{$ripple-target} {\n    @include feature-targeting.targets($feat-structure) {\n      position: absolute;\n      // Ripple needs content-box as the box sizing and box-sizing: border-box\n      // is often set as a default, so we override that here.\n      box-sizing: content-box;\n      overflow: hidden;\n      z-index: 0;\n      top: 0;\n      left: 0;\n      bottom: 0;\n      right: 0;\n    }\n  }\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/rtl/rtl';\n@use '@material/touch-target/mixins' as touch-target-mixins;\n@use '@material/typography/typography';\n@use './button-base';\n@use './button-filled';\n@use './button-filled-theme';\n@use './button-outlined';\n@use './button-outlined-theme';\n@use './button-protected';\n@use './button-protected-theme';\n@use './button-ripple';\n@use './button-text';\n@use './button-text-theme';\n@use './button-theme';\n@use './button-shared-theme';\n\n// For backwards compatibility.\n@forward './button-base' show deprecated-base, base, icon, icon-trailing, icon-svg, icon-contained, icon-contained-trailing, deprecated-icon, deprecated-icon-trailing, deprecated-icon-svg, deprecated-icon-contained, deprecated-icon-contained-trailing;\n\n@mixin styles($query: feature-targeting.all()) {\n  @include static-styles($query: $query);\n  @include theme-styles($query: $query);\n}\n\n@mixin theme-styles($query: feature-targeting.all()) {\n  .mdc-button {\n    @include button-text-theme.theme-styles(\n      button-text-theme.$light-theme,\n      $query: $query\n    );\n  }\n  .mdc-button--unelevated {\n    @include button-filled-theme.theme-styles(\n      button-filled-theme.$light-theme,\n      $query: $query\n    );\n  }\n  .mdc-button--raised {\n    @include button-protected-theme.theme-styles(\n      button-protected-theme.$light-theme,\n      $query: $query\n    );\n  }\n  .mdc-button--outlined {\n    @include button-outlined-theme.theme-styles(\n      button-outlined-theme.$light-theme,\n      $query: $query\n    );\n  }\n}\n\n@mixin static-styles($query: feature-targeting.all()) {\n  @include static-styles-without-ripple($query: $query);\n  @include button-ripple.static-styles($query: $query);\n}\n\n@mixin static-styles-without-ripple($query: feature-targeting.all()) {\n  @include button-base.static-styles($query: $query);\n  @include button-text.static-styles-without-ripple($query: $query);\n  @include button-filled.static-styles-without-ripple($query: $query);\n  @include button-protected.static-styles-without-ripple($query: $query);\n  @include button-outlined.static-styles-without-ripple($query: $query);\n}\n\n// Legacy mixins\n\n@mixin without-ripple($query: feature-targeting.all()) {\n  @include button-base.deprecated-static-styles-without-ripple($query: $query);\n  @include button-text.static-styles-without-ripple($query: $query);\n  @include button-filled.static-styles-without-ripple($query: $query);\n  @include button-protected.static-styles-without-ripple($query: $query);\n  @include button-outlined.static-styles-without-ripple($query: $query);\n  @include _theme-styles-without-ripple($query: $query);\n}\n\n@mixin _theme-styles-without-ripple($query: feature-targeting.all()) {\n  @include button-text-theme.deprecated-theme-styles($query: $query);\n  @include button-filled-theme.deprecated-theme-styles($query: $query);\n  @include button-outlined-theme.deprecated-theme-styles($query: $query);\n  // Elevation has been recategorized into theme styles. Keeping old elevation\n  // styles here for backwards compatibility.\n  .mdc-button--raised {\n    @include button-protected.raised($query: $query);\n  }\n}\n\n// @deprecated - use styles() instead.\n@mixin core-styles($query: feature-targeting.all()) {\n  @include styles($query: $query);\n  // TODO: remove this rule since padding now takes care of icon margins.\n  $feat-structure: feature-targeting.create-target($query, structure);\n  .mdc-button--raised,\n  .mdc-button--unelevated,\n  .mdc-button--outlined {\n    .mdc-button__icon {\n      @include feature-targeting.targets($feat-structure) {\n        // Icons inside contained buttons have different styles due to increased button padding\n        @include button-base.icon-contained;\n      }\n    }\n\n    .mdc-button__label + .mdc-button__icon {\n      @include feature-targeting.targets($feat-structure) {\n        @include button-base.icon-contained-trailing;\n      }\n    }\n  }\n}\n", "//\n// Copyright 2016 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:color';\n@use 'sass:map';\n@use '@material/animation/functions' as functions2;\n@use '@material/animation/variables' as variables2;\n@use '@material/base/mixins' as base-mixins;\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/css';\n@use '@material/theme/custom-properties';\n@use '@material/theme/theme';\n@use '@material/theme/keys';\n@use '@material/theme/shadow-dom';\n@use '@material/theme/theme-color';\n\n$custom-property-prefix: 'ripple';\n\n$fade-in-duration: 75ms !default;\n$fade-out-duration: 150ms !default;\n$translate-duration: 225ms !default;\n$states-wash-duration: 15ms !default;\n\n// Notes on states:\n// * focus takes precedence over hover (i.e. if an element is both focused and hovered, only focus value applies)\n// * press state applies to a separate pseudo-element, so it has an additive effect on top of other states\n// * selected/activated are applied additively to hover/focus via calculations at preprocessing time\n\n$dark-ink-opacities: (\n  hover: 0.04,\n  focus: 0.12,\n  press: 0.12,\n  selected: 0.08,\n  activated: 0.12,\n) !default;\n\n$light-ink-opacities: (\n  hover: 0.08,\n  focus: 0.24,\n  press: 0.24,\n  selected: 0.16,\n  activated: 0.24,\n) !default;\n\n// Legacy\n\n$pressed-dark-ink-opacity: 0.16 !default;\n$pressed-light-ink-opacity: 0.32 !default;\n\n// State selector variables used for state selector mixins below.\n$_hover-selector: '&:hover';\n$_focus-selector: '&.mdc-ripple-upgraded--background-focused, &:not(.mdc-ripple-upgraded):focus';\n$_active-selector: '&:not(:disabled):active';\n\n$light-theme: (\n  focus-state-layer-color: theme-color.$on-surface,\n  focus-state-layer-opacity: map.get($dark-ink-opacities, focus),\n  hover-state-layer-color: theme-color.$on-surface,\n  hover-state-layer-opacity: map.get($dark-ink-opacities, hover),\n  pressed-state-layer-color: theme-color.$on-surface,\n  pressed-state-layer-opacity: map.get($dark-ink-opacities, press),\n);\n\n@mixin theme($theme) {\n  @include keys.declare-custom-properties(\n    $theme,\n    $prefix: $custom-property-prefix\n  );\n\n  @if shadow-dom.$css-selector-fallback-declarations {\n    .mdc-ripple-surface {\n      @include theme-styles($theme);\n    }\n  }\n}\n\n$_ripple-theme: (\n  hover-state-layer-color: null,\n  focus-state-layer-color: null,\n  pressed-state-layer-color: null,\n  hover-state-layer-opacity: null,\n  focus-state-layer-opacity: null,\n  pressed-state-layer-opacity: null,\n);\n\n@mixin theme-styles($theme, $ripple-target: '&') {\n  $theme: keys.create-theme-properties(\n    $theme,\n    $prefix: $custom-property-prefix\n  );\n\n  // TODO(b/191298796): Support states layer color for every interactive states.\n  // Use only hover state layer color, ignoring focus and pressed color.\n  // Consider replacing states-base-color with states-colors.\n  @include internal-theme-styles($theme, $ripple-target);\n}\n\n@mixin internal-theme-styles($theme, $ripple-target: '&') {\n  @include theme.validate-theme-styles($_ripple-theme, $theme);\n\n  @include states-base-color(\n    map.get($theme, hover-state-layer-color),\n    $ripple-target: $ripple-target\n  );\n  @include states-hover-opacity(\n    map.get($theme, hover-state-layer-opacity),\n    $ripple-target: $ripple-target\n  );\n  @include states-focus-opacity(\n    map.get($theme, focus-state-layer-opacity),\n    $ripple-target: $ripple-target\n  );\n  @include states-press-opacity(\n    map.get($theme, pressed-state-layer-opacity),\n    $ripple-target: $ripple-target\n  );\n}\n\n/// @deprecated Use states-colors instead.\n@mixin states-base-color(\n  $color,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $color {\n    @if not custom-properties.is-custom-prop($color) {\n      $color: custom-properties.create(\n        ripple-color,\n        theme-color.get-custom-property($color)\n      );\n    }\n\n    #{$ripple-target}::before,\n    #{$ripple-target}::after {\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(background-color, $color);\n      }\n    }\n  }\n}\n\n///\n/// Customizes ripple color in `hover` and `press` states\n/// @param {map} $color-map - map specifying custom opacity of zero or more states\n/// @param {string} $ripple-target - the optional selector for the ripple element\n///\n@mixin states-colors(\n  $color-map: (),\n  $ripple-target: '&',\n  $query: feature-targeting.all()\n) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if map.get($color-map, hover) {\n    @include hover-state-layer-selector($ripple-target) {\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(background-color, map.get($color-map, hover));\n      }\n    }\n  }\n\n  @if map.get($color-map, press) {\n    @include press-state-layer-selector($ripple-target) {\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(background-color, map.get($color-map, press));\n      }\n    }\n  }\n}\n\n///\n/// Customizes ripple opacities in `hover`, `focus`, or `press` states\n/// @param {map} $opacity-map - map specifying custom opacity of zero or more states\n/// @param {bool} $has-nested-focusable-element - whether the component contains a focusable element in the root\n/// @param {string} $ripple-target - the optional selector for the ripple element\n///\n@mixin states-opacities(\n  $opacity-map: (),\n  $has-nested-focusable-element: false,\n  $ripple-target: '&',\n  $query: feature-targeting.all()\n) {\n  // Ensure sufficient specificity to override base state opacities\n  @if map.get($opacity-map, hover) {\n    @include states-hover-opacity(\n      map.get($opacity-map, hover),\n      $ripple-target: $ripple-target,\n      $query: $query\n    );\n  }\n\n  @if map.get($opacity-map, focus) {\n    @include states-focus-opacity(\n      map.get($opacity-map, focus),\n      $ripple-target: $ripple-target,\n      $has-nested-focusable-element: $has-nested-focusable-element,\n      $query: $query\n    );\n  }\n\n  @if map.get($opacity-map, press) {\n    @include states-press-opacity(\n      map.get($opacity-map, press),\n      $ripple-target: $ripple-target,\n      $query: $query\n    );\n  }\n}\n\n@mixin states-hover-opacity(\n  $opacity,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $opacity and not custom-properties.is-custom-prop($opacity) {\n    $opacity: custom-properties.create(ripple-hover-opacity, $opacity);\n  }\n\n  // Background wash styles, for both CSS-only and upgraded stateful surfaces\n  &:hover,\n  &.mdc-ripple-surface--hover {\n    @include states-background-selector($ripple-target) {\n      // Opacity falls under color because the chosen opacity is color-dependent in typical usage\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(opacity, $opacity);\n      }\n    }\n  }\n}\n\n@mixin states-focus-opacity(\n  $opacity,\n  $has-nested-focusable-element: false,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  // Focus overrides hover by reusing the ::before pseudo-element.\n  // :focus-within generally works on non-MS browsers and matches when a *child* of the element has focus.\n  // It is useful for cases where a component has a focusable element within the root node, e.g. text field,\n  // but undesirable in general in case of nested stateful components.\n  // We use a modifier class for JS-enabled surfaces to support all use cases in all browsers.\n  @if $has-nested-focusable-element {\n    // JS-enabled selectors.\n    &.mdc-ripple-upgraded--background-focused,\n    // CSS-only selectors.\n    &:not(.mdc-ripple-upgraded):focus,\n    &:focus-within {\n      @include states-background-selector($ripple-target) {\n        @include states-focus-opacity-properties_(\n          $opacity: $opacity,\n          $query: $query\n        );\n      }\n    }\n  } @else {\n    // JS-enabled selectors.\n    &.mdc-ripple-upgraded--background-focused,\n    // CSS-only selectors.\n    &:not(.mdc-ripple-upgraded):focus {\n      @include states-background-selector($ripple-target) {\n        @include states-focus-opacity-properties_(\n          $opacity: $opacity,\n          $query: $query\n        );\n      }\n    }\n  }\n}\n\n@mixin states-focus-opacity-properties_($opacity, $query) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  // Opacity falls under color because the chosen opacity is color-dependent in typical usage\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $opacity {\n    @if not custom-properties.is-custom-prop($opacity) {\n      $opacity: custom-properties.create(ripple-focus-opacity, $opacity);\n    }\n\n    // Note that this duration is only effective on focus, not blur\n    @include feature-targeting.targets($feat-animation) {\n      transition-duration: 75ms;\n    }\n\n    @include feature-targeting.targets($feat-color) {\n      @include theme.property(opacity, $opacity);\n    }\n  }\n}\n\n@mixin states-press-opacity(\n  $opacity,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-animation: feature-targeting.create-target($query, animation);\n  $feat-color: feature-targeting.create-target($query, color);\n\n  // Styles for non-upgraded (CSS-only) stateful surfaces\n\n  @if $opacity {\n    @if not custom-properties.is-custom-prop($opacity) {\n      $opacity: custom-properties.create(ripple-press-opacity, $opacity);\n    }\n\n    &:not(.mdc-ripple-upgraded) {\n      @include press-state-layer-selector($ripple-target) {\n        @include feature-targeting.targets($feat-animation) {\n          transition: opacity $fade-out-duration linear;\n        }\n      }\n\n      &:active {\n        @include press-state-layer-selector($ripple-target) {\n          @include feature-targeting.targets($feat-animation) {\n            transition-duration: $fade-in-duration;\n          }\n\n          // Opacity falls under color because the chosen opacity is color-dependent in typical usage\n          @include feature-targeting.targets($feat-color) {\n            @include theme.property(opacity, $opacity);\n          }\n        }\n      }\n    }\n\n    &.mdc-ripple-upgraded {\n      @include feature-targeting.targets($feat-color) {\n        // Upgraded ripple should always emit custom property, regardless of\n        // configuration, since ripple itself feature detects custom property\n        // support at runtime.\n        @include custom-properties.configure($emit-custom-properties: true) {\n          @include theme.property(\n            custom-properties.create(ripple-fg-opacity, $opacity)\n          );\n        }\n      }\n    }\n  }\n}\n\n// Simple mixin for base states which automatically selects opacity values based on whether the ink color is\n// light or dark.\n@mixin states(\n  $color: theme-color.prop-value(on-surface),\n  $has-nested-focusable-element: false,\n  $query: feature-targeting.all(),\n  $ripple-target: '&',\n  $opacity-map: null\n) {\n  @include states-interactions_(\n    $color: $color,\n    $has-nested-focusable-element: $has-nested-focusable-element,\n    $query: $query,\n    $ripple-target: $ripple-target,\n    $opacity-map: $opacity-map\n  );\n}\n\n// Simple mixin for activated states which automatically selects opacity values based on whether the ink color is\n// light or dark.\n@mixin states-activated(\n  $color,\n  $has-nested-focusable-element: false,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-color: feature-targeting.create-target($query, color);\n  $activated-opacity: states-opacity($color, activated);\n\n  &--activated {\n    // Stylelint seems to think that '&' qualifies as a type selector here?\n    @include states-background-selector($ripple-target) {\n      // Opacity falls under color because the chosen opacity is color-dependent.\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(\n          opacity,\n          custom-properties.create(\n            --mdc-ripple-activated-opacity,\n            $activated-opacity\n          )\n        );\n      }\n    }\n\n    @include states-interactions_(\n      $color: $color,\n      $has-nested-focusable-element: $has-nested-focusable-element,\n      $opacity-modifier: $activated-opacity,\n      $query: $query,\n      $ripple-target: $ripple-target\n    );\n  }\n}\n\n// Simple mixin for selected states which automatically selects opacity values based on whether the ink color is\n// light or dark.\n@mixin states-selected(\n  $color,\n  $has-nested-focusable-element: false,\n  $query: feature-targeting.all(),\n  $ripple-target: '&'\n) {\n  $feat-color: feature-targeting.create-target($query, color);\n  $selected-opacity: states-opacity($color, selected);\n\n  &--selected {\n    @include states-background-selector($ripple-target) {\n      // Opacity falls under color because the chosen opacity is color-dependent.\n      @include feature-targeting.targets($feat-color) {\n        @include theme.property(\n          opacity,\n          custom-properties.create(\n            --mdc-ripple-selected-opacity,\n            $selected-opacity\n          )\n        );\n      }\n    }\n\n    @include states-interactions_(\n      $color: $color,\n      $has-nested-focusable-element: $has-nested-focusable-element,\n      $opacity-modifier: $selected-opacity,\n      $query: $query,\n      $ripple-target: $ripple-target\n    );\n  }\n}\n\n@mixin states-interactions_(\n  $color,\n  $has-nested-focusable-element,\n  $opacity-modifier: 0,\n  $query: feature-targeting.all(),\n  $ripple-target: '&',\n  $opacity-map: null\n) {\n  @include target-selector($ripple-target) {\n    @include states-base-color($color, $query);\n  }\n\n  @if $opacity-map == null {\n    $opacity-map: (\n      hover: states-opacity($color, hover) + $opacity-modifier,\n      focus: states-opacity($color, focus) + $opacity-modifier,\n      press: states-opacity($color, press) + $opacity-modifier,\n    );\n  }\n\n  @include states-opacities(\n    $opacity-map,\n    $has-nested-focusable-element: $has-nested-focusable-element,\n    $ripple-target: $ripple-target,\n    $query: $query\n  );\n}\n\n// Wraps content in the `ripple-target` selector if it exists.\n@mixin target-selector($ripple-target: '&') {\n  @if $ripple-target == '&' {\n    @content;\n  } @else {\n    #{$ripple-target} {\n      @content;\n    }\n  }\n}\n\n/// Selector for hover, active and focus states.\n@mixin states-selector() {\n  #{$_hover-selector},\n  #{$_focus-selector},\n  #{$_active-selector} {\n    @content;\n  }\n}\n\n@mixin hover() {\n  #{$_hover-selector} {\n    @content;\n  }\n}\n\n// Selector for focus state. Using ':not(.mdc-ripple-upgraded)' to continue\n// applying focus styles on JS-disabled components, and control focus\n// on JS-enabled components with '.mdc-ripple-upgraded--background-focused'.\n@mixin focus() {\n  #{$_focus-selector} {\n    @content;\n  }\n}\n\n// Selector for active state. Using `:active:active` to override focus styles.\n@mixin pressed() {\n  #{$_active-selector} {\n    @content;\n  }\n}\n\n// @deprecated Use `pressed()` mixin - renamed for consistency.\n@mixin active() {\n  @include pressed() {\n    @content;\n  }\n}\n\n/// Keep the ripple (State overlay) behind the content.\n@mixin behind-content(\n  $ripple-target,\n  $content-root-selector: '&',\n  $query: feature-targeting.all()\n) {\n  // Needed for IE11. Without this, IE11 renders the state layer completely\n  // underneath the container, making it invisible.\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  #{$content-root-selector} {\n    @include feature-targeting.targets($feat-structure) {\n      z-index: 0;\n    }\n  }\n\n  #{$ripple-target}::before,\n  #{$ripple-target}::after {\n    @include feature-targeting.targets($feat-structure) {\n      @include theme.property(\n        z-index,\n        custom-properties.create(--mdc-ripple-z-index, -1)\n      );\n    }\n  }\n}\n\n@function states-opacity($color, $state) {\n  $color-value: theme-color.prop-value($color);\n  $opacity-map: if(\n    theme-color.tone($color-value) == 'light',\n    $light-ink-opacities,\n    $dark-ink-opacities\n  );\n\n  @if not map.has-key($opacity-map, $state) {\n    @error \"Invalid state: '#{$state}'. Choose one of: #{map.keys($opacity-map)}\";\n  }\n\n  @return map.get($opacity-map, $state);\n}\n\n/// @deprecated Use hover-state-layer-selector instead.\n@mixin states-background-selector($ripple-target) {\n  @include hover-state-layer-selector($ripple-target) {\n    @content;\n  }\n}\n\n@mixin hover-state-layer-selector($ripple-target) {\n  #{$ripple-target}::before {\n    @content;\n  }\n}\n\n@mixin press-state-layer-selector($ripple-target) {\n  #{$ripple-target}::after {\n    @content;\n  }\n}\n", "//\n// Copyright 2021 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n// stylelint-disable selector-class-pattern --\n// Selector '.mdc-*' should only be used in this project.\n\n@use 'sass:map';\n@use 'sass:math';\n@use '@material/feature-targeting/feature-targeting';\n@use '@material/theme/custom-properties';\n@use '@material/theme/keys';\n@use '@material/theme/state';\n@use '@material/theme/theme';\n@use '@material/theme/theme-color';\n@use '@material/tokens/resolvers';\n@use '@material/ripple/ripple-theme';\n@use './button-base';\n@use './button-shared-theme';\n@use './button-ripple';\n\n$outlined-border-width: 1px !default;\n$outline-color: rgba(theme-color.prop-value(on-surface), 0.12) !default;\n\n$custom-property-prefix: 'outlined-button';\n\n$light-theme: (\n  container-height: button-shared-theme.$height,\n  container-shape: button-shared-theme.$shape-radius,\n  disabled-label-text-color: button-shared-theme.$disabled-ink-color,\n  disabled-outline-color: button-shared-theme.$disabled-container-color,\n  focus-label-text-color: null,\n  focus-outline-color: null,\n  focus-state-layer-color: primary,\n  focus-state-layer-opacity: 0.12,\n  hover-label-text-color: null,\n  hover-outline-color: null,\n  hover-state-layer-color: primary,\n  hover-state-layer-opacity: 0.04,\n  /// Prevent the increased touch target from being reseted if the\n  /// container-height differs from the default (36px)\n  keep-touch-target: false,\n  label-text-color: primary,\n  label-text-font: button-font-family,\n  label-text-size: button-font-size,\n  label-text-tracking: button-letter-spacing,\n  label-text-transform: button-text-transform,\n  label-text-weight: button-font-weight,\n  outline-color: $outline-color,\n  outline-width: $outlined-border-width,\n  pressed-label-text-color: null,\n  pressed-outline-color: null,\n  pressed-state-layer-color: primary,\n  pressed-state-layer-opacity: 0.12,\n  with-icon-icon-color: null,\n  with-icon-icon-size: 18px,\n  with-icon-hover-icon-color: null,\n  with-icon-focus-icon-color: null,\n  with-icon-pressed-icon-color: null,\n  with-icon-disabled-icon-color: null,\n  focus-ring-color: null,\n  focus-ring-offset: 0,\n);\n\n/// Sets theme based on provided theme configuration.\n/// Only emits theme related styles.\n/// @param {Map} $theme - Theme configuration to use.\n@mixin theme($theme, $resolver: resolvers.$material) {\n  @include theme.validate-theme($light-theme, $theme);\n  $theme: button-shared-theme.resolve-theme-elevation-keys(\n    $theme,\n    $resolver: $resolver\n  );\n  @include keys.declare-custom-properties($theme, $custom-property-prefix);\n}\n\n@mixin theme-styles(\n  $theme,\n  $resolver: resolvers.$material,\n  $query: feature-targeting.all()\n) {\n  @include theme.validate-theme-styles($light-theme, $theme);\n  $theme: keys.create-theme-properties(\n    $theme,\n    $prefix: $custom-property-prefix\n  );\n  @include _theme($theme, $resolver, $query: $query);\n}\n\n@mixin _theme($theme, $resolver, $query) {\n  @include button-shared-theme.theme-styles($theme, $resolver, $query: $query);\n  @include outline-color(\n    (\n      default: map.get($theme, outline-color),\n      disabled: map.get($theme, disabled-outline-color),\n      focus: map.get($theme, focus-outline-color),\n      hover: map.get($theme, hover-outline-color),\n      pressed: map.get($theme, pressed-outline-color),\n    ),\n    $query: $query\n  );\n  @include outline-width(map.get($theme, outline-width), $query: $query);\n}\n\n@mixin deprecated-theme-styles($query: feature-targeting.all()) {\n  .mdc-button--outlined {\n    $theme: map.merge(\n      $light-theme,\n      (\n        focus-state-layer-color: null,\n        focus-state-layer-opacity: null,\n        hover-state-layer-color: null,\n        hover-state-layer-opacity: null,\n        pressed-state-layer-color: null,\n        pressed-state-layer-opacity: null,\n        label-text-font: null,\n        label-text-size: null,\n        label-text-tracking: null,\n        label-text-transform: null,\n        label-text-weight: null,\n      )\n    );\n    @include _theme($theme, resolvers.$material, $query: $query);\n  }\n}\n\n///\n/// Sets the outline color to the given color for an enabled button.\n/// @param {Color} $color-or-map - The desired outline color, specified either\n///     as a flat value or a map of colors with states\n///     {default, hover, focused, pressed, disabled} as keys.\n///\n@mixin outline-color($color-or-map, $query: feature-targeting.all()) {\n  &:not(:disabled) {\n    @include _outline-color(\n      state.get-default-state($color-or-map),\n      $query: $query\n    );\n\n    &:hover {\n      @include _outline-color(\n        state.get-hover-state($color-or-map),\n        $query: $query\n      );\n    }\n\n    @include ripple-theme.focus() {\n      @include _outline-color(\n        state.get-focus-state($color-or-map),\n        $query: $query\n      );\n    }\n\n    // Increase active state specificity due to ripple-theme.focus().\n    &:active,\n    &:focus:active {\n      @include _outline-color(\n        state.get-pressed-state($color-or-map),\n        $query: $query\n      );\n    }\n  }\n\n  &:disabled {\n    @include _outline-color(\n      state.get-disabled-state($color-or-map),\n      $query: $query\n    );\n  }\n}\n\n///\n/// Sets the outline color to the given color for a disabled button.\n/// @param {Color} $color - The desired outline color.\n/// @deprecated - call `outline-color` instead with `disabled` as a map key.\n///\n@mixin disabled-outline-color($color, $query: feature-targeting.all()) {\n  @include outline-color(\n    (\n      disabled: $color,\n    ),\n    $query: $query\n  );\n}\n\n@mixin outline-width(\n  $outline-width,\n  $padding: button-shared-theme.$contained-horizontal-padding,\n  // For a button with an icon, the padding on the side of the button with the\n  // icon.\n  $padding-icon: button-shared-theme.$contained-horizontal-padding-icon,\n  $query: feature-targeting.all()\n) {\n  $feat-structure: feature-targeting.create-target($query, structure);\n\n  @if $outline-width != null {\n    $outline-width-value: if(\n      custom-properties.is-custom-prop($outline-width),\n      custom-properties.get-fallback($outline-width),\n      $outline-width\n    );\n    // TODO(b/194792044): uncouple padding from outline-width\n    // Note: Adjust padding to maintain consistent width with non-outlined buttons\n    $padding-value: math.max($padding - $outline-width-value, 0);\n    $padding-icon-value: math.max($padding-icon - $outline-width-value, 0);\n\n    @include button-shared-theme.horizontal-padding(\n      $padding: $padding-value,\n      $padding-icon: $padding-icon-value,\n      $query: $query\n    );\n\n    @include feature-targeting.targets($feat-structure) {\n      @include theme.property(border-width, $outline-width);\n    }\n\n    #{button-ripple.$ripple-target} {\n      @include feature-targeting.targets($feat-structure) {\n        @include theme.property(top, -1 * $outline-width-value);\n        @include theme.property(left, -1 * $outline-width-value);\n        @include theme.property(bottom, -1 * $outline-width-value);\n        @include theme.property(right, -1 * $outline-width-value);\n        @include theme.property(border-width, $outline-width);\n      }\n    }\n    .mdc-button__touch {\n      @include feature-targeting.targets($feat-structure) {\n        @include theme.property(\n          left,\n          'calc(-1 * outline-width)',\n          $replace: (outline-width: $outline-width)\n        );\n        @include theme.property(\n          width,\n          'calc(100% + 2 * outline-width)',\n          $replace: (outline-width: $outline-width)\n        );\n      }\n    }\n  }\n}\n\n///\n/// Sets the outline color to the given color. This mixin should be\n/// wrapped in a selector that qualifies button state.\n/// @access private\n///\n@mixin _outline-color($color, $query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  @if $color {\n    @include feature-targeting.targets($feat-color) {\n      @include theme.property(border-color, $color);\n    }\n  }\n}\n"], "sourceRoot": ""}