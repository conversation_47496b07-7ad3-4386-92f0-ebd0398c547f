//
// !!! THIS FILE WAS AUTOMATICALLY GENERATED !!!
// !!! DO NOT MODIFY IT BY HAND !!!
// Design system display name: Google Material 3
// Design system version: v0.161
//

@use 'sass:map';

@use './md-sys-color';

@use './md-sys-elevation';

@use './md-sys-shape';

@use './md-sys-typescale';

$_default: (
  'md-sys-color': md-sys-color.values-light(),
  'md-sys-elevation': md-sys-elevation.values(),
  'md-sys-shape': md-sys-shape.values(),
  'md-sys-typescale': md-sys-typescale.values(),
);

@function values($deps: $_default, $exclude-hardcoded-values: false) {
  @return (
    'container-color': map.get($deps, 'md-sys-color', 'surface'),
    'container-elevation': map.get($deps, 'md-sys-elevation', 'level1'),
    'container-shape': map.get($deps, 'md-sys-shape', 'corner-none'),
    'container-surface-tint-layer-color':
      map.get($deps, 'md-sys-color', 'surface-tint'),
    'desktop-with-single-line-container-height':
      if($exclude-hardcoded-values, null, 52px),
    'desktop-with-three-lines-container-height':
      if($exclude-hardcoded-values, null, 90px),
    'desktop-with-two-lines-with-image-container-height':
      if($exclude-hardcoded-values, null, 72px),
    'mobile-with-single-line-container-height':
      if($exclude-hardcoded-values, null, 54px),
    'mobile-with-two-lines-container-height':
      if($exclude-hardcoded-values, null, 112px),
    'mobile-with-two-lines-with-image-container-height':
      if($exclude-hardcoded-values, null, 120px),
    'supporting-text-color':
      map.get($deps, 'md-sys-color', 'on-surface-variant'),
    'supporting-text-font':
      map.get($deps, 'md-sys-typescale', 'body-medium-font'),
    'supporting-text-line-height':
      map.get($deps, 'md-sys-typescale', 'body-medium-line-height'),
    'supporting-text-size':
      map.get($deps, 'md-sys-typescale', 'body-medium-size'),
    'supporting-text-tracking':
      map.get($deps, 'md-sys-typescale', 'body-medium-tracking'),
    'supporting-text-type':
      if(
        $exclude-hardcoded-values,
        null,
        /** Warning: risk of reduced fidelity from using this composite typography token. Tokens md.comp.banner.supporting-text.tracking cannot be represented in the "font" property shorthand. Consider using the discrete properties instead. */
          map.get($deps, 'md-sys-typescale', 'body-medium-weight')
          map.get($deps, 'md-sys-typescale', 'body-medium-size') #{'/'} map.get(
            $deps,
            'md-sys-typescale',
            'body-medium-line-height'
          ) map.get($deps, 'md-sys-typescale', 'body-medium-font')
      ),
    'supporting-text-weight':
      map.get($deps, 'md-sys-typescale', 'body-medium-weight'),
    'with-image-image-shape': map.get($deps, 'md-sys-shape', 'corner-full'),
    'with-image-image-size': if($exclude-hardcoded-values, null, 40px)
  );
}
