"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */function patchLongStackTrace(t){const n="\n",e={},a="__creationTrace__",r="STACKTRACE TRACKING",c="__SEP_TAG__";let o=c+"@[native]";class s{constructor(){this.error=f(),this.timestamp=new Date}}function i(){return new Error(r)}function l(){try{throw i()}catch(t){return t}}const _=i(),u=l(),f=_.stack?i:u.stack?l:i;function k(t){return t.stack?t.stack.split(n):[]}function h(t,n){let a=k(n);for(let n=0;n<a.length;n++)e.hasOwnProperty(a[n])||t.push(a[n])}function T(t,e){const a=[e?e.trim():""];if(t){let n=(new Date).getTime();for(let e=0;e<t.length;e++){const r=t[e],s=r.timestamp;let i=`____________________Elapsed ${n-s.getTime()} ms; At: ${s}`;i=i.replace(/[^\w\d]/g,"_"),a.push(o.replace(c,i)),h(a,r.error),n=s.getTime()}}return a.join(n)}function g(){return Error.stackTraceLimit>0}function d(t,n){n>0&&(t.push(k((new s).error)),d(t,n-1))}t.longStackTraceZoneSpec={name:"long-stack-trace",longStackTraceLimit:10,getLongStackTrace:function(n){if(!n)return;const e=n[t.__symbol__("currentTaskTrace")];return e?T(e,n.stack):n.stack},onScheduleTask:function(n,e,r,c){if(g()){const n=t.currentTask;let e=n&&n.data&&n.data[a]||[];e=[new s].concat(e),e.length>this.longStackTraceLimit&&(e.length=this.longStackTraceLimit),c.data||(c.data={}),"eventTask"===c.type&&(c.data={...c.data}),c.data[a]=e}return n.scheduleTask(r,c)},onHandleError:function(n,e,r,c){if(g()){const n=t.currentTask||c.task;if(c instanceof Error&&n){const t=T(n.data&&n.data[a],c.stack);try{c.stack=c.longStack=t}catch(t){}}}return n.handleError(r,c)}},function m(){if(!g())return;const t=[];d(t,2);const n=t[0],a=t[1];for(let t=0;t<n.length;t++){const e=n[t];if(-1==e.indexOf(r)){let t=e.match(/^\s*at\s+/);if(t){o=t[0]+c+" (http://localhost)";break}}}for(let t=0;t<n.length;t++){const r=n[t];if(r!==a[t])break;e[r]=!0}}()}patchLongStackTrace(Zone);