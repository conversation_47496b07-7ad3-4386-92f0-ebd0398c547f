<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf8">
    <title>Unit tests</title>

    <script type="module">
      (async () => {
        // To initialize tests correctly we load things in a very particular order.

        // Step 1. Load user polyfills (including `zone.js`). Does *not* include `zone.js/testing`, which gets executed after Jasmine.
        await import('./polyfills.js');

        // Step 2. Import Jasmine.
        // Jasmine gets wrapped into a CommonJS context by the bundling process which makes it think it is running in NodeJS, so it does not
        // find the `window` global. Assign this to the NodeJS `global` symbol so Jasmine initializes correctly.
        window.global = window;
        const { default: jasmineRequire } = await import('./jasmine.js');
        delete window.global; // Avoid leaking `global` into user tests or libraries, which might think they are running in NodeJS.

        // Step 3. Initialize Jasmine on the page. Doing this after `zone.js` means Zone can patch browser globals before Jasmine runs.
        // Doing this before `zone.js/testing`, means Zone can patch Jasmine-defined globals.
        const jasmine = jasmineRequire.core(jasmineRequire);
        const jasmineGlobal = jasmine.getGlobal();
        jasmineGlobal.jasmine = jasmine;
        const jasmineEnv = jasmine.getEnv();
        Object.assign(window, jasmineRequire.interface(jasmine, jasmineEnv));

        // Step 4. Import `zone.js/testing`, which will find and patch Jasmine globals from steps 2. and 3.
        // https://github.com/angular/angular/blob/af4f5df150d527a1b523def1eb51d2b661a25f83/packages/zone.js/lib/jasmine/jasmine.ts
        await import('./testing.js');

        // Step 5. Run the actual tests.
        const { runJasmineTests } = await import('./jasmine_runner.js');
        await runJasmineTests(jasmineEnv);
      })();
    </script>
  </head>
  <body></body>
</html>
