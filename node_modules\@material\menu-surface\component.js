/**
 * @license
 * Copyright 2018 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
import { __extends } from "tslib";
import { getCorrectPropertyName } from '@material/animation/util';
import { MDCComponent } from '@material/base/component';
import { cssClasses, strings } from './constants';
import { MDCMenuSurfaceFoundation } from './foundation';
/** MDC Menu Surface */
var MDCMenuSurface = /** @class */ (function (_super) {
    __extends(MDCMenuSurface, _super);
    function MDCMenuSurface() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    MDCMenuSurface.attachTo = function (root) {
        return new MDCMenuSurface(root);
    };
    MDCMenuSurface.prototype.initialSyncWithDOM = function () {
        var _this = this;
        var parentEl = this.root.parentElement;
        this.anchorElement =
            parentEl && parentEl.classList.contains(cssClasses.ANCHOR) ? parentEl :
                null;
        if (this.root.classList.contains(cssClasses.FIXED)) {
            this.setFixedPosition(true);
        }
        this.handleKeydown = function (event) {
            _this.foundation.handleKeydown(event);
        };
        this.handleBodyClick = function (event) {
            _this.foundation.handleBodyClick(event);
        };
        // capture so that no race between handleBodyClick and quickOpen when
        // menusurface opened on button click which registers this listener
        this.registerBodyClickListener = function () {
            document.body.addEventListener('click', _this.handleBodyClick, { capture: true });
        };
        this.deregisterBodyClickListener = function () {
            document.body.removeEventListener('click', _this.handleBodyClick, { capture: true });
        };
        this.listen('keydown', this.handleKeydown);
        this.listen(strings.OPENED_EVENT, this.registerBodyClickListener);
        this.listen(strings.CLOSED_EVENT, this.deregisterBodyClickListener);
    };
    MDCMenuSurface.prototype.destroy = function () {
        this.unlisten('keydown', this.handleKeydown);
        this.unlisten(strings.OPENED_EVENT, this.registerBodyClickListener);
        this.unlisten(strings.CLOSED_EVENT, this.deregisterBodyClickListener);
        _super.prototype.destroy.call(this);
    };
    MDCMenuSurface.prototype.isOpen = function () {
        return this.foundation.isOpen();
    };
    MDCMenuSurface.prototype.open = function () {
        this.foundation.open();
    };
    MDCMenuSurface.prototype.close = function (skipRestoreFocus) {
        if (skipRestoreFocus === void 0) { skipRestoreFocus = false; }
        this.foundation.close(skipRestoreFocus);
    };
    Object.defineProperty(MDCMenuSurface.prototype, "quickOpen", {
        set: function (quickOpen) {
            this.foundation.setQuickOpen(quickOpen);
        },
        enumerable: false,
        configurable: true
    });
    /**
     * Sets the foundation to use page offsets for a positioning when the menu is
     * hoisted to the body.
     */
    MDCMenuSurface.prototype.setIsHoisted = function (isHoisted) {
        this.foundation.setIsHoisted(isHoisted);
    };
    /** Sets the element that the menu-surface is anchored to. */
    MDCMenuSurface.prototype.setMenuSurfaceAnchorElement = function (element) {
        this.anchorElement = element;
    };
    /** Sets the menu-surface to position: fixed. */
    MDCMenuSurface.prototype.setFixedPosition = function (isFixed) {
        if (isFixed) {
            this.root.classList.add(cssClasses.FIXED);
        }
        else {
            this.root.classList.remove(cssClasses.FIXED);
        }
        this.foundation.setFixedPosition(isFixed);
    };
    /**
     * Sets the absolute x/y position to position based on. Requires the menu to
     * be hoisted.
     */
    MDCMenuSurface.prototype.setAbsolutePosition = function (x, y) {
        this.foundation.setAbsolutePosition(x, y);
        this.setIsHoisted(true);
    };
    /**
     * @param corner Default anchor corner alignment of top-left surface corner.
     */
    MDCMenuSurface.prototype.setAnchorCorner = function (corner) {
        this.foundation.setAnchorCorner(corner);
    };
    MDCMenuSurface.prototype.setAnchorMargin = function (margin) {
        this.foundation.setAnchorMargin(margin);
    };
    MDCMenuSurface.prototype.getDefaultFoundation = function () {
        var _this = this;
        // DO NOT INLINE this variable. For backward compatibility, foundations take
        // a Partial<MDCFooAdapter>. To ensure we don't accidentally omit any
        // methods, we need a separate, strongly typed adapter variable.
        // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.
        var adapter = {
            addClass: function (className) {
                _this.root.classList.add(className);
            },
            removeClass: function (className) {
                _this.root.classList.remove(className);
            },
            hasClass: function (className) { return _this.root.classList.contains(className); },
            hasAnchor: function () { return !!_this.anchorElement; },
            notifyClose: function () {
                _this.emit(MDCMenuSurfaceFoundation.strings.CLOSED_EVENT, {});
            },
            notifyClosing: function () {
                _this.emit(MDCMenuSurfaceFoundation.strings.CLOSING_EVENT, {});
            },
            notifyOpen: function () {
                _this.emit(MDCMenuSurfaceFoundation.strings.OPENED_EVENT, {});
            },
            notifyOpening: function () {
                _this.emit(MDCMenuSurfaceFoundation.strings.OPENING_EVENT, {});
            },
            isElementInContainer: function (el) { return _this.root.contains(el); },
            isRtl: function () {
                return getComputedStyle(_this.root).getPropertyValue('direction') === 'rtl';
            },
            setTransformOrigin: function (origin) {
                var propertyName = getCorrectPropertyName(window, 'transform') + "-origin";
                _this.root.style.setProperty(propertyName, origin);
            },
            isFocused: function () { return document.activeElement === _this.root; },
            saveFocus: function () {
                _this.previousFocus =
                    document.activeElement;
            },
            restoreFocus: function () {
                if (_this.root.contains(document.activeElement)) {
                    if (_this.previousFocus && _this.previousFocus.focus) {
                        _this.previousFocus.focus();
                    }
                }
            },
            getInnerDimensions: function () {
                return { width: _this.root.offsetWidth, height: _this.root.offsetHeight };
            },
            getAnchorDimensions: function () { return _this.anchorElement ?
                _this.anchorElement.getBoundingClientRect() :
                null; },
            getViewportDimensions: function () {
                return { width: window.innerWidth, height: window.innerHeight };
            },
            getBodyDimensions: function () {
                return {
                    width: document.body.clientWidth,
                    height: document.body.clientHeight
                };
            },
            getWindowScroll: function () {
                return { x: window.pageXOffset, y: window.pageYOffset };
            },
            setPosition: function (position) {
                var rootHTML = _this.root;
                rootHTML.style.left = 'left' in position ? position.left + "px" : '';
                rootHTML.style.right = 'right' in position ? position.right + "px" : '';
                rootHTML.style.top = 'top' in position ? position.top + "px" : '';
                rootHTML.style.bottom =
                    'bottom' in position ? position.bottom + "px" : '';
            },
            setMaxHeight: function (height) {
                _this.root.style.maxHeight = height;
            },
            registerWindowEventHandler: function (eventType, handler) {
                window.addEventListener(eventType, handler);
            },
            deregisterWindowEventHandler: function (eventType, handler) {
                window.removeEventListener(eventType, handler);
            },
        };
        // tslint:enable:object-literal-sort-keys
        return new MDCMenuSurfaceFoundation(adapter);
    };
    return MDCMenuSurface;
}(MDCComponent));
export { MDCMenuSurface };
//# sourceMappingURL=component.js.map