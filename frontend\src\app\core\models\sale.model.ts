import { Product } from './product.model';
import { User } from './user.model';

export interface Sale {
  id: string;
  saleNumber: string;
  workerId: string;
  customerId?: string;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  status: SaleStatus;
  createdAt: string;
  updatedAt: string;
  worker?: User;
  customer?: Customer;
  items?: SaleItem[];
  payments?: Payment[];
}

export interface SaleItem {
  id: string;
  saleId: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  total: number;
  createdAt: string;
  product?: Product;
}

export interface Payment {
  id: string;
  saleId?: string;
  orderId?: string;
  amount: number;
  method: PaymentMethod;
  status: PaymentStatus;
  reference?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Customer {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  country?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  totalPurchases?: number;
  lastPurchase?: string;
}

export enum SaleStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED'
}

export enum PaymentMethod {
  CASH = 'CASH',
  CARD = 'CARD',
  DIGITAL = 'DIGITAL',
  BANK_TRANSFER = 'BANK_TRANSFER'
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export interface CreateSaleRequest {
  customerId?: string;
  items: CreateSaleItemRequest[];
  payments: CreatePaymentRequest[];
  discount?: number;
  notes?: string;
}

export interface CreateSaleItemRequest {
  productId: string;
  quantity: number;
  unitPrice?: number;
  discount?: number;
}

export interface CreatePaymentRequest {
  amount: number;
  method: PaymentMethod;
  reference?: string;
}

export interface SaleFilter {
  workerId?: string;
  customerId?: string;
  status?: SaleStatus;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: string;
}

export interface SalesStats {
  totalSales: number;
  totalRevenue: number;
  totalItems: number;
  averageSaleValue: number;
  period: string;
  dateRange: {
    from: string;
    to: string;
  };
}

export interface Receipt {
  id: string;
  saleId: string;
  receiptNumber: string;
  format: ReceiptFormat;
  content: string;
  createdAt: string;
  sale?: Sale;
}

export enum ReceiptFormat {
  JSON = 'JSON',
  PDF = 'PDF',
  HTML = 'HTML'
}

export interface CartItem {
  product: Product;
  quantity: number;
  unitPrice: number;
  discount: number;
  total: number;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
}
