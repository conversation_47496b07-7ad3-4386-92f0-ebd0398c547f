!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/menu",[],t):"object"==typeof exports?exports.menu=t():(e.mdc=e.mdc||{},e.mdc.menu=t())}(this,function(){return r={},i.m=n=[function(e,t,n){"use strict";(function(e){}).call(this,n(20))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapResourceUrl=t.isResourceUrl=t.createResourceUrl=t.TrustedResourceUrl=void 0,n(0);var i=n(4),o=n(9),s=(r.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},r);function r(e,t){this.privateDoNotAccessOrElseWrappedResourceUrl=e}var a=window.TrustedScriptURL;t.TrustedResourceUrl=null!=a?a:s,t.createResourceUrl=function(e){var t,n=e,r=null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScriptURL(n);return null!=r?r:new s(n,i.secretToken)},t.isResourceUrl=function(e){return e instanceof t.TrustedResourceUrl},t.unwrapResourceUrl=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isScriptURL(e))return e;if(e instanceof s)return e.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapHtml=t.isHtml=t.EMPTY_HTML=t.createHtml=t.SafeHtml=void 0,n(0);var r=n(4),i=n(9),o=(s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},s);function s(e,t){this.privateDoNotAccessOrElseWrappedHtml=e}function a(e,t){return null!=t?t:new o(e,r.secretToken)}var c=window.TrustedHTML;t.SafeHtml=null!=c?c:o,t.createHtml=function(e){var t,n=e;return a(n,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createHTML(n))},t.EMPTY_HTML=function(){var e;return a("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyHTML)}(),t.isHtml=function(e){return e instanceof t.SafeHtml},t.unwrapHtml=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isHTML(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},function(e,t,n){"use strict";function r(e){var t;try{t=new URL(e)}catch(e){return"https:"}return t.protocol}Object.defineProperty(t,"__esModule",{value:!0}),t.restrictivelySanitizeUrl=t.unwrapUrlOrSanitize=t.sanitizeJavascriptUrl=void 0,n(0);var i=["data:","http:","https:","mailto:","ftp:"];function o(e){if("javascript:"!==r(e))return e}t.sanitizeJavascriptUrl=o,t.unwrapUrlOrSanitize=function(e){return o(e)},t.restrictivelySanitizeUrl=function(e){var t=r(e);return void 0!==t&&-1!==i.indexOf(t.toLowerCase())?e:"about:invalid#zClosurez"}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureTokenIsValid=t.secretToken=void 0,t.secretToken={},t.ensureTokenIsValid=function(e){if(e!==t.secretToken)throw new Error("Bad secret")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapScript=t.isScript=t.EMPTY_SCRIPT=t.createScript=t.SafeScript=void 0,n(0);var r=n(4),i=n(9),o=(s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},s);function s(e,t){this.privateDoNotAccessOrElseWrappedScript=e}function a(e,t){return null!=t?t:new o(e,r.secretToken)}var c=window.TrustedScript;t.SafeScript=null!=c?c:o,t.createScript=function(e){var t,n=e;return a(n,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScript(n))},t.EMPTY_SCRIPT=function(){var e;return a("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyScript)}(),t.isScript=function(e){return e instanceof t.SafeScript},t.unwrapScript=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isScript(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertIsTemplateObject=void 0,t.assertIsTemplateObject=function(e,t,n){if(!Array.isArray(e)||!Array.isArray(e.raw)||!t&&1!==e.length)throw new TypeError(n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCFoundation=void 0;var r=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(e){void 0===e&&(e={}),this.adapter=e}t.MDCFoundation=r,t.default=r},function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapAttributePrefix=t.createAttributePrefix=t.SafeAttributePrefix=void 0,n(0);function o(){}var s=n(4);t.SafeAttributePrefix=o;var a,c=(i(u,a=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},u);function u(e,t){var n=a.call(this)||this;return n.privateDoNotAccessOrElseWrappedAttrPrefix=e,n}t.createAttributePrefix=function(e){return new c(e,s.secretToken)},t.unwrapAttributePrefix=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEST_ONLY=t.getTrustedTypesPolicy=t.getTrustedTypes=void 0;var r,i="google#safe";function o(){var e;return""!==i&&null!==(e=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==e?e:null}t.getTrustedTypes=o,t.getTrustedTypesPolicy=function(){var e,t;if(void 0===r)try{r=null!==(t=null===(e=o())||void 0===e?void 0:e.createPolicy(i,{createHTML:function(e){return e},createScript:function(e){return e},createScriptURL:function(e){return e}}))&&void 0!==t?t:null}catch(e){r=null}return r},t.TEST_ONLY={resetDefaults:function(){r=void 0,i="google#safe"},setTrustedTypesPolicyName:function(e){i=e}}},function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyle=t.isStyle=t.createStyle=t.SafeStyle=void 0,n(0);function o(){}var s=n(4);t.SafeStyle=o;var a,c=(i(u,a=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},u);function u(e,t){var n=a.call(this)||this;return n.privateDoNotAccessOrElseWrappedStyle=e,n}t.createStyle=function(e){return new c(e,s.secretToken)},t.isStyle=function(e){return e instanceof c},t.unwrapStyle=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AttributePolicyAction=t.SanitizerTable=void 0;var r,i,o=(s.prototype.isAllowedElement=function(e){return"form"!==e.toLowerCase()&&(this.allowedElements.has(e)||this.elementPolicies.has(e))},s.prototype.getAttributePolicy=function(e,t){var n=this.elementPolicies.get(t);return(null==n?void 0:n.has(e))?n.get(e):this.allowedGlobalAttributes.has(e)?{policyAction:r.KEEP}:this.globalAttributePolicies.get(e)||{policyAction:r.DROP}},s);function s(e,t,n,r){this.allowedElements=e,this.elementPolicies=t,this.allowedGlobalAttributes=n,this.globalAttributePolicies=r}t.SanitizerTable=o,(i=r=t.AttributePolicyAction||(t.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.isStyleSheet=t.createStyleSheet=t.SafeStyleSheet=void 0,n(0);function o(){}var s=n(4);t.SafeStyleSheet=o;var a,c=(i(u,a=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},u);function u(e,t){var n=a.call(this)||this;return n.privateDoNotAccessOrElseWrappedStyleSheet=e,n}t.createStyleSheet=function(e){return new c(e,s.secretToken)},t.isStyleSheet=function(e){return e instanceof c},t.unwrapStyleSheet=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},function(e,t,n){"use strict";var i=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},o=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},s=this&&this.__spreadArray||function(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCComponent=void 0;var a=n(17),c=n(18),r=n(7);var u,l,d=(f.attachTo=function(e){return new f(e,new r.MDCFoundation({}))},f.prototype.initialize=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},f.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},f.prototype.initialSyncWithDOM=function(){},f.prototype.destroy=function(){this.foundation.destroy()},f.prototype.listen=function(e,t,n){this.root.addEventListener(e,t,n)},f.prototype.unlisten=function(e,t,n){this.root.removeEventListener(e,t,n)},f.prototype.emit=function(e,t,n){var r;void 0===n&&(n=!1),"function"==typeof CustomEvent?r=new CustomEvent(e,{bubbles:n,detail:t}):(r=document.createEvent("CustomEvent")).initCustomEvent(e,n,!1,t),this.root.dispatchEvent(r)},f.prototype.safeSetAttribute=function(e,t,n){if("tabindex"===t.toLowerCase())e.tabIndex=Number(n);else if(0===t.indexOf("data-")){var r=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})}(t.replace(/^data-/,""));e.dataset[r]=n}else c.safeElement.setPrefixedAttribute([a.safeAttrPrefix(u=u||i(["aria-"],["aria-"])),a.safeAttrPrefix(l=l||i(["role"],["role"]))],e,t,n)},f);function f(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];this.root=e,this.initialize.apply(this,s([],o(n))),this.foundation=void 0===t?this.getDefaultFoundation():t,this.foundation.init(),this.initialSyncWithDOM()}t.MDCComponent=d,t.default=d},function(e,t,n){"use strict";var p=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.HtmlSanitizerImpl=void 0,n(0);var r=n(2),i=n(4),h=n(3),c=n(23),m=n(24),o=n(16),E=n(11),s=(a.prototype.sanitizeAssertUnchanged=function(e){this.changes=[];var t=this.sanitize(e);if(0===this.changes.length)return t;throw new Error("")},a.prototype.sanitize=function(e){var t=document.createElement("span");t.appendChild(this.sanitizeToFragment(e));var n=(new XMLSerializer).serializeToString(t);return n=n.slice(n.indexOf(">")+1,n.lastIndexOf("</")),(0,r.createHtml)(n)},a.prototype.sanitizeToFragment=function(e){for(var t=this,n=(0,c.createInertFragment)(e),r=document.createTreeWalker(n,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(e){return t.nodeFilter(e)},!1),i=r.nextNode(),o=document.createDocumentFragment(),s=o;null!==i;){var a=void 0;if((0,m.isText)(i))a=this.sanitizeTextNode(i);else{if(!(0,m.isElement)(i))throw new Error("Node is not of type text or element");a=this.sanitizeElementNode(i)}if(s.appendChild(a),i=r.firstChild())s=a;else for(;!(i=r.nextSibling())&&(i=r.parentNode());)s=s.parentNode}return o},a.prototype.sanitizeTextNode=function(e){return document.createTextNode(e.data)},a.prototype.sanitizeElementNode=function(e){var t,n,r=(0,m.getNodeName)(e),i=document.createElement(r),o=e.attributes;try{for(var s=p(o),a=s.next();!a.done;a=s.next()){var c=a.value,u=c.name,l=c.value,d=this.sanitizerTable.getAttributePolicy(u,r);if(this.satisfiesAllConditions(d.conditions,o))switch(d.policyAction){case E.AttributePolicyAction.KEEP:i.setAttribute(u,l);break;case E.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var f=(0,h.restrictivelySanitizeUrl)(l);f!==l&&this.recordChange("Url in attribute ".concat(u,' was modified during sanitization. Original url:"').concat(l,'" was sanitized to: "').concat(f,'"')),i.setAttribute(u,f);break;case E.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(u,l.toLowerCase());break;case E.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(u,l);break;case E.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(u," was dropped"));break;default:y(d.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(u,"."))}}catch(e){t={error:e}}finally{try{a&&!a.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}return i},a.prototype.nodeFilter=function(e){if((0,m.isText)(e))return NodeFilter.FILTER_ACCEPT;if(!(0,m.isElement)(e))return NodeFilter.FILTER_REJECT;var t=(0,m.getNodeName)(e);return null===t?(this.recordChange("Node name was null for node: ".concat(e)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(t)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(t," was dropped")),NodeFilter.FILTER_REJECT)},a.prototype.recordChange=function(e){0===this.changes.length&&this.changes.push("")},a.prototype.satisfiesAllConditions=function(e,t){var n,r,i;if(!e)return!0;try{for(var o=p(e),s=o.next();!s.done;s=o.next()){var a=d(s.value,2),c=a[0],u=a[1],l=null===(i=t.getNamedItem(c))||void 0===i?void 0:i.value;if(l&&!u.has(l))return!1}}catch(e){n={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return!0},a);function a(e,t){this.sanitizerTable=e,this.changes=[],(0,i.ensureTokenIsValid)(t)}t.HtmlSanitizerImpl=s;var u=function(){return new s(o.defaultSanitizerTable,i.secretToken)}();function y(e,t){throw void 0===t&&(t="unexpected value ".concat(e,"!")),new Error(t)}t.sanitizeHtml=function(e){return u.sanitize(e)},t.sanitizeHtmlAssertUnchanged=function(e){return u.sanitizeAssertUnchanged(e)},t.sanitizeHtmlToFragment=function(e){return u.sanitizeToFragment(e)}},function(e,t,n){"use strict";var i=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},o=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||((r=r||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.setPrefixedAttribute=t.buildPrefixedAttributeSetter=t.insertAdjacentHtml=t.setCssText=t.setOuterHtml=t.setInnerHtml=void 0;var s=n(8),a=n(2),r=n(10);function c(e,t,n,r){if(0===e.length)throw new Error("No prefixes are provided");var i=e.map(function(e){return(0,s.unwrapAttributePrefix)(e)}),o=n.toLowerCase();if(i.every(function(e){return 0!==o.indexOf(e)}))throw new Error('Attribute "'.concat(n,'" does not match any of the allowed prefixes.'));t.setAttribute(n,r)}function u(e){if("script"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}t.setInnerHtml=function(e,t){!function(e){return void 0!==e.tagName}(e)||u(e),e.innerHTML=(0,a.unwrapHtml)(t)},t.setOuterHtml=function(e,t){var n=e.parentElement;null!==n&&u(n),e.outerHTML=(0,a.unwrapHtml)(t)},t.setCssText=function(e,t){e.style.cssText=(0,r.unwrapStyle)(t)},t.insertAdjacentHtml=function(e,t,n){var r="beforebegin"===t||"afterend"===t?e.parentElement:e;null!==r&&u(r),e.insertAdjacentHTML(t,(0,a.unwrapHtml)(n))},t.buildPrefixedAttributeSetter=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var r=o([e],i(t),!1);return function(e,t,n){c(r,e,t,n)}},t.setPrefixedAttribute=c},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSanitizerTable=void 0;var r=n(11);t.defaultSanitizerTable=new r.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:r.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:r.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:r.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:r.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:r.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.SafeStyleSheet=t.isStyleSheet=t.unwrapStyle=t.SafeStyle=t.isStyle=t.unwrapScript=t.SafeScript=t.isScript=t.EMPTY_SCRIPT=t.unwrapResourceUrl=t.TrustedResourceUrl=t.isResourceUrl=t.unwrapHtml=t.SafeHtml=t.isHtml=t.EMPTY_HTML=t.unwrapAttributePrefix=t.SafeAttributePrefix=t.safeStyleSheet=t.concatStyleSheets=t.safeStyle=t.concatStyles=t.scriptFromJson=t.safeScriptWithArgs=t.safeScript=t.concatScripts=t.trustedResourceUrl=t.replaceFragment=t.blobUrlFromScript=t.appendParams=t.HtmlSanitizerBuilder=t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.htmlEscape=t.createScriptSrc=t.createScript=t.concatHtmls=t.safeAttrPrefix=void 0;var r=n(19);Object.defineProperty(t,"safeAttrPrefix",{enumerable:!0,get:function(){return r.safeAttrPrefix}});var i=n(22);Object.defineProperty(t,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(t,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(t,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(t,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var o=n(14);Object.defineProperty(t,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(t,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(t,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var s=n(25);Object.defineProperty(t,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return s.HtmlSanitizerBuilder}});var a=n(26);Object.defineProperty(t,"appendParams",{enumerable:!0,get:function(){return a.appendParams}}),Object.defineProperty(t,"blobUrlFromScript",{enumerable:!0,get:function(){return a.blobUrlFromScript}}),Object.defineProperty(t,"replaceFragment",{enumerable:!0,get:function(){return a.replaceFragment}}),Object.defineProperty(t,"trustedResourceUrl",{enumerable:!0,get:function(){return a.trustedResourceUrl}});var c=n(27);Object.defineProperty(t,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(t,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(t,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(t,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var u=n(28);Object.defineProperty(t,"concatStyles",{enumerable:!0,get:function(){return u.concatStyles}}),Object.defineProperty(t,"safeStyle",{enumerable:!0,get:function(){return u.safeStyle}});var l=n(29);Object.defineProperty(t,"concatStyleSheets",{enumerable:!0,get:function(){return l.concatStyleSheets}}),Object.defineProperty(t,"safeStyleSheet",{enumerable:!0,get:function(){return l.safeStyleSheet}});var d=n(8);Object.defineProperty(t,"SafeAttributePrefix",{enumerable:!0,get:function(){return d.SafeAttributePrefix}}),Object.defineProperty(t,"unwrapAttributePrefix",{enumerable:!0,get:function(){return d.unwrapAttributePrefix}});var f=n(2);Object.defineProperty(t,"EMPTY_HTML",{enumerable:!0,get:function(){return f.EMPTY_HTML}}),Object.defineProperty(t,"isHtml",{enumerable:!0,get:function(){return f.isHtml}}),Object.defineProperty(t,"SafeHtml",{enumerable:!0,get:function(){return f.SafeHtml}}),Object.defineProperty(t,"unwrapHtml",{enumerable:!0,get:function(){return f.unwrapHtml}});var p=n(1);Object.defineProperty(t,"isResourceUrl",{enumerable:!0,get:function(){return p.isResourceUrl}}),Object.defineProperty(t,"TrustedResourceUrl",{enumerable:!0,get:function(){return p.TrustedResourceUrl}}),Object.defineProperty(t,"unwrapResourceUrl",{enumerable:!0,get:function(){return p.unwrapResourceUrl}});var h=n(5);Object.defineProperty(t,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return h.EMPTY_SCRIPT}}),Object.defineProperty(t,"isScript",{enumerable:!0,get:function(){return h.isScript}}),Object.defineProperty(t,"SafeScript",{enumerable:!0,get:function(){return h.SafeScript}}),Object.defineProperty(t,"unwrapScript",{enumerable:!0,get:function(){return h.unwrapScript}});var m=n(10);Object.defineProperty(t,"isStyle",{enumerable:!0,get:function(){return m.isStyle}}),Object.defineProperty(t,"SafeStyle",{enumerable:!0,get:function(){return m.SafeStyle}}),Object.defineProperty(t,"unwrapStyle",{enumerable:!0,get:function(){return m.unwrapStyle}});var E=n(12);Object.defineProperty(t,"isStyleSheet",{enumerable:!0,get:function(){return E.isStyleSheet}}),Object.defineProperty(t,"SafeStyleSheet",{enumerable:!0,get:function(){return E.SafeStyleSheet}}),Object.defineProperty(t,"unwrapStyleSheet",{enumerable:!0,get:function(){return E.unwrapStyleSheet}})},function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&("get"in i?t.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.safeWorker=t.safeWindow=t.safeServiceWorkerContainer=t.safeRange=t.safeLocation=t.safeGlobal=t.safeDomParser=t.safeDocument=t.safeStyleEl=t.safeScriptEl=t.safeObjectEl=t.safeLinkEl=t.safeInputEl=t.safeIframeEl=t.safeFormEl=t.safeEmbedEl=t.safeElement=t.safeButtonEl=t.safeAreaEl=t.safeAnchorEl=void 0,t.safeAnchorEl=o(n(30)),t.safeAreaEl=o(n(31)),t.safeButtonEl=o(n(32)),t.safeElement=o(n(15)),t.safeEmbedEl=o(n(33)),t.safeFormEl=o(n(34)),t.safeIframeEl=o(n(35)),t.safeInputEl=o(n(36)),t.safeLinkEl=o(n(37)),t.safeObjectEl=o(n(38)),t.safeScriptEl=o(n(39)),t.safeStyleEl=o(n(40)),t.safeDocument=o(n(41)),t.safeDomParser=o(n(42)),t.safeGlobal=o(n(43)),t.safeLocation=o(n(44)),t.safeRange=o(n(45)),t.safeServiceWorkerContainer=o(n(46)),t.safeWindow=o(n(47)),t.safeWorker=o(n(48))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeAttrPrefix=void 0,n(0);var r=n(8);n(6),n(21);t.safeAttrPrefix=function(e){var t=e[0].toLowerCase();return(0,r.createAttributePrefix)(t)}},function(e,t){var n,r,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(t){if(n===setTimeout)return setTimeout(t,0);if((n===o||!n)&&setTimeout)return n=setTimeout,setTimeout(t,0);try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:o}catch(e){n=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var c,u=[],l=!1,d=-1;function f(){l&&c&&(l=!1,c.length?u=c.concat(u):d=-1,u.length&&p())}function p(){if(!l){var e=a(f);l=!0;for(var t=u.length;t;){for(c=u,u=[];++d<t;)c&&c[d].run();d=-1,t=u.length}c=null,l=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(e){try{return r.call(null,t)}catch(e){return r.call(this,t)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function m(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new h(e,t)),1!==u.length||l||a(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=m,i.addListener=m,i.once=m,i.off=m,i.removeListener=m,i.removeAllListeners=m,i.emit=m,i.prependListener=m,i.prependOnceListener=m,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SECURITY_SENSITIVE_ATTRIBUTES=void 0,t.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatHtmls=t.createScriptSrc=t.createScript=t.htmlEscape=void 0;var o=n(2),s=n(1),i=n(5);function a(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}t.htmlEscape=function(e,t){void 0===t&&(t={});var n=a(e);return t.preserveSpaces&&(n=n.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),t.preserveNewlines&&(n=n.replace(/(\r\n|\n|\r)/g,"<br>")),t.preserveTabs&&(n=n.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(n)},t.createScript=function(e,t){void 0===t&&(t={});var n=(0,i.unwrapScript)(e).toString(),r="<script";return t.id&&(r+=' id="'.concat(a(t.id),'"')),t.nonce&&(r+=' nonce="'.concat(a(t.nonce),'"')),t.type&&(r+=' type="'.concat(a(t.type),'"')),r+=">".concat(n,"<\/script>"),(0,o.createHtml)(r)},t.createScriptSrc=function(e,t,n){var r=(0,s.unwrapResourceUrl)(e).toString(),i='<script src="'.concat(a(r),'"');return t&&(i+=" async"),n&&(i+=' nonce="'.concat(a(n),'"')),i+="><\/script>",(0,o.createHtml)(i)},t.concatHtmls=function(e){return(0,o.createHtml)(e.map(o.unwrapHtml).join(""))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInertFragment=void 0;var r=n(15),i=n(2);t.createInertFragment=function(e){var t=document.createElement("template"),n=(0,i.createHtml)(e);return(0,r.setInnerHtml)(t,n),t.content}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElement=t.isText=t.getNodeName=void 0,t.getNodeName=function(e){var t=e.nodeName;return"string"==typeof t?t:"FORM"},t.isText=function(e){return e.nodeType===Node.TEXT_NODE},t.isElement=function(e){var t=e.nodeType;return t===Node.ELEMENT_NODE||"number"!=typeof t}},function(e,t,n){"use strict";var T=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},_=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s};Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlSanitizerBuilder=void 0;var r=n(4),i=n(14),o=n(16),g=n(11),s=(a.prototype.onlyAllowElements=function(e){var t,n,r=new Set,i=new Map;try{for(var o=T(e),s=o.next();!s.done;s=o.next()){var a=s.value;if(a=a.toUpperCase(),!this.sanitizerTable.isAllowedElement(a))throw new Error("Element: ".concat(a,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(a);void 0!==c?i.set(a,c):r.add(a)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(t)throw t.error}}return this.sanitizerTable=new g.SanitizerTable(r,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},a.prototype.onlyAllowAttributes=function(e){var t,n,r,i,o,s,a=new Set,c=new Map,u=new Map;try{for(var l=T(e),d=l.next();!d.done;d=l.next()){var f=d.value;this.sanitizerTable.allowedGlobalAttributes.has(f)&&a.add(f),this.sanitizerTable.globalAttributePolicies.has(f)&&c.set(f,this.sanitizerTable.globalAttributePolicies.get(f))}}catch(e){t={error:e}}finally{try{d&&!d.done&&(n=l.return)&&n.call(l)}finally{if(t)throw t.error}}try{for(var p=T(this.sanitizerTable.elementPolicies.entries()),h=p.next();!h.done;h=p.next()){var m=_(h.value,2),E=m[0],y=m[1],S=new Map;try{for(var I=(o=void 0,T(y.entries())),b=I.next();!b.done;b=I.next()){var v=_(b.value,2),A=(f=v[0],v[1]);e.has(f)&&S.set(f,A)}}catch(e){o={error:e}}finally{try{b&&!b.done&&(s=I.return)&&s.call(I)}finally{if(o)throw o.error}}u.set(E,S)}}catch(e){r={error:e}}finally{try{h&&!h.done&&(i=p.return)&&i.call(p)}finally{if(r)throw r.error}}return this.sanitizerTable=new g.SanitizerTable(this.sanitizerTable.allowedElements,u,a,c),this},a.prototype.allowDataAttributes=function(e){var t,n,r=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=T(e),o=i.next();!o.done;o=i.next()){var s=o.value;if(0!==s.indexOf("data-"))throw new Error("data attribute: ".concat(s,' does not begin with the prefix "data-"'));r.add(s)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(n=i.return)&&n.call(i)}finally{if(t)throw t.error}}return this.sanitizerTable=new g.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,r,this.sanitizerTable.globalAttributePolicies),this},a.prototype.allowStyleAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("style",{policyAction:g.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new g.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},a.prototype.allowClassAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("class",{policyAction:g.AttributePolicyAction.KEEP}),this.sanitizerTable=new g.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},a.prototype.allowIdAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("id",{policyAction:g.AttributePolicyAction.KEEP}),this.sanitizerTable=new g.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},a.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,r.secretToken)},a);function a(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}t.HtmlSanitizerBuilder=s},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.blobUrlFromScript=t.replaceFragment=t.appendParams=t.trustedResourceUrl=void 0,n(0);var a=n(1),r=n(5);n(6);t.trustedResourceUrl=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(0===t.length)return(0,a.createResourceUrl)(e[0]);e[0].toLowerCase();for(var r=[e[0]],i=0;i<t.length;i++)r.push(encodeURIComponent(t[i])),r.push(e[i+1]);return(0,a.createResourceUrl)(r.join(""))},t.appendParams=function(e,t){var o=(0,a.unwrapResourceUrl)(e).toString();if(/#/.test(o)){throw new Error("")}var s=/\?/.test(o)?"&":"?";return t.forEach(function(e,t){for(var n=e instanceof Array?e:[e],r=0;r<n.length;r++){var i=n[r];null!=i&&(o+=s+encodeURIComponent(t)+"="+encodeURIComponent(String(i)),s="&")}}),(0,a.createResourceUrl)(o)};var i=/[^#]*/;t.replaceFragment=function(e,t){var n=(0,a.unwrapResourceUrl)(e).toString();return(0,a.createResourceUrl)(i.exec(n)[0]+"#"+t)},t.blobUrlFromScript=function(e){var t=(0,r.unwrapScript)(e).toString(),n=new Blob([t],{type:"text/javascript"});return(0,a.createResourceUrl)(URL.createObjectURL(n))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeScriptWithArgs=t.scriptFromJson=t.concatScripts=t.safeScript=void 0,n(0);var i=n(5);n(6);function o(e){return(0,i.createScript)(JSON.stringify(e).replace(/</g,"\\x3c"))}t.safeScript=function(e){return(0,i.createScript)(e[0])},t.concatScripts=function(e){return(0,i.createScript)(e.map(i.unwrapScript).join(""))},t.scriptFromJson=o,t.safeScriptWithArgs=function(r){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.map(function(e){return o(e).toString()});return(0,i.createScript)("(".concat(r.join(""),")(").concat(n.join(","),")"))}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyles=t.safeStyle=void 0,n(0);n(6);var r=n(10);t.safeStyle=function(e){var t=e[0];return(0,r.createStyle)(t)},t.concatStyles=function(e){return(0,r.createStyle)(e.map(r.unwrapStyle).join(""))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyleSheets=t.safeStyleSheet=void 0,n(0);n(6);var r=n(12);t.safeStyleSheet=function(e){var t=e[0];return(0,r.createStyleSheet)(t)},t.concatStyleSheets=function(e){return(0,r.createStyleSheet)(e.map(r.unwrapStyleSheet).join(""))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var r=n(3);t.setHref=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&(e.href=n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var r=n(3);t.setHref=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&(e.href=n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var r=n(3);t.setFormaction=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&(e.formAction=n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=void 0;var r=n(1);t.setSrc=function(e,t){e.src=(0,r.unwrapResourceUrl)(t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setAction=void 0;var r=n(3);t.setAction=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&(e.action=n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrcdoc=t.setSrc=void 0;var r=n(2),i=n(1);t.setSrc=function(e,t){e.src=(0,i.unwrapResourceUrl)(t).toString()},t.setSrcdoc=function(e,t){e.srcdoc=(0,r.unwrapHtml)(t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var r=n(3);t.setFormaction=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&(e.formAction=n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHrefAndRel=void 0;var i=n(3),o=n(1),s=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];t.setHrefAndRel=function(e,t,n){if(t instanceof o.TrustedResourceUrl)e.href=(0,o.unwrapResourceUrl)(t).toString();else{if(-1===s.indexOf(n))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(n,'"'));var r=(0,i.unwrapUrlOrSanitize)(t);if(void 0===r)return;e.href=r}e.rel=n}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setData=void 0;var r=n(1);t.setData=function(e,t){e.data=(0,r.unwrapResourceUrl)(t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=t.setTextContent=void 0;var r=n(1),i=n(5);function o(e){var t=function(e){var t,n=e.document,r=null===(t=n.querySelector)||void 0===t?void 0:t.call(n,"script[nonce]");return r&&(r.nonce||r.getAttribute("nonce"))||""}(e.ownerDocument&&e.ownerDocument.defaultView||window);t&&e.setAttribute("nonce",t)}t.setTextContent=function(e,t){e.textContent=(0,i.unwrapScript)(t),o(e)},t.setSrc=function(e,t){e.src=(0,r.unwrapResourceUrl)(t),o(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setTextContent=void 0;var r=n(12);t.setTextContent=function(e,t){e.textContent=(0,r.unwrapStyleSheet)(t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execCommandInsertHtml=t.execCommand=t.write=void 0;var o=n(2);t.write=function(e,t){e.write((0,o.unwrapHtml)(t))},t.execCommand=function(e,t,n){var r=String(t),i=n;return"inserthtml"===r.toLowerCase()&&(i=(0,o.unwrapHtml)(n)),e.execCommand(r,!1,i)},t.execCommandInsertHtml=function(e,t){return e.execCommand("insertHTML",!1,(0,o.unwrapHtml)(t))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseFromString=t.parseHtml=void 0;var r=n(2);function i(e,t,n){return e.parseFromString((0,r.unwrapHtml)(t),n)}t.parseHtml=function(e,t){return i(e,t,"text/html")},t.parseFromString=i},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.globalEval=void 0;var i=n(5);t.globalEval=function(e,t){var n=(0,i.unwrapScript)(t),r=e.eval(n);return r===n&&(r=e.eval(n.toString())),r}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assign=t.replace=t.setHref=void 0;var r=n(3);t.setHref=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&(e.href=n)},t.replace=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&e.replace(n)},t.assign=function(e,t){var n=(0,r.unwrapUrlOrSanitize)(t);void 0!==n&&e.assign(n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createContextualFragment=void 0;var r=n(2);t.createContextualFragment=function(e,t){return e.createContextualFragment((0,r.unwrapHtml)(t))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.register=void 0;var r=n(1);t.register=function(e,t,n){return e.register((0,r.unwrapResourceUrl)(t),n)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.open=void 0;var o=n(3);t.open=function(e,t,n,r){var i=(0,o.unwrapUrlOrSanitize)(t);return void 0!==i?e.open(i,n,r):null}},function(e,t,n){"use strict";var r=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},i=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||((r=r||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.importScripts=t.createShared=t.create=void 0;var o=n(1);t.create=function(e,t){return new Worker((0,o.unwrapResourceUrl)(e),t)},t.createShared=function(e,t){return new SharedWorker((0,o.unwrapResourceUrl)(e),t)},t.importScripts=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];e.importScripts.apply(e,i([],r(t.map(function(e){return(0,o.unwrapResourceUrl)(e)})),!1))}},function(e,t,n){"use strict";function r(e,t){return(e.matches||e.webkitMatchesSelector||e.msMatchesSelector).call(e,t)}Object.defineProperty(t,"__esModule",{value:!0}),t.estimateScrollWidth=t.matches=t.closest=void 0,t.closest=function(e,t){if(e.closest)return e.closest(t);for(var n=e;n;){if(r(n,t))return n;n=n.parentElement}return null},t.matches=r,t.estimateScrollWidth=function(e){var t=e;if(null!==t.offsetParent)return t.scrollWidth;var n=t.cloneNode(!0);n.style.setProperty("position","absolute"),n.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(n);var r=n.scrollWidth;return document.documentElement.removeChild(n),r}},,,,,,function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.isNavigationEvent=r.normalizeKey=r.KEY=void 0,r.KEY={UNKNOWN:"Unknown",BACKSPACE:"Backspace",ENTER:"Enter",SPACEBAR:"Spacebar",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown",END:"End",HOME:"Home",ARROW_LEFT:"ArrowLeft",ARROW_UP:"ArrowUp",ARROW_RIGHT:"ArrowRight",ARROW_DOWN:"ArrowDown",DELETE:"Delete",ESCAPE:"Escape",TAB:"Tab"};var i=new Set;i.add(r.KEY.BACKSPACE),i.add(r.KEY.ENTER),i.add(r.KEY.SPACEBAR),i.add(r.KEY.PAGE_UP),i.add(r.KEY.PAGE_DOWN),i.add(r.KEY.END),i.add(r.KEY.HOME),i.add(r.KEY.ARROW_LEFT),i.add(r.KEY.ARROW_UP),i.add(r.KEY.ARROW_RIGHT),i.add(r.KEY.ARROW_DOWN),i.add(r.KEY.DELETE),i.add(r.KEY.ESCAPE),i.add(r.KEY.TAB);var n=8,o=13,s=32,a=33,c=34,u=35,l=36,d=37,f=38,p=39,h=40,m=46,E=27,y=9,S=new Map;S.set(n,r.KEY.BACKSPACE),S.set(o,r.KEY.ENTER),S.set(s,r.KEY.SPACEBAR),S.set(a,r.KEY.PAGE_UP),S.set(c,r.KEY.PAGE_DOWN),S.set(u,r.KEY.END),S.set(l,r.KEY.HOME),S.set(d,r.KEY.ARROW_LEFT),S.set(f,r.KEY.ARROW_UP),S.set(p,r.KEY.ARROW_RIGHT),S.set(h,r.KEY.ARROW_DOWN),S.set(m,r.KEY.DELETE),S.set(E,r.KEY.ESCAPE),S.set(y,r.KEY.TAB);var I=new Set;function b(e){var t=e.key;if(i.has(t))return t;var n=S.get(e.keyCode);return n||r.KEY.UNKNOWN}I.add(r.KEY.PAGE_UP),I.add(r.KEY.PAGE_DOWN),I.add(r.KEY.END),I.add(r.KEY.HOME),I.add(r.KEY.ARROW_LEFT),I.add(r.KEY.ARROW_UP),I.add(r.KEY.ARROW_RIGHT),I.add(r.KEY.ARROW_DOWN),r.normalizeKey=b,r.isNavigationEvent=function(e){return I.has(b(e))}},function(e,t,n){"use strict";var r,i;Object.defineProperty(t,"__esModule",{value:!0}),t.evolutionClassNameMap=t.evolutionAttribute=t.deprecatedClassNameMap=t.numbers=t.cssClasses=t.strings=void 0;var o={LIST_ITEM_ACTIVATED_CLASS:"mdc-list-item--activated",LIST_ITEM_CLASS:"mdc-list-item",LIST_ITEM_DISABLED_CLASS:"mdc-list-item--disabled",LIST_ITEM_SELECTED_CLASS:"mdc-list-item--selected",LIST_ITEM_TEXT_CLASS:"mdc-list-item__text",LIST_ITEM_PRIMARY_TEXT_CLASS:"mdc-list-item__primary-text",ROOT:"mdc-list"},s=((r={})[""+(t.cssClasses=o).LIST_ITEM_ACTIVATED_CLASS]="mdc-list-item--activated",r[""+o.LIST_ITEM_CLASS]="mdc-list-item",r[""+o.LIST_ITEM_DISABLED_CLASS]="mdc-list-item--disabled",r[""+o.LIST_ITEM_SELECTED_CLASS]="mdc-list-item--selected",r[""+o.LIST_ITEM_PRIMARY_TEXT_CLASS]="mdc-list-item__primary-text",r[""+o.ROOT]="mdc-list",r);t.evolutionClassNameMap=s;var a=((i={})[""+o.LIST_ITEM_ACTIVATED_CLASS]="mdc-deprecated-list-item--activated",i[""+o.LIST_ITEM_CLASS]="mdc-deprecated-list-item",i[""+o.LIST_ITEM_DISABLED_CLASS]="mdc-deprecated-list-item--disabled",i[""+o.LIST_ITEM_SELECTED_CLASS]="mdc-deprecated-list-item--selected",i[""+o.LIST_ITEM_TEXT_CLASS]="mdc-deprecated-list-item__text",i[""+o.LIST_ITEM_PRIMARY_TEXT_CLASS]="mdc-deprecated-list-item__primary-text",i[""+o.ROOT]="mdc-deprecated-list",i);t.deprecatedClassNameMap=a;var c={ACTION_EVENT:"MDCList:action",SELECTION_CHANGE_EVENT:"MDCList:selectionChange",ARIA_CHECKED:"aria-checked",ARIA_CHECKED_CHECKBOX_SELECTOR:'[role="checkbox"][aria-checked="true"]',ARIA_CHECKED_RADIO_SELECTOR:'[role="radio"][aria-checked="true"]',ARIA_CURRENT:"aria-current",ARIA_DISABLED:"aria-disabled",ARIA_ORIENTATION:"aria-orientation",ARIA_ORIENTATION_HORIZONTAL:"horizontal",ARIA_ROLE_CHECKBOX_SELECTOR:'[role="checkbox"]',ARIA_SELECTED:"aria-selected",ARIA_INTERACTIVE_ROLES_SELECTOR:'[role="listbox"], [role="menu"]',ARIA_MULTI_SELECTABLE_SELECTOR:'[aria-multiselectable="true"]',CHECKBOX_RADIO_SELECTOR:'input[type="checkbox"], input[type="radio"]',CHECKBOX_SELECTOR:'input[type="checkbox"]',CHILD_ELEMENTS_TO_TOGGLE_TABINDEX:"\n    ."+o.LIST_ITEM_CLASS+" button:not(:disabled),\n    ."+o.LIST_ITEM_CLASS+" a,\n    ."+a[o.LIST_ITEM_CLASS]+" button:not(:disabled),\n    ."+a[o.LIST_ITEM_CLASS]+" a\n  ",DEPRECATED_SELECTOR:".mdc-deprecated-list",FOCUSABLE_CHILD_ELEMENTS:"\n    ."+o.LIST_ITEM_CLASS+" button:not(:disabled),\n    ."+o.LIST_ITEM_CLASS+" a,\n    ."+o.LIST_ITEM_CLASS+' input[type="radio"]:not(:disabled),\n    .'+o.LIST_ITEM_CLASS+' input[type="checkbox"]:not(:disabled),\n    .'+a[o.LIST_ITEM_CLASS]+" button:not(:disabled),\n    ."+a[o.LIST_ITEM_CLASS]+" a,\n    ."+a[o.LIST_ITEM_CLASS]+' input[type="radio"]:not(:disabled),\n    .'+a[o.LIST_ITEM_CLASS]+' input[type="checkbox"]:not(:disabled)\n  ',RADIO_SELECTOR:'input[type="radio"]',SELECTED_ITEM_SELECTOR:'[aria-selected="true"], [aria-current="true"]'};t.strings=c;t.numbers={UNSET_INDEX:-1,TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS:300};t.evolutionAttribute="evolution"},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getCorrectEventName=t.getCorrectPropertyName=void 0;var s={animation:{prefixed:"-webkit-animation",standard:"animation"},transform:{prefixed:"-webkit-transform",standard:"transform"},transition:{prefixed:"-webkit-transition",standard:"transition"}},a={animationend:{cssProperty:"animation",prefixed:"webkitAnimationEnd",standard:"animationend"},animationiteration:{cssProperty:"animation",prefixed:"webkitAnimationIteration",standard:"animationiteration"},animationstart:{cssProperty:"animation",prefixed:"webkitAnimationStart",standard:"animationstart"},transitionend:{cssProperty:"transition",prefixed:"webkitTransitionEnd",standard:"transitionend"}};function c(e){return Boolean(e.document)&&"function"==typeof e.document.createElement}t.getCorrectPropertyName=function(e,t){if(c(e)&&t in s){var n=e.document.createElement("div"),r=s[t],i=r.standard,o=r.prefixed;return i in n.style?i:o}return t},t.getCorrectEventName=function(e,t){if(c(e)&&t in a){var n=e.document.createElement("div"),r=a[t],i=r.standard,o=r.prefixed;return r.cssProperty in n.style?i:o}return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Corner=t.CornerBit=t.numbers=t.strings=t.cssClasses=void 0;t.cssClasses={ANCHOR:"mdc-menu-surface--anchor",ANIMATING_CLOSED:"mdc-menu-surface--animating-closed",ANIMATING_OPEN:"mdc-menu-surface--animating-open",FIXED:"mdc-menu-surface--fixed",IS_OPEN_BELOW:"mdc-menu-surface--is-open-below",OPEN:"mdc-menu-surface--open",ROOT:"mdc-menu-surface"};var r={CLOSED_EVENT:"MDCMenuSurface:closed",CLOSING_EVENT:"MDCMenuSurface:closing",OPENED_EVENT:"MDCMenuSurface:opened",OPENING_EVENT:"MDCMenuSurface:opening",FOCUSABLE_ELEMENTS:["button:not(:disabled)",'[href]:not([aria-disabled="true"])',"input:not(:disabled)","select:not(:disabled)","textarea:not(:disabled)",'[tabindex]:not([tabindex="-1"]):not([aria-disabled="true"])'].join(", ")};t.strings=r;var i,o,s,a;t.numbers={TRANSITION_OPEN_DURATION:120,TRANSITION_CLOSE_DURATION:75,MARGIN_TO_EDGE:32,ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO:.67,TOUCH_EVENT_WAIT_MS:30},(o=i=i||{})[o.BOTTOM=1]="BOTTOM",o[o.CENTER=2]="CENTER",o[o.RIGHT=4]="RIGHT",o[o.FLIP_RTL=8]="FLIP_RTL",t.CornerBit=i,(a=s=s||{})[a.TOP_LEFT=0]="TOP_LEFT",a[a.TOP_RIGHT=4]="TOP_RIGHT",a[a.BOTTOM_LEFT=1]="BOTTOM_LEFT",a[a.BOTTOM_RIGHT=5]="BOTTOM_RIGHT",a[a.TOP_START=8]="TOP_START",a[a.TOP_END=12]="TOP_END",a[a.BOTTOM_START=9]="BOTTOM_START",a[a.BOTTOM_END=13]="BOTTOM_END",t.Corner=s},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.preventDefaultEvent=void 0;var r=["input","button","textarea","select"];t.preventDefaultEvent=function(e){var t=e.target;if(t){var n=(""+t.tagName).toLowerCase();-1===r.indexOf(n)&&e.preventDefault()}}},function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},s=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),c=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&s(t,e,n);return a(t,e),t},d=this&&this.__read||function(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s},f=this&&this.__spreadArray||function(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e},u=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCListFoundation=void 0;var l=n(7),v=n(55),A=n(56),T=n(60),_=c(n(70));var p=["Alt","Control","Meta","Shift"];function g(t){var n=new Set(t?p.filter(function(e){return t.getModifierState(e)}):[]);return function(e){return e.every(function(e){return n.has(e)})&&e.length===n.size}}var h,m=(h=l.MDCFoundation,i(E,h),Object.defineProperty(E,"strings",{get:function(){return A.strings},enumerable:!1,configurable:!0}),Object.defineProperty(E,"cssClasses",{get:function(){return A.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(E,"numbers",{get:function(){return A.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(E,"defaultAdapter",{get:function(){return{addClassForElementIndex:function(){},focusItemAtIndex:function(){},getAttributeForElementIndex:function(){return null},getFocusedElementIndex:function(){return 0},getListItemCount:function(){return 0},hasCheckboxAtIndex:function(){return!1},hasRadioAtIndex:function(){return!1},isCheckboxCheckedAtIndex:function(){return!1},isFocusInsideList:function(){return!1},isRootFocused:function(){return!1},listItemAtIndexHasClass:function(){return!1},notifyAction:function(){},notifySelectionChange:function(){},removeClassForElementIndex:function(){},setAttributeForElementIndex:function(){},setCheckedCheckboxOrRadioAtIndex:function(){},setTabIndexForListItemChildren:function(){},getPrimaryTextAtIndex:function(){return""}}},enumerable:!1,configurable:!0}),E.prototype.layout=function(){0!==this.adapter.getListItemCount()&&(this.adapter.hasCheckboxAtIndex(0)?(this.isCheckboxList=!0,this.selectedIndex=[]):this.adapter.hasRadioAtIndex(0)?this.isRadioList=!0:this.maybeInitializeSingleSelection(),this.hasTypeahead&&(this.sortedIndexByFirstChar=this.typeaheadInitSortedIndex()))},E.prototype.getFocusedItemIndex=function(){return this.focusedItemIndex},E.prototype.setWrapFocus=function(e){this.wrapFocus=e},E.prototype.setVerticalOrientation=function(e){this.isVertical=e},E.prototype.setSingleSelection=function(e){(this.isSingleSelectionList=e)&&(this.maybeInitializeSingleSelection(),this.selectedIndex=this.getSelectedIndexFromDOM())},E.prototype.setDisabledItemsFocusable=function(e){this.areDisabledItemsFocusable=e},E.prototype.maybeInitializeSingleSelection=function(){var e=this.getSelectedIndexFromDOM();e!==A.numbers.UNSET_INDEX&&(this.adapter.listItemAtIndexHasClass(e,A.cssClasses.LIST_ITEM_ACTIVATED_CLASS)&&this.setUseActivatedClass(!0),this.isSingleSelectionList=!0,this.selectedIndex=e)},E.prototype.getSelectedIndexFromDOM=function(){for(var e=A.numbers.UNSET_INDEX,t=this.adapter.getListItemCount(),n=0;n<t;n++){var r=this.adapter.listItemAtIndexHasClass(n,A.cssClasses.LIST_ITEM_SELECTED_CLASS),i=this.adapter.listItemAtIndexHasClass(n,A.cssClasses.LIST_ITEM_ACTIVATED_CLASS);if(r||i){e=n;break}}return e},E.prototype.setHasTypeahead=function(e){(this.hasTypeahead=e)&&(this.sortedIndexByFirstChar=this.typeaheadInitSortedIndex())},E.prototype.isTypeaheadInProgress=function(){return this.hasTypeahead&&_.isTypingInProgress(this.typeaheadState)},E.prototype.setUseActivatedClass=function(e){this.useActivatedClass=e},E.prototype.setUseSelectedAttribute=function(e){this.useSelectedAttr=e},E.prototype.getSelectedIndex=function(){return this.selectedIndex},E.prototype.setSelectedIndex=function(e,t){void 0===t&&(t={}),this.isIndexValid(e)&&(this.isCheckboxList?this.setCheckboxAtIndex(e,t):this.isRadioList?this.setRadioAtIndex(e,t):this.setSingleSelectionAtIndex(e,t))},E.prototype.handleFocusIn=function(e){0<=e&&(this.focusedItemIndex=e,this.adapter.setAttributeForElementIndex(e,"tabindex","0"),this.adapter.setTabIndexForListItemChildren(e,"0"))},E.prototype.handleFocusOut=function(e){var t=this;0<=e&&(this.adapter.setAttributeForElementIndex(e,"tabindex","-1"),this.adapter.setTabIndexForListItemChildren(e,"-1")),setTimeout(function(){t.adapter.isFocusInsideList()||t.setTabindexToFirstSelectedOrFocusedItem()},0)},E.prototype.isIndexDisabled=function(e){return this.adapter.listItemAtIndexHasClass(e,A.cssClasses.LIST_ITEM_DISABLED_CLASS)},E.prototype.handleKeydown=function(e,t,n){var r,i=this,o="ArrowLeft"===v.normalizeKey(e),s="ArrowUp"===v.normalizeKey(e),a="ArrowRight"===v.normalizeKey(e),c="ArrowDown"===v.normalizeKey(e),u="Home"===v.normalizeKey(e),l="End"===v.normalizeKey(e),d="Enter"===v.normalizeKey(e),f="Spacebar"===v.normalizeKey(e),p=this.isVertical&&c||!this.isVertical&&a,h=this.isVertical&&s||!this.isVertical&&o,m="A"===e.key||"a"===e.key,E=g(e);if(this.adapter.isRootFocused()){if((h||l)&&E([])?(e.preventDefault(),this.focusLastElement()):(p||u)&&E([])?(e.preventDefault(),this.focusFirstElement()):h&&E(["Shift"])&&this.isCheckboxList?(e.preventDefault(),-1!==(I=this.focusLastElement())&&this.setSelectedIndexOnAction(I,!1)):p&&E(["Shift"])&&this.isCheckboxList&&(e.preventDefault(),-1!==(I=this.focusFirstElement())&&this.setSelectedIndexOnAction(I,!1)),this.hasTypeahead){var y={event:e,focusItemAtIndex:function(e){i.focusItemAtIndex(e)},focusedItemIndex:-1,isTargetListItem:t,sortedIndexByFirstChar:this.sortedIndexByFirstChar,isItemAtIndexDisabled:function(e){return i.isIndexDisabled(e)}};_.handleKeydown(y,this.typeaheadState)}}else{var S=this.adapter.getFocusedElementIndex();if(!(-1===S&&(S=n)<0)){if(p&&E([]))T.preventDefaultEvent(e),this.focusNextElement(S);else if(h&&E([]))T.preventDefaultEvent(e),this.focusPrevElement(S);else if(p&&E(["Shift"])&&this.isCheckboxList)T.preventDefaultEvent(e),-1!==(I=this.focusNextElement(S))&&this.setSelectedIndexOnAction(I,!1);else if(h&&E(["Shift"])&&this.isCheckboxList){var I;T.preventDefaultEvent(e),-1!==(I=this.focusPrevElement(S))&&this.setSelectedIndexOnAction(I,!1)}else if(u&&E([]))T.preventDefaultEvent(e),this.focusFirstElement();else if(l&&E([]))T.preventDefaultEvent(e),this.focusLastElement();else if(u&&E(["Control","Shift"])&&this.isCheckboxList){if(T.preventDefaultEvent(e),this.isIndexDisabled(S))return;this.focusFirstElement(),this.toggleCheckboxRange(0,S,S)}else if(l&&E(["Control","Shift"])&&this.isCheckboxList){if(T.preventDefaultEvent(e),this.isIndexDisabled(S))return;this.focusLastElement(),this.toggleCheckboxRange(S,this.adapter.getListItemCount()-1,S)}else if(m&&E(["Control"])&&this.isCheckboxList)e.preventDefault(),this.checkboxListToggleAll(this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex,!0);else if((d||f)&&(E([])||E(["Alt"]))){if(t){if((b=e.target)&&"A"===b.tagName&&d)return;if(T.preventDefaultEvent(e),this.isIndexDisabled(S))return;this.isTypeaheadInProgress()||(this.isSelectableList()&&this.setSelectedIndexOnAction(S,!1),this.adapter.notifyAction(S))}}else if((d||f)&&E(["Shift"])&&this.isCheckboxList){var b;if((b=e.target)&&"A"===b.tagName&&d)return;if(T.preventDefaultEvent(e),this.isIndexDisabled(S))return;this.isTypeaheadInProgress()||(this.toggleCheckboxRange(null!==(r=this.lastSelectedIndex)&&void 0!==r?r:S,S,S),this.adapter.notifyAction(S))}this.hasTypeahead&&(y={event:e,focusItemAtIndex:function(e){i.focusItemAtIndex(e)},focusedItemIndex:this.focusedItemIndex,isTargetListItem:t,sortedIndexByFirstChar:this.sortedIndexByFirstChar,isItemAtIndexDisabled:function(e){return i.isIndexDisabled(e)}},_.handleKeydown(y,this.typeaheadState))}}},E.prototype.handleClick=function(e,t,n){var r,i=g(n);e!==A.numbers.UNSET_INDEX&&(this.isIndexDisabled(e)||(i([])?(this.isSelectableList()&&this.setSelectedIndexOnAction(e,t),this.adapter.notifyAction(e)):this.isCheckboxList&&i(["Shift"])&&(this.toggleCheckboxRange(null!==(r=this.lastSelectedIndex)&&void 0!==r?r:e,e,e),this.adapter.notifyAction(e))))},E.prototype.focusNextElement=function(e){var t=this.adapter.getListItemCount(),n=e,r=null;do{if(t<=++n){if(!this.wrapFocus)return e;n=0}if(n===r)return-1;r=null!=r?r:n}while(!this.areDisabledItemsFocusable&&this.isIndexDisabled(n));return this.focusItemAtIndex(n),n},E.prototype.focusPrevElement=function(e){var t=this.adapter.getListItemCount(),n=e,r=null;do{if(--n<0){if(!this.wrapFocus)return e;n=t-1}if(n===r)return-1;r=null!=r?r:n}while(!this.areDisabledItemsFocusable&&this.isIndexDisabled(n));return this.focusItemAtIndex(n),n},E.prototype.focusFirstElement=function(){return this.focusNextElement(-1)},E.prototype.focusLastElement=function(){return this.focusPrevElement(this.adapter.getListItemCount())},E.prototype.focusInitialElement=function(){var e=this.getFirstSelectedOrFocusedItemIndex();return e!==A.numbers.UNSET_INDEX&&this.focusItemAtIndex(e),e},E.prototype.setEnabled=function(e,t){this.isIndexValid(e,!1)&&(t?(this.adapter.removeClassForElementIndex(e,A.cssClasses.LIST_ITEM_DISABLED_CLASS),this.adapter.setAttributeForElementIndex(e,A.strings.ARIA_DISABLED,"false")):(this.adapter.addClassForElementIndex(e,A.cssClasses.LIST_ITEM_DISABLED_CLASS),this.adapter.setAttributeForElementIndex(e,A.strings.ARIA_DISABLED,"true")))},E.prototype.setSingleSelectionAtIndex=function(e,t){if(void 0===t&&(t={}),this.selectedIndex!==e||t.forceUpdate){var n=A.cssClasses.LIST_ITEM_SELECTED_CLASS;this.useActivatedClass&&(n=A.cssClasses.LIST_ITEM_ACTIVATED_CLASS),this.selectedIndex!==A.numbers.UNSET_INDEX&&this.adapter.removeClassForElementIndex(this.selectedIndex,n),this.setAriaForSingleSelectionAtIndex(e),this.setTabindexAtIndex(e),e!==A.numbers.UNSET_INDEX&&this.adapter.addClassForElementIndex(e,n),this.selectedIndex=e,t.isUserInteraction&&!t.forceUpdate&&this.adapter.notifySelectionChange([e])}},E.prototype.setAriaForSingleSelectionAtIndex=function(e){this.selectedIndex===A.numbers.UNSET_INDEX&&e!==A.numbers.UNSET_INDEX&&(this.ariaCurrentAttrValue=this.adapter.getAttributeForElementIndex(e,A.strings.ARIA_CURRENT));var t=null!==this.ariaCurrentAttrValue,n=t?A.strings.ARIA_CURRENT:A.strings.ARIA_SELECTED;if(this.selectedIndex!==A.numbers.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(this.selectedIndex,n,"false"),e!==A.numbers.UNSET_INDEX){var r=t?this.ariaCurrentAttrValue:"true";this.adapter.setAttributeForElementIndex(e,n,r)}},E.prototype.getSelectionAttribute=function(){return this.useSelectedAttr?A.strings.ARIA_SELECTED:A.strings.ARIA_CHECKED},E.prototype.setRadioAtIndex=function(e,t){void 0===t&&(t={});var n=this.getSelectionAttribute();this.adapter.setCheckedCheckboxOrRadioAtIndex(e,!0),this.selectedIndex===e&&!t.forceUpdate||(this.selectedIndex!==A.numbers.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(this.selectedIndex,n,"false"),this.adapter.setAttributeForElementIndex(e,n,"true"),this.selectedIndex=e,t.isUserInteraction&&!t.forceUpdate&&this.adapter.notifySelectionChange([e]))},E.prototype.setCheckboxAtIndex=function(e,t){void 0===t&&(t={});for(var n=this.selectedIndex,r=t.isUserInteraction?new Set(n===A.numbers.UNSET_INDEX?[]:n):null,i=this.getSelectionAttribute(),o=[],s=0;s<this.adapter.getListItemCount();s++)if(!t.omitDisabledItems||!this.isIndexDisabled(s)){var a=null==r?void 0:r.has(s),c=0<=e.indexOf(s);c!==a&&o.push(s),this.adapter.setCheckedCheckboxOrRadioAtIndex(s,c),this.adapter.setAttributeForElementIndex(s,i,c?"true":"false")}this.selectedIndex=t.omitDisabledItems?this.resolveSelectedIndices(e):e,t.isUserInteraction&&o.length&&this.adapter.notifySelectionChange(o)},E.prototype.resolveSelectedIndices=function(e){var t=this,n=(this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex).filter(function(e){return t.isIndexDisabled(e)}),r=e.filter(function(e){return!t.isIndexDisabled(e)});return f([],d(new Set(f(f([],d(r)),d(n))))).sort(function(e,t){return e-t})},E.prototype.toggleCheckboxRange=function(e,t,n){this.lastSelectedIndex=n;for(var r=new Set(this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex),i=!(null==r?void 0:r.has(n)),o=d([e,t].sort(),2),s=o[0],a=o[1],c=this.getSelectionAttribute(),u=[],l=s;l<=a;l++)this.isIndexDisabled(l)||i!==r.has(l)&&(u.push(l),this.adapter.setCheckedCheckboxOrRadioAtIndex(l,i),this.adapter.setAttributeForElementIndex(l,c,""+i),i?r.add(l):r.delete(l));u.length&&(this.selectedIndex=f([],d(r)),this.adapter.notifySelectionChange(u))},E.prototype.setTabindexAtIndex=function(e){this.focusedItemIndex===A.numbers.UNSET_INDEX&&0!==e&&e!==A.numbers.UNSET_INDEX?this.adapter.setAttributeForElementIndex(0,"tabindex","-1"):0<=this.focusedItemIndex&&this.focusedItemIndex!==e&&this.adapter.setAttributeForElementIndex(this.focusedItemIndex,"tabindex","-1"),this.selectedIndex instanceof Array||this.selectedIndex===e||this.focusedItemIndex===A.numbers.UNSET_INDEX||this.adapter.setAttributeForElementIndex(this.selectedIndex,"tabindex","-1"),e!==A.numbers.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(e,"tabindex","0")},E.prototype.isSelectableList=function(){return this.isSingleSelectionList||this.isCheckboxList||this.isRadioList},E.prototype.setTabindexToFirstSelectedOrFocusedItem=function(){var e=this.getFirstSelectedOrFocusedItemIndex();this.setTabindexAtIndex(e)},E.prototype.getFirstSelectedOrFocusedItemIndex=function(){var t,e,n=this.getFirstEnabledItem();if(0===this.adapter.getListItemCount())return A.numbers.UNSET_INDEX;if(!this.isSelectableList())return Math.max(this.focusedItemIndex,n);if("number"==typeof this.selectedIndex&&this.selectedIndex!==A.numbers.UNSET_INDEX)return this.areDisabledItemsFocusable&&this.isIndexDisabled(this.selectedIndex)?n:this.selectedIndex;if(function(e){return e instanceof Array}(this.selectedIndex)&&0<this.selectedIndex.length){var r=f([],d(this.selectedIndex)).sort(function(e,t){return e-t});try{for(var i=u(r),o=i.next();!o.done;o=i.next()){var s=o.value;if(!this.isIndexDisabled(s)||this.areDisabledItemsFocusable)return s}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}}return n},E.prototype.getFirstEnabledItem=function(){for(var e=this.adapter.getListItemCount(),t=0;t<e&&this.isIndexDisabled(t);)t++;return t===e?A.numbers.UNSET_INDEX:t},E.prototype.isIndexValid=function(e,t){var n=this;if(void 0===t&&(t=!0),e instanceof Array){if(!this.isCheckboxList&&t)throw new Error("MDCListFoundation: Array of index is only supported for checkbox based list");return 0===e.length||e.some(function(e){return n.isIndexInRange(e)})}if("number"!=typeof e)return!1;if(this.isCheckboxList&&t)throw new Error("MDCListFoundation: Expected array of index for checkbox based list but got number: "+e);return this.isIndexInRange(e)||this.isSingleSelectionList&&e===A.numbers.UNSET_INDEX},E.prototype.isIndexInRange=function(e){var t=this.adapter.getListItemCount();return 0<=e&&e<t},E.prototype.setSelectedIndexOnAction=function(e,t){this.lastSelectedIndex=e,this.isCheckboxList?(this.toggleCheckboxAtIndex(e,t),this.adapter.notifySelectionChange([e])):this.setSelectedIndex(e,{isUserInteraction:!0})},E.prototype.toggleCheckboxAtIndex=function(t,e){var n,r=this.getSelectionAttribute(),i=this.adapter.isCheckboxCheckedAtIndex(t);e?n=i:(n=!i,this.adapter.setCheckedCheckboxOrRadioAtIndex(t,n)),this.adapter.setAttributeForElementIndex(t,r,n?"true":"false");var o=this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex.slice();n?o.push(t):o=o.filter(function(e){return e!==t}),this.selectedIndex=o},E.prototype.focusItemAtIndex=function(e){this.adapter.focusItemAtIndex(e),this.focusedItemIndex=e},E.prototype.getEnabledListItemCount=function(){for(var e=this.adapter.getListItemCount(),t=0,n=0;n<e;n++)this.isIndexDisabled(n)||t++;return t},E.prototype.checkboxListToggleAll=function(e,t){var n=this,r=this.getEnabledListItemCount(),i=this.adapter.getListItemCount();if(e.filter(function(e){return!n.isIndexDisabled(e)}).length>=r)this.setCheckboxAtIndex([],{isUserInteraction:t,omitDisabledItems:!0});else{for(var o=[],s=0;s<i;s++)(!this.isIndexDisabled(s)||-1<e.indexOf(s))&&o.push(s);this.setCheckboxAtIndex(o,{isUserInteraction:t,omitDisabledItems:!0})}},E.prototype.typeaheadMatchItem=function(e,t,n){var r=this;void 0===n&&(n=!1);var i={focusItemAtIndex:function(e){r.focusItemAtIndex(e)},focusedItemIndex:t||this.focusedItemIndex,nextChar:e,sortedIndexByFirstChar:this.sortedIndexByFirstChar,skipFocus:n,isItemAtIndexDisabled:function(e){return r.isIndexDisabled(e)}};return _.matchItem(i,this.typeaheadState)},E.prototype.typeaheadInitSortedIndex=function(){return _.initSortedIndex(this.adapter.getListItemCount(),this.adapter.getPrimaryTextAtIndex)},E.prototype.clearTypeaheadBuffer=function(){_.clearBuffer(this.typeaheadState)},E);function E(e){var t=h.call(this,o(o({},E.defaultAdapter),e))||this;return t.wrapFocus=!1,t.isVertical=!0,t.isSingleSelectionList=!1,t.areDisabledItemsFocusable=!1,t.selectedIndex=A.numbers.UNSET_INDEX,t.focusedItemIndex=A.numbers.UNSET_INDEX,t.useActivatedClass=!1,t.useSelectedAttr=!1,t.ariaCurrentAttrValue=null,t.isCheckboxList=!1,t.isRadioList=!1,t.lastSelectedIndex=null,t.hasTypeahead=!1,t.typeaheadState=_.initState(),t.sortedIndexByFirstChar=new Map,t}t.MDCListFoundation=m,t.default=m},,,,,,function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},p=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCMenuSurfaceFoundation=void 0;var s,a=n(7),y=n(58),c=(s=a.MDCFoundation,i(S,s),Object.defineProperty(S,"cssClasses",{get:function(){return y.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(S,"strings",{get:function(){return y.strings},enumerable:!1,configurable:!0}),Object.defineProperty(S,"numbers",{get:function(){return y.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(S,"Corner",{get:function(){return y.Corner},enumerable:!1,configurable:!0}),Object.defineProperty(S,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},hasAnchor:function(){return!1},isElementInContainer:function(){return!1},isFocused:function(){return!1},isRtl:function(){return!1},getInnerDimensions:function(){return{height:0,width:0}},getAnchorDimensions:function(){return null},getViewportDimensions:function(){return{height:0,width:0}},getBodyDimensions:function(){return{height:0,width:0}},getWindowScroll:function(){return{x:0,y:0}},setPosition:function(){},setMaxHeight:function(){},setTransformOrigin:function(){},saveFocus:function(){},restoreFocus:function(){},notifyClose:function(){},notifyClosing:function(){},notifyOpen:function(){},notifyOpening:function(){},registerWindowEventHandler:function(){},deregisterWindowEventHandler:function(){}}},enumerable:!1,configurable:!0}),S.prototype.init=function(){var e=S.cssClasses,t=e.ROOT,n=e.OPEN;if(!this.adapter.hasClass(t))throw new Error(t+" class required in root element.");this.adapter.hasClass(n)&&(this.isSurfaceOpen=!0),this.resizeListener=this.handleResize.bind(this),this.adapter.registerWindowEventHandler("resize",this.resizeListener)},S.prototype.destroy=function(){clearTimeout(this.openAnimationEndTimerId),clearTimeout(this.closeAnimationEndTimerId),cancelAnimationFrame(this.animationRequestId),this.adapter.deregisterWindowEventHandler("resize",this.resizeListener)},S.prototype.setAnchorCorner=function(e){this.anchorCorner=e},S.prototype.flipCornerHorizontally=function(){this.originCorner=this.originCorner^y.CornerBit.RIGHT},S.prototype.setAnchorMargin=function(e){this.anchorMargin.top=e.top||0,this.anchorMargin.right=e.right||0,this.anchorMargin.bottom=e.bottom||0,this.anchorMargin.left=e.left||0},S.prototype.setIsHoisted=function(e){this.isHoistedElement=e},S.prototype.setFixedPosition=function(e){this.isFixedPosition=e},S.prototype.isFixed=function(){return this.isFixedPosition},S.prototype.setAbsolutePosition=function(e,t){this.position.x=this.isFinite(e)?e:0,this.position.y=this.isFinite(t)?t:0},S.prototype.setIsHorizontallyCenteredOnViewport=function(e){this.isHorizontallyCenteredOnViewport=e},S.prototype.setQuickOpen=function(e){this.isQuickOpen=e},S.prototype.setMaxHeight=function(e){this.maxHeight=e},S.prototype.setOpenBottomBias=function(e){this.openBottomBias=e},S.prototype.isOpen=function(){return this.isSurfaceOpen},S.prototype.open=function(){var e=this;this.isSurfaceOpen||(this.adapter.notifyOpening(),this.adapter.saveFocus(),this.isQuickOpen?(this.isSurfaceOpen=!0,this.adapter.addClass(S.cssClasses.OPEN),this.dimensions=this.adapter.getInnerDimensions(),this.autoposition(),this.adapter.notifyOpen()):(this.adapter.addClass(S.cssClasses.ANIMATING_OPEN),this.animationRequestId=requestAnimationFrame(function(){e.dimensions=e.adapter.getInnerDimensions(),e.autoposition(),e.adapter.addClass(S.cssClasses.OPEN),e.openAnimationEndTimerId=setTimeout(function(){e.openAnimationEndTimerId=0,e.adapter.removeClass(S.cssClasses.ANIMATING_OPEN),e.adapter.notifyOpen()},y.numbers.TRANSITION_OPEN_DURATION)}),this.isSurfaceOpen=!0),this.adapter.registerWindowEventHandler("resize",this.resizeListener))},S.prototype.close=function(e){var t=this;if(void 0===e&&(e=!1),this.isSurfaceOpen){if(this.adapter.notifyClosing(),this.adapter.deregisterWindowEventHandler("resize",this.resizeListener),this.isQuickOpen)return this.isSurfaceOpen=!1,e||this.maybeRestoreFocus(),this.adapter.removeClass(S.cssClasses.OPEN),this.adapter.removeClass(S.cssClasses.IS_OPEN_BELOW),void this.adapter.notifyClose();this.adapter.addClass(S.cssClasses.ANIMATING_CLOSED),requestAnimationFrame(function(){t.adapter.removeClass(S.cssClasses.OPEN),t.adapter.removeClass(S.cssClasses.IS_OPEN_BELOW),t.closeAnimationEndTimerId=setTimeout(function(){t.closeAnimationEndTimerId=0,t.adapter.removeClass(S.cssClasses.ANIMATING_CLOSED),t.adapter.notifyClose()},y.numbers.TRANSITION_CLOSE_DURATION)}),this.isSurfaceOpen=!1,e||this.maybeRestoreFocus()}},S.prototype.handleBodyClick=function(e){var t=e.target;this.adapter.isElementInContainer(t)||this.close()},S.prototype.handleKeydown=function(e){var t=e.keyCode;"Escape"!==e.key&&27!==t||this.close()},S.prototype.handleResize=function(){this.dimensions=this.adapter.getInnerDimensions(),this.autoposition()},S.prototype.autoposition=function(){var e;this.measurements=this.getAutoLayoutmeasurements();var t=this.getoriginCorner(),n=this.getMenuSurfaceMaxHeight(t),r=this.hasBit(t,y.CornerBit.BOTTOM)?"bottom":"top",i=this.hasBit(t,y.CornerBit.RIGHT)?"right":"left",o=this.getHorizontalOriginOffset(t),s=this.getVerticalOriginOffset(t),a=this.measurements,c=a.anchorSize,u=a.surfaceSize,l=((e={})[i]=o,e[r]=s,e);c.width/u.width>y.numbers.ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO&&(i="center"),(this.isHoistedElement||this.isFixedPosition)&&this.adjustPositionForHoistedElement(l),this.adapter.setTransformOrigin(i+" "+r),this.adapter.setPosition(l),this.adapter.setMaxHeight(n?n+"px":""),this.hasBit(t,y.CornerBit.BOTTOM)||this.adapter.addClass(S.cssClasses.IS_OPEN_BELOW)},S.prototype.getAutoLayoutmeasurements=function(){var e=this.adapter.getAnchorDimensions(),t=this.adapter.getBodyDimensions(),n=this.adapter.getViewportDimensions(),r=this.adapter.getWindowScroll();return{anchorSize:e=e||{top:this.position.y,right:this.position.x,bottom:this.position.y,left:this.position.x,width:0,height:0},bodySize:t,surfaceSize:this.dimensions,viewportDistance:{top:e.top,right:n.width-e.right,bottom:n.height-e.bottom,left:e.left},viewportSize:n,windowScroll:r}},S.prototype.getoriginCorner=function(){var e,t,n=this.originCorner,r=this.measurements,i=r.viewportDistance,o=r.anchorSize,s=r.surfaceSize,a=S.numbers.MARGIN_TO_EDGE;!(0<(t=this.hasBit(this.anchorCorner,y.CornerBit.BOTTOM)?(e=i.top-a+this.anchorMargin.bottom,i.bottom-a-this.anchorMargin.bottom):(e=i.top-a+this.anchorMargin.top,i.bottom-a+o.height-this.anchorMargin.top))-s.height)&&e>t+this.openBottomBias&&(n=this.setBit(n,y.CornerBit.BOTTOM));var c,u,l=this.adapter.isRtl(),d=this.hasBit(this.anchorCorner,y.CornerBit.FLIP_RTL),f=this.hasBit(this.anchorCorner,y.CornerBit.RIGHT)||this.hasBit(n,y.CornerBit.RIGHT),p=!1;u=(p=l&&d?!f:f)?(c=i.left+o.width+this.anchorMargin.left,i.right-this.anchorMargin.left):(c=i.left+this.anchorMargin.left,i.right+o.width-this.anchorMargin.left);var h=0<c-s.width,m=0<u-s.width,E=this.hasBit(n,y.CornerBit.FLIP_RTL)&&this.hasBit(n,y.CornerBit.RIGHT);return m&&E&&l||!h&&E?n=this.unsetBit(n,y.CornerBit.RIGHT):(h&&p&&l||h&&!p&&f||!m&&u<=c)&&(n=this.setBit(n,y.CornerBit.RIGHT)),n},S.prototype.getMenuSurfaceMaxHeight=function(e){if(0<this.maxHeight)return this.maxHeight;var t=this.measurements.viewportDistance,n=0,r=this.hasBit(e,y.CornerBit.BOTTOM),i=this.hasBit(this.anchorCorner,y.CornerBit.BOTTOM),o=S.numbers.MARGIN_TO_EDGE;return r?(n=t.top+this.anchorMargin.top-o,i||(n+=this.measurements.anchorSize.height)):(n=t.bottom-this.anchorMargin.bottom+this.measurements.anchorSize.height-o,i&&(n-=this.measurements.anchorSize.height)),n},S.prototype.getHorizontalOriginOffset=function(e){var t=this.measurements.anchorSize,n=this.hasBit(e,y.CornerBit.RIGHT),r=this.hasBit(this.anchorCorner,y.CornerBit.RIGHT);if(n){var i=r?t.width-this.anchorMargin.left:this.anchorMargin.right;return this.isHoistedElement||this.isFixedPosition?i-(this.measurements.viewportSize.width-this.measurements.bodySize.width):i}return r?t.width-this.anchorMargin.right:this.anchorMargin.left},S.prototype.getVerticalOriginOffset=function(e){var t=this.measurements.anchorSize,n=this.hasBit(e,y.CornerBit.BOTTOM),r=this.hasBit(this.anchorCorner,y.CornerBit.BOTTOM);return n?r?t.height-this.anchorMargin.top:-this.anchorMargin.bottom:r?t.height+this.anchorMargin.bottom:this.anchorMargin.top},S.prototype.adjustPositionForHoistedElement=function(e){var t,n,r=this.measurements,i=r.windowScroll,o=r.viewportDistance,s=r.surfaceSize,a=r.viewportSize,c=Object.keys(e);try{for(var u=p(c),l=u.next();!l.done;l=u.next()){var d=l.value,f=e[d]||0;!this.isHorizontallyCenteredOnViewport||"left"!==d&&"right"!==d?(f+=o[d],this.isFixedPosition||("top"===d?f+=i.y:"bottom"===d?f-=i.y:"left"===d?f+=i.x:f-=i.x),e[d]=f):e[d]=(a.width-s.width)/2}}catch(e){t={error:e}}finally{try{l&&!l.done&&(n=u.return)&&n.call(u)}finally{if(t)throw t.error}}},S.prototype.maybeRestoreFocus=function(){var e=this,t=this.adapter.isFocused(),n=this.adapter.getOwnerDocument?this.adapter.getOwnerDocument():document,r=n.activeElement&&this.adapter.isElementInContainer(n.activeElement);(t||r)&&setTimeout(function(){e.adapter.restoreFocus()},y.numbers.TOUCH_EVENT_WAIT_MS)},S.prototype.hasBit=function(e,t){return Boolean(e&t)},S.prototype.setBit=function(e,t){return e|t},S.prototype.unsetBit=function(e,t){return e^t},S.prototype.isFinite=function(e){return"number"==typeof e&&isFinite(e)},S);function S(e){var t=s.call(this,o(o({},S.defaultAdapter),e))||this;return t.isSurfaceOpen=!1,t.isQuickOpen=!1,t.isHoistedElement=!1,t.isFixedPosition=!1,t.isHorizontallyCenteredOnViewport=!1,t.maxHeight=0,t.openBottomBias=0,t.openAnimationEndTimerId=0,t.closeAnimationEndTimerId=0,t.animationRequestId=0,t.anchorCorner=y.Corner.TOP_START,t.originCorner=y.Corner.TOP_START,t.anchorMargin={top:0,right:0,bottom:0,left:0},t.position={x:0,y:0},t}t.MDCMenuSurfaceFoundation=c,t.default=c},,function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCList=void 0;var o,s=n(13),a=n(49),c=n(56),u=n(61),l=(o=s.MDCComponent,i(d,o),Object.defineProperty(d.prototype,"vertical",{set:function(e){this.foundation.setVerticalOrientation(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"listElements",{get:function(){return Array.from(this.root.querySelectorAll("."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]))},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"wrapFocus",{set:function(e){this.foundation.setWrapFocus(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"typeaheadInProgress",{get:function(){return this.foundation.isTypeaheadInProgress()},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"hasTypeahead",{set:function(e){this.foundation.setHasTypeahead(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"singleSelection",{set:function(e){this.foundation.setSingleSelection(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"disabledItemsFocusable",{set:function(e){this.foundation.setDisabledItemsFocusable(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"selectedIndex",{get:function(){return this.foundation.getSelectedIndex()},set:function(e){this.foundation.setSelectedIndex(e)},enumerable:!1,configurable:!0}),d.attachTo=function(e){return new d(e)},d.prototype.initialSyncWithDOM=function(){this.isEvolutionEnabled=c.evolutionAttribute in this.root.dataset,this.isEvolutionEnabled?this.classNameMap=c.evolutionClassNameMap:a.matches(this.root,c.strings.DEPRECATED_SELECTOR)?this.classNameMap=c.deprecatedClassNameMap:this.classNameMap=Object.values(c.cssClasses).reduce(function(e,t){return e[t]=t,e},{}),this.handleClick=this.handleClickEvent.bind(this),this.handleKeydown=this.handleKeydownEvent.bind(this),this.focusInEventListener=this.handleFocusInEvent.bind(this),this.focusOutEventListener=this.handleFocusOutEvent.bind(this),this.listen("keydown",this.handleKeydown),this.listen("click",this.handleClick),this.listen("focusin",this.focusInEventListener),this.listen("focusout",this.focusOutEventListener),this.layout(),this.initializeListType(),this.ensureFocusable()},d.prototype.destroy=function(){this.unlisten("keydown",this.handleKeydown),this.unlisten("click",this.handleClick),this.unlisten("focusin",this.focusInEventListener),this.unlisten("focusout",this.focusOutEventListener)},d.prototype.layout=function(){var e=this.root.getAttribute(c.strings.ARIA_ORIENTATION);this.vertical=e!==c.strings.ARIA_ORIENTATION_HORIZONTAL;var t="."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+":not([tabindex])",n=c.strings.FOCUSABLE_CHILD_ELEMENTS,r=this.root.querySelectorAll(t);r.length&&Array.prototype.forEach.call(r,function(e){e.setAttribute("tabindex","-1")});var i=this.root.querySelectorAll(n);i.length&&Array.prototype.forEach.call(i,function(e){e.setAttribute("tabindex","-1")}),this.isEvolutionEnabled&&this.foundation.setUseSelectedAttribute(!0),this.foundation.layout()},d.prototype.getPrimaryText=function(e){var t,n=e.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_PRIMARY_TEXT_CLASS]);if(this.isEvolutionEnabled||n)return null!==(t=null==n?void 0:n.textContent)&&void 0!==t?t:"";var r=e.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_TEXT_CLASS]);return r&&r.textContent||""},d.prototype.initializeListType=function(){var t=this;if(this.isInteractive=a.matches(this.root,c.strings.ARIA_INTERACTIVE_ROLES_SELECTOR),this.isEvolutionEnabled&&this.isInteractive){var e=Array.from(this.root.querySelectorAll(c.strings.SELECTED_ITEM_SELECTOR),function(e){return t.listElements.indexOf(e)});a.matches(this.root,c.strings.ARIA_MULTI_SELECTABLE_SELECTOR)?this.selectedIndex=e:0<e.length&&(this.selectedIndex=e[0])}else{var n=this.root.querySelectorAll(c.strings.ARIA_ROLE_CHECKBOX_SELECTOR),r=this.root.querySelector(c.strings.ARIA_CHECKED_RADIO_SELECTOR);if(n.length){var i=this.root.querySelectorAll(c.strings.ARIA_CHECKED_CHECKBOX_SELECTOR);this.selectedIndex=Array.from(i,function(e){return t.listElements.indexOf(e)})}else r&&(this.selectedIndex=this.listElements.indexOf(r))}},d.prototype.setEnabled=function(e,t){this.foundation.setEnabled(e,t)},d.prototype.typeaheadMatchItem=function(e,t){return this.foundation.typeaheadMatchItem(e,t,!0)},d.prototype.getDefaultFoundation=function(){var i=this,e={addClassForElementIndex:function(e,t){var n=i.listElements[e];n&&n.classList.add(i.classNameMap[t])},focusItemAtIndex:function(e){var t;null===(t=i.listElements[e])||void 0===t||t.focus()},getAttributeForElementIndex:function(e,t){return i.listElements[e].getAttribute(t)},getFocusedElementIndex:function(){return i.listElements.indexOf(document.activeElement)},getListItemCount:function(){return i.listElements.length},getPrimaryTextAtIndex:function(e){return i.getPrimaryText(i.listElements[e])},hasCheckboxAtIndex:function(e){return!!i.listElements[e].querySelector(c.strings.CHECKBOX_SELECTOR)},hasRadioAtIndex:function(e){return!!i.listElements[e].querySelector(c.strings.RADIO_SELECTOR)},isCheckboxCheckedAtIndex:function(e){return i.listElements[e].querySelector(c.strings.CHECKBOX_SELECTOR).checked},isFocusInsideList:function(){return i.root!==document.activeElement&&i.root.contains(document.activeElement)},isRootFocused:function(){return document.activeElement===i.root},listItemAtIndexHasClass:function(e,t){return i.listElements[e].classList.contains(i.classNameMap[t])},notifyAction:function(e){i.emit(c.strings.ACTION_EVENT,{index:e},!0)},notifySelectionChange:function(e){i.emit(c.strings.SELECTION_CHANGE_EVENT,{changedIndices:e},!0)},removeClassForElementIndex:function(e,t){var n=i.listElements[e];n&&n.classList.remove(i.classNameMap[t])},setAttributeForElementIndex:function(e,t,n){var r=i.listElements[e];r&&i.safeSetAttribute(r,t,n)},setCheckedCheckboxOrRadioAtIndex:function(e,t){var n=i.listElements[e].querySelector(c.strings.CHECKBOX_RADIO_SELECTOR);n.checked=t;var r=document.createEvent("Event");r.initEvent("change",!0,!0),n.dispatchEvent(r)},setTabIndexForListItemChildren:function(e,t){var n=i.listElements[e],r=c.strings.CHILD_ELEMENTS_TO_TOGGLE_TABINDEX;Array.prototype.forEach.call(n.querySelectorAll(r),function(e){e.tabIndex=Number(t)})}};return new u.MDCListFoundation(e)},d.prototype.ensureFocusable=function(){if(this.isEvolutionEnabled&&this.isInteractive&&!this.root.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+'[tabindex="0"]')){var e=this.initialFocusIndex();-1!==e&&(this.listElements[e].tabIndex=0)}},d.prototype.initialFocusIndex=function(){if(this.selectedIndex instanceof Array&&0<this.selectedIndex.length)return this.selectedIndex[0];if("number"==typeof this.selectedIndex&&this.selectedIndex!==c.numbers.UNSET_INDEX)return this.selectedIndex;var e=this.root.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+":not(."+this.classNameMap[c.cssClasses.LIST_ITEM_DISABLED_CLASS]+")");return null===e?-1:this.getListItemIndex(e)},d.prototype.getListItemIndex=function(e){var t=a.closest(e,"."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+", ."+this.classNameMap[c.cssClasses.ROOT]);return t&&a.matches(t,"."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS])?this.listElements.indexOf(t):-1},d.prototype.handleFocusInEvent=function(e){var t=this.getListItemIndex(e.target);this.foundation.handleFocusIn(t)},d.prototype.handleFocusOutEvent=function(e){var t=this.getListItemIndex(e.target);this.foundation.handleFocusOut(t)},d.prototype.handleKeydownEvent=function(e){var t=this.getListItemIndex(e.target),n=e.target;this.foundation.handleKeydown(e,n.classList.contains(this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]),t)},d.prototype.handleClickEvent=function(e){var t=this.getListItemIndex(e.target),n=e.target;this.foundation.handleClick(t,a.matches(n,c.strings.CHECKBOX_RADIO_SELECTOR),e)},d);function d(){return null!==o&&o.apply(this,arguments)||this}t.MDCList=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleKeydown=t.clearBuffer=t.isTypingInProgress=t.matchItem=t.initSortedIndex=t.initState=void 0;var E=n(55),u=n(56),y=n(60);function S(e,t){var n,r=e.nextChar,i=e.focusItemAtIndex,o=e.sortedIndexByFirstChar,s=e.focusedItemIndex,a=e.skipFocus,c=e.isItemAtIndexDisabled;return clearTimeout(t.bufferClearTimeout),t.bufferClearTimeout=setTimeout(function(){l(t)},u.numbers.TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS),t.typeaheadBuffer=t.typeaheadBuffer+r,-1===(n=1===t.typeaheadBuffer.length?function(e,t,n,r){var i=r.typeaheadBuffer[0],o=e.get(i);if(!o)return-1;if(i===r.currentFirstChar&&o[r.sortedIndexCursor].index===t){r.sortedIndexCursor=(r.sortedIndexCursor+1)%o.length;var s=o[r.sortedIndexCursor].index;if(!n(s))return s}r.currentFirstChar=i;var a,c=-1;for(a=0;a<o.length;a++)if(!n(o[a].index)){c=a;break}for(;a<o.length;a++)if(o[a].index>t&&!n(o[a].index)){c=a;break}return-1===c?-1:(r.sortedIndexCursor=c,o[r.sortedIndexCursor].index)}(o,s,c,t):function(e,t,n){var r=n.typeaheadBuffer[0],i=e.get(r);if(!i)return-1;var o=i[n.sortedIndexCursor];if(0===o.text.lastIndexOf(n.typeaheadBuffer,0)&&!t(o.index))return o.index;var s=(n.sortedIndexCursor+1)%i.length,a=-1;for(;s!==n.sortedIndexCursor;){var c=i[s],u=0===c.text.lastIndexOf(n.typeaheadBuffer,0),l=!t(c.index);if(u&&l){a=s;break}s=(s+1)%i.length}return-1===a?-1:(n.sortedIndexCursor=a,i[n.sortedIndexCursor].index)}(o,c,t))||a||i(n),n}function I(e){return 0<e.typeaheadBuffer.length}function l(e){e.typeaheadBuffer=""}t.initState=function(){return{bufferClearTimeout:0,currentFirstChar:"",sortedIndexCursor:0,typeaheadBuffer:""}},t.initSortedIndex=function(e,t){for(var n=new Map,r=0;r<e;r++){var i=t(r).trim();if(i){var o=i[0].toLowerCase();n.has(o)||n.set(o,[]),n.get(o).push({text:i.toLowerCase(),index:r})}}return n.forEach(function(e){e.sort(function(e,t){return e.index-t.index})}),n},t.matchItem=S,t.isTypingInProgress=I,t.clearBuffer=l,t.handleKeydown=function(e,t){var n=e.event,r=e.isTargetListItem,i=e.focusedItemIndex,o=e.focusItemAtIndex,s=e.sortedIndexByFirstChar,a=e.isItemAtIndexDisabled,c="ArrowLeft"===E.normalizeKey(n),u="ArrowUp"===E.normalizeKey(n),l="ArrowRight"===E.normalizeKey(n),d="ArrowDown"===E.normalizeKey(n),f="Home"===E.normalizeKey(n),p="End"===E.normalizeKey(n),h="Enter"===E.normalizeKey(n),m="Spacebar"===E.normalizeKey(n);return n.altKey||n.ctrlKey||n.metaKey||c||u||l||d||f||p||h?-1:m||1!==n.key.length?m?(r&&y.preventDefaultEvent(n),r&&I(t)?S({focusItemAtIndex:o,focusedItemIndex:i,nextChar:" ",sortedIndexByFirstChar:s,skipFocus:!1,isItemAtIndexDisabled:a},t):-1):-1:(y.preventDefaultEvent(n),S({focusItemAtIndex:o,focusedItemIndex:i,nextChar:n.key.toLowerCase(),sortedIndexByFirstChar:s,skipFocus:!1,isItemAtIndexDisabled:a},t))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultFocusState=t.numbers=t.strings=t.cssClasses=void 0;t.cssClasses={MENU_SELECTED_LIST_ITEM:"mdc-menu-item--selected",MENU_SELECTION_GROUP:"mdc-menu__selection-group",ROOT:"mdc-menu"};t.strings={ARIA_CHECKED_ATTR:"aria-checked",ARIA_DISABLED_ATTR:"aria-disabled",CHECKBOX_SELECTOR:'input[type="checkbox"]',LIST_SELECTOR:".mdc-list,.mdc-deprecated-list",SELECTED_EVENT:"MDCMenu:selected",SKIP_RESTORE_FOCUS:"data-menu-item-skip-restore-focus"};var r,i;t.numbers={FOCUS_ROOT_INDEX:-1},(i=r=r||{})[i.NONE=0]="NONE",i[i.LIST_ROOT=1]="LIST_ROOT",i[i.FIRST_ITEM=2]="FIRST_ITEM",i[i.LAST_ITEM=3]="LAST_ITEM",t.DefaultFocusState=r},,,,,,,,,,,,,function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCMenuSurface=void 0;var o,s=n(57),a=n(13),c=n(58),u=n(67),l=(o=a.MDCComponent,i(d,o),d.attachTo=function(e){return new d(e)},d.prototype.initialSyncWithDOM=function(){var t=this,e=this.root.parentElement;this.anchorElement=e&&e.classList.contains(c.cssClasses.ANCHOR)?e:null,this.root.classList.contains(c.cssClasses.FIXED)&&this.setFixedPosition(!0),this.handleKeydown=function(e){t.foundation.handleKeydown(e)},this.handleBodyClick=function(e){t.foundation.handleBodyClick(e)},this.registerBodyClickListener=function(){document.body.addEventListener("click",t.handleBodyClick,{capture:!0})},this.deregisterBodyClickListener=function(){document.body.removeEventListener("click",t.handleBodyClick,{capture:!0})},this.listen("keydown",this.handleKeydown),this.listen(c.strings.OPENED_EVENT,this.registerBodyClickListener),this.listen(c.strings.CLOSED_EVENT,this.deregisterBodyClickListener)},d.prototype.destroy=function(){this.unlisten("keydown",this.handleKeydown),this.unlisten(c.strings.OPENED_EVENT,this.registerBodyClickListener),this.unlisten(c.strings.CLOSED_EVENT,this.deregisterBodyClickListener),o.prototype.destroy.call(this)},d.prototype.isOpen=function(){return this.foundation.isOpen()},d.prototype.open=function(){this.foundation.open()},d.prototype.close=function(e){void 0===e&&(e=!1),this.foundation.close(e)},Object.defineProperty(d.prototype,"quickOpen",{set:function(e){this.foundation.setQuickOpen(e)},enumerable:!1,configurable:!0}),d.prototype.setIsHoisted=function(e){this.foundation.setIsHoisted(e)},d.prototype.setMenuSurfaceAnchorElement=function(e){this.anchorElement=e},d.prototype.setFixedPosition=function(e){e?this.root.classList.add(c.cssClasses.FIXED):this.root.classList.remove(c.cssClasses.FIXED),this.foundation.setFixedPosition(e)},d.prototype.setAbsolutePosition=function(e,t){this.foundation.setAbsolutePosition(e,t),this.setIsHoisted(!0)},d.prototype.setAnchorCorner=function(e){this.foundation.setAnchorCorner(e)},d.prototype.setAnchorMargin=function(e){this.foundation.setAnchorMargin(e)},d.prototype.getDefaultFoundation=function(){var n=this,e={addClass:function(e){n.root.classList.add(e)},removeClass:function(e){n.root.classList.remove(e)},hasClass:function(e){return n.root.classList.contains(e)},hasAnchor:function(){return!!n.anchorElement},notifyClose:function(){n.emit(u.MDCMenuSurfaceFoundation.strings.CLOSED_EVENT,{})},notifyClosing:function(){n.emit(u.MDCMenuSurfaceFoundation.strings.CLOSING_EVENT,{})},notifyOpen:function(){n.emit(u.MDCMenuSurfaceFoundation.strings.OPENED_EVENT,{})},notifyOpening:function(){n.emit(u.MDCMenuSurfaceFoundation.strings.OPENING_EVENT,{})},isElementInContainer:function(e){return n.root.contains(e)},isRtl:function(){return"rtl"===getComputedStyle(n.root).getPropertyValue("direction")},setTransformOrigin:function(e){var t=s.getCorrectPropertyName(window,"transform")+"-origin";n.root.style.setProperty(t,e)},isFocused:function(){return document.activeElement===n.root},saveFocus:function(){n.previousFocus=document.activeElement},restoreFocus:function(){n.root.contains(document.activeElement)&&n.previousFocus&&n.previousFocus.focus&&n.previousFocus.focus()},getInnerDimensions:function(){return{width:n.root.offsetWidth,height:n.root.offsetHeight}},getAnchorDimensions:function(){return n.anchorElement?n.anchorElement.getBoundingClientRect():null},getViewportDimensions:function(){return{width:window.innerWidth,height:window.innerHeight}},getBodyDimensions:function(){return{width:document.body.clientWidth,height:document.body.clientHeight}},getWindowScroll:function(){return{x:window.pageXOffset,y:window.pageYOffset}},setPosition:function(e){var t=n.root;t.style.left="left"in e?e.left+"px":"",t.style.right="right"in e?e.right+"px":"",t.style.top="top"in e?e.top+"px":"",t.style.bottom="bottom"in e?e.bottom+"px":""},setMaxHeight:function(e){n.root.style.maxHeight=e},registerWindowEventHandler:function(e,t){window.addEventListener(e,t)},deregisterWindowEventHandler:function(e,t){window.removeEventListener(e,t)}};return new u.MDCMenuSurfaceFoundation(e)},d);function d(){return null!==o&&o.apply(this,arguments)||this}t.MDCMenuSurface=l},,,,,,,,,,function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCMenuFoundation=void 0;var s,a=n(7),c=n(56),u=n(71),l=(s=a.MDCFoundation,i(d,s),Object.defineProperty(d,"cssClasses",{get:function(){return u.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(d,"strings",{get:function(){return u.strings},enumerable:!1,configurable:!0}),Object.defineProperty(d,"numbers",{get:function(){return u.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(d,"defaultAdapter",{get:function(){return{addClassToElementAtIndex:function(){},removeClassFromElementAtIndex:function(){},addAttributeToElementAtIndex:function(){},removeAttributeFromElementAtIndex:function(){},getAttributeFromElementAtIndex:function(){return null},elementContainsClass:function(){return!1},closeSurface:function(){},getElementIndex:function(){return-1},notifySelected:function(){},getMenuItemCount:function(){return 0},focusItemAtIndex:function(){},focusListRoot:function(){},getSelectedSiblingOfItemAtIndex:function(){return-1},isSelectableItemAtIndex:function(){return!1}}},enumerable:!1,configurable:!0}),d.prototype.destroy=function(){this.adapter.closeSurface()},d.prototype.handleKeydown=function(e){var t=e.key,n=e.keyCode;"Tab"!==t&&9!==n||this.adapter.closeSurface(!0)},d.prototype.handleItemAction=function(e){var t=this.adapter.getElementIndex(e);if(!(t<0)){this.adapter.notifySelected({index:t});var n="true"===this.adapter.getAttributeFromElementAtIndex(t,u.strings.SKIP_RESTORE_FOCUS);this.adapter.closeSurface(n),this.adapter.isSelectableItemAtIndex(t)&&this.setSelectedIndex(t)}},d.prototype.handleMenuSurfaceOpened=function(){switch(this.defaultFocusState){case u.DefaultFocusState.FIRST_ITEM:this.adapter.focusItemAtIndex(0);break;case u.DefaultFocusState.LAST_ITEM:this.adapter.focusItemAtIndex(this.adapter.getMenuItemCount()-1);break;case u.DefaultFocusState.NONE:break;default:this.adapter.focusListRoot()}},d.prototype.setDefaultFocusState=function(e){this.defaultFocusState=e},d.prototype.getSelectedIndex=function(){return this.selectedIndex},d.prototype.setSelectedIndex=function(e){if(this.validatedIndex(e),!this.adapter.isSelectableItemAtIndex(e))throw new Error("MDCMenuFoundation: No selection group at specified index.");var t=this.adapter.getSelectedSiblingOfItemAtIndex(e);0<=t&&(this.adapter.removeAttributeFromElementAtIndex(t,u.strings.ARIA_CHECKED_ATTR),this.adapter.removeClassFromElementAtIndex(t,u.cssClasses.MENU_SELECTED_LIST_ITEM)),this.adapter.addClassToElementAtIndex(e,u.cssClasses.MENU_SELECTED_LIST_ITEM),this.adapter.addAttributeToElementAtIndex(e,u.strings.ARIA_CHECKED_ATTR,"true"),this.selectedIndex=e},d.prototype.setEnabled=function(e,t){this.validatedIndex(e),t?(this.adapter.removeClassFromElementAtIndex(e,c.cssClasses.LIST_ITEM_DISABLED_CLASS),this.adapter.addAttributeToElementAtIndex(e,u.strings.ARIA_DISABLED_ATTR,"false")):(this.adapter.addClassToElementAtIndex(e,c.cssClasses.LIST_ITEM_DISABLED_CLASS),this.adapter.addAttributeToElementAtIndex(e,u.strings.ARIA_DISABLED_ATTR,"true"))},d.prototype.validatedIndex=function(e){var t=this.adapter.getMenuItemCount();if(!(0<=e&&e<t))throw new Error("MDCMenuFoundation: No list item at specified index.")},d);function d(e){var t=s.call(this,o(o({},d.defaultAdapter),e))||this;return t.defaultFocusState=u.DefaultFocusState.LIST_ROOT,t.selectedIndex=-1,t}t.MDCMenuFoundation=l,t.default=l},,,,,,,,,,,,,function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCMenu=void 0;var o,s=n(13),a=n(49),c=n(69),u=n(56),l=n(61),d=n(84),f=n(67),p=n(71),h=n(94),m=(o=s.MDCComponent,i(E,o),E.attachTo=function(e){return new E(e)},E.prototype.initialize=function(e,t){void 0===e&&(e=function(e){return new d.MDCMenuSurface(e)}),void 0===t&&(t=function(e){return new c.MDCList(e)}),this.menuSurfaceFactory=e,this.listFactory=t},E.prototype.initialSyncWithDOM=function(){var t=this;this.menuSurface=this.menuSurfaceFactory(this.root);var e=this.root.querySelector(p.strings.LIST_SELECTOR);e?(this.list=this.listFactory(e),this.list.wrapFocus=!0):this.list=null,this.handleKeydown=function(e){t.foundation.handleKeydown(e)},this.handleItemAction=function(e){t.foundation.handleItemAction(t.items[e.detail.index])},this.handleMenuSurfaceOpened=function(){t.foundation.handleMenuSurfaceOpened()},this.menuSurface.listen(f.MDCMenuSurfaceFoundation.strings.OPENED_EVENT,this.handleMenuSurfaceOpened),this.listen("keydown",this.handleKeydown),this.listen(l.MDCListFoundation.strings.ACTION_EVENT,this.handleItemAction)},E.prototype.destroy=function(){this.list&&this.list.destroy(),this.menuSurface.destroy(),this.menuSurface.unlisten(f.MDCMenuSurfaceFoundation.strings.OPENED_EVENT,this.handleMenuSurfaceOpened),this.unlisten("keydown",this.handleKeydown),this.unlisten(l.MDCListFoundation.strings.ACTION_EVENT,this.handleItemAction),o.prototype.destroy.call(this)},Object.defineProperty(E.prototype,"open",{get:function(){return this.menuSurface.isOpen()},set:function(e){e?this.menuSurface.open():this.menuSurface.close()},enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype,"wrapFocus",{get:function(){return!!this.list&&this.list.wrapFocus},set:function(e){this.list&&(this.list.wrapFocus=e)},enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype,"hasTypeahead",{set:function(e){this.list&&(this.list.hasTypeahead=e)},enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype,"typeaheadInProgress",{get:function(){return!!this.list&&this.list.typeaheadInProgress},enumerable:!1,configurable:!0}),E.prototype.typeaheadMatchItem=function(e,t){return this.list?this.list.typeaheadMatchItem(e,t):-1},E.prototype.layout=function(){this.list&&this.list.layout()},Object.defineProperty(E.prototype,"items",{get:function(){return this.list?this.list.listElements:[]},enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype,"singleSelection",{set:function(e){this.list&&(this.list.singleSelection=e)},enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype,"selectedIndex",{get:function(){return this.list?this.list.selectedIndex:u.numbers.UNSET_INDEX},set:function(e){this.list&&(this.list.selectedIndex=e)},enumerable:!1,configurable:!0}),Object.defineProperty(E.prototype,"quickOpen",{set:function(e){this.menuSurface.quickOpen=e},enumerable:!1,configurable:!0}),E.prototype.setDefaultFocusState=function(e){this.foundation.setDefaultFocusState(e)},E.prototype.setAnchorCorner=function(e){this.menuSurface.setAnchorCorner(e)},E.prototype.setAnchorMargin=function(e){this.menuSurface.setAnchorMargin(e)},E.prototype.setSelectedIndex=function(e){this.foundation.setSelectedIndex(e)},E.prototype.setEnabled=function(e,t){this.foundation.setEnabled(e,t)},E.prototype.getOptionByIndex=function(e){return e<this.items.length?this.items[e]:null},E.prototype.getPrimaryTextAtIndex=function(e){var t=this.getOptionByIndex(e);return t&&this.list&&this.list.getPrimaryText(t)||""},E.prototype.setFixedPosition=function(e){this.menuSurface.setFixedPosition(e)},E.prototype.setIsHoisted=function(e){this.menuSurface.setIsHoisted(e)},E.prototype.setAbsolutePosition=function(e,t){this.menuSurface.setAbsolutePosition(e,t)},E.prototype.setAnchorElement=function(e){this.menuSurface.anchorElement=e},E.prototype.getDefaultFoundation=function(){var i=this,e={addClassToElementAtIndex:function(e,t){i.items[e].classList.add(t)},removeClassFromElementAtIndex:function(e,t){i.items[e].classList.remove(t)},addAttributeToElementAtIndex:function(e,t,n){var r=i.items;i.safeSetAttribute(r[e],t,n)},removeAttributeFromElementAtIndex:function(e,t){i.items[e].removeAttribute(t)},getAttributeFromElementAtIndex:function(e,t){return i.items[e].getAttribute(t)},elementContainsClass:function(e,t){return e.classList.contains(t)},closeSurface:function(e){i.menuSurface.close(e)},getElementIndex:function(e){return i.items.indexOf(e)},notifySelected:function(e){i.emit(p.strings.SELECTED_EVENT,{index:e.index,item:i.items[e.index]})},getMenuItemCount:function(){return i.items.length},focusItemAtIndex:function(e){i.items[e].focus()},focusListRoot:function(){i.root.querySelector(p.strings.LIST_SELECTOR).focus()},isSelectableItemAtIndex:function(e){return!!a.closest(i.items[e],"."+p.cssClasses.MENU_SELECTION_GROUP)},getSelectedSiblingOfItemAtIndex:function(e){var t=a.closest(i.items[e],"."+p.cssClasses.MENU_SELECTION_GROUP).querySelector("."+p.cssClasses.MENU_SELECTED_LIST_ITEM);return t?i.items.indexOf(t):-1}};return new h.MDCMenuFoundation(e)},E);function E(){return null!==o&&o.apply(this,arguments)||this}t.MDCMenu=m},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.Corner=void 0;var o=n(58);Object.defineProperty(t,"Corner",{enumerable:!0,get:function(){return o.Corner}}),i(n(248),t),i(n(107),t),i(n(71),t),i(n(94),t),i(n(249),t)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0})}],i.c=r,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)i.d(n,r,function(e){return t[e]}.bind(null,r));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=247);function i(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}var n,r});