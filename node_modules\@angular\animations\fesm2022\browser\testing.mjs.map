{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../../packages/animations/browser/testing/src/mock_animation_driver.ts", "../../../../../../../packages/animations/browser/testing/public_api.ts", "../../../../../../../packages/animations/browser/testing/index.ts", "../../../../../../../packages/animations/browser/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {AnimationPlayer, AUTO_STYLE, NoopAnimationPlayer, ɵStyleDataMap} from '@angular/animations';\nimport {\n  AnimationDriver,\n  ɵallowPreviousPlayerStylesMerge as allowPreviousPlayerStylesMerge,\n  ɵcamelCaseToDashCase,\n  ɵcontainsElement as containsElement,\n  ɵgetParentElement as getParentElement,\n  ɵinvokeQuery as invokeQuery,\n  ɵnormalizeKeyframes as normalizeKeyframes,\n  ɵvalidateStyleProperty as validateStyleProperty,\n  ɵvalidateWebAnimatableStyleProperty,\n} from '@angular/animations/browser';\n\n/**\n * @publicApi\n */\nexport class MockAnimationDriver implements AnimationDriver {\n  static log: AnimationPlayer[] = [];\n\n  validateStyleProperty(prop: string): boolean {\n    return validateStyleProperty(prop);\n  }\n\n  validateAnimatableStyleProperty(prop: string): boolean {\n    const cssProp = ɵcamelCaseToDashCase(prop);\n    return ɵvalidateWebAnimatableStyleProperty(cssProp);\n  }\n\n  matchesElement(_element: any, _selector: string): boolean {\n    return false;\n  }\n\n  containsElement(elm1: any, elm2: any): boolean {\n    return containsElement(elm1, elm2);\n  }\n\n  getParentElement(element: unknown): unknown {\n    return getParentElement(element);\n  }\n\n  query(element: any, selector: string, multi: boolean): any[] {\n    return invokeQuery(element, selector, multi);\n  }\n\n  computeStyle(element: any, prop: string, defaultValue?: string): string {\n    return defaultValue || '';\n  }\n\n  animate(\n    element: any,\n    keyframes: Array<ɵStyleDataMap>,\n    duration: number,\n    delay: number,\n    easing: string,\n    previousPlayers: any[] = [],\n  ): MockAnimationPlayer {\n    const player = new MockAnimationPlayer(\n      element,\n      keyframes,\n      duration,\n      delay,\n      easing,\n      previousPlayers,\n    );\n    MockAnimationDriver.log.push(<AnimationPlayer>player);\n    return player;\n  }\n}\n\n/**\n * @publicApi\n */\nexport class MockAnimationPlayer extends NoopAnimationPlayer {\n  private __finished = false;\n  private __started = false;\n  public previousStyles: ɵStyleDataMap = new Map();\n  private _onInitFns: (() => any)[] = [];\n  public currentSnapshot: ɵStyleDataMap = new Map();\n  private _keyframes: Array<ɵStyleDataMap> = [];\n\n  constructor(\n    public element: any,\n    public keyframes: Array<ɵStyleDataMap>,\n    public duration: number,\n    public delay: number,\n    public easing: string,\n    public previousPlayers: any[],\n  ) {\n    super(duration, delay);\n\n    this._keyframes = normalizeKeyframes(keyframes);\n\n    if (allowPreviousPlayerStylesMerge(duration, delay)) {\n      previousPlayers.forEach((player) => {\n        if (player instanceof MockAnimationPlayer) {\n          const styles = player.currentSnapshot;\n          styles.forEach((val, prop) => this.previousStyles.set(prop, val));\n        }\n      });\n    }\n  }\n\n  /** @internal */\n  onInit(fn: () => any) {\n    this._onInitFns.push(fn);\n  }\n\n  /** @internal */\n  override init() {\n    super.init();\n    this._onInitFns.forEach((fn) => fn());\n    this._onInitFns = [];\n  }\n\n  override reset() {\n    super.reset();\n    this.__started = false;\n  }\n\n  override finish(): void {\n    super.finish();\n    this.__finished = true;\n  }\n\n  override destroy(): void {\n    super.destroy();\n    this.__finished = true;\n  }\n\n  /** @internal */\n  triggerMicrotask() {}\n\n  override play(): void {\n    super.play();\n    this.__started = true;\n  }\n\n  override hasStarted() {\n    return this.__started;\n  }\n\n  beforeDestroy() {\n    const captures: ɵStyleDataMap = new Map();\n\n    this.previousStyles.forEach((val, prop) => captures.set(prop, val));\n\n    if (this.hasStarted()) {\n      // when assembling the captured styles, it's important that\n      // we build the keyframe styles in the following order:\n      // {other styles within keyframes, ... previousStyles }\n      this._keyframes.forEach((kf) => {\n        for (let [prop, val] of kf) {\n          if (prop !== 'offset') {\n            captures.set(prop, this.__finished ? val : AUTO_STYLE);\n          }\n        }\n      });\n    }\n\n    this.currentSnapshot = captures;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verifcation. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["validateStyleProperty", "containsElement", "getParentElement", "invoke<PERSON><PERSON>y", "normalizeKeyframes", "allowPreviousPlayerStylesMerge"], "mappings": ";;;;;;;;;AAoBA;;AAEG;MACU,mBAAmB,CAAA;aACvB,IAAG,CAAA,GAAA,GAAsB,EAAE,CAAC,EAAA;AAEnC,IAAA,qBAAqB,CAAC,IAAY,EAAA;AAChC,QAAA,OAAOA,sBAAqB,CAAC,IAAI,CAAC,CAAC;KACpC;AAED,IAAA,+BAA+B,CAAC,IAAY,EAAA;AAC1C,QAAA,MAAM,OAAO,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC3C,QAAA,OAAO,mCAAmC,CAAC,OAAO,CAAC,CAAC;KACrD;IAED,cAAc,CAAC,QAAa,EAAE,SAAiB,EAAA;AAC7C,QAAA,OAAO,KAAK,CAAC;KACd;IAED,eAAe,CAAC,IAAS,EAAE,IAAS,EAAA;AAClC,QAAA,OAAOC,gBAAe,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACpC;AAED,IAAA,gBAAgB,CAAC,OAAgB,EAAA;AAC/B,QAAA,OAAOC,iBAAgB,CAAC,OAAO,CAAC,CAAC;KAClC;AAED,IAAA,KAAK,CAAC,OAAY,EAAE,QAAgB,EAAE,KAAc,EAAA;QAClD,OAAOC,YAAW,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC9C;AAED,IAAA,YAAY,CAAC,OAAY,EAAE,IAAY,EAAE,YAAqB,EAAA;QAC5D,OAAO,YAAY,IAAI,EAAE,CAAC;KAC3B;AAED,IAAA,OAAO,CACL,OAAY,EACZ,SAA+B,EAC/B,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAA,GAAyB,EAAE,EAAA;AAE3B,QAAA,MAAM,MAAM,GAAG,IAAI,mBAAmB,CACpC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,KAAK,EACL,MAAM,EACN,eAAe,CAChB,CAAC;AACF,QAAA,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAkB,MAAM,CAAC,CAAC;AACtD,QAAA,OAAO,MAAM,CAAC;KACf;;AAGH;;AAEG;AACG,MAAO,mBAAoB,SAAQ,mBAAmB,CAAA;IAQ1D,WACS,CAAA,OAAY,EACZ,SAA+B,EAC/B,QAAgB,EAChB,KAAa,EACb,MAAc,EACd,eAAsB,EAAA;AAE7B,QAAA,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;QAPhB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAK;QACZ,IAAS,CAAA,SAAA,GAAT,SAAS,CAAsB;QAC/B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QAChB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACd,IAAe,CAAA,eAAA,GAAf,eAAe,CAAO;QAbvB,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;QACnB,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;AACnB,QAAA,IAAA,CAAA,cAAc,GAAkB,IAAI,GAAG,EAAE,CAAC;QACzC,IAAU,CAAA,UAAA,GAAkB,EAAE,CAAC;AAChC,QAAA,IAAA,CAAA,eAAe,GAAkB,IAAI,GAAG,EAAE,CAAC;QAC1C,IAAU,CAAA,UAAA,GAAyB,EAAE,CAAC;AAY5C,QAAA,IAAI,CAAC,UAAU,GAAGC,mBAAkB,CAAC,SAAS,CAAC,CAAC;AAEhD,QAAA,IAAIC,+BAA8B,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE;AACnD,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACjC,gBAAA,IAAI,MAAM,YAAY,mBAAmB,EAAE;AACzC,oBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;oBACtC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;iBACnE;AACH,aAAC,CAAC,CAAC;SACJ;KACF;;AAGD,IAAA,MAAM,CAAC,EAAa,EAAA;AAClB,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC1B;;IAGQ,IAAI,GAAA;QACX,KAAK,CAAC,IAAI,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACtC,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;KACtB;IAEQ,KAAK,GAAA;QACZ,KAAK,CAAC,KAAK,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;IAEQ,MAAM,GAAA;QACb,KAAK,CAAC,MAAM,EAAE,CAAC;AACf,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;IAEQ,OAAO,GAAA;QACd,KAAK,CAAC,OAAO,EAAE,CAAC;AAChB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACxB;;AAGD,IAAA,gBAAgB,MAAK;IAEZ,IAAI,GAAA;QACX,KAAK,CAAC,IAAI,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;KACvB;IAEQ,UAAU,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;IAED,aAAa,GAAA;AACX,QAAA,MAAM,QAAQ,GAAkB,IAAI,GAAG,EAAE,CAAC;QAE1C,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAEpE,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;;;YAIrB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAE,KAAI;gBAC7B,KAAK,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;AAC1B,oBAAA,IAAI,IAAI,KAAK,QAAQ,EAAE;AACrB,wBAAA,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC;qBACxD;iBACF;AACH,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;KACjC;AACF;;AChKD;;;;AAIG;;ACJH;;ACRA;;AAEG;;;;"}