!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/tab-scroller",[],t):"object"==typeof exports?exports["tab-scroller"]=t():(e.mdc=e.mdc||{},e.mdc["tab-scroller"]=t())}(this,function(){return n={},o.m=r={0:function(e,t,r){"use strict";(function(e){}).call(this,r(20))},1:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapResourceUrl=t.isResourceUrl=t.createResourceUrl=t.TrustedResourceUrl=void 0,r(0);var o=r(4),i=r(9),a=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(e,t){this.privateDoNotAccessOrElseWrappedResourceUrl=e}var l=window.TrustedScriptURL;t.TrustedResourceUrl=null!=l?l:a,t.createResourceUrl=function(e){var t,r=e,n=null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScriptURL(r);return null!=n?n:new a(r,o.secretToken)},t.isResourceUrl=function(e){return e instanceof t.TrustedResourceUrl},t.unwrapResourceUrl=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isScriptURL(e))return e;if(e instanceof a)return e.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},10:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyle=t.isStyle=t.createStyle=t.SafeStyle=void 0,r(0);function i(){}var a=r(4);t.SafeStyle=i;var l,c=(o(s,l=i),s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},s);function s(e,t){var r=l.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=e,r}t.createStyle=function(e){return new c(e,a.secretToken)},t.isStyle=function(e){return e instanceof c},t.unwrapStyle=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},11:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AttributePolicyAction=t.SanitizerTable=void 0;var n,o,i=(a.prototype.isAllowedElement=function(e){return"form"!==e.toLowerCase()&&(this.allowedElements.has(e)||this.elementPolicies.has(e))},a.prototype.getAttributePolicy=function(e,t){var r=this.elementPolicies.get(t);return(null==r?void 0:r.has(e))?r.get(e):this.allowedGlobalAttributes.has(e)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(e)||{policyAction:n.DROP}},a);function a(e,t,r,n){this.allowedElements=e,this.elementPolicies=t,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}t.SanitizerTable=i,(o=n=t.AttributePolicyAction||(t.AttributePolicyAction={}))[o.DROP=0]="DROP",o[o.KEEP=1]="KEEP",o[o.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",o[o.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",o[o.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},113:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),l=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&i(t,e,r);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCTabScroller=void 0;var c,s=r(13),u=r(52),f=r(49),p=r(97),d=l(r(98)),h=(c=s.MDCComponent,o(y,c),y.attachTo=function(e){return new y(e)},y.prototype.initialize=function(){this.area=this.root.querySelector(p.MDCTabScrollerFoundation.strings.AREA_SELECTOR),this.content=this.root.querySelector(p.MDCTabScrollerFoundation.strings.CONTENT_SELECTOR)},y.prototype.initialSyncWithDOM=function(){var t=this;this.handleInteraction=function(){t.foundation.handleInteraction()},this.handleTransitionEnd=function(e){t.foundation.handleTransitionEnd(e)},this.area.addEventListener("wheel",this.handleInteraction,u.applyPassive()),this.area.addEventListener("touchstart",this.handleInteraction,u.applyPassive()),this.area.addEventListener("pointerdown",this.handleInteraction,u.applyPassive()),this.area.addEventListener("mousedown",this.handleInteraction,u.applyPassive()),this.area.addEventListener("keydown",this.handleInteraction,u.applyPassive()),this.content.addEventListener("transitionend",this.handleTransitionEnd)},y.prototype.destroy=function(){c.prototype.destroy.call(this),this.area.removeEventListener("wheel",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("touchstart",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("pointerdown",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("mousedown",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("keydown",this.handleInteraction,u.applyPassive()),this.content.removeEventListener("transitionend",this.handleTransitionEnd)},y.prototype.getDefaultFoundation=function(){var r=this,e={eventTargetMatchesSelector:function(e,t){return f.matches(e,t)},addClass:function(e){r.root.classList.add(e)},removeClass:function(e){r.root.classList.remove(e)},addScrollAreaClass:function(e){r.area.classList.add(e)},setScrollAreaStyleProperty:function(e,t){r.area.style.setProperty(e,t)},setScrollContentStyleProperty:function(e,t){r.content.style.setProperty(e,t)},getScrollContentStyleValue:function(e){return window.getComputedStyle(r.content).getPropertyValue(e)},setScrollAreaScrollLeft:function(e){return r.area.scrollLeft=e},getScrollAreaScrollLeft:function(){return r.area.scrollLeft},getScrollContentOffsetWidth:function(){return r.content.offsetWidth},getScrollAreaOffsetWidth:function(){return r.area.offsetWidth},computeScrollAreaClientRect:function(){return r.area.getBoundingClientRect()},computeScrollContentClientRect:function(){return r.content.getBoundingClientRect()},computeHorizontalScrollbarHeight:function(){return d.computeHorizontalScrollbarHeight(document)}};return new p.MDCTabScrollerFoundation(e)},y.prototype.getScrollPosition=function(){return this.foundation.getScrollPosition()},y.prototype.getScrollContentWidth=function(){return this.content.offsetWidth},y.prototype.incrementScroll=function(e){this.foundation.incrementScroll(e)},y.prototype.scrollTo=function(e){this.foundation.scrollTo(e)},y);function y(){return null!==c&&c.apply(this,arguments)||this}t.MDCTabScroller=h},114:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCTabScrollerRTLDefault=void 0;var i,a=r(72),l=(i=a.MDCTabScrollerRTL,o(c,i),c.prototype.getScrollPositionRTL=function(){var e=this.adapter.getScrollAreaScrollLeft(),t=this.calculateScrollEdges().right;return Math.round(t-e)},c.prototype.scrollToRTL=function(e){var t=this.calculateScrollEdges(),r=this.adapter.getScrollAreaScrollLeft(),n=this.clampScrollValue(t.right-e);return{finalScrollPosition:n,scrollDelta:n-r}},c.prototype.incrementScrollRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(t-e);return{finalScrollPosition:r,scrollDelta:r-t}},c.prototype.getAnimatingScrollPosition=function(e){return e},c.prototype.calculateScrollEdges=function(){return{left:0,right:this.adapter.getScrollContentOffsetWidth()-this.adapter.getScrollAreaOffsetWidth()}},c.prototype.clampScrollValue=function(e){var t=this.calculateScrollEdges();return Math.min(Math.max(t.left,e),t.right)},c);function c(){return null!==i&&i.apply(this,arguments)||this}t.MDCTabScrollerRTLDefault=l,t.default=l},115:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCTabScrollerRTLNegative=void 0;var i,a=r(72),l=(i=a.MDCTabScrollerRTL,o(c,i),c.prototype.getScrollPositionRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft();return Math.round(e-t)},c.prototype.scrollToRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(-e);return{finalScrollPosition:r,scrollDelta:r-t}},c.prototype.incrementScrollRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(t-e);return{finalScrollPosition:r,scrollDelta:r-t}},c.prototype.getAnimatingScrollPosition=function(e,t){return e-t},c.prototype.calculateScrollEdges=function(){var e=this.adapter.getScrollContentOffsetWidth();return{left:this.adapter.getScrollAreaOffsetWidth()-e,right:0}},c.prototype.clampScrollValue=function(e){var t=this.calculateScrollEdges();return Math.max(Math.min(t.right,e),t.left)},c);function c(){return null!==i&&i.apply(this,arguments)||this}t.MDCTabScrollerRTLNegative=l,t.default=l},116:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCTabScrollerRTLReverse=void 0;var i,a=r(72),l=(i=a.MDCTabScrollerRTL,o(c,i),c.prototype.getScrollPositionRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft();return Math.round(t-e)},c.prototype.scrollToRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(e);return{finalScrollPosition:r,scrollDelta:t-r}},c.prototype.incrementScrollRTL=function(e){var t=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(t+e);return{finalScrollPosition:r,scrollDelta:t-r}},c.prototype.getAnimatingScrollPosition=function(e,t){return e+t},c.prototype.calculateScrollEdges=function(){return{left:this.adapter.getScrollContentOffsetWidth()-this.adapter.getScrollAreaOffsetWidth(),right:0}},c.prototype.clampScrollValue=function(e){var t=this.calculateScrollEdges();return Math.min(Math.max(t.right,e),t.left)},c);function c(){return null!==i&&i.apply(this,arguments)||this}t.MDCTabScrollerRTLReverse=l,t.default=l},12:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.isStyleSheet=t.createStyleSheet=t.SafeStyleSheet=void 0,r(0);function i(){}var a=r(4);t.SafeStyleSheet=i;var l,c=(o(s,l=i),s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},s);function s(e,t){var r=l.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=e,r}t.createStyleSheet=function(e){return new c(e,a.secretToken)},t.isStyleSheet=function(e){return e instanceof c},t.unwrapStyleSheet=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},13:function(e,t,r){"use strict";var o=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},i=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},a=this&&this.__spreadArray||function(e,t){for(var r=0,n=t.length,o=e.length;r<n;r++,o++)e[o]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCComponent=void 0;var l=r(17),c=r(18),n=r(7);var s,u,f=(p.attachTo=function(e){return new p(e,new n.MDCFoundation({}))},p.prototype.initialize=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},p.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},p.prototype.initialSyncWithDOM=function(){},p.prototype.destroy=function(){this.foundation.destroy()},p.prototype.listen=function(e,t,r){this.root.addEventListener(e,t,r)},p.prototype.unlisten=function(e,t,r){this.root.removeEventListener(e,t,r)},p.prototype.emit=function(e,t,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(e,{bubbles:r,detail:t}):(n=document.createEvent("CustomEvent")).initCustomEvent(e,r,!1,t),this.root.dispatchEvent(n)},p.prototype.safeSetAttribute=function(e,t,r){if("tabindex"===t.toLowerCase())e.tabIndex=Number(r);else if(0===t.indexOf("data-")){var n=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})}(t.replace(/^data-/,""));e.dataset[n]=r}else c.safeElement.setPrefixedAttribute([l.safeAttrPrefix(s=s||o(["aria-"],["aria-"])),l.safeAttrPrefix(u=u||o(["role"],["role"]))],e,t,r)},p);function p(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=e,this.initialize.apply(this,a([],i(r))),this.foundation=void 0===t?this.getDefaultFoundation():t,this.foundation.init(),this.initialSyncWithDOM()}t.MDCComponent=f,t.default=f},14:function(e,t,r){"use strict";var d=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},f=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.HtmlSanitizerImpl=void 0,r(0);var n=r(2),o=r(4),h=r(3),c=r(23),y=r(24),i=r(16),S=r(11),a=(l.prototype.sanitizeAssertUnchanged=function(e){this.changes=[];var t=this.sanitize(e);if(0===this.changes.length)return t;throw new Error("")},l.prototype.sanitize=function(e){var t=document.createElement("span");t.appendChild(this.sanitizeToFragment(e));var r=(new XMLSerializer).serializeToString(t);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},l.prototype.sanitizeToFragment=function(e){for(var t=this,r=(0,c.createInertFragment)(e),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(e){return t.nodeFilter(e)},!1),o=n.nextNode(),i=document.createDocumentFragment(),a=i;null!==o;){var l=void 0;if((0,y.isText)(o))l=this.sanitizeTextNode(o);else{if(!(0,y.isElement)(o))throw new Error("Node is not of type text or element");l=this.sanitizeElementNode(o)}if(a.appendChild(l),o=n.firstChild())a=l;else for(;!(o=n.nextSibling())&&(o=n.parentNode());)a=a.parentNode}return i},l.prototype.sanitizeTextNode=function(e){return document.createTextNode(e.data)},l.prototype.sanitizeElementNode=function(e){var t,r,n=(0,y.getNodeName)(e),o=document.createElement(n),i=e.attributes;try{for(var a=d(i),l=a.next();!l.done;l=a.next()){var c=l.value,s=c.name,u=c.value,f=this.sanitizerTable.getAttributePolicy(s,n);if(this.satisfiesAllConditions(f.conditions,i))switch(f.policyAction){case S.AttributePolicyAction.KEEP:o.setAttribute(s,u);break;case S.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var p=(0,h.restrictivelySanitizeUrl)(u);p!==u&&this.recordChange("Url in attribute ".concat(s,' was modified during sanitization. Original url:"').concat(u,'" was sanitized to: "').concat(p,'"')),o.setAttribute(s,p);break;case S.AttributePolicyAction.KEEP_AND_NORMALIZE:o.setAttribute(s,u.toLowerCase());break;case S.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:o.setAttribute(s,u);break;case S.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(s," was dropped"));break;default:v(f.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(s,"."))}}catch(e){t={error:e}}finally{try{l&&!l.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return o},l.prototype.nodeFilter=function(e){if((0,y.isText)(e))return NodeFilter.FILTER_ACCEPT;if(!(0,y.isElement)(e))return NodeFilter.FILTER_REJECT;var t=(0,y.getNodeName)(e);return null===t?(this.recordChange("Node name was null for node: ".concat(e)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(t)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(t," was dropped")),NodeFilter.FILTER_REJECT)},l.prototype.recordChange=function(e){0===this.changes.length&&this.changes.push("")},l.prototype.satisfiesAllConditions=function(e,t){var r,n,o;if(!e)return!0;try{for(var i=d(e),a=i.next();!a.done;a=i.next()){var l=f(a.value,2),c=l[0],s=l[1],u=null===(o=t.getNamedItem(c))||void 0===o?void 0:o.value;if(u&&!s.has(u))return!1}}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return!0},l);function l(e,t){this.sanitizerTable=e,this.changes=[],(0,o.ensureTokenIsValid)(t)}t.HtmlSanitizerImpl=a;var s=function(){return new a(i.defaultSanitizerTable,o.secretToken)}();function v(e,t){throw void 0===t&&(t="unexpected value ".concat(e,"!")),new Error(t)}t.sanitizeHtml=function(e){return s.sanitize(e)},t.sanitizeHtmlAssertUnchanged=function(e){return s.sanitizeAssertUnchanged(e)},t.sanitizeHtmlToFragment=function(e){return s.sanitizeToFragment(e)}},15:function(e,t,r){"use strict";var o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||((n=n||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.setPrefixedAttribute=t.buildPrefixedAttributeSetter=t.insertAdjacentHtml=t.setCssText=t.setOuterHtml=t.setInnerHtml=void 0;var a=r(8),l=r(2),n=r(10);function c(e,t,r,n){if(0===e.length)throw new Error("No prefixes are provided");var o=e.map(function(e){return(0,a.unwrapAttributePrefix)(e)}),i=r.toLowerCase();if(o.every(function(e){return 0!==i.indexOf(e)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));t.setAttribute(r,n)}function s(e){if("script"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}t.setInnerHtml=function(e,t){!function(e){return void 0!==e.tagName}(e)||s(e),e.innerHTML=(0,l.unwrapHtml)(t)},t.setOuterHtml=function(e,t){var r=e.parentElement;null!==r&&s(r),e.outerHTML=(0,l.unwrapHtml)(t)},t.setCssText=function(e,t){e.style.cssText=(0,n.unwrapStyle)(t)},t.insertAdjacentHtml=function(e,t,r){var n="beforebegin"===t||"afterend"===t?e.parentElement:e;null!==n&&s(n),e.insertAdjacentHTML(t,(0,l.unwrapHtml)(r))},t.buildPrefixedAttributeSetter=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=i([e],o(t),!1);return function(e,t,r){c(n,e,t,r)}},t.setPrefixedAttribute=c},16:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSanitizerTable=void 0;var n=r(11);t.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},17:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.SafeStyleSheet=t.isStyleSheet=t.unwrapStyle=t.SafeStyle=t.isStyle=t.unwrapScript=t.SafeScript=t.isScript=t.EMPTY_SCRIPT=t.unwrapResourceUrl=t.TrustedResourceUrl=t.isResourceUrl=t.unwrapHtml=t.SafeHtml=t.isHtml=t.EMPTY_HTML=t.unwrapAttributePrefix=t.SafeAttributePrefix=t.safeStyleSheet=t.concatStyleSheets=t.safeStyle=t.concatStyles=t.scriptFromJson=t.safeScriptWithArgs=t.safeScript=t.concatScripts=t.trustedResourceUrl=t.replaceFragment=t.blobUrlFromScript=t.appendParams=t.HtmlSanitizerBuilder=t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.htmlEscape=t.createScriptSrc=t.createScript=t.concatHtmls=t.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(t,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var o=r(22);Object.defineProperty(t,"concatHtmls",{enumerable:!0,get:function(){return o.concatHtmls}}),Object.defineProperty(t,"createScript",{enumerable:!0,get:function(){return o.createScript}}),Object.defineProperty(t,"createScriptSrc",{enumerable:!0,get:function(){return o.createScriptSrc}}),Object.defineProperty(t,"htmlEscape",{enumerable:!0,get:function(){return o.htmlEscape}});var i=r(14);Object.defineProperty(t,"sanitizeHtml",{enumerable:!0,get:function(){return i.sanitizeHtml}}),Object.defineProperty(t,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return i.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(t,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return i.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(t,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var l=r(26);Object.defineProperty(t,"appendParams",{enumerable:!0,get:function(){return l.appendParams}}),Object.defineProperty(t,"blobUrlFromScript",{enumerable:!0,get:function(){return l.blobUrlFromScript}}),Object.defineProperty(t,"replaceFragment",{enumerable:!0,get:function(){return l.replaceFragment}}),Object.defineProperty(t,"trustedResourceUrl",{enumerable:!0,get:function(){return l.trustedResourceUrl}});var c=r(27);Object.defineProperty(t,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(t,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(t,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(t,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var s=r(28);Object.defineProperty(t,"concatStyles",{enumerable:!0,get:function(){return s.concatStyles}}),Object.defineProperty(t,"safeStyle",{enumerable:!0,get:function(){return s.safeStyle}});var u=r(29);Object.defineProperty(t,"concatStyleSheets",{enumerable:!0,get:function(){return u.concatStyleSheets}}),Object.defineProperty(t,"safeStyleSheet",{enumerable:!0,get:function(){return u.safeStyleSheet}});var f=r(8);Object.defineProperty(t,"SafeAttributePrefix",{enumerable:!0,get:function(){return f.SafeAttributePrefix}}),Object.defineProperty(t,"unwrapAttributePrefix",{enumerable:!0,get:function(){return f.unwrapAttributePrefix}});var p=r(2);Object.defineProperty(t,"EMPTY_HTML",{enumerable:!0,get:function(){return p.EMPTY_HTML}}),Object.defineProperty(t,"isHtml",{enumerable:!0,get:function(){return p.isHtml}}),Object.defineProperty(t,"SafeHtml",{enumerable:!0,get:function(){return p.SafeHtml}}),Object.defineProperty(t,"unwrapHtml",{enumerable:!0,get:function(){return p.unwrapHtml}});var d=r(1);Object.defineProperty(t,"isResourceUrl",{enumerable:!0,get:function(){return d.isResourceUrl}}),Object.defineProperty(t,"TrustedResourceUrl",{enumerable:!0,get:function(){return d.TrustedResourceUrl}}),Object.defineProperty(t,"unwrapResourceUrl",{enumerable:!0,get:function(){return d.unwrapResourceUrl}});var h=r(5);Object.defineProperty(t,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return h.EMPTY_SCRIPT}}),Object.defineProperty(t,"isScript",{enumerable:!0,get:function(){return h.isScript}}),Object.defineProperty(t,"SafeScript",{enumerable:!0,get:function(){return h.SafeScript}}),Object.defineProperty(t,"unwrapScript",{enumerable:!0,get:function(){return h.unwrapScript}});var y=r(10);Object.defineProperty(t,"isStyle",{enumerable:!0,get:function(){return y.isStyle}}),Object.defineProperty(t,"SafeStyle",{enumerable:!0,get:function(){return y.SafeStyle}}),Object.defineProperty(t,"unwrapStyle",{enumerable:!0,get:function(){return y.unwrapStyle}});var S=r(12);Object.defineProperty(t,"isStyleSheet",{enumerable:!0,get:function(){return S.isStyleSheet}}),Object.defineProperty(t,"SafeStyleSheet",{enumerable:!0,get:function(){return S.SafeStyleSheet}}),Object.defineProperty(t,"unwrapStyleSheet",{enumerable:!0,get:function(){return S.unwrapStyleSheet}})},18:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&("get"in o?t.__esModule:!o.writable&&!o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.safeWorker=t.safeWindow=t.safeServiceWorkerContainer=t.safeRange=t.safeLocation=t.safeGlobal=t.safeDomParser=t.safeDocument=t.safeStyleEl=t.safeScriptEl=t.safeObjectEl=t.safeLinkEl=t.safeInputEl=t.safeIframeEl=t.safeFormEl=t.safeEmbedEl=t.safeElement=t.safeButtonEl=t.safeAreaEl=t.safeAnchorEl=void 0,t.safeAnchorEl=i(r(30)),t.safeAreaEl=i(r(31)),t.safeButtonEl=i(r(32)),t.safeElement=i(r(15)),t.safeEmbedEl=i(r(33)),t.safeFormEl=i(r(34)),t.safeIframeEl=i(r(35)),t.safeInputEl=i(r(36)),t.safeLinkEl=i(r(37)),t.safeObjectEl=i(r(38)),t.safeScriptEl=i(r(39)),t.safeStyleEl=i(r(40)),t.safeDocument=i(r(41)),t.safeDomParser=i(r(42)),t.safeGlobal=i(r(43)),t.safeLocation=i(r(44)),t.safeRange=i(r(45)),t.safeServiceWorkerContainer=i(r(46)),t.safeWindow=i(r(47)),t.safeWorker=i(r(48))},19:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);t.safeAttrPrefix=function(e){var t=e[0].toLowerCase();return(0,n.createAttributePrefix)(t)}},2:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapHtml=t.isHtml=t.EMPTY_HTML=t.createHtml=t.SafeHtml=void 0,r(0);var n=r(4),o=r(9),i=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedHtml=e}function l(e,t){return null!=t?t:new i(e,n.secretToken)}var c=window.TrustedHTML;t.SafeHtml=null!=c?c:i,t.createHtml=function(e){var t,r=e;return l(r,null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createHTML(r))},t.EMPTY_HTML=function(){var e;return l("",null===(e=(0,o.getTrustedTypes)())||void 0===e?void 0:e.emptyHTML)}(),t.isHtml=function(e){return e instanceof t.SafeHtml},t.unwrapHtml=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isHTML(e))return e;if(e instanceof i)return e.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},20:function(e,t){var r,n,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function l(t){if(r===setTimeout)return setTimeout(t,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(e){r=i}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var c,s=[],u=!1,f=-1;function p(){u&&c&&(u=!1,c.length?s=c.concat(s):f=-1,s.length&&d())}function d(){if(!u){var e=l(p);u=!0;for(var t=s.length;t;){for(c=s,s=[];++f<t;)c&&c[f].run();f=-1,t=s.length}c=null,u=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function y(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];s.push(new h(e,t)),1!==s.length||u||l(d)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=y,o.addListener=y,o.once=y,o.off=y,o.removeListener=y,o.removeAllListeners=y,o.emit=y,o.prependListener=y,o.prependOnceListener=y,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},21:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SECURITY_SENSITIVE_ATTRIBUTES=void 0,t.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},22:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatHtmls=t.createScriptSrc=t.createScript=t.htmlEscape=void 0;var i=r(2),a=r(1),o=r(5);function l(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}t.htmlEscape=function(e,t){void 0===t&&(t={});var r=l(e);return t.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),t.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),t.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,i.createHtml)(r)},t.createScript=function(e,t){void 0===t&&(t={});var r=(0,o.unwrapScript)(e).toString(),n="<script";return t.id&&(n+=' id="'.concat(l(t.id),'"')),t.nonce&&(n+=' nonce="'.concat(l(t.nonce),'"')),t.type&&(n+=' type="'.concat(l(t.type),'"')),n+=">".concat(r,"<\/script>"),(0,i.createHtml)(n)},t.createScriptSrc=function(e,t,r){var n=(0,a.unwrapResourceUrl)(e).toString(),o='<script src="'.concat(l(n),'"');return t&&(o+=" async"),r&&(o+=' nonce="'.concat(l(r),'"')),o+="><\/script>",(0,i.createHtml)(o)},t.concatHtmls=function(e){return(0,i.createHtml)(e.map(i.unwrapHtml).join(""))}},23:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInertFragment=void 0;var n=r(15),o=r(2);t.createInertFragment=function(e){var t=document.createElement("template"),r=(0,o.createHtml)(e);return(0,n.setInnerHtml)(t,r),t.content}},24:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElement=t.isText=t.getNodeName=void 0,t.getNodeName=function(e){var t=e.nodeName;return"string"==typeof t?t:"FORM"},t.isText=function(e){return e.nodeType===Node.TEXT_NODE},t.isElement=function(e){var t=e.nodeType;return t===Node.ELEMENT_NODE||"number"!=typeof t}},25:function(e,t,r){"use strict";var A=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},P=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlSanitizerBuilder=void 0;var n=r(4),o=r(14),i=r(16),w=r(11),a=(l.prototype.onlyAllowElements=function(e){var t,r,n=new Set,o=new Map;try{for(var i=A(e),a=i.next();!a.done;a=i.next()){var l=a.value;if(l=l.toUpperCase(),!this.sanitizerTable.isAllowedElement(l))throw new Error("Element: ".concat(l,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(l);void 0!==c?o.set(l,c):n.add(l)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return this.sanitizerTable=new w.SanitizerTable(n,o,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},l.prototype.onlyAllowAttributes=function(e){var t,r,n,o,i,a,l=new Set,c=new Map,s=new Map;try{for(var u=A(e),f=u.next();!f.done;f=u.next()){var p=f.value;this.sanitizerTable.allowedGlobalAttributes.has(p)&&l.add(p),this.sanitizerTable.globalAttributePolicies.has(p)&&c.set(p,this.sanitizerTable.globalAttributePolicies.get(p))}}catch(e){t={error:e}}finally{try{f&&!f.done&&(r=u.return)&&r.call(u)}finally{if(t)throw t.error}}try{for(var d=A(this.sanitizerTable.elementPolicies.entries()),h=d.next();!h.done;h=d.next()){var y=P(h.value,2),S=y[0],v=y[1],b=new Map;try{for(var m=(i=void 0,A(v.entries())),g=m.next();!g.done;g=m.next()){var T=P(g.value,2),_=(p=T[0],T[1]);e.has(p)&&b.set(p,_)}}catch(e){i={error:e}}finally{try{g&&!g.done&&(a=m.return)&&a.call(m)}finally{if(i)throw i.error}}s.set(S,b)}}catch(e){n={error:e}}finally{try{h&&!h.done&&(o=d.return)&&o.call(d)}finally{if(n)throw n.error}}return this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,s,l,c),this},l.prototype.allowDataAttributes=function(e){var t,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var o=A(e),i=o.next();!i.done;i=o.next()){var a=i.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));n.add(a)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},l.prototype.allowStyleAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("style",{policyAction:w.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},l.prototype.allowClassAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("class",{policyAction:w.AttributePolicyAction.KEEP}),this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},l.prototype.allowIdAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("id",{policyAction:w.AttributePolicyAction.KEEP}),this.sanitizerTable=new w.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},l.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new o.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},l);function l(){this.calledBuild=!1,this.sanitizerTable=i.defaultSanitizerTable}t.HtmlSanitizerBuilder=a},26:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.blobUrlFromScript=t.replaceFragment=t.appendParams=t.trustedResourceUrl=void 0,r(0);var l=r(1),n=r(5);r(6);t.trustedResourceUrl=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(0===t.length)return(0,l.createResourceUrl)(e[0]);e[0].toLowerCase();for(var n=[e[0]],o=0;o<t.length;o++)n.push(encodeURIComponent(t[o])),n.push(e[o+1]);return(0,l.createResourceUrl)(n.join(""))},t.appendParams=function(e,t){var i=(0,l.unwrapResourceUrl)(e).toString();if(/#/.test(i)){throw new Error("")}var a=/\?/.test(i)?"&":"?";return t.forEach(function(e,t){for(var r=e instanceof Array?e:[e],n=0;n<r.length;n++){var o=r[n];null!=o&&(i+=a+encodeURIComponent(t)+"="+encodeURIComponent(String(o)),a="&")}}),(0,l.createResourceUrl)(i)};var o=/[^#]*/;t.replaceFragment=function(e,t){var r=(0,l.unwrapResourceUrl)(e).toString();return(0,l.createResourceUrl)(o.exec(r)[0]+"#"+t)},t.blobUrlFromScript=function(e){var t=(0,n.unwrapScript)(e).toString(),r=new Blob([t],{type:"text/javascript"});return(0,l.createResourceUrl)(URL.createObjectURL(r))}},27:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeScriptWithArgs=t.scriptFromJson=t.concatScripts=t.safeScript=void 0,r(0);var o=r(5);r(6);function i(e){return(0,o.createScript)(JSON.stringify(e).replace(/</g,"\\x3c"))}t.safeScript=function(e){return(0,o.createScript)(e[0])},t.concatScripts=function(e){return(0,o.createScript)(e.map(o.unwrapScript).join(""))},t.scriptFromJson=i,t.safeScriptWithArgs=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.map(function(e){return i(e).toString()});return(0,o.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},28:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyles=t.safeStyle=void 0,r(0);r(6);var n=r(10);t.safeStyle=function(e){var t=e[0];return(0,n.createStyle)(t)},t.concatStyles=function(e){return(0,n.createStyle)(e.map(n.unwrapStyle).join(""))}},29:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyleSheets=t.safeStyleSheet=void 0,r(0);r(6);var n=r(12);t.safeStyleSheet=function(e){var t=e[0];return(0,n.createStyleSheet)(t)},t.concatStyleSheets=function(e){return(0,n.createStyleSheet)(e.map(n.unwrapStyleSheet).join(""))}},3:function(e,t,r){"use strict";function n(e){var t;try{t=new URL(e)}catch(e){return"https:"}return t.protocol}Object.defineProperty(t,"__esModule",{value:!0}),t.restrictivelySanitizeUrl=t.unwrapUrlOrSanitize=t.sanitizeJavascriptUrl=void 0,r(0);var o=["data:","http:","https:","mailto:","ftp:"];function i(e){if("javascript:"!==n(e))return e}t.sanitizeJavascriptUrl=i,t.unwrapUrlOrSanitize=function(e){return i(e)},t.restrictivelySanitizeUrl=function(e){var t=n(e);return void 0!==t&&-1!==o.indexOf(t.toLowerCase())?e:"about:invalid#zClosurez"}},30:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},300:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t},a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.util=void 0;var l=i(r(98));t.util=l,a(r(301),t),a(r(113),t),a(r(80),t),a(r(97),t),a(r(302),t)},301:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},302:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},31:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},32:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},33:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=void 0;var n=r(1);t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t)}},34:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setAction=void 0;var n=r(3);t.setAction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.action=r)}},35:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrcdoc=t.setSrc=void 0;var n=r(2),o=r(1);t.setSrc=function(e,t){e.src=(0,o.unwrapResourceUrl)(t).toString()},t.setSrcdoc=function(e,t){e.srcdoc=(0,n.unwrapHtml)(t)}},36:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},37:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHrefAndRel=void 0;var o=r(3),i=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];t.setHrefAndRel=function(e,t,r){if(t instanceof i.TrustedResourceUrl)e.href=(0,i.unwrapResourceUrl)(t).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,o.unwrapUrlOrSanitize)(t);if(void 0===n)return;e.href=n}e.rel=r}},38:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setData=void 0;var n=r(1);t.setData=function(e,t){e.data=(0,n.unwrapResourceUrl)(t)}},39:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=t.setTextContent=void 0;var n=r(1),o=r(5);function i(e){var t=function(e){var t,r=e.document,n=null===(t=r.querySelector)||void 0===t?void 0:t.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(e.ownerDocument&&e.ownerDocument.defaultView||window);t&&e.setAttribute("nonce",t)}t.setTextContent=function(e,t){e.textContent=(0,o.unwrapScript)(t),i(e)},t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t),i(e)}},4:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureTokenIsValid=t.secretToken=void 0,t.secretToken={},t.ensureTokenIsValid=function(e){if(e!==t.secretToken)throw new Error("Bad secret")}},40:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setTextContent=void 0;var n=r(12);t.setTextContent=function(e,t){e.textContent=(0,n.unwrapStyleSheet)(t)}},41:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execCommandInsertHtml=t.execCommand=t.write=void 0;var i=r(2);t.write=function(e,t){e.write((0,i.unwrapHtml)(t))},t.execCommand=function(e,t,r){var n=String(t),o=r;return"inserthtml"===n.toLowerCase()&&(o=(0,i.unwrapHtml)(r)),e.execCommand(n,!1,o)},t.execCommandInsertHtml=function(e,t){return e.execCommand("insertHTML",!1,(0,i.unwrapHtml)(t))}},42:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseFromString=t.parseHtml=void 0;var n=r(2);function o(e,t,r){return e.parseFromString((0,n.unwrapHtml)(t),r)}t.parseHtml=function(e,t){return o(e,t,"text/html")},t.parseFromString=o},43:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.globalEval=void 0;var o=r(5);t.globalEval=function(e,t){var r=(0,o.unwrapScript)(t),n=e.eval(r);return n===r&&(n=e.eval(r.toString())),n}},44:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assign=t.replace=t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)},t.replace=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.replace(r)},t.assign=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.assign(r)}},45:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createContextualFragment=void 0;var n=r(2);t.createContextualFragment=function(e,t){return e.createContextualFragment((0,n.unwrapHtml)(t))}},46:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.register=void 0;var n=r(1);t.register=function(e,t,r){return e.register((0,n.unwrapResourceUrl)(t),r)}},47:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.open=void 0;var i=r(3);t.open=function(e,t,r,n){var o=(0,i.unwrapUrlOrSanitize)(t);return void 0!==o?e.open(o,r,n):null}},48:function(e,t,r){"use strict";var n=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},o=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||((n=n||Array.prototype.slice.call(t,0,o))[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.importScripts=t.createShared=t.create=void 0;var i=r(1);t.create=function(e,t){return new Worker((0,i.unwrapResourceUrl)(e),t)},t.createShared=function(e,t){return new SharedWorker((0,i.unwrapResourceUrl)(e),t)},t.importScripts=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];e.importScripts.apply(e,o([],n(t.map(function(e){return(0,i.unwrapResourceUrl)(e)})),!1))}},49:function(e,t,r){"use strict";function n(e,t){return(e.matches||e.webkitMatchesSelector||e.msMatchesSelector).call(e,t)}Object.defineProperty(t,"__esModule",{value:!0}),t.estimateScrollWidth=t.matches=t.closest=void 0,t.closest=function(e,t){if(e.closest)return e.closest(t);for(var r=e;r;){if(n(r,t))return r;r=r.parentElement}return null},t.matches=n,t.estimateScrollWidth=function(e){var t=e;if(null!==t.offsetParent)return t.scrollWidth;var r=t.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},5:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapScript=t.isScript=t.EMPTY_SCRIPT=t.createScript=t.SafeScript=void 0,r(0);var n=r(4),o=r(9),i=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedScript=e}function l(e,t){return null!=t?t:new i(e,n.secretToken)}var c=window.TrustedScript;t.SafeScript=null!=c?c:i,t.createScript=function(e){var t,r=e;return l(r,null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScript(r))},t.EMPTY_SCRIPT=function(){var e;return l("",null===(e=(0,o.getTrustedTypes)())||void 0===e?void 0:e.emptyScript)}(),t.isScript=function(e){return e instanceof t.SafeScript},t.unwrapScript=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isScript(e))return e;if(e instanceof i)return e.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},52:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.applyPassive=void 0,t.applyPassive=function(e){return void 0===e&&(e=window),!!function(e){void 0===e&&(e=window);var t=!1;try{var r={get passive(){return!(t=!0)}},n=function(){};e.document.addEventListener("test",n,r),e.document.removeEventListener("test",n,r)}catch(e){t=!1}return t}(e)&&{passive:!0}}},6:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertIsTemplateObject=void 0,t.assertIsTemplateObject=function(e,t,r){if(!Array.isArray(e)||!Array.isArray(e.raw)||!t&&1!==e.length)throw new TypeError(r)}},7:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCFoundation=void 0;var n=(Object.defineProperty(o,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(o,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(o,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(o,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),o.prototype.init=function(){},o.prototype.destroy=function(){},o);function o(e){void 0===e&&(e={}),this.adapter=e}t.MDCFoundation=n,t.default=n},72:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCTabScrollerRTL=void 0;function n(e){this.adapter=e}t.MDCTabScrollerRTL=n,t.default=n},8:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapAttributePrefix=t.createAttributePrefix=t.SafeAttributePrefix=void 0,r(0);function i(){}var a=r(4);t.SafeAttributePrefix=i;var l,c=(o(s,l=i),s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},s);function s(e,t){var r=l.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=e,r}t.createAttributePrefix=function(e){return new c(e,a.secretToken)},t.unwrapAttributePrefix=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},80:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.strings=t.cssClasses=void 0;t.cssClasses={ANIMATING:"mdc-tab-scroller--animating",SCROLL_AREA_SCROLL:"mdc-tab-scroller__scroll-area--scroll",SCROLL_TEST:"mdc-tab-scroller__test"};t.strings={AREA_SELECTOR:".mdc-tab-scroller__scroll-area",CONTENT_SELECTOR:".mdc-tab-scroller__scroll-content"}},9:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEST_ONLY=t.getTrustedTypesPolicy=t.getTrustedTypes=void 0;var n,o="google#safe";function i(){var e;return""!==o&&null!==(e=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==e?e:null}t.getTrustedTypes=i,t.getTrustedTypesPolicy=function(){var e,t;if(void 0===n)try{n=null!==(t=null===(e=i())||void 0===e?void 0:e.createPolicy(o,{createHTML:function(e){return e},createScript:function(e){return e},createScriptURL:function(e){return e}}))&&void 0!==t?t:null}catch(e){n=null}return n},t.TEST_ONLY={resetDefaults:function(){n=void 0,o="google#safe"},setTrustedTypesPolicyName:function(e){o=e}}},97:function(e,t,r){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},a=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCTabScrollerFoundation=void 0;var l,c=r(7),s=r(80),u=r(114),f=r(115),p=r(116),d=(l=c.MDCFoundation,o(h,l),Object.defineProperty(h,"cssClasses",{get:function(){return s.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(h,"strings",{get:function(){return s.strings},enumerable:!1,configurable:!0}),Object.defineProperty(h,"defaultAdapter",{get:function(){return{eventTargetMatchesSelector:function(){return!1},addClass:function(){},removeClass:function(){},addScrollAreaClass:function(){},setScrollAreaStyleProperty:function(){},setScrollContentStyleProperty:function(){},getScrollContentStyleValue:function(){return""},setScrollAreaScrollLeft:function(){},getScrollAreaScrollLeft:function(){return 0},getScrollContentOffsetWidth:function(){return 0},getScrollAreaOffsetWidth:function(){return 0},computeScrollAreaClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},computeScrollContentClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},computeHorizontalScrollbarHeight:function(){return 0}}},enumerable:!1,configurable:!0}),h.prototype.init=function(){var e=this.adapter.computeHorizontalScrollbarHeight();this.adapter.setScrollAreaStyleProperty("margin-bottom",-e+"px"),this.adapter.addScrollAreaClass(h.cssClasses.SCROLL_AREA_SCROLL)},h.prototype.getScrollPosition=function(){if(this.isRTL())return this.computeCurrentScrollPositionRTL();var e=this.calculateCurrentTranslateX();return this.adapter.getScrollAreaScrollLeft()-e},h.prototype.handleInteraction=function(){this.isAnimating&&this.stopScrollAnimation()},h.prototype.handleTransitionEnd=function(e){var t=e.target;this.isAnimating&&this.adapter.eventTargetMatchesSelector(t,h.strings.CONTENT_SELECTOR)&&(this.isAnimating=!1,this.adapter.removeClass(h.cssClasses.ANIMATING))},h.prototype.incrementScroll=function(e){0!==e&&this.animate(this.getIncrementScrollOperation(e))},h.prototype.incrementScrollImmediate=function(e){if(0!==e){var t=this.getIncrementScrollOperation(e);0!==t.scrollDelta&&(this.stopScrollAnimation(),this.adapter.setScrollAreaScrollLeft(t.finalScrollPosition))}},h.prototype.scrollTo=function(e){this.isRTL()?this.scrollToImplRTL(e):this.scrollToImpl(e)},h.prototype.getRTLScroller=function(){return this.rtlScrollerInstance||(this.rtlScrollerInstance=this.rtlScrollerFactory()),this.rtlScrollerInstance},h.prototype.calculateCurrentTranslateX=function(){var e=this.adapter.getScrollContentStyleValue("transform");if("none"===e)return 0;var t=/\((.+?)\)/.exec(e);if(!t)return 0;var r=t[1],n=a(r.split(","),6),o=(n[0],n[1],n[2],n[3],n[4]);return n[5],parseFloat(o)},h.prototype.clampScrollValue=function(e){var t=this.calculateScrollEdges();return Math.min(Math.max(t.left,e),t.right)},h.prototype.computeCurrentScrollPositionRTL=function(){var e=this.calculateCurrentTranslateX();return this.getRTLScroller().getScrollPositionRTL(e)},h.prototype.calculateScrollEdges=function(){return{left:0,right:this.adapter.getScrollContentOffsetWidth()-this.adapter.getScrollAreaOffsetWidth()}},h.prototype.scrollToImpl=function(e){var t=this.getScrollPosition(),r=this.clampScrollValue(e),n=r-t;this.animate({finalScrollPosition:r,scrollDelta:n})},h.prototype.scrollToImplRTL=function(e){var t=this.getRTLScroller().scrollToRTL(e);this.animate(t)},h.prototype.getIncrementScrollOperation=function(e){if(this.isRTL())return this.getRTLScroller().incrementScrollRTL(e);var t=this.getScrollPosition(),r=e+t,n=this.clampScrollValue(r);return{finalScrollPosition:n,scrollDelta:n-t}},h.prototype.animate=function(e){var t=this;0!==e.scrollDelta&&(this.stopScrollAnimation(),this.adapter.setScrollAreaScrollLeft(e.finalScrollPosition),this.adapter.setScrollContentStyleProperty("transform","translateX("+e.scrollDelta+"px)"),this.adapter.computeScrollAreaClientRect(),requestAnimationFrame(function(){t.adapter.addClass(h.cssClasses.ANIMATING),t.adapter.setScrollContentStyleProperty("transform","none")}),this.isAnimating=!0)},h.prototype.stopScrollAnimation=function(){this.isAnimating=!1;var e=this.getAnimatingScrollPosition();this.adapter.removeClass(h.cssClasses.ANIMATING),this.adapter.setScrollContentStyleProperty("transform","translateX(0px)"),this.adapter.setScrollAreaScrollLeft(e)},h.prototype.getAnimatingScrollPosition=function(){var e=this.calculateCurrentTranslateX(),t=this.adapter.getScrollAreaScrollLeft();return this.isRTL()?this.getRTLScroller().getAnimatingScrollPosition(t,e):t-e},h.prototype.rtlScrollerFactory=function(){var e=this.adapter.getScrollAreaScrollLeft();this.adapter.setScrollAreaScrollLeft(e-1);var t=this.adapter.getScrollAreaScrollLeft();if(t<0)return this.adapter.setScrollAreaScrollLeft(e),new f.MDCTabScrollerRTLNegative(this.adapter);var r=this.adapter.computeScrollAreaClientRect(),n=this.adapter.computeScrollContentClientRect(),o=Math.round(n.right-r.right);return this.adapter.setScrollAreaScrollLeft(e),o===t?new p.MDCTabScrollerRTLReverse(this.adapter):new u.MDCTabScrollerRTLDefault(this.adapter)},h.prototype.isRTL=function(){return"rtl"===this.adapter.getScrollContentStyleValue("direction")},h);function h(e){var t=l.call(this,i(i({},h.defaultAdapter),e))||this;return t.isAnimating=!1,t}t.MDCTabScrollerFoundation=d,t.default=d},98:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.computeHorizontalScrollbarHeight=void 0;var o,i=r(80);t.computeHorizontalScrollbarHeight=function(e,t){if(void 0===t&&(t=!0),t&&void 0!==o)return o;var r=e.createElement("div");r.classList.add(i.cssClasses.SCROLL_TEST),e.body.appendChild(r);var n=r.offsetHeight-r.clientHeight;return e.body.removeChild(r),t&&(o=n),n}}},o.c=n,o.d=function(e,t,r){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)o.d(r,n,function(e){return t[e]}.bind(null,n));return r},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=300);function o(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,o),t.l=!0,t.exports}var r,n});