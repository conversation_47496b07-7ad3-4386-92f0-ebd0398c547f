{"version": 3, "sources": ["webpack:///./packages/mdc-theme/mdc-theme.scss", "webpack:///./packages/mdc-theme/_css.scss"], "names": [], "mappings": ";;;;;;;AAoCE,MC0CE,ojCD9BE,oBCkBA,cAYF,wCD9BE,sBCkBA,cAYF,0CDxBE,uBCYA,sBAYF,mDDxBE,oBCYA,sBAYF,gDD9BE,kBCkBA,cAYF,sCD9BE,uBCkBA,WAYF,wCD9BE,yBCkBA,WAYF,0CD9BE,uBCkBA,WAYF,wCD9BE,qBCkBA,WAYF,sCD9BE,uCCkBA,0BAYF,uED9BE,yCCkBA,0BAYF,yED9BE,oCCkBA,0BAYF,oED9BE,wCCkBA,0BAYF,wED9BE,oCCkBA,0BAYF,oED9BE,kCCkBA,0BAYF,kED9BE,oCCkBA,0BAYF,oED9BE,+BCkBA,0BAYF,+DD9BE,mCCkBA,0BAYF,mED9BE,+BCkBA,0BAYF,+DD9BE,iCCkBA,YAYF,mDD9BE,mCCkBA,+BAYF,wED9BE,8BCkBA,+BAYF,mED9BE,kCCkBA,+BAYF,uED9BE,8BCkBA,+BAYF,mEDdA,uBCEE,yBAYF,mDDdA,yBCEE,yBAYF,qD", "file": "mdc.theme.min.css", "sourcesContent": ["//\n// Copyright 2017 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use '@material/feature-targeting/feature-targeting';\n@use './css';\n@use './custom-properties';\n@use './gss';\n@use './keys';\n@use './replace';\n@use './theme-color';\n\n@mixin core-styles($query: feature-targeting.all()) {\n  $feat-color: feature-targeting.create-target($query, color);\n\n  :root {\n    @include feature-targeting.targets($feat-color) {\n      @each $style in theme-color.get-theme-keys() {\n        @include custom-properties.declaration(\n          keys.create-custom-property($style)\n        );\n      }\n    }\n  }\n\n  @each $style in theme-color.get-theme-keys() {\n    @if $style != 'background' and $style != 'surface' {\n      .mdc-theme--#{$style} {\n        @include feature-targeting.targets($feat-color) {\n          @include property(color, $style);\n        }\n      }\n    } @else {\n      .mdc-theme--#{$style} {\n        @include feature-targeting.targets($feat-color) {\n          @include property(background-color, $style);\n        }\n      }\n    }\n  }\n\n  // CSS rules for using primary and secondary (plus light/dark variants) as background colors.\n  @each $style in ('primary', 'secondary') {\n    .mdc-theme--#{$style}-bg {\n      @include feature-targeting.targets($feat-color) {\n        @include property(background-color, $style);\n      }\n    }\n  }\n}\n\n/// Applies a dynamic value to the specified property. This mixin should be used\n/// in theme style mixins when setting properties.\n///\n/// The value may be any of the following:\n/// - a standard CSS value\n/// - a custom property Map, e.g. (varname: --mdc-foo, fallback: blue)\n/// - a Material theme key String, e.g. 'primary', 'on-primary'\n///\n/// @example\n///   @include theme.property(color, teal);\n///   @include theme.property(color, custom-properties.create(foo, blue));\n///   @include theme.property(color, primary);\n///\n/// A `$replace` Map parameter may be provided to replace key/value pairs for\n/// string values. This can be used to substitute parameters in complex string\n/// values such as `calc()` with custom properties.\n///\n/// @example\n///   @include theme.property(\n///     width,\n///     calc(foo + bar),\n///     $replace: (foo: custom-properties.create(foo), bar: 8px)\n///   );\n///\n/// Note: Material theme key Strings (e.g. `primary`) are not supported as\n/// replacement values.\n///\n/// A CSS custom property declaration may be emitted by providing a custom\n/// property Map to `$property`. The fallback value (or `$value` if provided)\n/// will be used as the declaration value.\n///\n/// @example - scss\n///   .foo {\n///     @include theme.property(custom-properties.create(foo, teal));\n///     @include theme.property(custom-properties.create(bar, teal), blue);\n///   }\n///\n/// @example - css\n///   .foo {\n///     --mdc-foo: teal;\n///     --mdc-bar: blue;\n///   }\n///\n/// @param {String | Map} $property - The name of the CSS property. May also be\n///     a custom property Map to emit a custom propery declaration.\n/// @param {String | Number | Color | List | Map} $value - The property's value.\n///     This parameter may be omitted if `$property` is a custom property Map.\n/// @param {Map} $gss - Optional Map of GSS annotations to set.\n/// @param {Map} $replace - An optional Map of replacement key/value pairs if\n///     the `$value` is a string.\n/// @param {Bool} $important - Set to true to add an `!important` rule. Defaults\n///     to false.\n@mixin property(\n  $property,\n  $value: null,\n  $gss: (),\n  $replace: null,\n  $important: false\n) {\n  @if custom-properties.is-custom-prop($property) {\n    // $property is a custom property Map\n    //   --mdc-foo: value;\n    @if $value {\n      $property: custom-properties.set-fallback(\n        $property,\n        $value,\n        $shallow: true\n      );\n    }\n\n    @include custom-properties.declaration(\n      $property,\n      $gss: $gss,\n      $important: $important\n    );\n  } @else if custom-properties.is-custom-prop($value) {\n    // $value is a custom property Map\n    //   property: var(--mdc-foo, fallback);\n    @include custom-properties.declaration(\n      $property,\n      $value,\n      $gss: $gss,\n      $important: $important\n    );\n  } @else if keys.is-key($value) {\n    // $value is a key String\n    //   property: key;\n    $custom-prop: keys.create-custom-property($value);\n\n    @if theme-color.is-theme-key($value) {\n      // Determine if we need to use a compile-time updated value to support\n      // Angular.\n      $key: $value;\n      // (changed: Bool, value: *)\n      $result: theme-color.deprecated-get-global-theme-key-value-if-changed(\n        $key\n      );\n\n      @if map.get($result, changed) {\n        // $mdc-theme-property-values was changed at compile time. Use the\n        // global value instead. Otherwise if it was not changed, continue\n        // using the key store normally.\n        $custom-prop: keys.create-custom-property($key);\n        $custom-prop: custom-properties.set-fallback(\n          $custom-prop,\n          map.get($result, value)\n        );\n      }\n    }\n\n    @include custom-properties.declaration(\n      $property,\n      $custom-prop,\n      $gss: $gss,\n      $important: $important\n    );\n  } @else {\n    // $value is a standard CSS value\n    //   property: value;\n    $fallback: null;\n    @if $replace {\n      // If any replacements are null, treat the entire value as null (do not\n      // emit anything).\n      @each $name, $replacement in $replace {\n        @if $replacement == null {\n          $value: null;\n        }\n      }\n    }\n\n    @if $replace and $value {\n      @if meta.type-of($replace) != 'map' {\n        @error 'mdc-theme: Invalid replacement #{$replace}. Must be a Map.';\n      }\n\n      $replace-map-fallback: ();\n      $replace-map-value: ();\n      $needs-fallback: false;\n      @each $name, $replacement in $replace {\n        @if custom-properties.is-custom-prop($replacement) {\n          $replace-value: custom-properties.get-declaration-value($replacement);\n          $replace-fallback: custom-properties.get-declaration-fallback(\n            $replacement\n          );\n          @if $replace-fallback {\n            $needs-fallback: true;\n          }\n\n          $replace-map-value: map.set(\n            $replace-map-value,\n            $name,\n            $replace-value\n          );\n          $replace-map-fallback: map.set(\n            $replace-map-fallback,\n            $name,\n            $replace-fallback\n          );\n        } @else {\n          $replace-map-value: map.set($replace-map-value, $name, $replacement);\n          $replace-map-fallback: map.set(\n            $replace-map-fallback,\n            $name,\n            $replacement\n          );\n        }\n      }\n\n      @if meta.type-of($value) == 'string' {\n        @if $needs-fallback {\n          $fallback: replace.replace-string($value, $replace-map-fallback);\n        }\n        $value: replace.replace-string($value, $replace-map-value);\n      } @else if meta.type-of($value) == 'list' {\n        @if $needs-fallback {\n          $fallback: replace.replace-list($value, $replace-map-fallback);\n        }\n        $value: replace.replace-list($value, $replace-map-value);\n      } @else {\n        @error 'mdc-theme: Invalid replacement value #{$value}. $replace may only be used with string or list values.';\n      }\n    }\n\n    @include css.declaration(\n      $property,\n      $value,\n      $fallback-value: $fallback,\n      $gss: $gss,\n      $important: $important\n    );\n  }\n}\n\n// @deprecated use the `property()` mixin instead\n@mixin prop($property, $style) {\n  @include property($property, $style);\n}\n\n/// Validates theme configuration keys by comparing it with original theme\n/// configuration, also validates theme values to see if it has any unsupported\n/// value formats.\n///\n/// Use this in internal `theme()` mixins to validate library-provided\n/// `$theme` maps and ensure that all tokens are correct and present.\n///\n/// @example\n///     @mixin theme($theme) {\n///       @include theme.validate-theme($light-theme, $theme);\n///       ...\n///     }\n///\n/// @see validate-theme-styles to validate only theme keys.\n///\n/// @param {Map} $origin-theme - Original theme configuration in Sass map format\n///     that has all supported keys.\n/// @param {Map} $custom-theme - Provided theme configuration in Sass map format\n///     that should be validated against `$origin-theme`.\n@mixin validate-theme($origin-theme, $custom-theme, $test-only: false) {\n  @include validate-theme-styles(\n    $origin-theme,\n    $custom-theme,\n    $test-only: $test-only\n  );\n  @include _validate-theme-values($custom-theme, $test-only: $test-only);\n}\n\n/// Validates theme configuration keys by comparing it with original theme\n/// configuration.\n///\n/// Use this in internal `theme-styles()` mixins to validate library-provided\n/// `$theme` maps and ensure that all tokens are correct and present.\n///\n/// @example\n///     @mixin theme-styles($theme) {\n///       @include theme.validate-theme-styles($light-theme, $theme);\n///       ...\n///     }\n///\n/// @see validate-theme to validate both theme keys and theme values.\n///\n/// @param {Map} $origin-theme - Original theme configuration in Sass map format\n///     that has all supported keys.\n/// @param {Map} $custom-theme - Provided theme configuration in Sass map format\n///     that should be validated against `$origin-theme`.\n@mixin validate-theme-styles($origin-theme, $custom-theme, $test-only: false) {\n  $origin-keys: map.keys($origin-theme);\n  $unsupported-keys: ();\n\n  @each $key, $value in $custom-theme {\n    @if (not list.index($origin-keys, $key)) {\n      $unsupported-keys: list.append(\n        $unsupported-keys,\n        $key,\n        $separator: comma\n      );\n    }\n  }\n\n  @if list.length($unsupported-keys) > 0 {\n    $error-message: 'Unsupported keys found: #{$unsupported-keys}. Expected one of: #{$origin-keys}.';\n\n    @if $test-only {\n      content: $error-message;\n    } @else {\n      @error $error-message;\n    }\n  }\n}\n\n/// Validates theme configuration values to see if it has any unsupported value\n/// formats.\n/// @see Use `validate-theme()` to validate both theme keys and theme values.\n/// @param {Map} $custom-theme - Provided theme configuration in Sass map format\n///     that needs to be validated.\n@mixin _validate-theme-values($custom-theme, $test-only: false) {\n  $unsupported-custom-prop-keys: ();\n\n  @each $key, $value in $custom-theme {\n    @if custom-properties.is-custom-prop($value) {\n      $unsupported-custom-prop-keys: list.append(\n        $unsupported-custom-prop-keys,\n        $key,\n        $separator: comma\n      );\n    }\n  }\n\n  @if list.length($unsupported-custom-prop-keys) > 0 {\n    $error-message: 'Custom properties are not supported for theme map keys: #{$unsupported-custom-prop-keys}';\n\n    @if $test-only {\n      content: $error-message;\n    } @else {\n      @error $error-message;\n    }\n  }\n}\n", "//\n// Copyright 2020 Google Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n//\n\n@use 'sass:list';\n@use 'sass:map';\n@use 'sass:meta';\n@use './gss';\n\n/// When true, add an additional property/value declaration before declarations\n/// that use advanced features such as custom properties or CSS functions. This\n/// adds fallback support for older browsers such as IE11 that do not support\n/// these features at the cost of additional CSS. Set this variable to false to\n/// disable generating fallback declarations.\n$enable-fallback-declarations: true !default;\n\n/// Writes a CSS property/value declaration. This mixin is used throughout the\n/// theme package for consistency for dynamically setting CSS property values.\n///\n/// This mixin may optionally take a fallback value. For advanced features such\n/// as custom properties or CSS functions like min and max, a fallback value is\n/// recommended to support older browsers.\n///\n/// @param {String} $property - The CSS property of the declaration.\n/// @param {*} $value - The value of the CSS declaration. The value should be\n///     resolved by other theme functions first (i.e. custom property Maps and\n///     Material theme keys are not supported in this mixin). If the value is\n///     null, no declarations will be emitted.\n/// @param {*} $fallback - An optional fallback value for older browsers. If\n///     provided, a second property/value declaration will be added before the\n///     main property/value declaration.\n/// @param {Map} $gss - An optional Map of GSS annotations to add.\n/// @param {Bool} $important - If true, add `!important` to the declaration.\n@mixin declaration(\n  $property,\n  $value,\n  $fallback-value: null,\n  $gss: (),\n  $important: false\n) {\n  // Normally setting a null value to a property will not emit CSS, so mixins\n  // wouldn't need to check this. However, Sass will throw an error if the\n  // interpolated property is a custom property.\n  @if $value != null {\n    $important-rule: if($important, ' !important', '');\n\n    @if $fallback-value and $enable-fallback-declarations {\n      @include gss.annotate($gss);\n      #{$property}: #{$fallback-value} #{$important-rule};\n\n      // Add @alternate to annotations.\n      $gss: map.merge(\n        $gss,\n        (\n          alternate: true,\n        )\n      );\n    }\n\n    @include gss.annotate($gss);\n    #{$property}: #{$value}#{$important-rule};\n  }\n}\n\n/// Unpacks shorthand values for CSS properties (i.e. lists of 1-3 values).\n/// If a list of 4 values is given, it is returned as-is.\n///\n/// Examples:\n///\n/// unpack-value(4px) => 4px 4px 4px 4px\n/// unpack-value(4px 2px) => 4px 2px 4px 2px\n/// unpack-value(4px 2px 2px) => 4px 2px 2px 2px\n/// unpack-value(4px 2px 0 2px) => 4px 2px 0 2px\n///\n/// @param {Number | Map | List} $value - List of 1 to 4 value numbers.\n/// @return {List} a List of 4 value numbers.\n@function unpack-value($value) {\n  @if meta.type-of($value) == 'map' or list.length($value) == 1 {\n    @return $value $value $value $value;\n  } @else if list.length($value) == 4 {\n    @return $value;\n  } @else if list.length($value) == 3 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 3)\n      list.nth($value, 2);\n  } @else if list.length($value) == 2 {\n    @return list.nth($value, 1) list.nth($value, 2) list.nth($value, 1)\n      list.nth($value, 2);\n  }\n\n  @error \"Invalid CSS property value: '#{$value}' is more than 4 values\";\n}\n"], "sourceRoot": ""}