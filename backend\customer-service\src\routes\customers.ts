import express from 'express';
import axios from 'axios';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: express.Response, next: express.NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

// Service URLs
const SALES_SERVICE_URL = process.env.SALES_SERVICE_URL || 'http://localhost:3003';

/**
 * @swagger
 * /customers:
 *   get:
 *     summary: Get all customers
 *     tags: [Customers]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customers retrieved successfully
 */
router.get('/', requireAuth, requirePermission('customers:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { page, limit, search } = req.query as any;
  const skip = (page - 1) * limit;

  const where: any = { isActive: true };

  if (search) {
    where.OR = [
      { firstName: { contains: search, mode: 'insensitive' } },
      { lastName: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { phone: { contains: search, mode: 'insensitive' } },
    ];
  }

  const [customers, total] = await Promise.all([
    prisma.customer.findMany({
      where,
      skip,
      take: limit,
      include: {
        _count: {
          select: {
            sales: true,
            orders: true,
          }
        }
      },
      orderBy: { lastName: 'asc' }
    }),
    prisma.customer.count({ where })
  ]);

  const customersResponse = customers.map(customer => ({
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
  }));

  utils.sendSuccess(res, {
    customers: customersResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasNext: skip + limit < total,
      hasPrev: page > 1,
    }
  });
}));

/**
 * @swagger
 * /customers/{id}:
 *   get:
 *     summary: Get customer by ID
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer retrieved successfully
 *       404:
 *         description: Customer not found
 */
router.get('/:id', requireAuth, requirePermission('customers:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;

  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          sales: true,
          orders: true,
        }
      }
    }
  });

  if (!customer) {
    utils.sendError(res, 'Customer not found', 404);
    return;
  }

  const customerResponse = {
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
  };

  utils.sendSuccess(res, { customer: customerResponse });
}));

/**
 * @swagger
 * /customers:
 *   post:
 *     summary: Create new customer
 *     tags: [Customers]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               city:
 *                 type: string
 *               postalCode:
 *                 type: string
 *     responses:
 *       201:
 *         description: Customer created successfully
 */
router.post('/', requireAuth, requirePermission('customers:create'), validation.validateBody(validation.customerCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const customerData = req.body;

  // Check if customer with email already exists
  if (customerData.email) {
    const existingCustomer = await prisma.customer.findUnique({
      where: { email: customerData.email }
    });

    if (existingCustomer) {
      utils.sendError(res, 'Customer with this email already exists', 400);
      return;
    }
  }

  // Create customer
  const customer = await prisma.customer.create({
    data: customerData,
    include: {
      _count: {
        select: {
          sales: true,
          orders: true,
        }
      }
    }
  });

  const customerResponse = {
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
  };

  utils.sendSuccess(res, { customer: customerResponse }, 'Customer created successfully', 201);
}));

/**
 * @swagger
 * /customers/{id}:
 *   put:
 *     summary: Update customer
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               phone:
 *                 type: string
 *               address:
 *                 type: string
 *               city:
 *                 type: string
 *               postalCode:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Customer updated successfully
 */
router.put('/:id', requireAuth, requirePermission('customers:update'), validation.validateParams(validation.idSchema), validation.validateBody(validation.customerUpdateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if customer exists
  const existingCustomer = await prisma.customer.findUnique({
    where: { id }
  });

  if (!existingCustomer) {
    utils.sendError(res, 'Customer not found', 404);
    return;
  }

  // Check for email conflicts
  if (updateData.email) {
    const conflictCustomer = await prisma.customer.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { email: updateData.email }
        ]
      }
    });

    if (conflictCustomer) {
      utils.sendError(res, 'Email already exists', 400);
      return;
    }
  }

  // Update customer
  const customer = await prisma.customer.update({
    where: { id },
    data: updateData,
    include: {
      _count: {
        select: {
          sales: true,
          orders: true,
        }
      }
    }
  });

  const customerResponse = {
    ...customer,
    salesCount: customer._count.sales,
    ordersCount: customer._count.orders,
  };

  utils.sendSuccess(res, { customer: customerResponse }, 'Customer updated successfully');
}));

/**
 * @swagger
 * /customers/{id}:
 *   delete:
 *     summary: Delete customer
 *     tags: [Customers]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Customer deleted successfully
 */
router.delete('/:id', requireAuth, requirePermission('customers:delete'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: express.Response) => {
  const { id } = req.params;

  // Check if customer exists
  const customer = await prisma.customer.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          sales: true,
          orders: true,
        }
      }
    }
  });

  if (!customer) {
    utils.sendError(res, 'Customer not found', 404);
    return;
  }

  // Check if customer has sales or orders
  if (customer._count.sales > 0 || customer._count.orders > 0) {
    // Soft delete by deactivating
    await prisma.customer.update({
      where: { id },
      data: { isActive: false },
    });
  } else {
    // Hard delete if no sales or orders
    await prisma.customer.delete({
      where: { id }
    });
  }

  utils.sendSuccess(res, null, 'Customer deleted successfully');
}));

export default router;
