/**
 * @license
 * Copyright 2021 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
import { __extends } from "tslib";
import { MDCObserverFoundation } from '@material/base/observer-foundation';
import { CssClasses } from './constants';
/**
 * `MDCSwitchFoundation` provides a state-only foundation for a switch
 * component.
 *
 * State observers and event handler entrypoints update a component's adapter's
 * state with the logic needed for switch to function.
 */
var MDCSwitchFoundation = /** @class */ (function (_super) {
    __extends(MDCSwitchFoundation, _super);
    function MDCSwitchFoundation(adapter) {
        var _this = _super.call(this, adapter) || this;
        _this.handleClick = _this.handleClick.bind(_this);
        return _this;
    }
    /**
     * Initializes the foundation and starts observing state changes.
     */
    MDCSwitchFoundation.prototype.init = function () {
        this.observe(this.adapter.state, {
            disabled: this.stopProcessingIfDisabled,
            processing: this.stopProcessingIfDisabled,
        });
    };
    /**
     * Event handler for switch click events. Clicking on a switch will toggle its
     * selected state.
     */
    MDCSwitchFoundation.prototype.handleClick = function () {
        if (this.adapter.state.disabled) {
            return;
        }
        this.adapter.state.selected = !this.adapter.state.selected;
    };
    MDCSwitchFoundation.prototype.stopProcessingIfDisabled = function () {
        if (this.adapter.state.disabled) {
            this.adapter.state.processing = false;
        }
    };
    return MDCSwitchFoundation;
}(MDCObserverFoundation));
export { MDCSwitchFoundation };
/**
 * `MDCSwitchRenderFoundation` provides a state and rendering foundation for a
 * switch component.
 *
 * State observers and event handler entrypoints update a component's
 * adapter's state with the logic needed for switch to function.
 *
 * In response to state changes, the rendering foundation uses the component's
 * render adapter to keep the component's DOM updated with the state.
 */
var MDCSwitchRenderFoundation = /** @class */ (function (_super) {
    __extends(MDCSwitchRenderFoundation, _super);
    function MDCSwitchRenderFoundation() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    /**
     * Initializes the foundation and starts observing state changes.
     */
    MDCSwitchRenderFoundation.prototype.init = function () {
        _super.prototype.init.call(this);
        this.observe(this.adapter.state, {
            disabled: this.onDisabledChange,
            processing: this.onProcessingChange,
            selected: this.onSelectedChange,
        });
    };
    /**
     * Initializes the foundation from a server side rendered (SSR) component.
     * This will sync the adapter's state with the current state of the DOM.
     *
     * This method should be called after `init()`.
     */
    MDCSwitchRenderFoundation.prototype.initFromDOM = function () {
        // Turn off observers while setting state
        this.setObserversEnabled(this.adapter.state, false);
        this.adapter.state.selected = this.adapter.hasClass(CssClasses.SELECTED);
        // Ensure aria-checked is set if attribute is not present
        this.onSelectedChange();
        this.adapter.state.disabled = this.adapter.isDisabled();
        this.adapter.state.processing =
            this.adapter.hasClass(CssClasses.PROCESSING);
        // Re-observe state
        this.setObserversEnabled(this.adapter.state, true);
        this.stopProcessingIfDisabled();
    };
    MDCSwitchRenderFoundation.prototype.onDisabledChange = function () {
        this.adapter.setDisabled(this.adapter.state.disabled);
    };
    MDCSwitchRenderFoundation.prototype.onProcessingChange = function () {
        this.toggleClass(this.adapter.state.processing, CssClasses.PROCESSING);
    };
    MDCSwitchRenderFoundation.prototype.onSelectedChange = function () {
        this.adapter.setAriaChecked(String(this.adapter.state.selected));
        this.toggleClass(this.adapter.state.selected, CssClasses.SELECTED);
        this.toggleClass(!this.adapter.state.selected, CssClasses.UNSELECTED);
    };
    MDCSwitchRenderFoundation.prototype.toggleClass = function (addClass, className) {
        if (addClass) {
            this.adapter.addClass(className);
        }
        else {
            this.adapter.removeClass(className);
        }
    };
    return MDCSwitchRenderFoundation;
}(MDCSwitchFoundation));
export { MDCSwitchRenderFoundation };
//# sourceMappingURL=foundation.js.map