{"name": "@material/form-field", "description": "Material Components for the web wrapper for laying out form fields and labels next to one another", "version": "15.0.0-canary.7f224ddd4.0", "license": "MIT", "keywords": ["material components", "material design", "form"], "main": "dist/mdc.formField.js", "module": "index.js", "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/material-components/material-components-web.git", "directory": "packages/mdc-form-field"}, "dependencies": {"@material/base": "15.0.0-canary.7f224ddd4.0", "@material/feature-targeting": "15.0.0-canary.7f224ddd4.0", "@material/ripple": "15.0.0-canary.7f224ddd4.0", "@material/rtl": "15.0.0-canary.7f224ddd4.0", "@material/theme": "15.0.0-canary.7f224ddd4.0", "@material/typography": "15.0.0-canary.7f224ddd4.0", "tslib": "^2.1.0"}, "gitHead": "ff8ff3ce455054f1532d1ae0983f40634a524886"}