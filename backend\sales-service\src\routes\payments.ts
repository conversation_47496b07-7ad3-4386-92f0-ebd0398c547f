import express, { Response, NextFunction } from 'express';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

/**
 * @swagger
 * /payments:
 *   get:
 *     summary: Get all payments
 *     tags: [Payments]
 */
router.get('/', requireAuth, requirePermission('sales:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { page, limit, method, status, dateFrom, dateTo } = req.query;
  const skip = ((page as number) - 1) * (limit as number);

  const where: any = {};

  if (method) where.method = method as string;
  if (status) where.status = status as string;

  if (dateFrom || dateTo) {
    where.createdAt = {};
    if (dateFrom) where.createdAt.gte = new Date(dateFrom as string);
    if (dateTo) where.createdAt.lte = new Date(dateTo as string);
  }

  const [payments, total] = await Promise.all([
    prisma.payment.findMany({
      where,
      skip,
      take: limit as number,
      include: {
        sale: {
          select: {
            id: true,
            saleNumber: true,
            total: true,
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                username: true,
              }
            }
          }
        },
        order: {
          select: {
            id: true,
            orderNumber: true,
            total: true,
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.payment.count({ where })
  ]);

  utils.sendSuccess(res, {
    payments,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / (limit as number)),
    }
  });
}));

/**
 * @swagger
 * /payments/{id}:
 *   get:
 *     summary: Get payment by ID
 *     tags: [Payments]
 */
router.get('/:id', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const payment = await prisma.payment.findUnique({
    where: { id },
    include: {
      sale: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              username: true,
            }
          },
          customer: true,
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                }
              }
            }
          }
        }
      },
      order: true,
    }
  });

  if (!payment) {
    utils.sendError(res, 'Payment not found', 404);
    return;
  }

  utils.sendSuccess(res, { payment });
}));

/**
 * @swagger
 * /payments:
 *   post:
 *     summary: Process a payment
 *     tags: [Payments]
 */
router.post('/', requireAuth, requirePermission('sales:create'), validation.validateBody(validation.paymentCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { saleId, orderId, method, amount, reference } = req.body;

  // Validate that either saleId or orderId is provided
  if (!saleId && !orderId) {
    utils.sendError(res, 'Either saleId or orderId must be provided', 400);
    return;
  }

  // Validate the sale or order exists
  let targetEntity: any = null;
  if (saleId) {
    targetEntity = await prisma.sale.findUnique({
      where: { id: saleId },
      include: { payments: true }
    });
    if (!targetEntity) {
      utils.sendError(res, 'Sale not found', 404);
      return;
    }
  } else if (orderId) {
    targetEntity = await prisma.order.findUnique({
      where: { id: orderId },
      include: { payments: true }
    });
    if (!targetEntity) {
      utils.sendError(res, 'Order not found', 404);
      return;
    }
  }

  // Check if payment amount is valid
  const existingPayments = targetEntity.payments.reduce((sum: number, p: any) => sum + p.amount, 0);
  const remainingAmount = targetEntity.total - existingPayments;

  if (amount > remainingAmount) {
    utils.sendError(res, `Payment amount exceeds remaining balance. Remaining: ${remainingAmount}`, 400);
    return;
  }

  try {
    // Create payment
    const payment = await prisma.payment.create({
      data: {
        saleId,
        orderId,
        method,
        amount,
        reference,
        status: 'completed',
      },
      include: {
        sale: {
          select: {
            id: true,
            saleNumber: true,
            total: true,
          }
        },
        order: {
          select: {
            id: true,
            orderNumber: true,
            total: true,
          }
        }
      }
    });

    utils.sendSuccess(res, { payment }, 'Payment processed successfully', 201);
  } catch (error) {
    console.error('Payment creation failed:', error);
    utils.sendError(res, 'Failed to process payment', 500);
  }
}));

/**
 * @swagger
 * /payments/{id}/refund:
 *   put:
 *     summary: Refund a payment
 *     tags: [Payments]
 */
router.put('/:id/refund', requireAuth, requirePermission('sales:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { amount, reason } = req.body;

  const payment = await prisma.payment.findUnique({
    where: { id }
  });

  if (!payment) {
    utils.sendError(res, 'Payment not found', 404);
    return;
  }

  if (payment.status !== 'completed') {
    utils.sendError(res, 'Only completed payments can be refunded', 400);
    return;
  }

  const refundAmount = amount || payment.amount;

  if (refundAmount > payment.amount) {
    utils.sendError(res, 'Refund amount cannot exceed payment amount', 400);
    return;
  }

  try {
    // Create refund record
    const refund = await prisma.payment.create({
      data: {
        saleId: payment.saleId,
        orderId: payment.orderId,
        method: payment.method,
        amount: -refundAmount,
        reference: `REFUND-${payment.id}`,
        status: 'completed',
      }
    });

    utils.sendSuccess(res, { refund }, 'Payment refunded successfully');
  } catch (error) {
    console.error('Refund creation failed:', error);
    utils.sendError(res, 'Failed to process refund', 500);
  }
}));

/**
 * @swagger
 * /payments/stats:
 *   get:
 *     summary: Get payment statistics
 *     tags: [Payments]
 */
router.get('/stats', requireAuth, requirePermission('sales:read'), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { dateFrom, dateTo } = req.query;

  const where: any = {};

  if (dateFrom || dateTo) {
    where.createdAt = {};
    if (dateFrom) where.createdAt.gte = new Date(dateFrom as string);
    if (dateTo) where.createdAt.lte = new Date(dateTo as string);
  }

  const [
    totalPayments,
    paymentsByMethod,
    totalAmount
  ] = await Promise.all([
    prisma.payment.count({ where }),
    prisma.payment.groupBy({
      by: ['method'],
      where,
      _count: { method: true },
      _sum: { amount: true }
    }),
    prisma.payment.aggregate({
      where,
      _sum: { amount: true }
    })
  ]);

  const stats = {
    totalPayments,
    totalAmount: totalAmount._sum.amount || 0,
    byMethod: paymentsByMethod.map(item => ({
      method: item.method,
      count: item._count.method,
      amount: item._sum.amount || 0
    }))
  };

  utils.sendSuccess(res, { stats });
}));

export default router;
