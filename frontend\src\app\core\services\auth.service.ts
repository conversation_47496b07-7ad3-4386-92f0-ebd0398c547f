import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

import { environment } from '../../../environments/environment';
import { User, LoginRequest, LoginResponse, RegisterRequest, TokenPair } from '../models/user.model';
import { AppState } from '../store';
import { loginSuccess, loginFailure, logout as logoutAction } from '../store/auth/auth.actions';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private http = inject(HttpClient);
  private router = inject(Router);
  private store = inject(Store<AppState>);

  private readonly API_URL = `${environment.apiUrl}/auth`;
  private readonly TOKEN_KEY = 'shop_access_token';
  private readonly REFRESH_TOKEN_KEY = 'shop_refresh_token';

  private currentUserSubject = new BehaviorSubject<User | null>(null);
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  private loadingSubject = new BehaviorSubject<boolean>(false);

  public currentUser$ = this.currentUserSubject.asObservable();
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();

  constructor() {
    this.initializeAuth();
  }

  initializeAuth(): void {
    const token = this.getToken();
    if (token) {
      this.validateToken().subscribe({
        next: (user) => {
          this.setAuthState(user, true);
        },
        error: () => {
          this.clearAuthState();
        }
      });
    }
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    this.loadingSubject.next(true);
    
    return this.http.post<LoginResponse>(`${this.API_URL}/login`, credentials).pipe(
      tap(response => {
        this.setTokens(response.accessToken, response.refreshToken);
        this.setAuthState(response.user, true);
        this.store.dispatch(loginSuccess({ user: response.user }));
        this.router.navigate(['/dashboard']);
      }),
      catchError(error => {
        this.store.dispatch(loginFailure({ error: error.error?.message || 'Login failed' }));
        return throwError(() => error);
      }),
      tap(() => this.loadingSubject.next(false))
    );
  }

  register(userData: RegisterRequest): Observable<LoginResponse> {
    this.loadingSubject.next(true);
    
    return this.http.post<LoginResponse>(`${this.API_URL}/register`, userData).pipe(
      tap(response => {
        this.setTokens(response.accessToken, response.refreshToken);
        this.setAuthState(response.user, true);
        this.store.dispatch(loginSuccess({ user: response.user }));
        this.router.navigate(['/dashboard']);
      }),
      catchError(error => {
        return throwError(() => error);
      }),
      tap(() => this.loadingSubject.next(false))
    );
  }

  logout(): void {
    this.http.post(`${this.API_URL}/logout`, {}).subscribe({
      complete: () => {
        this.clearAuthState();
        this.store.dispatch(logoutAction());
        this.router.navigate(['/login']);
      }
    });
  }

  refreshToken(): Observable<TokenPair> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http.post<TokenPair>(`${this.API_URL}/refresh`, { refreshToken }).pipe(
      tap(tokens => {
        this.setTokens(tokens.accessToken, tokens.refreshToken);
      }),
      catchError(error => {
        this.clearAuthState();
        this.router.navigate(['/login']);
        return throwError(() => error);
      })
    );
  }

  validateToken(): Observable<User> {
    return this.http.get<{ user: User }>(`${this.API_URL}/me`).pipe(
      map(response => response.user)
    );
  }

  checkAuthStatus(): void {
    const token = this.getToken();
    if (token) {
      this.validateToken().subscribe({
        next: (user) => {
          this.setAuthState(user, true);
        },
        error: () => {
          this.clearAuthState();
        }
      });
    } else {
      this.clearAuthState();
    }
  }

  hasPermission(permission: string): boolean {
    const user = this.currentUserSubject.value;
    return user?.permissions?.includes(permission) || false;
  }

  hasRole(role: string): boolean {
    const user = this.currentUserSubject.value;
    return user?.roles?.includes(role) || false;
  }

  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  private setTokens(accessToken: string, refreshToken: string): void {
    localStorage.setItem(this.TOKEN_KEY, accessToken);
    localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
  }

  private setAuthState(user: User, isAuthenticated: boolean): void {
    this.currentUserSubject.next(user);
    this.isAuthenticatedSubject.next(isAuthenticated);
  }

  private clearAuthState(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }
}
