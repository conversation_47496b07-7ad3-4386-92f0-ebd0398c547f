# Backend Implementation Guide

## 🏗️ Architecture Overview

The backend is built using a **microservices architecture** with the following services:

### Core Services
1. **API Gateway** (Port 3000) - Central routing and authentication
2. **Auth Service** (Port 3001) - User authentication and authorization
3. **Inventory Service** (Port 3002) - Product and stock management
4. **Sales Service** (Port 3003) - Point of sale and transactions
5. **Order Service** (Port 3004) - Order processing and management
6. **Customer Service** (Port 3005) - Customer relationship management
7. **Reporting Service** (Port 3006) - Analytics and reports
8. **Notification Service** (Port 3007) - Real-time notifications

### Shared Infrastructure
- **Shared Module** - Common utilities, database models, and middleware
- **PostgreSQL Database** - Primary data storage
- **Redis** - Caching and session management
- **Docker** - Containerization

## 🛠️ Technology Stack

- **Language**: TypeScript (100% conversion complete)
- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Cache**: Redis
- **Authentication**: JWT with RBAC
- **API**: REST + GraphQL
- **Documentation**: Swagger/OpenAPI
- **Containerization**: Docker

## 📁 Project Structure

```
backend/
├── shared/                 # Shared utilities and models
│   ├── src/
│   │   ├── database/      # Prisma schema and migrations
│   │   ├── middleware/    # Common middleware
│   │   ├── utils/         # Utility functions
│   │   └── types/         # TypeScript type definitions
│   └── package.json
├── api-gateway/           # Central API gateway
├── auth-service/          # Authentication service
├── inventory-service/     # Inventory management
├── sales-service/         # Sales processing
├── order-service/         # Order management
├── customer-service/      # Customer management
├── reporting-service/     # Analytics and reporting
├── notification-service/  # Real-time notifications
└── docker-compose.yml     # Docker orchestration
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Docker (optional)

### Installation

1. **Clone and setup**:
```bash
cd backend
npm install
```

2. **Setup environment variables**:
```bash
# Copy environment template
cp .env.example .env

# Configure your database and Redis connections
DATABASE_URL="postgresql://user:password@localhost:5432/shop_db"
REDIS_URL="redis://localhost:6379"
JWT_SECRET="your-secret-key"
```

3. **Database setup**:
```bash
# Generate Prisma client
npx prisma generate

# Run migrations
npx prisma migrate deploy

# Seed database (optional)
npx prisma db seed
```

4. **Start services**:
```bash
# Start all services with Docker
docker-compose up

# Or start individual services
npm run dev:gateway
npm run dev:auth
npm run dev:inventory
npm run dev:sales
npm run dev:orders
npm run dev:customers
npm run dev:reporting
npm run dev:notifications
```

## 🔧 Service Details

### API Gateway (Port 3000)
**Purpose**: Central entry point for all API requests
**Features**:
- Request routing to appropriate services
- Authentication middleware
- Rate limiting
- CORS handling
- Request/response logging

**Key Files**:
- `src/index.ts` - Main server setup
- `src/routes/` - Route definitions
- `src/middleware/` - Gateway middleware

### Auth Service (Port 3001)
**Purpose**: User authentication and authorization
**Features**:
- JWT token generation and validation
- Role-based access control (RBAC)
- User registration and login
- Password hashing and validation
- Token refresh mechanism

**Key Endpoints**:
- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- `POST /auth/refresh` - Token refresh
- `GET /auth/me` - Get current user
- `POST /auth/logout` - User logout

### Inventory Service (Port 3002)
**Purpose**: Product and stock management
**Features**:
- Product CRUD operations
- Category management
- Stock level tracking
- Low stock alerts
- GraphQL API for complex queries

**Key Endpoints**:
- `GET /inventory/products` - List products
- `POST /inventory/products` - Create product
- `GET /inventory/stock` - Stock levels
- `PUT /inventory/stock/:id/adjust` - Adjust stock
- `GET /graphql` - GraphQL endpoint

### Sales Service (Port 3003)
**Purpose**: Point of sale and transaction processing
**Features**:
- Sale transaction processing
- Payment handling
- Receipt generation (PDF/HTML)
- Sales history and reporting
- Integration with inventory for stock updates

**Key Endpoints**:
- `POST /sales` - Process new sale
- `GET /sales` - Sales history
- `GET /sales/:id` - Sale details
- `POST /payments` - Process payment
- `GET /receipts/:saleId/pdf` - Download receipt

### Order Service (Port 3004)
**Purpose**: Order processing and management
**Features**:
- Order lifecycle management
- Order status tracking
- Integration with inventory
- Order fulfillment workflow

### Customer Service (Port 3005)
**Purpose**: Customer relationship management
**Features**:
- Customer profiles
- Purchase history
- Loyalty programs
- Customer analytics

### Reporting Service (Port 3006)
**Purpose**: Analytics and business intelligence
**Features**:
- Sales reports
- Inventory reports
- Customer analytics
- Financial summaries
- Export capabilities

### Notification Service (Port 3007)
**Purpose**: Real-time notifications
**Features**:
- WebSocket connections
- Email notifications
- Push notifications
- Event-driven messaging

## 🔐 Authentication & Authorization

### JWT Implementation
- Access tokens (15 minutes expiry)
- Refresh tokens (7 days expiry)
- Secure HTTP-only cookies
- Token blacklisting on logout

### Role-Based Access Control (RBAC)
**Roles**:
- `admin` - Full system access
- `manager` - Store management access
- `cashier` - POS and basic operations
- `viewer` - Read-only access

**Permissions**:
- `inventory:read/create/update/delete`
- `sales:read/create/update/delete`
- `orders:read/create/update/delete`
- `customers:read/create/update/delete`
- `reports:read`
- `admin:read/create/update/delete`

## 📊 Database Schema

### Core Tables
- `users` - User accounts and authentication
- `roles` - User roles definition
- `permissions` - Permission definitions
- `categories` - Product categories
- `products` - Product catalog
- `stock` - Inventory levels
- `sales` - Sales transactions
- `sale_items` - Individual sale items
- `payments` - Payment records
- `customers` - Customer information
- `orders` - Order management

### Relationships
- Users have roles (many-to-many)
- Roles have permissions (many-to-many)
- Products belong to categories (one-to-many)
- Products have stock records (one-to-one)
- Sales have multiple items (one-to-many)
- Sales have multiple payments (one-to-many)

## 🔄 Inter-Service Communication

### Service Discovery
- Environment-based service URLs
- Health check endpoints
- Circuit breaker pattern

### Data Consistency
- Database transactions
- Eventual consistency for cross-service operations
- Compensation patterns for failures

### Error Handling
- Standardized error responses
- Logging and monitoring
- Graceful degradation

## 📈 Monitoring & Logging

### Health Checks
Each service exposes `/health` endpoint:
```json
{
  "service": "inventory-service",
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Logging
- Structured JSON logging
- Request/response logging
- Error tracking
- Performance metrics

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Integration Tests
```bash
npm run test:integration
```

### API Testing
```bash
npm run test:api
```

## 🚀 Deployment

### Docker Deployment
```bash
# Build all services
docker-compose build

# Start production environment
docker-compose -f docker-compose.prod.yml up -d
```

### Environment Variables
```env
NODE_ENV=production
DATABASE_URL=postgresql://...
REDIS_URL=redis://...
JWT_SECRET=...
JWT_REFRESH_SECRET=...
```

## 🔧 Development

### Adding New Service
1. Create service directory
2. Setup package.json with TypeScript
3. Implement Express server
4. Add to docker-compose.yml
5. Update API gateway routes

### Database Changes
1. Update Prisma schema
2. Generate migration: `npx prisma migrate dev`
3. Update TypeScript types
4. Test changes

### API Documentation
- Swagger documentation available at `/docs`
- GraphQL playground at `/graphql`
- Postman collection in `/docs/postman/`

## 🛡️ Security

### Best Practices
- Input validation and sanitization
- SQL injection prevention (Prisma ORM)
- XSS protection
- CORS configuration
- Rate limiting
- Secure headers (Helmet.js)
- Environment variable protection

### Authentication Security
- Password hashing (bcrypt)
- JWT secret rotation
- Token expiration
- Secure cookie settings
- HTTPS enforcement (production)

## 📚 API Reference

### Standard Response Format
```json
{
  "success": true,
  "data": {...},
  "message": "Operation successful",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Pagination Format
```json
{
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## 🔍 Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URL and PostgreSQL status
2. **Redis Connection**: Verify Redis server and REDIS_URL
3. **Port Conflicts**: Ensure all service ports are available
4. **JWT Errors**: Verify JWT_SECRET configuration
5. **CORS Issues**: Check CORS configuration in API gateway

### Debug Mode
```bash
DEBUG=* npm run dev
```

### Logs Location
- Development: Console output
- Production: `/logs/` directory
- Docker: `docker logs <container_name>`
