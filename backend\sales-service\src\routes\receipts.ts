import express, { Response, NextFunction } from 'express';
import PDFDocument from 'pdfkit';
import QRCode from 'qrcode';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

/**
 * @swagger
 * /receipts/{saleId}:
 *   get:
 *     summary: Get receipt for a sale
 *     tags: [Receipts]
 */
router.get('/:saleId', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { saleId } = req.params;
  const { format = 'json' } = req.query;

  const sale = await prisma.sale.findUnique({
    where: { id: saleId },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      },
      customer: true,
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              price: true,
            }
          }
        }
      },
      payments: true,
    }
  });

  if (!sale) {
    utils.sendError(res, 'Sale not found', 404);
    return;
  }

  // Non-admin users can only access their own sales
  if (!req.user?.roles.includes('admin') && sale.userId !== req.user?.userId) {
    utils.sendError(res, 'Access denied', 403);
    return;
  }

  // Check if receipt already exists
  let receipt = await prisma.receipt.findUnique({
    where: { saleId }
  });

  if (!receipt) {
    // Generate receipt number
    const receiptNumber = utils.generateReceiptNumber();
    
    // Create receipt record
    receipt = await prisma.receipt.create({
      data: {
        saleId,
        receiptNumber,
        format: format as string,
        content: JSON.stringify(sale),
      }
    });
  }

  switch (format) {
    case 'pdf':
      return generatePDFReceipt(res, sale, receipt);
    case 'html':
      return generateHTMLReceipt(res, sale, receipt);
    default:
      return utils.sendSuccess(res, { receipt: { ...receipt, sale } });
  }
}));

/**
 * @swagger
 * /receipts/{saleId}/pdf:
 *   get:
 *     summary: Download PDF receipt
 *     tags: [Receipts]
 */
router.get('/:saleId/pdf', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { saleId } = req.params;

  const sale = await prisma.sale.findUnique({
    where: { id: saleId },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      },
      customer: true,
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              price: true,
            }
          }
        }
      },
      payments: true,
    }
  });

  if (!sale) {
    utils.sendError(res, 'Sale not found', 404);
    return;
  }

  // Non-admin users can only access their own sales
  if (!req.user?.roles.includes('admin') && sale.userId !== req.user?.userId) {
    utils.sendError(res, 'Access denied', 403);
    return;
  }

  let receipt = await prisma.receipt.findUnique({
    where: { saleId }
  });

  if (!receipt) {
    const receiptNumber = utils.generateReceiptNumber();
    receipt = await prisma.receipt.create({
      data: {
        saleId,
        receiptNumber,
        format: 'PDF',
        content: JSON.stringify(sale),
      }
    });
  }

  return generatePDFReceipt(res, sale, receipt);
}));

// Generate PDF receipt
async function generatePDFReceipt(res: Response, sale: any, receipt: any): Promise<void> {
  try {
    const doc = new PDFDocument({ margin: 50 });
    
    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="receipt-${receipt.receiptNumber}.pdf"`);
    
    // Pipe PDF to response
    doc.pipe(res);

    // Header
    doc.fontSize(20).text('RECEIPT', { align: 'center' });
    doc.moveDown();

    // Store info
    doc.fontSize(12)
       .text('Shop Management System', { align: 'center' })
       .text('123 Business Street', { align: 'center' })
       .text('City, State 12345', { align: 'center' })
       .text('Phone: (*************', { align: 'center' });
    
    doc.moveDown();

    // Receipt details
    doc.text(`Receipt #: ${receipt.receiptNumber}`)
       .text(`Sale #: ${sale.saleNumber}`)
       .text(`Date: ${new Date(sale.createdAt).toLocaleString()}`)
       .text(`Cashier: ${sale.user.firstName} ${sale.user.lastName}`);

    if (sale.customer) {
      doc.text(`Customer: ${sale.customer.firstName} ${sale.customer.lastName}`);
    }

    doc.moveDown();

    // Items table header
    const tableTop = doc.y;
    doc.text('Item', 50, tableTop)
       .text('Qty', 250, tableTop)
       .text('Price', 300, tableTop)
       .text('Total', 400, tableTop);

    // Draw line under header
    doc.moveTo(50, tableTop + 15)
       .lineTo(500, tableTop + 15)
       .stroke();

    // Items
    let currentY = tableTop + 25;
    sale.items.forEach((item: any) => {
      doc.text(item.product.name, 50, currentY)
         .text(item.quantity.toString(), 250, currentY)
         .text(`$${item.unitPrice.toFixed(2)}`, 300, currentY)
         .text(`$${item.total.toFixed(2)}`, 400, currentY);
      currentY += 20;
    });

    // Totals
    doc.moveDown();
    const totalsY = currentY + 20;
    doc.text(`Subtotal: $${sale.subtotal.toFixed(2)}`, 300, totalsY)
       .text(`Tax: $${sale.tax.toFixed(2)}`, 300, totalsY + 15)
       .text(`Discount: $${sale.discount.toFixed(2)}`, 300, totalsY + 30)
       .text(`Total: $${sale.total.toFixed(2)}`, 300, totalsY + 45, { underline: true });

    // Payments
    doc.moveDown();
    doc.text('Payments:', 50, totalsY + 70);
    let paymentY = totalsY + 90;
    sale.payments.forEach((payment: any) => {
      doc.text(`${payment.method}: $${payment.amount.toFixed(2)}`, 50, paymentY);
      paymentY += 15;
    });

    // Generate QR code for digital receipt
    try {
      const qrData = `${process.env.FRONTEND_URL}/receipts/${receipt.id}`;
      const qrCodeDataURL = await QRCode.toDataURL(qrData);
      const qrBuffer = Buffer.from(qrCodeDataURL.split(',')[1], 'base64');
      
      doc.moveDown();
      doc.text('Scan for digital receipt:', 50, doc.y + 20);
      doc.image(qrBuffer, 50, doc.y + 10, { width: 100 });
    } catch (qrError) {
      console.error('QR code generation failed:', qrError);
    }

    // Footer
    doc.moveDown();
    doc.fontSize(10)
       .text('Thank you for your business!', { align: 'center' })
       .text('Please keep this receipt for your records.', { align: 'center' });

    doc.end();
  } catch (error) {
    console.error('PDF generation failed:', error);
    res.status(500).json({ error: 'Failed to generate PDF receipt' });
  }
}

// Generate HTML receipt
function generateHTMLReceipt(res: Response, sale: any, receipt: any): void {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Receipt ${receipt.receiptNumber}</title>
      <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .receipt-info { margin-bottom: 20px; }
        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .items-table th { background-color: #f2f2f2; }
        .totals { text-align: right; margin-bottom: 20px; }
        .footer { text-align: center; margin-top: 30px; font-size: 12px; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>RECEIPT</h1>
        <p>Shop Management System</p>
        <p>123 Business Street, City, State 12345</p>
        <p>Phone: (*************</p>
      </div>
      
      <div class="receipt-info">
        <p><strong>Receipt #:</strong> ${receipt.receiptNumber}</p>
        <p><strong>Sale #:</strong> ${sale.saleNumber}</p>
        <p><strong>Date:</strong> ${new Date(sale.createdAt).toLocaleString()}</p>
        <p><strong>Cashier:</strong> ${sale.user.firstName} ${sale.user.lastName}</p>
        ${sale.customer ? `<p><strong>Customer:</strong> ${sale.customer.firstName} ${sale.customer.lastName}</p>` : ''}
      </div>
      
      <table class="items-table">
        <thead>
          <tr>
            <th>Item</th>
            <th>Qty</th>
            <th>Price</th>
            <th>Total</th>
          </tr>
        </thead>
        <tbody>
          ${sale.items.map((item: any) => `
            <tr>
              <td>${item.product.name}</td>
              <td>${item.quantity}</td>
              <td>$${item.unitPrice.toFixed(2)}</td>
              <td>$${item.total.toFixed(2)}</td>
            </tr>
          `).join('')}
        </tbody>
      </table>
      
      <div class="totals">
        <p>Subtotal: $${sale.subtotal.toFixed(2)}</p>
        <p>Tax: $${sale.tax.toFixed(2)}</p>
        <p>Discount: $${sale.discount.toFixed(2)}</p>
        <p><strong>Total: $${sale.total.toFixed(2)}</strong></p>
      </div>
      
      <div class="payments">
        <h3>Payments:</h3>
        ${sale.payments.map((payment: any) => `
          <p>${payment.method}: $${payment.amount.toFixed(2)}</p>
        `).join('')}
      </div>
      
      <div class="footer">
        <p>Thank you for your business!</p>
        <p>Please keep this receipt for your records.</p>
      </div>
    </body>
    </html>
  `;

  res.setHeader('Content-Type', 'text/html');
  res.send(html);
}

export default router;
