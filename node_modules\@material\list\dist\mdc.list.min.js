!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/list",[],t):"object"==typeof exports?exports.list=t():(e.mdc=e.mdc||{},e.mdc.list=t())}(this,function(){return n={},i.m=r={0:function(e,t,r){"use strict";(function(e){}).call(this,r(20))},1:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapResourceUrl=t.isResourceUrl=t.createResourceUrl=t.TrustedResourceUrl=void 0,r(0);var i=r(4),o=r(9),s=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(e,t){this.privateDoNotAccessOrElseWrappedResourceUrl=e}var a=window.TrustedScriptURL;t.TrustedResourceUrl=null!=a?a:s,t.createResourceUrl=function(e){var t,r=e,n=null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScriptURL(r);return null!=n?n:new s(r,i.secretToken)},t.isResourceUrl=function(e){return e instanceof t.TrustedResourceUrl},t.unwrapResourceUrl=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isScriptURL(e))return e;if(e instanceof s)return e.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},10:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyle=t.isStyle=t.createStyle=t.SafeStyle=void 0,r(0);function o(){}var s=r(4);t.SafeStyle=o;var a,c=(i(l,a=o),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},l);function l(e,t){var r=a.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=e,r}t.createStyle=function(e){return new c(e,s.secretToken)},t.isStyle=function(e){return e instanceof c},t.unwrapStyle=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},11:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AttributePolicyAction=t.SanitizerTable=void 0;var n,i,o=(s.prototype.isAllowedElement=function(e){return"form"!==e.toLowerCase()&&(this.allowedElements.has(e)||this.elementPolicies.has(e))},s.prototype.getAttributePolicy=function(e,t){var r=this.elementPolicies.get(t);return(null==r?void 0:r.has(e))?r.get(e):this.allowedGlobalAttributes.has(e)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(e)||{policyAction:n.DROP}},s);function s(e,t,r,n){this.allowedElements=e,this.elementPolicies=t,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}t.SanitizerTable=o,(i=n=t.AttributePolicyAction||(t.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},12:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.isStyleSheet=t.createStyleSheet=t.SafeStyleSheet=void 0,r(0);function o(){}var s=r(4);t.SafeStyleSheet=o;var a,c=(i(l,a=o),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},l);function l(e,t){var r=a.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=e,r}t.createStyleSheet=function(e){return new c(e,s.secretToken)},t.isStyleSheet=function(e){return e instanceof c},t.unwrapStyleSheet=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},13:function(e,t,r){"use strict";var i=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},s=this&&this.__spreadArray||function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCComponent=void 0;var a=r(17),c=r(18),n=r(7);var l,u,d=(f.attachTo=function(e){return new f(e,new n.MDCFoundation({}))},f.prototype.initialize=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},f.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},f.prototype.initialSyncWithDOM=function(){},f.prototype.destroy=function(){this.foundation.destroy()},f.prototype.listen=function(e,t,r){this.root.addEventListener(e,t,r)},f.prototype.unlisten=function(e,t,r){this.root.removeEventListener(e,t,r)},f.prototype.emit=function(e,t,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(e,{bubbles:r,detail:t}):(n=document.createEvent("CustomEvent")).initCustomEvent(e,r,!1,t),this.root.dispatchEvent(n)},f.prototype.safeSetAttribute=function(e,t,r){if("tabindex"===t.toLowerCase())e.tabIndex=Number(r);else if(0===t.indexOf("data-")){var n=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})}(t.replace(/^data-/,""));e.dataset[n]=r}else c.safeElement.setPrefixedAttribute([a.safeAttrPrefix(l=l||i(["aria-"],["aria-"])),a.safeAttrPrefix(u=u||i(["role"],["role"]))],e,t,r)},f);function f(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=e,this.initialize.apply(this,s([],o(r))),this.foundation=void 0===t?this.getDefaultFoundation():t,this.foundation.init(),this.initialSyncWithDOM()}t.MDCComponent=d,t.default=d},14:function(e,t,r){"use strict";var p=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.HtmlSanitizerImpl=void 0,r(0);var n=r(2),i=r(4),h=r(3),c=r(23),E=r(24),o=r(16),y=r(11),s=(a.prototype.sanitizeAssertUnchanged=function(e){this.changes=[];var t=this.sanitize(e);if(0===this.changes.length)return t;throw new Error("")},a.prototype.sanitize=function(e){var t=document.createElement("span");t.appendChild(this.sanitizeToFragment(e));var r=(new XMLSerializer).serializeToString(t);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},a.prototype.sanitizeToFragment=function(e){for(var t=this,r=(0,c.createInertFragment)(e),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(e){return t.nodeFilter(e)},!1),i=n.nextNode(),o=document.createDocumentFragment(),s=o;null!==i;){var a=void 0;if((0,E.isText)(i))a=this.sanitizeTextNode(i);else{if(!(0,E.isElement)(i))throw new Error("Node is not of type text or element");a=this.sanitizeElementNode(i)}if(s.appendChild(a),i=n.firstChild())s=a;else for(;!(i=n.nextSibling())&&(i=n.parentNode());)s=s.parentNode}return o},a.prototype.sanitizeTextNode=function(e){return document.createTextNode(e.data)},a.prototype.sanitizeElementNode=function(e){var t,r,n=(0,E.getNodeName)(e),i=document.createElement(n),o=e.attributes;try{for(var s=p(o),a=s.next();!a.done;a=s.next()){var c=a.value,l=c.name,u=c.value,d=this.sanitizerTable.getAttributePolicy(l,n);if(this.satisfiesAllConditions(d.conditions,o))switch(d.policyAction){case y.AttributePolicyAction.KEEP:i.setAttribute(l,u);break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var f=(0,h.restrictivelySanitizeUrl)(u);f!==u&&this.recordChange("Url in attribute ".concat(l,' was modified during sanitization. Original url:"').concat(u,'" was sanitized to: "').concat(f,'"')),i.setAttribute(l,f);break;case y.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(l,u.toLowerCase());break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(l,u);break;case y.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(l," was dropped"));break;default:S(d.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(l,"."))}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=s.return)&&r.call(s)}finally{if(t)throw t.error}}return i},a.prototype.nodeFilter=function(e){if((0,E.isText)(e))return NodeFilter.FILTER_ACCEPT;if(!(0,E.isElement)(e))return NodeFilter.FILTER_REJECT;var t=(0,E.getNodeName)(e);return null===t?(this.recordChange("Node name was null for node: ".concat(e)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(t)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(t," was dropped")),NodeFilter.FILTER_REJECT)},a.prototype.recordChange=function(e){0===this.changes.length&&this.changes.push("")},a.prototype.satisfiesAllConditions=function(e,t){var r,n,i;if(!e)return!0;try{for(var o=p(e),s=o.next();!s.done;s=o.next()){var a=d(s.value,2),c=a[0],l=a[1],u=null===(i=t.getNamedItem(c))||void 0===i?void 0:i.value;if(u&&!l.has(u))return!1}}catch(e){r={error:e}}finally{try{s&&!s.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0},a);function a(e,t){this.sanitizerTable=e,this.changes=[],(0,i.ensureTokenIsValid)(t)}t.HtmlSanitizerImpl=s;var l=function(){return new s(o.defaultSanitizerTable,i.secretToken)}();function S(e,t){throw void 0===t&&(t="unexpected value ".concat(e,"!")),new Error(t)}t.sanitizeHtml=function(e){return l.sanitize(e)},t.sanitizeHtmlAssertUnchanged=function(e){return l.sanitizeAssertUnchanged(e)},t.sanitizeHtmlToFragment=function(e){return l.sanitizeToFragment(e)}},15:function(e,t,r){"use strict";var i=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},o=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.setPrefixedAttribute=t.buildPrefixedAttributeSetter=t.insertAdjacentHtml=t.setCssText=t.setOuterHtml=t.setInnerHtml=void 0;var s=r(8),a=r(2),n=r(10);function c(e,t,r,n){if(0===e.length)throw new Error("No prefixes are provided");var i=e.map(function(e){return(0,s.unwrapAttributePrefix)(e)}),o=r.toLowerCase();if(i.every(function(e){return 0!==o.indexOf(e)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));t.setAttribute(r,n)}function l(e){if("script"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}t.setInnerHtml=function(e,t){!function(e){return void 0!==e.tagName}(e)||l(e),e.innerHTML=(0,a.unwrapHtml)(t)},t.setOuterHtml=function(e,t){var r=e.parentElement;null!==r&&l(r),e.outerHTML=(0,a.unwrapHtml)(t)},t.setCssText=function(e,t){e.style.cssText=(0,n.unwrapStyle)(t)},t.insertAdjacentHtml=function(e,t,r){var n="beforebegin"===t||"afterend"===t?e.parentElement:e;null!==n&&l(n),e.insertAdjacentHTML(t,(0,a.unwrapHtml)(r))},t.buildPrefixedAttributeSetter=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=o([e],i(t),!1);return function(e,t,r){c(n,e,t,r)}},t.setPrefixedAttribute=c},16:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSanitizerTable=void 0;var n=r(11);t.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},17:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.SafeStyleSheet=t.isStyleSheet=t.unwrapStyle=t.SafeStyle=t.isStyle=t.unwrapScript=t.SafeScript=t.isScript=t.EMPTY_SCRIPT=t.unwrapResourceUrl=t.TrustedResourceUrl=t.isResourceUrl=t.unwrapHtml=t.SafeHtml=t.isHtml=t.EMPTY_HTML=t.unwrapAttributePrefix=t.SafeAttributePrefix=t.safeStyleSheet=t.concatStyleSheets=t.safeStyle=t.concatStyles=t.scriptFromJson=t.safeScriptWithArgs=t.safeScript=t.concatScripts=t.trustedResourceUrl=t.replaceFragment=t.blobUrlFromScript=t.appendParams=t.HtmlSanitizerBuilder=t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.htmlEscape=t.createScriptSrc=t.createScript=t.concatHtmls=t.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(t,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var i=r(22);Object.defineProperty(t,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(t,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(t,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(t,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var o=r(14);Object.defineProperty(t,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(t,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(t,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var s=r(25);Object.defineProperty(t,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return s.HtmlSanitizerBuilder}});var a=r(26);Object.defineProperty(t,"appendParams",{enumerable:!0,get:function(){return a.appendParams}}),Object.defineProperty(t,"blobUrlFromScript",{enumerable:!0,get:function(){return a.blobUrlFromScript}}),Object.defineProperty(t,"replaceFragment",{enumerable:!0,get:function(){return a.replaceFragment}}),Object.defineProperty(t,"trustedResourceUrl",{enumerable:!0,get:function(){return a.trustedResourceUrl}});var c=r(27);Object.defineProperty(t,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(t,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(t,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(t,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var l=r(28);Object.defineProperty(t,"concatStyles",{enumerable:!0,get:function(){return l.concatStyles}}),Object.defineProperty(t,"safeStyle",{enumerable:!0,get:function(){return l.safeStyle}});var u=r(29);Object.defineProperty(t,"concatStyleSheets",{enumerable:!0,get:function(){return u.concatStyleSheets}}),Object.defineProperty(t,"safeStyleSheet",{enumerable:!0,get:function(){return u.safeStyleSheet}});var d=r(8);Object.defineProperty(t,"SafeAttributePrefix",{enumerable:!0,get:function(){return d.SafeAttributePrefix}}),Object.defineProperty(t,"unwrapAttributePrefix",{enumerable:!0,get:function(){return d.unwrapAttributePrefix}});var f=r(2);Object.defineProperty(t,"EMPTY_HTML",{enumerable:!0,get:function(){return f.EMPTY_HTML}}),Object.defineProperty(t,"isHtml",{enumerable:!0,get:function(){return f.isHtml}}),Object.defineProperty(t,"SafeHtml",{enumerable:!0,get:function(){return f.SafeHtml}}),Object.defineProperty(t,"unwrapHtml",{enumerable:!0,get:function(){return f.unwrapHtml}});var p=r(1);Object.defineProperty(t,"isResourceUrl",{enumerable:!0,get:function(){return p.isResourceUrl}}),Object.defineProperty(t,"TrustedResourceUrl",{enumerable:!0,get:function(){return p.TrustedResourceUrl}}),Object.defineProperty(t,"unwrapResourceUrl",{enumerable:!0,get:function(){return p.unwrapResourceUrl}});var h=r(5);Object.defineProperty(t,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return h.EMPTY_SCRIPT}}),Object.defineProperty(t,"isScript",{enumerable:!0,get:function(){return h.isScript}}),Object.defineProperty(t,"SafeScript",{enumerable:!0,get:function(){return h.SafeScript}}),Object.defineProperty(t,"unwrapScript",{enumerable:!0,get:function(){return h.unwrapScript}});var E=r(10);Object.defineProperty(t,"isStyle",{enumerable:!0,get:function(){return E.isStyle}}),Object.defineProperty(t,"SafeStyle",{enumerable:!0,get:function(){return E.SafeStyle}}),Object.defineProperty(t,"unwrapStyle",{enumerable:!0,get:function(){return E.unwrapStyle}});var y=r(12);Object.defineProperty(t,"isStyleSheet",{enumerable:!0,get:function(){return y.isStyleSheet}}),Object.defineProperty(t,"SafeStyleSheet",{enumerable:!0,get:function(){return y.SafeStyleSheet}}),Object.defineProperty(t,"unwrapStyleSheet",{enumerable:!0,get:function(){return y.unwrapStyleSheet}})},18:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&("get"in i?t.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.safeWorker=t.safeWindow=t.safeServiceWorkerContainer=t.safeRange=t.safeLocation=t.safeGlobal=t.safeDomParser=t.safeDocument=t.safeStyleEl=t.safeScriptEl=t.safeObjectEl=t.safeLinkEl=t.safeInputEl=t.safeIframeEl=t.safeFormEl=t.safeEmbedEl=t.safeElement=t.safeButtonEl=t.safeAreaEl=t.safeAnchorEl=void 0,t.safeAnchorEl=o(r(30)),t.safeAreaEl=o(r(31)),t.safeButtonEl=o(r(32)),t.safeElement=o(r(15)),t.safeEmbedEl=o(r(33)),t.safeFormEl=o(r(34)),t.safeIframeEl=o(r(35)),t.safeInputEl=o(r(36)),t.safeLinkEl=o(r(37)),t.safeObjectEl=o(r(38)),t.safeScriptEl=o(r(39)),t.safeStyleEl=o(r(40)),t.safeDocument=o(r(41)),t.safeDomParser=o(r(42)),t.safeGlobal=o(r(43)),t.safeLocation=o(r(44)),t.safeRange=o(r(45)),t.safeServiceWorkerContainer=o(r(46)),t.safeWindow=o(r(47)),t.safeWorker=o(r(48))},19:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);t.safeAttrPrefix=function(e){var t=e[0].toLowerCase();return(0,n.createAttributePrefix)(t)}},2:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapHtml=t.isHtml=t.EMPTY_HTML=t.createHtml=t.SafeHtml=void 0,r(0);var n=r(4),i=r(9),o=(s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},s);function s(e,t){this.privateDoNotAccessOrElseWrappedHtml=e}function a(e,t){return null!=t?t:new o(e,n.secretToken)}var c=window.TrustedHTML;t.SafeHtml=null!=c?c:o,t.createHtml=function(e){var t,r=e;return a(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createHTML(r))},t.EMPTY_HTML=function(){var e;return a("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyHTML)}(),t.isHtml=function(e){return e instanceof t.SafeHtml},t.unwrapHtml=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isHTML(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},20:function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function a(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:s}catch(e){n=s}}();var c,l=[],u=!1,d=-1;function f(){u&&c&&(u=!1,c.length?l=c.concat(l):d=-1,l.length&&p())}function p(){if(!u){var e=a(f);u=!0;for(var t=l.length;t;){for(c=l,l=[];++d<t;)c&&c[d].run();d=-1,t=l.length}c=null,u=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===s||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function E(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new h(e,t)),1!==l.length||u||a(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=E,i.addListener=E,i.once=E,i.off=E,i.removeListener=E,i.removeAllListeners=E,i.emit=E,i.prependListener=E,i.prependOnceListener=E,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},21:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SECURITY_SENSITIVE_ATTRIBUTES=void 0,t.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},22:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatHtmls=t.createScriptSrc=t.createScript=t.htmlEscape=void 0;var o=r(2),s=r(1),i=r(5);function a(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}t.htmlEscape=function(e,t){void 0===t&&(t={});var r=a(e);return t.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),t.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),t.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(r)},t.createScript=function(e,t){void 0===t&&(t={});var r=(0,i.unwrapScript)(e).toString(),n="<script";return t.id&&(n+=' id="'.concat(a(t.id),'"')),t.nonce&&(n+=' nonce="'.concat(a(t.nonce),'"')),t.type&&(n+=' type="'.concat(a(t.type),'"')),n+=">".concat(r,"<\/script>"),(0,o.createHtml)(n)},t.createScriptSrc=function(e,t,r){var n=(0,s.unwrapResourceUrl)(e).toString(),i='<script src="'.concat(a(n),'"');return t&&(i+=" async"),r&&(i+=' nonce="'.concat(a(r),'"')),i+="><\/script>",(0,o.createHtml)(i)},t.concatHtmls=function(e){return(0,o.createHtml)(e.map(o.unwrapHtml).join(""))}},23:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInertFragment=void 0;var n=r(15),i=r(2);t.createInertFragment=function(e){var t=document.createElement("template"),r=(0,i.createHtml)(e);return(0,n.setInnerHtml)(t,r),t.content}},239:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(240),t),i(r(69),t),i(r(56),t),i(r(61),t),i(r(241),t)},24:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElement=t.isText=t.getNodeName=void 0,t.getNodeName=function(e){var t=e.nodeName;return"string"==typeof t?t:"FORM"},t.isText=function(e){return e.nodeType===Node.TEXT_NODE},t.isElement=function(e){var t=e.nodeType;return t===Node.ELEMENT_NODE||"number"!=typeof t}},240:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},241:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},25:function(e,t,r){"use strict";var _=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},T=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s};Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlSanitizerBuilder=void 0;var n=r(4),i=r(14),o=r(16),x=r(11),s=(a.prototype.onlyAllowElements=function(e){var t,r,n=new Set,i=new Map;try{for(var o=_(e),s=o.next();!s.done;s=o.next()){var a=s.value;if(a=a.toUpperCase(),!this.sanitizerTable.isAllowedElement(a))throw new Error("Element: ".concat(a,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(a);void 0!==c?i.set(a,c):n.add(a)}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return this.sanitizerTable=new x.SanitizerTable(n,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},a.prototype.onlyAllowAttributes=function(e){var t,r,n,i,o,s,a=new Set,c=new Map,l=new Map;try{for(var u=_(e),d=u.next();!d.done;d=u.next()){var f=d.value;this.sanitizerTable.allowedGlobalAttributes.has(f)&&a.add(f),this.sanitizerTable.globalAttributePolicies.has(f)&&c.set(f,this.sanitizerTable.globalAttributePolicies.get(f))}}catch(e){t={error:e}}finally{try{d&&!d.done&&(r=u.return)&&r.call(u)}finally{if(t)throw t.error}}try{for(var p=_(this.sanitizerTable.elementPolicies.entries()),h=p.next();!h.done;h=p.next()){var E=T(h.value,2),y=E[0],S=E[1],m=new Map;try{for(var b=(o=void 0,_(S.entries())),I=b.next();!I.done;I=b.next()){var v=T(I.value,2),A=(f=v[0],v[1]);e.has(f)&&m.set(f,A)}}catch(e){o={error:e}}finally{try{I&&!I.done&&(s=b.return)&&s.call(b)}finally{if(o)throw o.error}}l.set(y,m)}}catch(e){n={error:e}}finally{try{h&&!h.done&&(i=p.return)&&i.call(p)}finally{if(n)throw n.error}}return this.sanitizerTable=new x.SanitizerTable(this.sanitizerTable.allowedElements,l,a,c),this},a.prototype.allowDataAttributes=function(e){var t,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=_(e),o=i.next();!o.done;o=i.next()){var s=o.value;if(0!==s.indexOf("data-"))throw new Error("data attribute: ".concat(s,' does not begin with the prefix "data-"'));n.add(s)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return this.sanitizerTable=new x.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},a.prototype.allowStyleAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("style",{policyAction:x.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new x.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},a.prototype.allowClassAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("class",{policyAction:x.AttributePolicyAction.KEEP}),this.sanitizerTable=new x.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},a.prototype.allowIdAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("id",{policyAction:x.AttributePolicyAction.KEEP}),this.sanitizerTable=new x.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},a.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},a);function a(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}t.HtmlSanitizerBuilder=s},26:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.blobUrlFromScript=t.replaceFragment=t.appendParams=t.trustedResourceUrl=void 0,r(0);var a=r(1),n=r(5);r(6);t.trustedResourceUrl=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(0===t.length)return(0,a.createResourceUrl)(e[0]);e[0].toLowerCase();for(var n=[e[0]],i=0;i<t.length;i++)n.push(encodeURIComponent(t[i])),n.push(e[i+1]);return(0,a.createResourceUrl)(n.join(""))},t.appendParams=function(e,t){var o=(0,a.unwrapResourceUrl)(e).toString();if(/#/.test(o)){throw new Error("")}var s=/\?/.test(o)?"&":"?";return t.forEach(function(e,t){for(var r=e instanceof Array?e:[e],n=0;n<r.length;n++){var i=r[n];null!=i&&(o+=s+encodeURIComponent(t)+"="+encodeURIComponent(String(i)),s="&")}}),(0,a.createResourceUrl)(o)};var i=/[^#]*/;t.replaceFragment=function(e,t){var r=(0,a.unwrapResourceUrl)(e).toString();return(0,a.createResourceUrl)(i.exec(r)[0]+"#"+t)},t.blobUrlFromScript=function(e){var t=(0,n.unwrapScript)(e).toString(),r=new Blob([t],{type:"text/javascript"});return(0,a.createResourceUrl)(URL.createObjectURL(r))}},27:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeScriptWithArgs=t.scriptFromJson=t.concatScripts=t.safeScript=void 0,r(0);var i=r(5);r(6);function o(e){return(0,i.createScript)(JSON.stringify(e).replace(/</g,"\\x3c"))}t.safeScript=function(e){return(0,i.createScript)(e[0])},t.concatScripts=function(e){return(0,i.createScript)(e.map(i.unwrapScript).join(""))},t.scriptFromJson=o,t.safeScriptWithArgs=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.map(function(e){return o(e).toString()});return(0,i.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},28:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyles=t.safeStyle=void 0,r(0);r(6);var n=r(10);t.safeStyle=function(e){var t=e[0];return(0,n.createStyle)(t)},t.concatStyles=function(e){return(0,n.createStyle)(e.map(n.unwrapStyle).join(""))}},29:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyleSheets=t.safeStyleSheet=void 0,r(0);r(6);var n=r(12);t.safeStyleSheet=function(e){var t=e[0];return(0,n.createStyleSheet)(t)},t.concatStyleSheets=function(e){return(0,n.createStyleSheet)(e.map(n.unwrapStyleSheet).join(""))}},3:function(e,t,r){"use strict";function n(e){var t;try{t=new URL(e)}catch(e){return"https:"}return t.protocol}Object.defineProperty(t,"__esModule",{value:!0}),t.restrictivelySanitizeUrl=t.unwrapUrlOrSanitize=t.sanitizeJavascriptUrl=void 0,r(0);var i=["data:","http:","https:","mailto:","ftp:"];function o(e){if("javascript:"!==n(e))return e}t.sanitizeJavascriptUrl=o,t.unwrapUrlOrSanitize=function(e){return o(e)},t.restrictivelySanitizeUrl=function(e){var t=n(e);return void 0!==t&&-1!==i.indexOf(t.toLowerCase())?e:"about:invalid#zClosurez"}},30:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},31:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},32:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},33:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=void 0;var n=r(1);t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t)}},34:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setAction=void 0;var n=r(3);t.setAction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.action=r)}},35:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrcdoc=t.setSrc=void 0;var n=r(2),i=r(1);t.setSrc=function(e,t){e.src=(0,i.unwrapResourceUrl)(t).toString()},t.setSrcdoc=function(e,t){e.srcdoc=(0,n.unwrapHtml)(t)}},36:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},37:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHrefAndRel=void 0;var i=r(3),o=r(1),s=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];t.setHrefAndRel=function(e,t,r){if(t instanceof o.TrustedResourceUrl)e.href=(0,o.unwrapResourceUrl)(t).toString();else{if(-1===s.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,i.unwrapUrlOrSanitize)(t);if(void 0===n)return;e.href=n}e.rel=r}},38:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setData=void 0;var n=r(1);t.setData=function(e,t){e.data=(0,n.unwrapResourceUrl)(t)}},39:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=t.setTextContent=void 0;var n=r(1),i=r(5);function o(e){var t=function(e){var t,r=e.document,n=null===(t=r.querySelector)||void 0===t?void 0:t.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(e.ownerDocument&&e.ownerDocument.defaultView||window);t&&e.setAttribute("nonce",t)}t.setTextContent=function(e,t){e.textContent=(0,i.unwrapScript)(t),o(e)},t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t),o(e)}},4:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureTokenIsValid=t.secretToken=void 0,t.secretToken={},t.ensureTokenIsValid=function(e){if(e!==t.secretToken)throw new Error("Bad secret")}},40:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setTextContent=void 0;var n=r(12);t.setTextContent=function(e,t){e.textContent=(0,n.unwrapStyleSheet)(t)}},41:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execCommandInsertHtml=t.execCommand=t.write=void 0;var o=r(2);t.write=function(e,t){e.write((0,o.unwrapHtml)(t))},t.execCommand=function(e,t,r){var n=String(t),i=r;return"inserthtml"===n.toLowerCase()&&(i=(0,o.unwrapHtml)(r)),e.execCommand(n,!1,i)},t.execCommandInsertHtml=function(e,t){return e.execCommand("insertHTML",!1,(0,o.unwrapHtml)(t))}},42:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseFromString=t.parseHtml=void 0;var n=r(2);function i(e,t,r){return e.parseFromString((0,n.unwrapHtml)(t),r)}t.parseHtml=function(e,t){return i(e,t,"text/html")},t.parseFromString=i},43:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.globalEval=void 0;var i=r(5);t.globalEval=function(e,t){var r=(0,i.unwrapScript)(t),n=e.eval(r);return n===r&&(n=e.eval(r.toString())),n}},44:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assign=t.replace=t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)},t.replace=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.replace(r)},t.assign=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.assign(r)}},45:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createContextualFragment=void 0;var n=r(2);t.createContextualFragment=function(e,t){return e.createContextualFragment((0,n.unwrapHtml)(t))}},46:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.register=void 0;var n=r(1);t.register=function(e,t,r){return e.register((0,n.unwrapResourceUrl)(t),r)}},47:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.open=void 0;var o=r(3);t.open=function(e,t,r,n){var i=(0,o.unwrapUrlOrSanitize)(t);return void 0!==i?e.open(i,r,n):null}},48:function(e,t,r){"use strict";var n=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.importScripts=t.createShared=t.create=void 0;var o=r(1);t.create=function(e,t){return new Worker((0,o.unwrapResourceUrl)(e),t)},t.createShared=function(e,t){return new SharedWorker((0,o.unwrapResourceUrl)(e),t)},t.importScripts=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];e.importScripts.apply(e,i([],n(t.map(function(e){return(0,o.unwrapResourceUrl)(e)})),!1))}},49:function(e,t,r){"use strict";function n(e,t){return(e.matches||e.webkitMatchesSelector||e.msMatchesSelector).call(e,t)}Object.defineProperty(t,"__esModule",{value:!0}),t.estimateScrollWidth=t.matches=t.closest=void 0,t.closest=function(e,t){if(e.closest)return e.closest(t);for(var r=e;r;){if(n(r,t))return r;r=r.parentElement}return null},t.matches=n,t.estimateScrollWidth=function(e){var t=e;if(null!==t.offsetParent)return t.scrollWidth;var r=t.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},5:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapScript=t.isScript=t.EMPTY_SCRIPT=t.createScript=t.SafeScript=void 0,r(0);var n=r(4),i=r(9),o=(s.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},s);function s(e,t){this.privateDoNotAccessOrElseWrappedScript=e}function a(e,t){return null!=t?t:new o(e,n.secretToken)}var c=window.TrustedScript;t.SafeScript=null!=c?c:o,t.createScript=function(e){var t,r=e;return a(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScript(r))},t.EMPTY_SCRIPT=function(){var e;return a("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyScript)}(),t.isScript=function(e){return e instanceof t.SafeScript},t.unwrapScript=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isScript(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},55:function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.isNavigationEvent=n.normalizeKey=n.KEY=void 0,n.KEY={UNKNOWN:"Unknown",BACKSPACE:"Backspace",ENTER:"Enter",SPACEBAR:"Spacebar",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown",END:"End",HOME:"Home",ARROW_LEFT:"ArrowLeft",ARROW_UP:"ArrowUp",ARROW_RIGHT:"ArrowRight",ARROW_DOWN:"ArrowDown",DELETE:"Delete",ESCAPE:"Escape",TAB:"Tab"};var i=new Set;i.add(n.KEY.BACKSPACE),i.add(n.KEY.ENTER),i.add(n.KEY.SPACEBAR),i.add(n.KEY.PAGE_UP),i.add(n.KEY.PAGE_DOWN),i.add(n.KEY.END),i.add(n.KEY.HOME),i.add(n.KEY.ARROW_LEFT),i.add(n.KEY.ARROW_UP),i.add(n.KEY.ARROW_RIGHT),i.add(n.KEY.ARROW_DOWN),i.add(n.KEY.DELETE),i.add(n.KEY.ESCAPE),i.add(n.KEY.TAB);var r=8,o=13,s=32,a=33,c=34,l=35,u=36,d=37,f=38,p=39,h=40,E=46,y=27,S=9,m=new Map;m.set(r,n.KEY.BACKSPACE),m.set(o,n.KEY.ENTER),m.set(s,n.KEY.SPACEBAR),m.set(a,n.KEY.PAGE_UP),m.set(c,n.KEY.PAGE_DOWN),m.set(l,n.KEY.END),m.set(u,n.KEY.HOME),m.set(d,n.KEY.ARROW_LEFT),m.set(f,n.KEY.ARROW_UP),m.set(p,n.KEY.ARROW_RIGHT),m.set(h,n.KEY.ARROW_DOWN),m.set(E,n.KEY.DELETE),m.set(y,n.KEY.ESCAPE),m.set(S,n.KEY.TAB);var b=new Set;function I(e){var t=e.key;if(i.has(t))return t;var r=m.get(e.keyCode);return r||n.KEY.UNKNOWN}b.add(n.KEY.PAGE_UP),b.add(n.KEY.PAGE_DOWN),b.add(n.KEY.END),b.add(n.KEY.HOME),b.add(n.KEY.ARROW_LEFT),b.add(n.KEY.ARROW_UP),b.add(n.KEY.ARROW_RIGHT),b.add(n.KEY.ARROW_DOWN),n.normalizeKey=I,n.isNavigationEvent=function(e){return b.has(I(e))}},56:function(e,t,r){"use strict";var n,i;Object.defineProperty(t,"__esModule",{value:!0}),t.evolutionClassNameMap=t.evolutionAttribute=t.deprecatedClassNameMap=t.numbers=t.cssClasses=t.strings=void 0;var o={LIST_ITEM_ACTIVATED_CLASS:"mdc-list-item--activated",LIST_ITEM_CLASS:"mdc-list-item",LIST_ITEM_DISABLED_CLASS:"mdc-list-item--disabled",LIST_ITEM_SELECTED_CLASS:"mdc-list-item--selected",LIST_ITEM_TEXT_CLASS:"mdc-list-item__text",LIST_ITEM_PRIMARY_TEXT_CLASS:"mdc-list-item__primary-text",ROOT:"mdc-list"},s=((n={})[""+(t.cssClasses=o).LIST_ITEM_ACTIVATED_CLASS]="mdc-list-item--activated",n[""+o.LIST_ITEM_CLASS]="mdc-list-item",n[""+o.LIST_ITEM_DISABLED_CLASS]="mdc-list-item--disabled",n[""+o.LIST_ITEM_SELECTED_CLASS]="mdc-list-item--selected",n[""+o.LIST_ITEM_PRIMARY_TEXT_CLASS]="mdc-list-item__primary-text",n[""+o.ROOT]="mdc-list",n);t.evolutionClassNameMap=s;var a=((i={})[""+o.LIST_ITEM_ACTIVATED_CLASS]="mdc-deprecated-list-item--activated",i[""+o.LIST_ITEM_CLASS]="mdc-deprecated-list-item",i[""+o.LIST_ITEM_DISABLED_CLASS]="mdc-deprecated-list-item--disabled",i[""+o.LIST_ITEM_SELECTED_CLASS]="mdc-deprecated-list-item--selected",i[""+o.LIST_ITEM_TEXT_CLASS]="mdc-deprecated-list-item__text",i[""+o.LIST_ITEM_PRIMARY_TEXT_CLASS]="mdc-deprecated-list-item__primary-text",i[""+o.ROOT]="mdc-deprecated-list",i);t.deprecatedClassNameMap=a;var c={ACTION_EVENT:"MDCList:action",SELECTION_CHANGE_EVENT:"MDCList:selectionChange",ARIA_CHECKED:"aria-checked",ARIA_CHECKED_CHECKBOX_SELECTOR:'[role="checkbox"][aria-checked="true"]',ARIA_CHECKED_RADIO_SELECTOR:'[role="radio"][aria-checked="true"]',ARIA_CURRENT:"aria-current",ARIA_DISABLED:"aria-disabled",ARIA_ORIENTATION:"aria-orientation",ARIA_ORIENTATION_HORIZONTAL:"horizontal",ARIA_ROLE_CHECKBOX_SELECTOR:'[role="checkbox"]',ARIA_SELECTED:"aria-selected",ARIA_INTERACTIVE_ROLES_SELECTOR:'[role="listbox"], [role="menu"]',ARIA_MULTI_SELECTABLE_SELECTOR:'[aria-multiselectable="true"]',CHECKBOX_RADIO_SELECTOR:'input[type="checkbox"], input[type="radio"]',CHECKBOX_SELECTOR:'input[type="checkbox"]',CHILD_ELEMENTS_TO_TOGGLE_TABINDEX:"\n    ."+o.LIST_ITEM_CLASS+" button:not(:disabled),\n    ."+o.LIST_ITEM_CLASS+" a,\n    ."+a[o.LIST_ITEM_CLASS]+" button:not(:disabled),\n    ."+a[o.LIST_ITEM_CLASS]+" a\n  ",DEPRECATED_SELECTOR:".mdc-deprecated-list",FOCUSABLE_CHILD_ELEMENTS:"\n    ."+o.LIST_ITEM_CLASS+" button:not(:disabled),\n    ."+o.LIST_ITEM_CLASS+" a,\n    ."+o.LIST_ITEM_CLASS+' input[type="radio"]:not(:disabled),\n    .'+o.LIST_ITEM_CLASS+' input[type="checkbox"]:not(:disabled),\n    .'+a[o.LIST_ITEM_CLASS]+" button:not(:disabled),\n    ."+a[o.LIST_ITEM_CLASS]+" a,\n    ."+a[o.LIST_ITEM_CLASS]+' input[type="radio"]:not(:disabled),\n    .'+a[o.LIST_ITEM_CLASS]+' input[type="checkbox"]:not(:disabled)\n  ',RADIO_SELECTOR:'input[type="radio"]',SELECTED_ITEM_SELECTOR:'[aria-selected="true"], [aria-current="true"]'};t.strings=c;t.numbers={UNSET_INDEX:-1,TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS:300};t.evolutionAttribute="evolution"},6:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertIsTemplateObject=void 0,t.assertIsTemplateObject=function(e,t,r){if(!Array.isArray(e)||!Array.isArray(e.raw)||!t&&1!==e.length)throw new TypeError(r)}},60:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.preventDefaultEvent=void 0;var n=["input","button","textarea","select"];t.preventDefaultEvent=function(e){var t=e.target;if(t){var r=(""+t.tagName).toLowerCase();-1===n.indexOf(r)&&e.preventDefault()}}},61:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},s=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),c=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&s(t,e,r);return a(t,e),t},d=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),s=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)s.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return s},f=this&&this.__spreadArray||function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e},l=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCListFoundation=void 0;var u=r(7),v=r(55),A=r(56),_=r(60),T=c(r(70));var p=["Alt","Control","Meta","Shift"];function x(t){var r=new Set(t?p.filter(function(e){return t.getModifierState(e)}):[]);return function(e){return e.every(function(e){return r.has(e)})&&e.length===r.size}}var h,E=(h=u.MDCFoundation,i(y,h),Object.defineProperty(y,"strings",{get:function(){return A.strings},enumerable:!1,configurable:!0}),Object.defineProperty(y,"cssClasses",{get:function(){return A.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(y,"numbers",{get:function(){return A.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(y,"defaultAdapter",{get:function(){return{addClassForElementIndex:function(){},focusItemAtIndex:function(){},getAttributeForElementIndex:function(){return null},getFocusedElementIndex:function(){return 0},getListItemCount:function(){return 0},hasCheckboxAtIndex:function(){return!1},hasRadioAtIndex:function(){return!1},isCheckboxCheckedAtIndex:function(){return!1},isFocusInsideList:function(){return!1},isRootFocused:function(){return!1},listItemAtIndexHasClass:function(){return!1},notifyAction:function(){},notifySelectionChange:function(){},removeClassForElementIndex:function(){},setAttributeForElementIndex:function(){},setCheckedCheckboxOrRadioAtIndex:function(){},setTabIndexForListItemChildren:function(){},getPrimaryTextAtIndex:function(){return""}}},enumerable:!1,configurable:!0}),y.prototype.layout=function(){0!==this.adapter.getListItemCount()&&(this.adapter.hasCheckboxAtIndex(0)?(this.isCheckboxList=!0,this.selectedIndex=[]):this.adapter.hasRadioAtIndex(0)?this.isRadioList=!0:this.maybeInitializeSingleSelection(),this.hasTypeahead&&(this.sortedIndexByFirstChar=this.typeaheadInitSortedIndex()))},y.prototype.getFocusedItemIndex=function(){return this.focusedItemIndex},y.prototype.setWrapFocus=function(e){this.wrapFocus=e},y.prototype.setVerticalOrientation=function(e){this.isVertical=e},y.prototype.setSingleSelection=function(e){(this.isSingleSelectionList=e)&&(this.maybeInitializeSingleSelection(),this.selectedIndex=this.getSelectedIndexFromDOM())},y.prototype.setDisabledItemsFocusable=function(e){this.areDisabledItemsFocusable=e},y.prototype.maybeInitializeSingleSelection=function(){var e=this.getSelectedIndexFromDOM();e!==A.numbers.UNSET_INDEX&&(this.adapter.listItemAtIndexHasClass(e,A.cssClasses.LIST_ITEM_ACTIVATED_CLASS)&&this.setUseActivatedClass(!0),this.isSingleSelectionList=!0,this.selectedIndex=e)},y.prototype.getSelectedIndexFromDOM=function(){for(var e=A.numbers.UNSET_INDEX,t=this.adapter.getListItemCount(),r=0;r<t;r++){var n=this.adapter.listItemAtIndexHasClass(r,A.cssClasses.LIST_ITEM_SELECTED_CLASS),i=this.adapter.listItemAtIndexHasClass(r,A.cssClasses.LIST_ITEM_ACTIVATED_CLASS);if(n||i){e=r;break}}return e},y.prototype.setHasTypeahead=function(e){(this.hasTypeahead=e)&&(this.sortedIndexByFirstChar=this.typeaheadInitSortedIndex())},y.prototype.isTypeaheadInProgress=function(){return this.hasTypeahead&&T.isTypingInProgress(this.typeaheadState)},y.prototype.setUseActivatedClass=function(e){this.useActivatedClass=e},y.prototype.setUseSelectedAttribute=function(e){this.useSelectedAttr=e},y.prototype.getSelectedIndex=function(){return this.selectedIndex},y.prototype.setSelectedIndex=function(e,t){void 0===t&&(t={}),this.isIndexValid(e)&&(this.isCheckboxList?this.setCheckboxAtIndex(e,t):this.isRadioList?this.setRadioAtIndex(e,t):this.setSingleSelectionAtIndex(e,t))},y.prototype.handleFocusIn=function(e){0<=e&&(this.focusedItemIndex=e,this.adapter.setAttributeForElementIndex(e,"tabindex","0"),this.adapter.setTabIndexForListItemChildren(e,"0"))},y.prototype.handleFocusOut=function(e){var t=this;0<=e&&(this.adapter.setAttributeForElementIndex(e,"tabindex","-1"),this.adapter.setTabIndexForListItemChildren(e,"-1")),setTimeout(function(){t.adapter.isFocusInsideList()||t.setTabindexToFirstSelectedOrFocusedItem()},0)},y.prototype.isIndexDisabled=function(e){return this.adapter.listItemAtIndexHasClass(e,A.cssClasses.LIST_ITEM_DISABLED_CLASS)},y.prototype.handleKeydown=function(e,t,r){var n,i=this,o="ArrowLeft"===v.normalizeKey(e),s="ArrowUp"===v.normalizeKey(e),a="ArrowRight"===v.normalizeKey(e),c="ArrowDown"===v.normalizeKey(e),l="Home"===v.normalizeKey(e),u="End"===v.normalizeKey(e),d="Enter"===v.normalizeKey(e),f="Spacebar"===v.normalizeKey(e),p=this.isVertical&&c||!this.isVertical&&a,h=this.isVertical&&s||!this.isVertical&&o,E="A"===e.key||"a"===e.key,y=x(e);if(this.adapter.isRootFocused()){if((h||u)&&y([])?(e.preventDefault(),this.focusLastElement()):(p||l)&&y([])?(e.preventDefault(),this.focusFirstElement()):h&&y(["Shift"])&&this.isCheckboxList?(e.preventDefault(),-1!==(b=this.focusLastElement())&&this.setSelectedIndexOnAction(b,!1)):p&&y(["Shift"])&&this.isCheckboxList&&(e.preventDefault(),-1!==(b=this.focusFirstElement())&&this.setSelectedIndexOnAction(b,!1)),this.hasTypeahead){var S={event:e,focusItemAtIndex:function(e){i.focusItemAtIndex(e)},focusedItemIndex:-1,isTargetListItem:t,sortedIndexByFirstChar:this.sortedIndexByFirstChar,isItemAtIndexDisabled:function(e){return i.isIndexDisabled(e)}};T.handleKeydown(S,this.typeaheadState)}}else{var m=this.adapter.getFocusedElementIndex();if(!(-1===m&&(m=r)<0)){if(p&&y([]))_.preventDefaultEvent(e),this.focusNextElement(m);else if(h&&y([]))_.preventDefaultEvent(e),this.focusPrevElement(m);else if(p&&y(["Shift"])&&this.isCheckboxList)_.preventDefaultEvent(e),-1!==(b=this.focusNextElement(m))&&this.setSelectedIndexOnAction(b,!1);else if(h&&y(["Shift"])&&this.isCheckboxList){var b;_.preventDefaultEvent(e),-1!==(b=this.focusPrevElement(m))&&this.setSelectedIndexOnAction(b,!1)}else if(l&&y([]))_.preventDefaultEvent(e),this.focusFirstElement();else if(u&&y([]))_.preventDefaultEvent(e),this.focusLastElement();else if(l&&y(["Control","Shift"])&&this.isCheckboxList){if(_.preventDefaultEvent(e),this.isIndexDisabled(m))return;this.focusFirstElement(),this.toggleCheckboxRange(0,m,m)}else if(u&&y(["Control","Shift"])&&this.isCheckboxList){if(_.preventDefaultEvent(e),this.isIndexDisabled(m))return;this.focusLastElement(),this.toggleCheckboxRange(m,this.adapter.getListItemCount()-1,m)}else if(E&&y(["Control"])&&this.isCheckboxList)e.preventDefault(),this.checkboxListToggleAll(this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex,!0);else if((d||f)&&(y([])||y(["Alt"]))){if(t){if((I=e.target)&&"A"===I.tagName&&d)return;if(_.preventDefaultEvent(e),this.isIndexDisabled(m))return;this.isTypeaheadInProgress()||(this.isSelectableList()&&this.setSelectedIndexOnAction(m,!1),this.adapter.notifyAction(m))}}else if((d||f)&&y(["Shift"])&&this.isCheckboxList){var I;if((I=e.target)&&"A"===I.tagName&&d)return;if(_.preventDefaultEvent(e),this.isIndexDisabled(m))return;this.isTypeaheadInProgress()||(this.toggleCheckboxRange(null!==(n=this.lastSelectedIndex)&&void 0!==n?n:m,m,m),this.adapter.notifyAction(m))}this.hasTypeahead&&(S={event:e,focusItemAtIndex:function(e){i.focusItemAtIndex(e)},focusedItemIndex:this.focusedItemIndex,isTargetListItem:t,sortedIndexByFirstChar:this.sortedIndexByFirstChar,isItemAtIndexDisabled:function(e){return i.isIndexDisabled(e)}},T.handleKeydown(S,this.typeaheadState))}}},y.prototype.handleClick=function(e,t,r){var n,i=x(r);e!==A.numbers.UNSET_INDEX&&(this.isIndexDisabled(e)||(i([])?(this.isSelectableList()&&this.setSelectedIndexOnAction(e,t),this.adapter.notifyAction(e)):this.isCheckboxList&&i(["Shift"])&&(this.toggleCheckboxRange(null!==(n=this.lastSelectedIndex)&&void 0!==n?n:e,e,e),this.adapter.notifyAction(e))))},y.prototype.focusNextElement=function(e){var t=this.adapter.getListItemCount(),r=e,n=null;do{if(t<=++r){if(!this.wrapFocus)return e;r=0}if(r===n)return-1;n=null!=n?n:r}while(!this.areDisabledItemsFocusable&&this.isIndexDisabled(r));return this.focusItemAtIndex(r),r},y.prototype.focusPrevElement=function(e){var t=this.adapter.getListItemCount(),r=e,n=null;do{if(--r<0){if(!this.wrapFocus)return e;r=t-1}if(r===n)return-1;n=null!=n?n:r}while(!this.areDisabledItemsFocusable&&this.isIndexDisabled(r));return this.focusItemAtIndex(r),r},y.prototype.focusFirstElement=function(){return this.focusNextElement(-1)},y.prototype.focusLastElement=function(){return this.focusPrevElement(this.adapter.getListItemCount())},y.prototype.focusInitialElement=function(){var e=this.getFirstSelectedOrFocusedItemIndex();return e!==A.numbers.UNSET_INDEX&&this.focusItemAtIndex(e),e},y.prototype.setEnabled=function(e,t){this.isIndexValid(e,!1)&&(t?(this.adapter.removeClassForElementIndex(e,A.cssClasses.LIST_ITEM_DISABLED_CLASS),this.adapter.setAttributeForElementIndex(e,A.strings.ARIA_DISABLED,"false")):(this.adapter.addClassForElementIndex(e,A.cssClasses.LIST_ITEM_DISABLED_CLASS),this.adapter.setAttributeForElementIndex(e,A.strings.ARIA_DISABLED,"true")))},y.prototype.setSingleSelectionAtIndex=function(e,t){if(void 0===t&&(t={}),this.selectedIndex!==e||t.forceUpdate){var r=A.cssClasses.LIST_ITEM_SELECTED_CLASS;this.useActivatedClass&&(r=A.cssClasses.LIST_ITEM_ACTIVATED_CLASS),this.selectedIndex!==A.numbers.UNSET_INDEX&&this.adapter.removeClassForElementIndex(this.selectedIndex,r),this.setAriaForSingleSelectionAtIndex(e),this.setTabindexAtIndex(e),e!==A.numbers.UNSET_INDEX&&this.adapter.addClassForElementIndex(e,r),this.selectedIndex=e,t.isUserInteraction&&!t.forceUpdate&&this.adapter.notifySelectionChange([e])}},y.prototype.setAriaForSingleSelectionAtIndex=function(e){this.selectedIndex===A.numbers.UNSET_INDEX&&e!==A.numbers.UNSET_INDEX&&(this.ariaCurrentAttrValue=this.adapter.getAttributeForElementIndex(e,A.strings.ARIA_CURRENT));var t=null!==this.ariaCurrentAttrValue,r=t?A.strings.ARIA_CURRENT:A.strings.ARIA_SELECTED;if(this.selectedIndex!==A.numbers.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(this.selectedIndex,r,"false"),e!==A.numbers.UNSET_INDEX){var n=t?this.ariaCurrentAttrValue:"true";this.adapter.setAttributeForElementIndex(e,r,n)}},y.prototype.getSelectionAttribute=function(){return this.useSelectedAttr?A.strings.ARIA_SELECTED:A.strings.ARIA_CHECKED},y.prototype.setRadioAtIndex=function(e,t){void 0===t&&(t={});var r=this.getSelectionAttribute();this.adapter.setCheckedCheckboxOrRadioAtIndex(e,!0),this.selectedIndex===e&&!t.forceUpdate||(this.selectedIndex!==A.numbers.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(this.selectedIndex,r,"false"),this.adapter.setAttributeForElementIndex(e,r,"true"),this.selectedIndex=e,t.isUserInteraction&&!t.forceUpdate&&this.adapter.notifySelectionChange([e]))},y.prototype.setCheckboxAtIndex=function(e,t){void 0===t&&(t={});for(var r=this.selectedIndex,n=t.isUserInteraction?new Set(r===A.numbers.UNSET_INDEX?[]:r):null,i=this.getSelectionAttribute(),o=[],s=0;s<this.adapter.getListItemCount();s++)if(!t.omitDisabledItems||!this.isIndexDisabled(s)){var a=null==n?void 0:n.has(s),c=0<=e.indexOf(s);c!==a&&o.push(s),this.adapter.setCheckedCheckboxOrRadioAtIndex(s,c),this.adapter.setAttributeForElementIndex(s,i,c?"true":"false")}this.selectedIndex=t.omitDisabledItems?this.resolveSelectedIndices(e):e,t.isUserInteraction&&o.length&&this.adapter.notifySelectionChange(o)},y.prototype.resolveSelectedIndices=function(e){var t=this,r=(this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex).filter(function(e){return t.isIndexDisabled(e)}),n=e.filter(function(e){return!t.isIndexDisabled(e)});return f([],d(new Set(f(f([],d(n)),d(r))))).sort(function(e,t){return e-t})},y.prototype.toggleCheckboxRange=function(e,t,r){this.lastSelectedIndex=r;for(var n=new Set(this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex),i=!(null==n?void 0:n.has(r)),o=d([e,t].sort(),2),s=o[0],a=o[1],c=this.getSelectionAttribute(),l=[],u=s;u<=a;u++)this.isIndexDisabled(u)||i!==n.has(u)&&(l.push(u),this.adapter.setCheckedCheckboxOrRadioAtIndex(u,i),this.adapter.setAttributeForElementIndex(u,c,""+i),i?n.add(u):n.delete(u));l.length&&(this.selectedIndex=f([],d(n)),this.adapter.notifySelectionChange(l))},y.prototype.setTabindexAtIndex=function(e){this.focusedItemIndex===A.numbers.UNSET_INDEX&&0!==e&&e!==A.numbers.UNSET_INDEX?this.adapter.setAttributeForElementIndex(0,"tabindex","-1"):0<=this.focusedItemIndex&&this.focusedItemIndex!==e&&this.adapter.setAttributeForElementIndex(this.focusedItemIndex,"tabindex","-1"),this.selectedIndex instanceof Array||this.selectedIndex===e||this.focusedItemIndex===A.numbers.UNSET_INDEX||this.adapter.setAttributeForElementIndex(this.selectedIndex,"tabindex","-1"),e!==A.numbers.UNSET_INDEX&&this.adapter.setAttributeForElementIndex(e,"tabindex","0")},y.prototype.isSelectableList=function(){return this.isSingleSelectionList||this.isCheckboxList||this.isRadioList},y.prototype.setTabindexToFirstSelectedOrFocusedItem=function(){var e=this.getFirstSelectedOrFocusedItemIndex();this.setTabindexAtIndex(e)},y.prototype.getFirstSelectedOrFocusedItemIndex=function(){var t,e,r=this.getFirstEnabledItem();if(0===this.adapter.getListItemCount())return A.numbers.UNSET_INDEX;if(!this.isSelectableList())return Math.max(this.focusedItemIndex,r);if("number"==typeof this.selectedIndex&&this.selectedIndex!==A.numbers.UNSET_INDEX)return this.areDisabledItemsFocusable&&this.isIndexDisabled(this.selectedIndex)?r:this.selectedIndex;if(function(e){return e instanceof Array}(this.selectedIndex)&&0<this.selectedIndex.length){var n=f([],d(this.selectedIndex)).sort(function(e,t){return e-t});try{for(var i=l(n),o=i.next();!o.done;o=i.next()){var s=o.value;if(!this.isIndexDisabled(s)||this.areDisabledItemsFocusable)return s}}catch(e){t={error:e}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(t)throw t.error}}}return r},y.prototype.getFirstEnabledItem=function(){for(var e=this.adapter.getListItemCount(),t=0;t<e&&this.isIndexDisabled(t);)t++;return t===e?A.numbers.UNSET_INDEX:t},y.prototype.isIndexValid=function(e,t){var r=this;if(void 0===t&&(t=!0),e instanceof Array){if(!this.isCheckboxList&&t)throw new Error("MDCListFoundation: Array of index is only supported for checkbox based list");return 0===e.length||e.some(function(e){return r.isIndexInRange(e)})}if("number"!=typeof e)return!1;if(this.isCheckboxList&&t)throw new Error("MDCListFoundation: Expected array of index for checkbox based list but got number: "+e);return this.isIndexInRange(e)||this.isSingleSelectionList&&e===A.numbers.UNSET_INDEX},y.prototype.isIndexInRange=function(e){var t=this.adapter.getListItemCount();return 0<=e&&e<t},y.prototype.setSelectedIndexOnAction=function(e,t){this.lastSelectedIndex=e,this.isCheckboxList?(this.toggleCheckboxAtIndex(e,t),this.adapter.notifySelectionChange([e])):this.setSelectedIndex(e,{isUserInteraction:!0})},y.prototype.toggleCheckboxAtIndex=function(t,e){var r,n=this.getSelectionAttribute(),i=this.adapter.isCheckboxCheckedAtIndex(t);e?r=i:(r=!i,this.adapter.setCheckedCheckboxOrRadioAtIndex(t,r)),this.adapter.setAttributeForElementIndex(t,n,r?"true":"false");var o=this.selectedIndex===A.numbers.UNSET_INDEX?[]:this.selectedIndex.slice();r?o.push(t):o=o.filter(function(e){return e!==t}),this.selectedIndex=o},y.prototype.focusItemAtIndex=function(e){this.adapter.focusItemAtIndex(e),this.focusedItemIndex=e},y.prototype.getEnabledListItemCount=function(){for(var e=this.adapter.getListItemCount(),t=0,r=0;r<e;r++)this.isIndexDisabled(r)||t++;return t},y.prototype.checkboxListToggleAll=function(e,t){var r=this,n=this.getEnabledListItemCount(),i=this.adapter.getListItemCount();if(e.filter(function(e){return!r.isIndexDisabled(e)}).length>=n)this.setCheckboxAtIndex([],{isUserInteraction:t,omitDisabledItems:!0});else{for(var o=[],s=0;s<i;s++)(!this.isIndexDisabled(s)||-1<e.indexOf(s))&&o.push(s);this.setCheckboxAtIndex(o,{isUserInteraction:t,omitDisabledItems:!0})}},y.prototype.typeaheadMatchItem=function(e,t,r){var n=this;void 0===r&&(r=!1);var i={focusItemAtIndex:function(e){n.focusItemAtIndex(e)},focusedItemIndex:t||this.focusedItemIndex,nextChar:e,sortedIndexByFirstChar:this.sortedIndexByFirstChar,skipFocus:r,isItemAtIndexDisabled:function(e){return n.isIndexDisabled(e)}};return T.matchItem(i,this.typeaheadState)},y.prototype.typeaheadInitSortedIndex=function(){return T.initSortedIndex(this.adapter.getListItemCount(),this.adapter.getPrimaryTextAtIndex)},y.prototype.clearTypeaheadBuffer=function(){T.clearBuffer(this.typeaheadState)},y);function y(e){var t=h.call(this,o(o({},y.defaultAdapter),e))||this;return t.wrapFocus=!1,t.isVertical=!0,t.isSingleSelectionList=!1,t.areDisabledItemsFocusable=!1,t.selectedIndex=A.numbers.UNSET_INDEX,t.focusedItemIndex=A.numbers.UNSET_INDEX,t.useActivatedClass=!1,t.useSelectedAttr=!1,t.ariaCurrentAttrValue=null,t.isCheckboxList=!1,t.isRadioList=!1,t.lastSelectedIndex=null,t.hasTypeahead=!1,t.typeaheadState=T.initState(),t.sortedIndexByFirstChar=new Map,t}t.MDCListFoundation=E,t.default=E},69:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCList=void 0;var o,s=r(13),a=r(49),c=r(56),l=r(61),u=(o=s.MDCComponent,i(d,o),Object.defineProperty(d.prototype,"vertical",{set:function(e){this.foundation.setVerticalOrientation(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"listElements",{get:function(){return Array.from(this.root.querySelectorAll("."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]))},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"wrapFocus",{set:function(e){this.foundation.setWrapFocus(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"typeaheadInProgress",{get:function(){return this.foundation.isTypeaheadInProgress()},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"hasTypeahead",{set:function(e){this.foundation.setHasTypeahead(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"singleSelection",{set:function(e){this.foundation.setSingleSelection(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"disabledItemsFocusable",{set:function(e){this.foundation.setDisabledItemsFocusable(e)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"selectedIndex",{get:function(){return this.foundation.getSelectedIndex()},set:function(e){this.foundation.setSelectedIndex(e)},enumerable:!1,configurable:!0}),d.attachTo=function(e){return new d(e)},d.prototype.initialSyncWithDOM=function(){this.isEvolutionEnabled=c.evolutionAttribute in this.root.dataset,this.isEvolutionEnabled?this.classNameMap=c.evolutionClassNameMap:a.matches(this.root,c.strings.DEPRECATED_SELECTOR)?this.classNameMap=c.deprecatedClassNameMap:this.classNameMap=Object.values(c.cssClasses).reduce(function(e,t){return e[t]=t,e},{}),this.handleClick=this.handleClickEvent.bind(this),this.handleKeydown=this.handleKeydownEvent.bind(this),this.focusInEventListener=this.handleFocusInEvent.bind(this),this.focusOutEventListener=this.handleFocusOutEvent.bind(this),this.listen("keydown",this.handleKeydown),this.listen("click",this.handleClick),this.listen("focusin",this.focusInEventListener),this.listen("focusout",this.focusOutEventListener),this.layout(),this.initializeListType(),this.ensureFocusable()},d.prototype.destroy=function(){this.unlisten("keydown",this.handleKeydown),this.unlisten("click",this.handleClick),this.unlisten("focusin",this.focusInEventListener),this.unlisten("focusout",this.focusOutEventListener)},d.prototype.layout=function(){var e=this.root.getAttribute(c.strings.ARIA_ORIENTATION);this.vertical=e!==c.strings.ARIA_ORIENTATION_HORIZONTAL;var t="."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+":not([tabindex])",r=c.strings.FOCUSABLE_CHILD_ELEMENTS,n=this.root.querySelectorAll(t);n.length&&Array.prototype.forEach.call(n,function(e){e.setAttribute("tabindex","-1")});var i=this.root.querySelectorAll(r);i.length&&Array.prototype.forEach.call(i,function(e){e.setAttribute("tabindex","-1")}),this.isEvolutionEnabled&&this.foundation.setUseSelectedAttribute(!0),this.foundation.layout()},d.prototype.getPrimaryText=function(e){var t,r=e.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_PRIMARY_TEXT_CLASS]);if(this.isEvolutionEnabled||r)return null!==(t=null==r?void 0:r.textContent)&&void 0!==t?t:"";var n=e.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_TEXT_CLASS]);return n&&n.textContent||""},d.prototype.initializeListType=function(){var t=this;if(this.isInteractive=a.matches(this.root,c.strings.ARIA_INTERACTIVE_ROLES_SELECTOR),this.isEvolutionEnabled&&this.isInteractive){var e=Array.from(this.root.querySelectorAll(c.strings.SELECTED_ITEM_SELECTOR),function(e){return t.listElements.indexOf(e)});a.matches(this.root,c.strings.ARIA_MULTI_SELECTABLE_SELECTOR)?this.selectedIndex=e:0<e.length&&(this.selectedIndex=e[0])}else{var r=this.root.querySelectorAll(c.strings.ARIA_ROLE_CHECKBOX_SELECTOR),n=this.root.querySelector(c.strings.ARIA_CHECKED_RADIO_SELECTOR);if(r.length){var i=this.root.querySelectorAll(c.strings.ARIA_CHECKED_CHECKBOX_SELECTOR);this.selectedIndex=Array.from(i,function(e){return t.listElements.indexOf(e)})}else n&&(this.selectedIndex=this.listElements.indexOf(n))}},d.prototype.setEnabled=function(e,t){this.foundation.setEnabled(e,t)},d.prototype.typeaheadMatchItem=function(e,t){return this.foundation.typeaheadMatchItem(e,t,!0)},d.prototype.getDefaultFoundation=function(){var i=this,e={addClassForElementIndex:function(e,t){var r=i.listElements[e];r&&r.classList.add(i.classNameMap[t])},focusItemAtIndex:function(e){var t;null===(t=i.listElements[e])||void 0===t||t.focus()},getAttributeForElementIndex:function(e,t){return i.listElements[e].getAttribute(t)},getFocusedElementIndex:function(){return i.listElements.indexOf(document.activeElement)},getListItemCount:function(){return i.listElements.length},getPrimaryTextAtIndex:function(e){return i.getPrimaryText(i.listElements[e])},hasCheckboxAtIndex:function(e){return!!i.listElements[e].querySelector(c.strings.CHECKBOX_SELECTOR)},hasRadioAtIndex:function(e){return!!i.listElements[e].querySelector(c.strings.RADIO_SELECTOR)},isCheckboxCheckedAtIndex:function(e){return i.listElements[e].querySelector(c.strings.CHECKBOX_SELECTOR).checked},isFocusInsideList:function(){return i.root!==document.activeElement&&i.root.contains(document.activeElement)},isRootFocused:function(){return document.activeElement===i.root},listItemAtIndexHasClass:function(e,t){return i.listElements[e].classList.contains(i.classNameMap[t])},notifyAction:function(e){i.emit(c.strings.ACTION_EVENT,{index:e},!0)},notifySelectionChange:function(e){i.emit(c.strings.SELECTION_CHANGE_EVENT,{changedIndices:e},!0)},removeClassForElementIndex:function(e,t){var r=i.listElements[e];r&&r.classList.remove(i.classNameMap[t])},setAttributeForElementIndex:function(e,t,r){var n=i.listElements[e];n&&i.safeSetAttribute(n,t,r)},setCheckedCheckboxOrRadioAtIndex:function(e,t){var r=i.listElements[e].querySelector(c.strings.CHECKBOX_RADIO_SELECTOR);r.checked=t;var n=document.createEvent("Event");n.initEvent("change",!0,!0),r.dispatchEvent(n)},setTabIndexForListItemChildren:function(e,t){var r=i.listElements[e],n=c.strings.CHILD_ELEMENTS_TO_TOGGLE_TABINDEX;Array.prototype.forEach.call(r.querySelectorAll(n),function(e){e.tabIndex=Number(t)})}};return new l.MDCListFoundation(e)},d.prototype.ensureFocusable=function(){if(this.isEvolutionEnabled&&this.isInteractive&&!this.root.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+'[tabindex="0"]')){var e=this.initialFocusIndex();-1!==e&&(this.listElements[e].tabIndex=0)}},d.prototype.initialFocusIndex=function(){if(this.selectedIndex instanceof Array&&0<this.selectedIndex.length)return this.selectedIndex[0];if("number"==typeof this.selectedIndex&&this.selectedIndex!==c.numbers.UNSET_INDEX)return this.selectedIndex;var e=this.root.querySelector("."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+":not(."+this.classNameMap[c.cssClasses.LIST_ITEM_DISABLED_CLASS]+")");return null===e?-1:this.getListItemIndex(e)},d.prototype.getListItemIndex=function(e){var t=a.closest(e,"."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]+", ."+this.classNameMap[c.cssClasses.ROOT]);return t&&a.matches(t,"."+this.classNameMap[c.cssClasses.LIST_ITEM_CLASS])?this.listElements.indexOf(t):-1},d.prototype.handleFocusInEvent=function(e){var t=this.getListItemIndex(e.target);this.foundation.handleFocusIn(t)},d.prototype.handleFocusOutEvent=function(e){var t=this.getListItemIndex(e.target);this.foundation.handleFocusOut(t)},d.prototype.handleKeydownEvent=function(e){var t=this.getListItemIndex(e.target),r=e.target;this.foundation.handleKeydown(e,r.classList.contains(this.classNameMap[c.cssClasses.LIST_ITEM_CLASS]),t)},d.prototype.handleClickEvent=function(e){var t=this.getListItemIndex(e.target),r=e.target;this.foundation.handleClick(t,a.matches(r,c.strings.CHECKBOX_RADIO_SELECTOR),e)},d);function d(){return null!==o&&o.apply(this,arguments)||this}t.MDCList=u},7:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCFoundation=void 0;var n=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(e){void 0===e&&(e={}),this.adapter=e}t.MDCFoundation=n,t.default=n},70:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleKeydown=t.clearBuffer=t.isTypingInProgress=t.matchItem=t.initSortedIndex=t.initState=void 0;var y=r(55),l=r(56),S=r(60);function m(e,t){var r,n=e.nextChar,i=e.focusItemAtIndex,o=e.sortedIndexByFirstChar,s=e.focusedItemIndex,a=e.skipFocus,c=e.isItemAtIndexDisabled;return clearTimeout(t.bufferClearTimeout),t.bufferClearTimeout=setTimeout(function(){u(t)},l.numbers.TYPEAHEAD_BUFFER_CLEAR_TIMEOUT_MS),t.typeaheadBuffer=t.typeaheadBuffer+n,-1===(r=1===t.typeaheadBuffer.length?function(e,t,r,n){var i=n.typeaheadBuffer[0],o=e.get(i);if(!o)return-1;if(i===n.currentFirstChar&&o[n.sortedIndexCursor].index===t){n.sortedIndexCursor=(n.sortedIndexCursor+1)%o.length;var s=o[n.sortedIndexCursor].index;if(!r(s))return s}n.currentFirstChar=i;var a,c=-1;for(a=0;a<o.length;a++)if(!r(o[a].index)){c=a;break}for(;a<o.length;a++)if(o[a].index>t&&!r(o[a].index)){c=a;break}return-1===c?-1:(n.sortedIndexCursor=c,o[n.sortedIndexCursor].index)}(o,s,c,t):function(e,t,r){var n=r.typeaheadBuffer[0],i=e.get(n);if(!i)return-1;var o=i[r.sortedIndexCursor];if(0===o.text.lastIndexOf(r.typeaheadBuffer,0)&&!t(o.index))return o.index;var s=(r.sortedIndexCursor+1)%i.length,a=-1;for(;s!==r.sortedIndexCursor;){var c=i[s],l=0===c.text.lastIndexOf(r.typeaheadBuffer,0),u=!t(c.index);if(l&&u){a=s;break}s=(s+1)%i.length}return-1===a?-1:(r.sortedIndexCursor=a,i[r.sortedIndexCursor].index)}(o,c,t))||a||i(r),r}function b(e){return 0<e.typeaheadBuffer.length}function u(e){e.typeaheadBuffer=""}t.initState=function(){return{bufferClearTimeout:0,currentFirstChar:"",sortedIndexCursor:0,typeaheadBuffer:""}},t.initSortedIndex=function(e,t){for(var r=new Map,n=0;n<e;n++){var i=t(n).trim();if(i){var o=i[0].toLowerCase();r.has(o)||r.set(o,[]),r.get(o).push({text:i.toLowerCase(),index:n})}}return r.forEach(function(e){e.sort(function(e,t){return e.index-t.index})}),r},t.matchItem=m,t.isTypingInProgress=b,t.clearBuffer=u,t.handleKeydown=function(e,t){var r=e.event,n=e.isTargetListItem,i=e.focusedItemIndex,o=e.focusItemAtIndex,s=e.sortedIndexByFirstChar,a=e.isItemAtIndexDisabled,c="ArrowLeft"===y.normalizeKey(r),l="ArrowUp"===y.normalizeKey(r),u="ArrowRight"===y.normalizeKey(r),d="ArrowDown"===y.normalizeKey(r),f="Home"===y.normalizeKey(r),p="End"===y.normalizeKey(r),h="Enter"===y.normalizeKey(r),E="Spacebar"===y.normalizeKey(r);return r.altKey||r.ctrlKey||r.metaKey||c||l||u||d||f||p||h?-1:E||1!==r.key.length?E?(n&&S.preventDefaultEvent(r),n&&b(t)?m({focusItemAtIndex:o,focusedItemIndex:i,nextChar:" ",sortedIndexByFirstChar:s,skipFocus:!1,isItemAtIndexDisabled:a},t):-1):-1:(S.preventDefaultEvent(r),m({focusItemAtIndex:o,focusedItemIndex:i,nextChar:r.key.toLowerCase(),sortedIndexByFirstChar:s,skipFocus:!1,isItemAtIndexDisabled:a},t))}},8:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapAttributePrefix=t.createAttributePrefix=t.SafeAttributePrefix=void 0,r(0);function o(){}var s=r(4);t.SafeAttributePrefix=o;var a,c=(i(l,a=o),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},l);function l(e,t){var r=a.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=e,r}t.createAttributePrefix=function(e){return new c(e,s.secretToken)},t.unwrapAttributePrefix=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},9:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEST_ONLY=t.getTrustedTypesPolicy=t.getTrustedTypes=void 0;var n,i="google#safe";function o(){var e;return""!==i&&null!==(e=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==e?e:null}t.getTrustedTypes=o,t.getTrustedTypesPolicy=function(){var e,t;if(void 0===n)try{n=null!==(t=null===(e=o())||void 0===e?void 0:e.createPolicy(i,{createHTML:function(e){return e},createScript:function(e){return e},createScriptURL:function(e){return e}}))&&void 0!==t?t:null}catch(e){n=null}return n},t.TEST_ONLY={resetDefaults:function(){n=void 0,i="google#safe"},setTrustedTypesPolicyName:function(e){i=e}}}},i.c=n,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=239);function i(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}var r,n});