import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, RouterModule } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatListModule } from '@angular/material/list';
import { MatBadgeModule } from '@angular/material/badge';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { Observable } from 'rxjs';

import { AuthService } from './core/services/auth.service';
import { User } from './core/models/user.model';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    CommonModule,
    RouterOutlet,
    MatToolbarModule,
    MatSidenavModule,
    MatIconModule,
    MatButtonModule,
    MatListModule,
    MatBadgeModule
  ],
  template: `
    <div class="app-container" *ngIf="isAuthenticated$ | async; else loginView">
      <mat-toolbar color="primary" class="app-toolbar">
        <button mat-icon-button (click)="sidenav.toggle()">
          <mat-icon>menu</mat-icon>
        </button>
        <span class="app-title">Shop Management System</span>
        <span class="spacer"></span>

        <!-- Notifications -->
        <button mat-icon-button routerLink="/notifications">
          <mat-icon [matBadge]="unreadCount$ | async" matBadgeColor="warn">
            notifications
          </mat-icon>
        </button>

        <!-- User Menu -->
        <button mat-icon-button [matMenuTriggerFor]="userMenu">
          <mat-icon>account_circle</mat-icon>
        </button>
        <mat-menu #userMenu="matMenu">
          <div mat-menu-item disabled>
            <span>{{ (currentUser$ | async)?.firstName }} {{ (currentUser$ | async)?.lastName }}</span>
          </div>
          <mat-divider></mat-divider>
          <button mat-menu-item routerLink="/profile">
            <mat-icon>person</mat-icon>
            <span>Profile</span>
          </button>
          <button mat-menu-item (click)="onLogout()">
            <mat-icon>logout</mat-icon>
            <span>Logout</span>
          </button>
        </mat-menu>
      </mat-toolbar>

      <mat-sidenav-container class="app-sidenav-container">
        <mat-sidenav #sidenav mode="side" opened class="app-sidenav">
          <mat-nav-list>
            <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
              <mat-icon matListItemIcon>dashboard</mat-icon>
              <span matListItemTitle>Dashboard</span>
            </a>

            <a mat-list-item routerLink="/inventory" routerLinkActive="active">
              <mat-icon matListItemIcon>inventory</mat-icon>
              <span matListItemTitle>Inventory</span>
            </a>

            <a mat-list-item routerLink="/sales" routerLinkActive="active">
              <mat-icon matListItemIcon>point_of_sale</mat-icon>
              <span matListItemTitle>Sales</span>
            </a>

            <a mat-list-item routerLink="/orders" routerLinkActive="active">
              <mat-icon matListItemIcon>receipt_long</mat-icon>
              <span matListItemTitle>Orders</span>
            </a>

            <a mat-list-item routerLink="/customers" routerLinkActive="active">
              <mat-icon matListItemIcon>people</mat-icon>
              <span matListItemTitle>Customers</span>
            </a>

            <a mat-list-item routerLink="/reports" routerLinkActive="active">
              <mat-icon matListItemIcon>analytics</mat-icon>
              <span matListItemTitle>Reports</span>
            </a>

            <div *ngIf="(currentUser$ | async)?.roles?.includes('admin')">
              <mat-divider></mat-divider>
              <h3 matSubheader>Administration</h3>

              <a mat-list-item routerLink="/admin/users" routerLinkActive="active">
                <mat-icon matListItemIcon>manage_accounts</mat-icon>
                <span matListItemTitle>Users</span>
              </a>

              <a mat-list-item routerLink="/admin/roles" routerLinkActive="active">
                <mat-icon matListItemIcon>security</mat-icon>
                <span matListItemTitle>Roles</span>
              </a>

              <a mat-list-item routerLink="/admin/settings" routerLinkActive="active">
                <mat-icon matListItemIcon>settings</mat-icon>
                <span matListItemTitle>Settings</span>
              </a>
            </div>
          </mat-nav-list>
        </mat-sidenav>

        <mat-sidenav-content class="app-content">
          <router-outlet></router-outlet>
        </mat-sidenav-content>
      </mat-sidenav-container>
    </div>

    <ng-template #loginView>
      <router-outlet></router-outlet>
    </ng-template>
  `,
  styles: [`
    .app-container {
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .app-toolbar {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
    }

    .app-title {
      font-size: 1.2rem;
      font-weight: 500;
    }

    .spacer {
      flex: 1 1 auto;
    }

    .app-sidenav-container {
      flex: 1;
      margin-top: 64px;
    }

    .app-sidenav {
      width: 250px;
      border-right: 1px solid rgba(0, 0, 0, 0.12);
    }

    .app-content {
      padding: 20px;
      background-color: #fafafa;
      min-height: calc(100vh - 64px);
    }

    .active {
      background-color: rgba(0, 0, 0, 0.04);
    }

    @media (max-width: 768px) {
      .app-sidenav {
        width: 200px;
      }

      .app-content {
        padding: 16px;
      }
    }
  `]
})
export class AppComponent implements OnInit {
  private store = inject(Store<AppState>);
  private authService = inject(AuthService);
  private notificationService = inject(NotificationService);

  isAuthenticated$ = this.store.select(selectIsAuthenticated);
  currentUser$ = this.store.select(selectCurrentUser);
  unreadCount$ = this.store.select(selectUnreadNotificationCount);

  ngOnInit(): void {
    // Initialize auth state
    this.authService.initializeAuth();

    // Load notifications if authenticated
    this.isAuthenticated$.subscribe(isAuth => {
      if (isAuth) {
        this.store.dispatch(loadNotifications());
        this.notificationService.startPolling();
      } else {
        this.notificationService.stopPolling();
      }
    });
  }

  onLogout(): void {
    this.store.dispatch(logout());
  }
}
