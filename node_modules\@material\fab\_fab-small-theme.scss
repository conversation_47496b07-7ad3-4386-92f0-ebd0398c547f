// Copyright 2021 Google Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

@use '@material/theme/keys';
@use '@material/theme/theme';
@use '@material/theme/theme-color';
@use '@material/tokens/resolvers';
@use './fab-theme';
@use 'sass:map';

$height: 56px !default;
$mini-height: 40px !default;
$shape-radius: 50% !default;
$ripple-target: '.mdc-fab__ripple';

$_light-theme: (
  container-color: secondary,
  container-elevation: 6,
  container-height: 40px,
  container-shadow-color: black,
  container-shape: $shape-radius,
  container-width: 40px,
  focus-container-elevation: null,
  focus-icon-color: null,
  focus-outline-color: null,
  focus-outline-width: null,
  focus-state-layer-color: theme-color.$primary,
  focus-state-layer-opacity: null,
  hover-container-elevation: null,
  hover-icon-color: null,
  hover-state-layer-color: theme-color.$primary,
  hover-state-layer-opacity: null,
  icon-color: on-secondary,
  icon-size: 24px,
  lowered-container-elevation: null,
  lowered-focus-container-elevation: null,
  lowered-hover-container-elevation: null,
  lowered-pressed-container-elevation: null,
  pressed-container-elevation: null,
  pressed-icon-color: null,
  pressed-ripple-color: null,
  pressed-ripple-opacity: null,
  pressed-state-layer-color: theme-color.$primary,
  pressed-state-layer-opacity: null,
);

$custom-property-prefix: 'fab-small';

///
/// Applies the given theme as custom properties without any selectors.
///
/// @param {Map} $theme - Theme configuration to use for theming the small fab.
/// @param {Map} $resolvers [resolvers.$material] - map of resolver functions
///     that resolve token names to a valid Material theme config.
@mixin theme($theme, $resolvers: resolvers.$material) {
  @include theme.validate-theme($_light-theme, $theme);
  $resolved-theme: fab-theme.resolve-theme($theme, $resolvers);
  @include keys.declare-custom-properties(
    $resolved-theme,
    $prefix: $custom-property-prefix
  );
}

///
/// Applies the themable styles for small fabs and exposes CSS custom properties
/// as well as selectors.
///
/// @param {Map} $theme - Theme configuration to use for theming the small fab.
/// @param {Map} $resolvers [resolvers.$material] - map of resolver functions
///     that resolve token names to a valid Material theme config.
///
@mixin theme-styles($theme, $resolvers: resolvers.$material) {
  @include theme.validate-theme($_light-theme, $theme);

  $theme: keys.create-theme-properties(
    $theme,
    $prefix: $custom-property-prefix
  );
  @include fab-theme.base-theme-styles($theme, $resolvers: $resolvers);

  $shape-radius: map.get($theme, container-shape);
  @if $shape-radius {
    @include fab-theme.shape-radius($shape-radius);
  }
}
