{"version": 3, "sources": ["webpack://mdc.[name]/webpack/universalModuleDefinition", "webpack://mdc.[name]/webpack/bootstrap", "webpack://mdc.[name]/./packages/mdc-dom/events.ts", "webpack://mdc.[name]/./packages/mdc-dom/focus-trap.ts", "webpack://mdc.[name]/./packages/mdc-dom/index.ts", "webpack://mdc.[name]/./packages/mdc-dom/keyboard.ts", "webpack://mdc.[name]/./packages/mdc-dom/ponyfill.ts"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,kDAA0C,gCAAgC;AAC1E;AACA;;AAEA;AACA;AACA;AACA,gEAAwD,kBAAkB;AAC1E;AACA,yDAAiD,cAAc;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAyC,iCAAiC;AAC1E,wHAAgH,mBAAmB,EAAE;AACrI;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;;AAGA;AACA;;;;;;;;;;;;;;AC7DG;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;;;;AACH,SAA4B,aAA2B;AAA1B;AAAA,oBAA0B;;AAErD,WAA4B,sBAAa,aACrC,EAAQ,SAAoC,SAElD;AAAC;AALD,uBAKC;AAED,SAA8B,sBAA2B;AAA1B;AAAA,oBAA0B;;AACjD;AAC0E;AAChF,QAAoB,mBAAS;AAE7B,QAAI;AACF,YAAa;AACqC;AACL;AAC3C,gBAAW;AACO,mCAAQ;AACxB,uBACF;AACA;AAPc;AAShB,YAAa,UAAG,mBAAO,CAAE;AAChB,kBAAS,SAAiB,iBAAO,QAAS,SAAW;AACrD,kBAAS,SAAoB,oBAC5B,QAAS,SAAmC;AACvD,MAAC,OAAU,KAAE;AACI,2BAAS;AAC1B;AAED,WACF;AAAC,C;;;;;;;;;;;;;ACrCE;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,IAA0B,uBAA4B;AAQnD;;;;;;;AACH;AAIE,uBACsC,MACS;AAA1B;AAAA,sBAA0B;;AAD1B,aAAI,OAAa;AACjB,aAAO,UAAmB;AALK;AAC5C,aAAwB,2BAIkB;AAAC;AAKhD;;;;AACH,wBAAS,YAAT;AACE,YAAkB,eAAO,KAAqB,qBAAK,KAAO;AAC1D,YAAgB,aAAO,WAAM,GAAE;AAC7B,kBAAM,IAAS,MACmD;AACnE;AAEG,aAAyB,2BACjB,SAAc,yBAAyB,cAAS,SAAgB,gBACnB;AACrD,aAAa,aAAK,KAAO;AAE7B,YAAI,CAAK,KAAQ,QAAiB,kBAAE;AAC9B,iBAAoB,oBAAa,cAAM,KAAQ,QAAiB;AAExE;AAAC;AAKE;;;;AACH,wBAAY,eAAZ;AACO,cACI,KACG,KAAK,KAAiB,iBAAc,MAA4B,uBAChE,QAAC,UAAwB;AACrB,uBAAe,cAAY,YACvC;AAAG;AAEP,YAAI,CAAK,KAAQ,QAAiB,oBAAQ,KAAyB,0BAAE;AAC/D,iBAAyB,yBAAS;AAE1C;AAAC;AAQE;;;;;;;AACK,wBAAY,eAApB,UAAoC;AAApC,oBAmBC;AAlBC,YAAmB,gBAAO,KAAkB;AAC5C,YAAiB,cAAO,KAAkB;AAE7B,sBAAiB,iBAAQ,SAAE;AACtC,gBAAkB,eAAO,MAAqB,qBAAK;AACnD,gBAAgB,aAAO,SAAI,GAAE;AACf,6BAAa,aAAO,SAAK,GAAS;AAElD;AAAG;AACQ,oBAAiB,iBAAQ,SAAE;AACpC,gBAAkB,eAAO,MAAqB,qBAAK;AACnD,gBAAgB,aAAO,SAAI,GAAE;AACf,6BAAG,GAAS;AAE5B;AAAG;AAED,WAAa,aAAc,eAAI,GAAS,SAAK;AAC7C,WAAY,YAChB;AAAC;AAKE;;;;AACK,wBAAmB,sBAA3B,UAC+B,cAA8B;AAC3D,YAAc,aAAK;AACnB,YAAkB,gBAAE;AACR,yBAAO,KAAI,IAAa,aAAQ,QAAgB,iBAAK;AAChE;AACW,qBAAY,YAC1B;AAAC;AAEO,wBAAoB,uBAA5B,UAA8C;AAC5C,YAAkB,eAAQ,MAAK,KAAK,KAAiB,iBACe;AACpE,4BAA0B,OAAC,UAAG;AAC5B,gBAAwB,qBAAK,GAAa,aAAiB,qBAAW,UAChE,GAAa,aAAY,eAAQ,QACjC,GAAa,aAAU,aAAQ,QAC/B,GAAa,aAAe,mBAAY;AAC9C,gBAA0B,uBAAK,GAAS,YAAK,KACvC,GAAwB,wBAAM,QAAI,KACpC,CAAG,GAAU,UAAS,SAAsB,yBAAI,CAAoB;AAExE,gBAA4B,2BAAS;AACrC,gBAAwB,sBAAE;AACxB,oBAAW,QAAmB,iBAAK;AACX,2CACf,MAAQ,YAAW,UAAS,MAAW,eAAc;AAC/D;AACD,mBAA2B,wBAAI,CACjC;AACF,SAjBqB;AAiBpB;AAEO,wBAAc,iBAAtB;AACE,YAAc,WAAW,SAAc,cAAQ;AACvC,iBAAa,aAAW,YAAO;AACH;AAC5B,iBAAa,aAAc,eAAU;AACrC,iBAAU,UAAI,IAAuB;AAC7C,eACF;AAAC;AACH,WAAC;AAAA;AArHY,oBAAS,U;;;;;;;;;;;;;ACXnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,8CAAmC;AAK3B,iBAAM;AAJd,iDAA0C;AAI1B,oBAAS;AAHzB,gDAAuC;AAGZ,mBAAQ;AAFnC,gDAAuC;AAEF,mBAAQ,S;;;;;;;;;;;;;ACP1C;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;;;AACU,QAAG;AACP,aAAW;AACT,eAAa;AACjB,WAAS;AACN,cAAY;AACb,aAAU;AACR,eAAY;AAClB,SAAO;AACN,UAAQ;AACF,gBAAa;AACf,cAAW;AACR,iBAAc;AACf,gBAAa;AACjB,YAAU;AACV,YAAU;AACb,SACH;AAhBiB;AAkBnB,IAAoB,iBAAG,IAAkB;AACqC;AACnE;AACG,eAAI,IAAC,QAAG,IAAY;AACpB,eAAI,IAAC,QAAG,IAAQ;AAChB,eAAI,IAAC,QAAG,IAAW;AACnB,eAAI,IAAC,QAAG,IAAU;AAClB,eAAI,IAAC,QAAG,IAAY;AACpB,eAAI,IAAC,QAAG,IAAM;AACd,eAAI,IAAC,QAAG,IAAO;AACf,eAAI,IAAC,QAAG,IAAa;AACrB,eAAI,IAAC,QAAG,IAAW;AACnB,eAAI,IAAC,QAAG,IAAc;AACtB,eAAI,IAAC,QAAG,IAAa;AACrB,eAAI,IAAC,QAAG,IAAS;AACjB,eAAI,IAAC,QAAG,IAAS;AACjB,eAAI,IAAC,QAAG,IAAM;AAE5B,IAAc;AACH,eAAG;AACP,WAAI;AACD,cAAI;AACL,aAAI;AACF,eAAI;AACV,SAAI;AACH,UAAI;AACE,gBAAI;AACN,cAAI;AACD,iBAAI;AACL,gBAAI;AACR,YAAI;AACJ,YAAI;AACP,SACH;AAfe;AAiBjB,IAAoB,iBAAG,IAA0B;AAC6B;AACnE;AACG,eAAI,IAAS,SAAU,WAAE,QAAG,IAAY;AACxC,eAAI,IAAS,SAAM,OAAE,QAAG,IAAQ;AAChC,eAAI,IAAS,SAAS,UAAE,QAAG,IAAW;AACtC,eAAI,IAAS,SAAQ,SAAE,QAAG,IAAU;AACpC,eAAI,IAAS,SAAU,WAAE,QAAG,IAAY;AACxC,eAAI,IAAS,SAAI,KAAE,QAAG,IAAM;AAC5B,eAAI,IAAS,SAAK,MAAE,QAAG,IAAO;AAC9B,eAAI,IAAS,SAAW,YAAE,QAAG,IAAa;AAC1C,eAAI,IAAS,SAAS,UAAE,QAAG,IAAW;AACtC,eAAI,IAAS,SAAY,aAAE,QAAG,IAAc;AAC5C,eAAI,IAAS,SAAW,YAAE,QAAG,IAAa;AAC1C,eAAI,IAAS,SAAO,QAAE,QAAG,IAAS;AAClC,eAAI,IAAS,SAAO,QAAE,QAAG,IAAS;AAClC,eAAI,IAAS,SAAI,KAAE,QAAG,IAAM;AAE1C,IAAoB,iBAAG,IAAkB;AACqC;AACnE;AACG,eAAI,IAAC,QAAG,IAAU;AAClB,eAAI,IAAC,QAAG,IAAY;AACpB,eAAI,IAAC,QAAG,IAAM;AACd,eAAI,IAAC,QAAG,IAAO;AACf,eAAI,IAAC,QAAG,IAAa;AACrB,eAAI,IAAC,QAAG,IAAW;AACnB,eAAI,IAAC,QAAG,IAAc;AACtB,eAAI,IAAC,QAAG,IAAa;AAIhC;;;AACH,SAA4B,aAAqB;AACxC,QAAG,MAAS,MAAC;AACmC;AACvD,QAAkB,eAAI,IAAK,MAAE;AAC3B,eAAW;AACZ;AAEsC;AACvC,QAAe,YAAiB,eAAI,IAAM,MAAU;AACpD,QAAa,WAAE;AACb,eAAiB;AAClB;AACD,WAAO,QAAG,IACZ;AAAC;AAbD,uBAaC;AAIE;;;AACH,SAAiC,kBAAqB;AACpD,WAAqB,eAAI,IAAa,aACxC;AAAC;AAFD,4BAEC,kB;;;;;;;;;;;;;AC/GE;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;;;;;AAEH,SAAuB,QAAiB,SAAkB;AACxD,QAAW,QAAQ,SAAE;AACnB,eAAc,QAAQ,QAAW;AAClC;AAED,QAAM,KAAyB;AAC/B,WAAS,IAAE;AACT,YAAW,QAAG,IAAW,WAAE;AACzB,mBAAU;AACX;AACC,aAAK,GAAe;AACvB;AACD,WACF;AAAC;AAbD,kBAaC;AAEqD;AACtD,SAAuB,QAAiB,SAAkB;AACxD,QAAmB,gBAAU,QAAQ,WAAW,QAAsB,yBAClD,QAAmB;AACvC,WAAoB,cAAK,KAAQ,SACnC;AAAC;AAJD,kBAIC;AASE;;;;;;;;AACH,SAAmC,oBAAiB;AACuB;AACnB;AACwB;AAClB;AAC5D,QAAY,SAA0B;AACtC,QAAU,OAAa,iBAAS,MAAE;AAChC,eAAa,OAAa;AAC3B;AAED,QAAW,QAAS,OAAU,UAAsB;AAC/C,UAAM,MAAY,YAAW,YAAc;AAC3C,UAAM,MAAY,YAAY,aAAiC;AAC5D,aAAgB,gBAAY,YAAQ;AAC5C,QAAiB,cAAQ,MAAa;AAC9B,aAAgB,gBAAY,YAAQ;AAC5C,WACF;AAAC;AAjBD,8BAiBC,oB", "file": "mdc.dom.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"@material/dom\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"dom\"] = factory();\n\telse\n\t\troot[\"mdc\"] = root[\"mdc\"] || {}, root[\"mdc\"][\"dom\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./packages/mdc-dom/index.ts\");\n", "/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * Determine whether the current browser supports passive event listeners, and\n * if so, use them.\n */\nexport function applyPassive(globalObj: Window = window): boolean|\n    EventListenerOptions {\n  return supportsPassiveOption(globalObj) ?\n      {passive: true} as AddEventListenerOptions :\n      false;\n}\n\nfunction supportsPassiveOption(globalObj: Window = window): boolean {\n  // See\n  // https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener\n  let passiveSupported = false;\n\n  try {\n    const options = {\n      // This function will be called when the browser\n      // attempts to access the passive property.\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n\n    const handler = () => {};\n    globalObj.document.addEventListener('test', handler, options);\n    globalObj.document.removeEventListener(\n        'test', handler, options as EventListenerOptions);\n  } catch (err) {\n    passiveSupported = false;\n  }\n\n  return passiveSupported;\n}\n", "/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nconst FOCUS_SENTINEL_CLASS = 'mdc-dom-focus-sentinel';\n\n/**\n * Utility to trap focus in a given root element, e.g. for modal components such\n * as dialogs. The root should have at least one focusable child element,\n * for setting initial focus when trapping focus.\n * Also tracks the previously focused element, and restores focus to that\n * element when releasing focus.\n */\nexport class FocusTrap {\n  // Previously focused element before trapping focus.\n  private elFocusedBeforeTrapFocus: HTMLElement|null = null;\n\n  constructor(\n      private readonly root: HTMLElement,\n      private readonly options: FocusOptions = {}) {}\n\n  /**\n   * Traps focus in `root`. Also focuses on either `initialFocusEl` if set;\n   * otherwises sets initial focus to the first focusable child element.\n   */\n  trapFocus() {\n    const focusableEls = this.getFocusableElements(this.root);\n    if (focusableEls.length === 0) {\n      throw new Error(\n          'FocusTrap: Element must have at least one focusable child.');\n    }\n\n    this.elFocusedBeforeTrapFocus =\n        document.activeElement instanceof HTMLElement ? document.activeElement :\n                                                        null;\n    this.wrapTabFocus(this.root);\n\n    if (!this.options.skipInitialFocus) {\n      this.focusInitialElement(focusableEls, this.options.initialFocusEl);\n    }\n  }\n\n  /**\n   * Releases focus from `root`. Also restores focus to the previously focused\n   * element.\n   */\n  releaseFocus() {\n    Array\n        .from(\n            this.root.querySelectorAll<HTMLElement>(`.${FOCUS_SENTINEL_CLASS}`))\n        .forEach((sentinelEl: HTMLElement) => {\n          sentinelEl.parentElement!.removeChild(sentinelEl);\n        });\n\n    if (!this.options.skipRestoreFocus && this.elFocusedBeforeTrapFocus) {\n      this.elFocusedBeforeTrapFocus.focus();\n    }\n  }\n\n  /**\n   * Wraps tab focus within `el` by adding two hidden sentinel divs which are\n   * used to mark the beginning and the end of the tabbable region. When\n   * focused, these sentinel elements redirect focus to the first/last\n   * children elements of the tabbable region, ensuring that focus is trapped\n   * within that region.\n   */\n  private wrapTabFocus(el: HTMLElement) {\n    const sentinelStart = this.createSentinel();\n    const sentinelEnd = this.createSentinel();\n\n    sentinelStart.addEventListener('focus', () => {\n      const focusableEls = this.getFocusableElements(el);\n      if (focusableEls.length > 0) {\n        focusableEls[focusableEls.length - 1].focus();\n      }\n    });\n    sentinelEnd.addEventListener('focus', () => {\n      const focusableEls = this.getFocusableElements(el);\n      if (focusableEls.length > 0) {\n        focusableEls[0].focus();\n      }\n    });\n\n    el.insertBefore(sentinelStart, el.children[0]);\n    el.appendChild(sentinelEnd);\n  }\n\n  /**\n   * Focuses on `initialFocusEl` if defined and a child of the root element.\n   * Otherwise, focuses on the first focusable child element of the root.\n   */\n  private focusInitialElement(\n      focusableEls: HTMLElement[], initialFocusEl?: HTMLElement) {\n    let focusIndex = 0;\n    if (initialFocusEl) {\n      focusIndex = Math.max(focusableEls.indexOf(initialFocusEl), 0);\n    }\n    focusableEls[focusIndex].focus();\n  }\n\n  private getFocusableElements(root: HTMLElement): HTMLElement[] {\n    const focusableEls = Array.from(root.querySelectorAll<HTMLElement>(\n        '[autofocus], [tabindex], a, input, textarea, select, button'));\n    return focusableEls.filter((el) => {\n      const isDisabledOrHidden = el.getAttribute('aria-disabled') === 'true' ||\n          el.getAttribute('disabled') != null ||\n          el.getAttribute('hidden') != null ||\n          el.getAttribute('aria-hidden') === 'true';\n      const isTabbableAndVisible = el.tabIndex >= 0 &&\n          el.getBoundingClientRect().width > 0 &&\n          !el.classList.contains(FOCUS_SENTINEL_CLASS) && !isDisabledOrHidden;\n\n      let isProgrammaticallyHidden = false;\n      if (isTabbableAndVisible) {\n        const style = getComputedStyle(el);\n        isProgrammaticallyHidden =\n            style.display === 'none' || style.visibility === 'hidden';\n      }\n      return isTabbableAndVisible && !isProgrammaticallyHidden;\n    });\n  }\n\n  private createSentinel() {\n    const sentinel = document.createElement('div');\n    sentinel.setAttribute('tabindex', '0');\n    // Don't announce in screen readers.\n    sentinel.setAttribute('aria-hidden', 'true');\n    sentinel.classList.add(FOCUS_SENTINEL_CLASS);\n    return sentinel;\n  }\n}\n\n/** Customization options. */\nexport interface FocusOptions {\n  // The element to focus initially when trapping focus.\n  //  Must be a child of the root element.\n  initialFocusEl?: HTMLElement;\n\n  // Whether to skip initially focusing on any element when trapping focus.\n  // By default, focus is set on the first focusable child element of the root.\n  // This is useful if the caller wants to handle setting initial focus.\n  skipInitialFocus?: boolean;\n\n  // Whether to restore focus on the previously focused element when releasing\n  // focus. This is useful if the caller wants to handle restoring focus.\n  skipRestoreFocus?: boolean;\n}\n", "/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nimport * as events from './events';\nimport * as focusTrap from './focus-trap';\nimport * as keyboard from './keyboard';\nimport * as ponyfill from './ponyfill';\n\nexport {events, focusTrap, keyboard, ponyfill};\n", "/**\n * @license\n * Copyright 2020 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * <PERSON><PERSON><PERSON> provides normalized string values for keys.\n */\nexport const KEY = {\n  UNKNOWN: 'Unknown',\n  BACKSPACE: 'Backspace',\n  ENTER: 'Enter',\n  SPACEBAR: 'Spacebar',\n  PAGE_UP: 'PageUp',\n  PAGE_DOWN: 'PageDown',\n  END: 'End',\n  HOME: 'Home',\n  ARROW_LEFT: 'ArrowLeft',\n  ARROW_UP: 'ArrowUp',\n  ARROW_RIGHT: 'ArrowRight',\n  ARROW_DOWN: 'ArrowDown',\n  DELETE: 'Delete',\n  ESCAPE: 'Escape',\n  TAB: 'Tab',\n};\n\nconst normalizedKeys = new Set<string>();\n// IE11 has no support for new Map with iterable so we need to initialize this\n// by hand.\nnormalizedKeys.add(KEY.BACKSPACE);\nnormalizedKeys.add(KEY.ENTER);\nnormalizedKeys.add(KEY.SPACEBAR);\nnormalizedKeys.add(KEY.PAGE_UP);\nnormalizedKeys.add(KEY.PAGE_DOWN);\nnormalizedKeys.add(KEY.END);\nnormalizedKeys.add(KEY.HOME);\nnormalizedKeys.add(KEY.ARROW_LEFT);\nnormalizedKeys.add(KEY.ARROW_UP);\nnormalizedKeys.add(KEY.ARROW_RIGHT);\nnormalizedKeys.add(KEY.ARROW_DOWN);\nnormalizedKeys.add(KEY.DELETE);\nnormalizedKeys.add(KEY.ESCAPE);\nnormalizedKeys.add(KEY.TAB);\n\nconst KEY_CODE = {\n  BACKSPACE: 8,\n  ENTER: 13,\n  SPACEBAR: 32,\n  PAGE_UP: 33,\n  PAGE_DOWN: 34,\n  END: 35,\n  HOME: 36,\n  ARROW_LEFT: 37,\n  ARROW_UP: 38,\n  ARROW_RIGHT: 39,\n  ARROW_DOWN: 40,\n  DELETE: 46,\n  ESCAPE: 27,\n  TAB: 9,\n};\n\nconst mappedKeyCodes = new Map<number, string>();\n// IE11 has no support for new Map with iterable so we need to initialize this\n// by hand.\nmappedKeyCodes.set(KEY_CODE.BACKSPACE, KEY.BACKSPACE);\nmappedKeyCodes.set(KEY_CODE.ENTER, KEY.ENTER);\nmappedKeyCodes.set(KEY_CODE.SPACEBAR, KEY.SPACEBAR);\nmappedKeyCodes.set(KEY_CODE.PAGE_UP, KEY.PAGE_UP);\nmappedKeyCodes.set(KEY_CODE.PAGE_DOWN, KEY.PAGE_DOWN);\nmappedKeyCodes.set(KEY_CODE.END, KEY.END);\nmappedKeyCodes.set(KEY_CODE.HOME, KEY.HOME);\nmappedKeyCodes.set(KEY_CODE.ARROW_LEFT, KEY.ARROW_LEFT);\nmappedKeyCodes.set(KEY_CODE.ARROW_UP, KEY.ARROW_UP);\nmappedKeyCodes.set(KEY_CODE.ARROW_RIGHT, KEY.ARROW_RIGHT);\nmappedKeyCodes.set(KEY_CODE.ARROW_DOWN, KEY.ARROW_DOWN);\nmappedKeyCodes.set(KEY_CODE.DELETE, KEY.DELETE);\nmappedKeyCodes.set(KEY_CODE.ESCAPE, KEY.ESCAPE);\nmappedKeyCodes.set(KEY_CODE.TAB, KEY.TAB);\n\nconst navigationKeys = new Set<string>();\n// IE11 has no support for new Set with iterable so we need to initialize this\n// by hand.\nnavigationKeys.add(KEY.PAGE_UP);\nnavigationKeys.add(KEY.PAGE_DOWN);\nnavigationKeys.add(KEY.END);\nnavigationKeys.add(KEY.HOME);\nnavigationKeys.add(KEY.ARROW_LEFT);\nnavigationKeys.add(KEY.ARROW_UP);\nnavigationKeys.add(KEY.ARROW_RIGHT);\nnavigationKeys.add(KEY.ARROW_DOWN);\n\n/**\n * normalizeKey returns the normalized string for a navigational action.\n */\nexport function normalizeKey(event: KeyboardEvent): string {\n  const {key} = event;\n  // If the event already has a normalized key, return it\n  if (normalizedKeys.has(key)) {\n    return key;\n  }\n\n  // tslint:disable-next-line:deprecation\n  const mappedKey = mappedKeyCodes.get(event.keyCode);\n  if (mappedKey) {\n    return mappedKey;\n  }\n  return KEY.UNKNOWN;\n}\n\n/**\n * isNavigationEvent returns whether the event is a navigation event\n */\nexport function isNavigationEvent(event: KeyboardEvent): boolean {\n  return navigationKeys.has(normalizeKey(event));\n}\n", "/**\n * @license\n * Copyright 2018 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * @fileoverview A \"ponyfill\" is a polyfill that doesn't modify the global\n * prototype chain. This makes ponyfills safer than traditional polyfills,\n * especially for libraries like MDC.\n */\n\nexport function closest(element: Element, selector: string): Element|null {\n  if (element.closest) {\n    return element.closest(selector);\n  }\n\n  let el: Element|null = element;\n  while (el) {\n    if (matches(el, selector)) {\n      return el;\n    }\n    el = el.parentElement;\n  }\n  return null;\n}\n\n/** Element.matches with support for webkit and IE. */\nexport function matches(element: Element, selector: string): boolean {\n  const nativeMatches = element.matches || element.webkitMatchesSelector ||\n      (element as any).msMatchesSelector;\n  return nativeMatches.call(element, selector);\n}\n\n/**\n * Used to compute the estimated scroll width of elements. When an element is\n * hidden due to display: none; being applied to a parent element, the width is\n * returned as 0. However, the element will have a true width once no longer\n * inside a display: none context. This method computes an estimated width when\n * the element is hidden or returns the true width when the element is visble.\n * @param {Element} element the element whose width to estimate\n */\nexport function estimateScrollWidth(element: Element): number {\n  // Check the offsetParent. If the element inherits display: none from any\n  // parent, the offsetParent property will be null (see\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n  // This check ensures we only clone the node when necessary.\n  const htmlEl = element as HTMLElement;\n  if (htmlEl.offsetParent !== null) {\n    return htmlEl.scrollWidth;\n  }\n\n  const clone = htmlEl.cloneNode(true) as HTMLElement;\n  clone.style.setProperty('position', 'absolute');\n  clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n  document.documentElement.appendChild(clone);\n  const scrollWidth = clone.scrollWidth;\n  document.documentElement.removeChild(clone);\n  return scrollWidth;\n}\n"], "sourceRoot": ""}