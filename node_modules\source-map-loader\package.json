{"name": "source-map-loader", "version": "5.0.0", "description": "extracts inlined source map and offers it to webpack", "license": "MIT", "repository": "webpack-contrib/source-map-loader", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack-contrib/source-map-loader", "bugs": "https://github.com/webpack-contrib/source-map-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit", "lint:prettier": "prettier --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky install && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"webpack": "^5.72.1"}, "dependencies": {"iconv-lite": "^0.6.3", "source-map-js": "^1.0.2"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.8", "@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "cspell": "^8.3.2", "del": "^7.1.0", "del-cli": "^5.1.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.2.0", "memfs": "^4.6.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.2", "standard-version": "^9.5.0", "webpack": "^5.88.2"}, "keywords": ["webpack"]}