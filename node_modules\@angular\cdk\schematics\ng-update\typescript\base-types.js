"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.determineBaseTypes = void 0;
const ts = require("typescript");
/** Determines the base types of the specified class declaration. */
function determineBaseTypes(node) {
    if (!node.heritageClauses) {
        return null;
    }
    return node.heritageClauses
        .reduce((types, clause) => types.concat(clause.types), [])
        .map(typeExpression => typeExpression.expression)
        .filter(expression => expression && ts.isIdentifier(expression))
        .map(identifier => identifier.text);
}
exports.determineBaseTypes = determineBaseTypes;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmFzZS10eXBlcy5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uLy4uL3NyYy9jZGsvc2NoZW1hdGljcy9uZy11cGRhdGUvdHlwZXNjcmlwdC9iYXNlLXR5cGVzLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTs7Ozs7O0dBTUc7OztBQUVILGlDQUFpQztBQUVqQyxvRUFBb0U7QUFDcEUsU0FBZ0Isa0JBQWtCLENBQUMsSUFBeUI7SUFDMUQsSUFBSSxDQUFDLElBQUksQ0FBQyxlQUFlLEVBQUUsQ0FBQztRQUMxQixPQUFPLElBQUksQ0FBQztJQUNkLENBQUM7SUFFRCxPQUFPLElBQUksQ0FBQyxlQUFlO1NBQ3hCLE1BQU0sQ0FBQyxDQUFDLEtBQUssRUFBRSxNQUFNLEVBQUUsRUFBRSxDQUFDLEtBQUssQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLEtBQUssQ0FBQyxFQUFFLEVBQXNDLENBQUM7U0FDN0YsR0FBRyxDQUFDLGNBQWMsQ0FBQyxFQUFFLENBQUMsY0FBYyxDQUFDLFVBQVUsQ0FBQztTQUNoRCxNQUFNLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBQyxVQUFVLElBQUksRUFBRSxDQUFDLFlBQVksQ0FBQyxVQUFVLENBQUMsQ0FBQztTQUMvRCxHQUFHLENBQUMsVUFBVSxDQUFDLEVBQUUsQ0FBRSxVQUE0QixDQUFDLElBQUksQ0FBQyxDQUFDO0FBQzNELENBQUM7QUFWRCxnREFVQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5pbXBvcnQgKiBhcyB0cyBmcm9tICd0eXBlc2NyaXB0JztcblxuLyoqIERldGVybWluZXMgdGhlIGJhc2UgdHlwZXMgb2YgdGhlIHNwZWNpZmllZCBjbGFzcyBkZWNsYXJhdGlvbi4gKi9cbmV4cG9ydCBmdW5jdGlvbiBkZXRlcm1pbmVCYXNlVHlwZXMobm9kZTogdHMuQ2xhc3NEZWNsYXJhdGlvbik6IHN0cmluZ1tdIHwgbnVsbCB7XG4gIGlmICghbm9kZS5oZXJpdGFnZUNsYXVzZXMpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHJldHVybiBub2RlLmhlcml0YWdlQ2xhdXNlc1xuICAgIC5yZWR1Y2UoKHR5cGVzLCBjbGF1c2UpID0+IHR5cGVzLmNvbmNhdChjbGF1c2UudHlwZXMpLCBbXSBhcyB0cy5FeHByZXNzaW9uV2l0aFR5cGVBcmd1bWVudHNbXSlcbiAgICAubWFwKHR5cGVFeHByZXNzaW9uID0+IHR5cGVFeHByZXNzaW9uLmV4cHJlc3Npb24pXG4gICAgLmZpbHRlcihleHByZXNzaW9uID0+IGV4cHJlc3Npb24gJiYgdHMuaXNJZGVudGlmaWVyKGV4cHJlc3Npb24pKVxuICAgIC5tYXAoaWRlbnRpZmllciA9PiAoaWRlbnRpZmllciBhcyB0cy5JZGVudGlmaWVyKS50ZXh0KTtcbn1cbiJdfQ==