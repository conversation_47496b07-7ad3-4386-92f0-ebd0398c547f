"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */const global="object"==typeof window&&window||"object"==typeof self&&self||globalThis.global,OriginalDate=global.Date;function FakeDate(){if(0===arguments.length){const e=new OriginalDate;return e.setTime(FakeDate.now()),e}{const e=Array.prototype.slice.call(arguments);return new OriginalDate(...e)}}let patchedTimers;FakeDate.now=function(){const e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():OriginalDate.now.apply(this,arguments)},FakeDate.UTC=OriginalDate.UTC,FakeDate.parse=OriginalDate.parse;const timeoutCallback=function(){};class Scheduler{static{this.nextNodeJSId=1}static{this.nextId=-1}constructor(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=OriginalDate.now(),this._currentTickRequeuePeriodicEntries=[]}static getNextId(){const e=patchedTimers.nativeSetTimeout.call(global,timeoutCallback,0);return patchedTimers.nativeClearTimeout.call(global,e),"number"==typeof e?e:Scheduler.nextNodeJSId++}getCurrentTickTime(){return this._currentTickTime}getFakeSystemTime(){return this._currentFakeBaseSystemTime+this._currentTickTime}setFakeBaseSystemTime(e){this._currentFakeBaseSystemTime=e}getRealSystemTime(){return OriginalDate.now()}scheduleFunction(e,t,s){let r=(s={args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1,...s}).id<0?Scheduler.nextId:s.id;Scheduler.nextId=Scheduler.getNextId();let i={endTime:this._currentTickTime+t,id:r,func:e,args:s.args,delay:t,isPeriodic:s.isPeriodic,isRequestAnimationFrame:s.isRequestAnimationFrame};s.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(i);let n=0;for(;n<this._schedulerQueue.length&&!(i.endTime<this._schedulerQueue[n].endTime);n++);return this._schedulerQueue.splice(n,0,i),r}removeScheduledFunctionWithId(e){for(let t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}}removeAll(){this._schedulerQueue=[]}getTimerCount(){return this._schedulerQueue.length}tickToNext(e=1,t,s){this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,s)}tick(e=0,t,s){let r=this._currentTickTime+e,i=0;const n=(s=Object.assign({processNewMacroTasksSynchronously:!0},s)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===n.length&&t)t(e);else{for(;n.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(r<n[0].endTime));){let e=n.shift();if(!s.processNewMacroTasksSynchronously){const t=this._schedulerQueue.indexOf(e);t>=0&&this._schedulerQueue.splice(t,1)}if(i=this._currentTickTime,this._currentTickTime=e.endTime,t&&t(this._currentTickTime-i),!e.func.apply(global,e.isRequestAnimationFrame?[this._currentTickTime]:e.args))break;s.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((e=>{let t=0;for(;t<n.length&&!(e.endTime<n[t].endTime);t++);n.splice(t,0,e)}))}i=this._currentTickTime,this._currentTickTime=r,t&&t(this._currentTickTime-i)}}flushOnlyPendingTimers(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t}flush(e=20,t=!1,s){return t?this.flushPeriodic(s):this.flushNonPeriodic(e,s)}flushPeriodic(e){if(0===this._schedulerQueue.length)return 0;const t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t}flushNonPeriodic(e,t){const s=this._currentTickTime;let r=0,i=0;for(;this._schedulerQueue.length>0;){if(i++,i>e)throw new Error("flush failed after reaching the limit of "+e+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((e=>!e.isPeriodic&&!e.isRequestAnimationFrame)).length)break;const s=this._schedulerQueue.shift();if(r=this._currentTickTime,this._currentTickTime=s.endTime,t&&t(this._currentTickTime-r),!s.func.apply(global,s.args))break}return this._currentTickTime-s}}class FakeAsyncTestZoneSpec{static assertInZone(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")}constructor(e,t=!1,s){this.trackPendingRequestAnimationFrame=t,this.macroTaskOptions=s,this._scheduler=new Scheduler,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+e,this.macroTaskOptions||(this.macroTaskOptions=global[Zone.__symbol__("FakeAsyncTestMacroTask")])}_fnAndFlush(e,t){return(...s)=>(e.apply(global,s),null===this._lastError?(null!=t.onSuccess&&t.onSuccess.apply(global),this.flushMicrotasks()):null!=t.onError&&t.onError.apply(global),null===this._lastError)}static _removeTimer(e,t){let s=e.indexOf(t);s>-1&&e.splice(s,1)}_dequeueTimer(e){return()=>{FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers,e)}}_requeuePeriodicTimer(e,t,s,r){return()=>{-1!==this.pendingPeriodicTimers.indexOf(r)&&this._scheduler.scheduleFunction(e,t,{args:s,isPeriodic:!0,id:r,isRequeuePeriodic:!0})}}_dequeuePeriodicTimer(e){return()=>{FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers,e)}}_setTimeout(e,t,s,r=!0){let i=this._dequeueTimer(Scheduler.nextId),n=this._fnAndFlush(e,{onSuccess:i,onError:i}),a=this._scheduler.scheduleFunction(n,t,{args:s,isRequestAnimationFrame:!r});return r&&this.pendingTimers.push(a),a}_clearTimeout(e){FakeAsyncTestZoneSpec._removeTimer(this.pendingTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_setInterval(e,t,s){let r=Scheduler.nextId,i={onSuccess:null,onError:this._dequeuePeriodicTimer(r)},n=this._fnAndFlush(e,i);return i.onSuccess=this._requeuePeriodicTimer(n,t,s,r),this._scheduler.scheduleFunction(n,t,{args:s,isPeriodic:!0}),this.pendingPeriodicTimers.push(r),r}_clearInterval(e){FakeAsyncTestZoneSpec._removeTimer(this.pendingPeriodicTimers,e),this._scheduler.removeScheduledFunctionWithId(e)}_resetLastErrorAndThrow(){let e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e}getCurrentTickTime(){return this._scheduler.getCurrentTickTime()}getFakeSystemTime(){return this._scheduler.getFakeSystemTime()}setFakeBaseSystemTime(e){this._scheduler.setFakeBaseSystemTime(e)}getRealSystemTime(){return this._scheduler.getRealSystemTime()}static patchDate(){global[Zone.__symbol__("disableDatePatching")]||global.Date!==FakeDate&&(global.Date=FakeDate,FakeDate.prototype=OriginalDate.prototype,FakeAsyncTestZoneSpec.checkTimerPatch())}static resetDate(){global.Date===FakeDate&&(global.Date=OriginalDate)}static checkTimerPatch(){if(!patchedTimers)throw new Error("Expected timers to have been patched.");global.setTimeout!==patchedTimers.setTimeout&&(global.setTimeout=patchedTimers.setTimeout,global.clearTimeout=patchedTimers.clearTimeout),global.setInterval!==patchedTimers.setInterval&&(global.setInterval=patchedTimers.setInterval,global.clearInterval=patchedTimers.clearInterval)}lockDatePatch(){this.patchDateLocked=!0,FakeAsyncTestZoneSpec.patchDate()}unlockDatePatch(){this.patchDateLocked=!1,FakeAsyncTestZoneSpec.resetDate()}tickToNext(e=1,t,s={processNewMacroTasksSynchronously:!0}){e<=0||(FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(e,t,s),null!==this._lastError&&this._resetLastErrorAndThrow())}tick(e=0,t,s={processNewMacroTasksSynchronously:!0}){FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(e,t,s),null!==this._lastError&&this._resetLastErrorAndThrow()}flushMicrotasks(){for(FakeAsyncTestZoneSpec.assertInZone();this._microtasks.length>0;){let e=this._microtasks.shift();e.func.apply(e.target,e.args)}(()=>{(null!==this._lastError||this._uncaughtPromiseErrors.length)&&this._resetLastErrorAndThrow()})()}flush(e,t,s){FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks();const r=this._scheduler.flush(e,t,s);return null!==this._lastError&&this._resetLastErrorAndThrow(),r}flushOnlyPendingTimers(e){FakeAsyncTestZoneSpec.assertInZone(),this.flushMicrotasks();const t=this._scheduler.flushOnlyPendingTimers(e);return null!==this._lastError&&this._resetLastErrorAndThrow(),t}removeAllTimers(){FakeAsyncTestZoneSpec.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]}getTimerCount(){return this._scheduler.getTimerCount()+this._microtasks.length}onScheduleTask(e,t,s,r){switch(r.type){case"microTask":let t,i=r.data&&r.data.args;if(i){let e=r.data.cbIdx;"number"==typeof i.length&&i.length>e+1&&(t=Array.prototype.slice.call(i,e+1))}this._microtasks.push({func:r.invoke,args:t,target:r.data&&r.data.target});break;case"macroTask":switch(r.source){case"setTimeout":r.data.handleId=this._setTimeout(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"setImmediate":r.data.handleId=this._setTimeout(r.invoke,0,Array.prototype.slice.call(r.data.args,1));break;case"setInterval":r.data.handleId=this._setInterval(r.invoke,r.data.delay,Array.prototype.slice.call(r.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+r.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":r.data.handleId=this._setTimeout(r.invoke,16,r.data.args,this.trackPendingRequestAnimationFrame);break;default:const e=this.findMacroTaskOption(r);if(e){const t=r.data&&r.data.args,s=t&&t.length>1?t[1]:0;let i=e.callbackArgs?e.callbackArgs:t;e.isPeriodic?(r.data.handleId=this._setInterval(r.invoke,s,i),r.data.isPeriodic=!0):r.data.handleId=this._setTimeout(r.invoke,s,i);break}throw new Error("Unknown macroTask scheduled in fake async test: "+r.source)}break;case"eventTask":r=e.scheduleTask(s,r)}return r}onCancelTask(e,t,s,r){switch(r.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(r.data.handleId);case"setInterval":return this._clearInterval(r.data.handleId);default:const t=this.findMacroTaskOption(r);if(t){const e=r.data.handleId;return t.isPeriodic?this._clearInterval(e):this._clearTimeout(e)}return e.cancelTask(s,r)}}onInvoke(e,t,s,r,i,n,a){try{return FakeAsyncTestZoneSpec.patchDate(),e.invoke(s,r,i,n,a)}finally{this.patchDateLocked||FakeAsyncTestZoneSpec.resetDate()}}findMacroTaskOption(e){if(!this.macroTaskOptions)return null;for(let t=0;t<this.macroTaskOptions.length;t++){const s=this.macroTaskOptions[t];if(s.source===e.source)return s}return null}onHandleError(e,t,s,r){return this._lastError=r,!1}}let _fakeAsyncTestZoneSpec=null;function getProxyZoneSpec(){return Zone&&Zone.ProxyZoneSpec}function resetFakeAsyncZone(){_fakeAsyncTestZoneSpec&&_fakeAsyncTestZoneSpec.unlockDatePatch(),_fakeAsyncTestZoneSpec=null,getProxyZoneSpec()&&getProxyZoneSpec().assertPresent().resetDelegate()}function fakeAsync(e,t={}){const{flush:s=!1}=t,r=function(...t){const r=getProxyZoneSpec();if(!r)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const i=r.assertPresent();if(Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!_fakeAsyncTestZoneSpec){const e=Zone&&Zone.FakeAsyncTestZoneSpec;if(i.getDelegate()instanceof e)throw new Error("fakeAsync() calls can not be nested");_fakeAsyncTestZoneSpec=new e}let r;const n=i.getDelegate();i.setDelegate(_fakeAsyncTestZoneSpec),_fakeAsyncTestZoneSpec.lockDatePatch();try{r=e.apply(this,t),s?_fakeAsyncTestZoneSpec.flush(20,!0):flushMicrotasks()}finally{i.setDelegate(n)}if(!s){if(_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length>0)throw new Error(`${_fakeAsyncTestZoneSpec.pendingPeriodicTimers.length} periodic timer(s) still in the queue.`);if(_fakeAsyncTestZoneSpec.pendingTimers.length>0)throw new Error(`${_fakeAsyncTestZoneSpec.pendingTimers.length} timer(s) still in the queue.`)}return r}finally{resetFakeAsyncZone()}};return r.isFakeAsync=!0,r}function _getFakeAsyncZoneSpec(){if(null==_fakeAsyncTestZoneSpec&&(_fakeAsyncTestZoneSpec=Zone.current.get("FakeAsyncTestZoneSpec"),null==_fakeAsyncTestZoneSpec))throw new Error("The code should be running in the fakeAsync zone to call this function");return _fakeAsyncTestZoneSpec}function tick(e=0,t=!1){_getFakeAsyncZoneSpec().tick(e,null,t)}function flush(e){return _getFakeAsyncZoneSpec().flush(e)}function discardPeriodicTasks(){_getFakeAsyncZoneSpec().pendingPeriodicTimers.length=0}function flushMicrotasks(){_getFakeAsyncZoneSpec().flushMicrotasks()}function patchFakeAsyncTest(e){e.FakeAsyncTestZoneSpec=FakeAsyncTestZoneSpec,e.__load_patch("fakeasync",((e,t,s)=>{t[s.symbol("fakeAsyncTest")]={resetFakeAsyncZone:resetFakeAsyncZone,flushMicrotasks:flushMicrotasks,discardPeriodicTasks:discardPeriodicTasks,tick:tick,flush:flush,fakeAsync:fakeAsync}}),!0),patchedTimers={setTimeout:global.setTimeout,setInterval:global.setInterval,clearTimeout:global.clearTimeout,clearInterval:global.clearInterval,nativeSetTimeout:global[e.__symbol__("setTimeout")],nativeClearTimeout:global[e.__symbol__("clearTimeout")]},Scheduler.nextId=Scheduler.getNextId()}patchFakeAsyncTest(Zone);