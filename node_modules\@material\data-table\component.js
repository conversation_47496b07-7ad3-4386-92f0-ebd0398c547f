/**
 * @license
 * Copyright 2019 Google Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
import { __extends, __values } from "tslib";
import { MDCComponent } from '@material/base/component';
import { MDCCheckbox } from '@material/checkbox/component';
import { closest } from '@material/dom/ponyfill';
import { MDCLinearProgress } from '@material/linear-progress/component';
import { cssClasses, dataAttributes, events, messages, selectors, SortValue } from './constants';
import { MDCDataTableFoundation } from './foundation';
/** MDC Data Table */
var MDCDataTable = /** @class */ (function (_super) {
    __extends(MDCDataTable, _super);
    function MDCDataTable() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    MDCDataTable.attachTo = function (root) {
        return new MDCDataTable(root);
    };
    MDCDataTable.prototype.initialize = function (checkboxFactory) {
        if (checkboxFactory === void 0) { checkboxFactory = function (el) { return new MDCCheckbox(el); }; }
        this.checkboxFactory = checkboxFactory;
    };
    MDCDataTable.prototype.initialSyncWithDOM = function () {
        var _this = this;
        this.headerRow =
            this.root.querySelector("." + cssClasses.HEADER_ROW);
        this.handleHeaderRowCheckboxChange = function () {
            _this.foundation.handleHeaderRowCheckboxChange();
        };
        this.headerRow.addEventListener('change', this.handleHeaderRowCheckboxChange);
        this.headerRowClickListener = function (event) {
            _this.handleHeaderRowClick(event);
        };
        this.headerRow.addEventListener('click', this.headerRowClickListener);
        this.content =
            this.root.querySelector("." + cssClasses.CONTENT);
        this.handleContentClick = function (event) {
            var dataRowEl = closest(event.target, selectors.ROW);
            if (!dataRowEl)
                return;
            _this.foundation.handleRowClick({
                rowId: _this.getRowIdByRowElement(dataRowEl),
                row: dataRowEl,
                altKey: event.altKey,
                ctrlKey: event.ctrlKey,
                metaKey: event.metaKey,
                shiftKey: event.shiftKey,
            });
        };
        this.content.addEventListener('click', this.handleContentClick);
        this.handleRowCheckboxChange = function (event) {
            _this.foundation.handleRowCheckboxChange(event);
        };
        this.content.addEventListener('change', this.handleRowCheckboxChange);
        this.layout();
    };
    /**
     * Re-initializes header row checkbox and row checkboxes when selectable rows
     * are added or removed from table.
     */
    MDCDataTable.prototype.layout = function () {
        this.foundation.layout();
    };
    /**
     * @return Returns array of header row cell elements.
     */
    MDCDataTable.prototype.getHeaderCells = function () {
        return Array.from(this.root.querySelectorAll(selectors.HEADER_CELL));
    };
    /**
     * @return Returns array of row elements.
     */
    MDCDataTable.prototype.getRows = function () {
        return this.foundation.getRows();
    };
    /**
     * @return Returns array of selected row ids.
     */
    MDCDataTable.prototype.getSelectedRowIds = function () {
        return this.foundation.getSelectedRowIds();
    };
    /**
     * Sets selected row ids. Overwrites previously selected rows.
     * @param rowIds Array of row ids that needs to be selected.
     */
    MDCDataTable.prototype.setSelectedRowIds = function (rowIds) {
        this.foundation.setSelectedRowIds(rowIds);
    };
    /**
     * Shows progress indicator when data table is in loading state.
     */
    MDCDataTable.prototype.showProgress = function () {
        this.getLinearProgress().open();
        this.foundation.showProgress();
    };
    /**
     * Hides progress indicator after data table is finished loading.
     */
    MDCDataTable.prototype.hideProgress = function () {
        this.foundation.hideProgress();
        this.getLinearProgress().close();
    };
    MDCDataTable.prototype.destroy = function () {
        var e_1, _a;
        if (this.handleHeaderRowCheckboxChange) {
            this.headerRow.removeEventListener('change', this.handleHeaderRowCheckboxChange);
        }
        if (this.headerRowClickListener) {
            this.headerRow.removeEventListener('click', this.headerRowClickListener);
        }
        if (this.handleRowCheckboxChange) {
            this.content.removeEventListener('change', this.handleRowCheckboxChange);
        }
        if (this.headerRowCheckbox) {
            this.headerRowCheckbox.destroy();
        }
        if (this.rowCheckboxList) {
            try {
                for (var _b = __values(this.rowCheckboxList), _c = _b.next(); !_c.done; _c = _b.next()) {
                    var checkbox = _c.value;
                    checkbox.destroy();
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);
                }
                finally { if (e_1) throw e_1.error; }
            }
        }
        if (this.handleContentClick) {
            this.content.removeEventListener('click', this.handleContentClick);
        }
    };
    MDCDataTable.prototype.getDefaultFoundation = function () {
        var _this = this;
        // DO NOT INLINE this variable. For backward compatibility, foundations take
        // a Partial<MDCFooAdapter>. To ensure we don't accidentally omit any
        // methods, we need a separate, strongly typed adapter variable.
        // tslint:disable:object-literal-sort-keys Methods should be in the same order as the adapter interface.
        var adapter = {
            addClass: function (className) {
                _this.root.classList.add(className);
            },
            removeClass: function (className) {
                _this.root.classList.remove(className);
            },
            getHeaderCellElements: function () { return _this.getHeaderCells(); },
            getHeaderCellCount: function () { return _this.getHeaderCells().length; },
            getAttributeByHeaderCellIndex: function (index, attribute) {
                return _this.getHeaderCells()[index].getAttribute(attribute);
            },
            setAttributeByHeaderCellIndex: function (index, attribute, value) {
                _this.safeSetAttribute(_this.getHeaderCells()[index], attribute, value);
            },
            setClassNameByHeaderCellIndex: function (index, className) {
                _this.getHeaderCells()[index].classList.add(className);
            },
            removeClassNameByHeaderCellIndex: function (index, className) {
                _this.getHeaderCells()[index].classList.remove(className);
            },
            notifySortAction: function (data) {
                _this.emit(events.SORTED, data, /** shouldBubble */ true);
            },
            getTableContainerHeight: function () {
                var tableContainer = _this.root.querySelector("." + cssClasses.TABLE_CONTAINER);
                if (!tableContainer) {
                    throw new Error('MDCDataTable: Table container element not found.');
                }
                return tableContainer.getBoundingClientRect().height;
            },
            getTableHeaderHeight: function () {
                var tableHeader = _this.root.querySelector(selectors.HEADER_ROW);
                if (!tableHeader) {
                    throw new Error('MDCDataTable: Table header element not found.');
                }
                return tableHeader.getBoundingClientRect().height;
            },
            setProgressIndicatorStyles: function (styles) {
                var progressIndicator = _this.root.querySelector(selectors.PROGRESS_INDICATOR);
                if (!progressIndicator) {
                    throw new Error('MDCDataTable: Progress indicator element not found.');
                }
                progressIndicator.style.setProperty('height', styles.height);
                progressIndicator.style.setProperty('top', styles.top);
            },
            addClassAtRowIndex: function (rowIndex, className) {
                _this.getRows()[rowIndex].classList.add(className);
            },
            getRowCount: function () { return _this.getRows().length; },
            getRowElements: function () {
                return Array.from(_this.root.querySelectorAll(selectors.ROW));
            },
            getRowIdAtIndex: function (rowIndex) {
                return _this.getRows()[rowIndex].getAttribute(dataAttributes.ROW_ID);
            },
            getRowIndexByChildElement: function (el) {
                return _this.getRows().indexOf(closest(el, selectors.ROW));
            },
            getSelectedRowCount: function () {
                return _this.root.querySelectorAll(selectors.ROW_SELECTED)
                    .length;
            },
            isCheckboxAtRowIndexChecked: function (rowIndex) {
                return _this.rowCheckboxList[rowIndex].checked;
            },
            isHeaderRowCheckboxChecked: function () { return _this.headerRowCheckbox.checked; },
            isRowsSelectable: function () {
                return !!_this.root.querySelector(selectors.ROW_CHECKBOX) ||
                    !!_this.root.querySelector(selectors.HEADER_ROW_CHECKBOX);
            },
            notifyRowSelectionChanged: function (data) {
                _this.emit(events.ROW_SELECTION_CHANGED, {
                    row: _this.getRowByIndex(data.rowIndex),
                    rowId: _this.getRowIdByIndex(data.rowIndex),
                    rowIndex: data.rowIndex,
                    selected: data.selected,
                }, 
                /** shouldBubble */ true);
            },
            notifySelectedAll: function () {
                _this.emit(events.SELECTED_ALL, {}, /** shouldBubble */ true);
            },
            notifyUnselectedAll: function () {
                _this.emit(events.UNSELECTED_ALL, {}, /** shouldBubble */ true);
            },
            notifyRowClick: function (data) {
                _this.emit(events.ROW_CLICK, data, /** shouldBubble */ true);
            },
            registerHeaderRowCheckbox: function () {
                if (_this.headerRowCheckbox) {
                    _this.headerRowCheckbox.destroy();
                }
                var checkboxEl = _this.root.querySelector(selectors.HEADER_ROW_CHECKBOX);
                _this.headerRowCheckbox = _this.checkboxFactory(checkboxEl);
            },
            registerRowCheckboxes: function () {
                if (_this.rowCheckboxList) {
                    _this.rowCheckboxList.forEach(function (checkbox) {
                        checkbox.destroy();
                    });
                }
                _this.rowCheckboxList = [];
                _this.getRows().forEach(function (rowEl) {
                    var checkbox = _this.checkboxFactory(rowEl.querySelector(selectors.ROW_CHECKBOX));
                    _this.rowCheckboxList.push(checkbox);
                });
            },
            removeClassAtRowIndex: function (rowIndex, className) {
                _this.getRows()[rowIndex].classList.remove(className);
            },
            setAttributeAtRowIndex: function (rowIndex, attr, value) {
                _this.safeSetAttribute(_this.getRows()[rowIndex], attr, value);
            },
            setHeaderRowCheckboxChecked: function (checked) {
                _this.headerRowCheckbox.checked = checked;
            },
            setHeaderRowCheckboxIndeterminate: function (indeterminate) {
                _this.headerRowCheckbox.indeterminate = indeterminate;
            },
            setRowCheckboxCheckedAtIndex: function (rowIndex, checked) {
                _this.rowCheckboxList[rowIndex].checked = checked;
            },
            setSortStatusLabelByHeaderCellIndex: function (columnIndex, sortValue) {
                var headerCell = _this.getHeaderCells()[columnIndex];
                var sortStatusLabel = headerCell.querySelector(selectors.SORT_STATUS_LABEL);
                if (!sortStatusLabel)
                    return;
                sortStatusLabel.textContent =
                    _this.getSortStatusMessageBySortValue(sortValue);
            },
        };
        return new MDCDataTableFoundation(adapter);
    };
    MDCDataTable.prototype.getRowByIndex = function (index) {
        return this.getRows()[index];
    };
    MDCDataTable.prototype.getRowIdByIndex = function (index) {
        return this.getRowByIndex(index).getAttribute(dataAttributes.ROW_ID);
    };
    MDCDataTable.prototype.handleHeaderRowClick = function (event) {
        var headerCell = closest(event.target, selectors.HEADER_CELL_WITH_SORT);
        if (!headerCell) {
            return;
        }
        var columnId = headerCell.getAttribute(dataAttributes.COLUMN_ID);
        var columnIndex = this.getHeaderCells().indexOf(headerCell);
        if (columnIndex === -1) {
            return;
        }
        this.foundation.handleSortAction({ columnId: columnId, columnIndex: columnIndex, headerCell: headerCell });
    };
    MDCDataTable.prototype.getSortStatusMessageBySortValue = function (sortValue) {
        switch (sortValue) {
            case SortValue.ASCENDING:
                return messages.SORTED_IN_ASCENDING;
            case SortValue.DESCENDING:
                return messages.SORTED_IN_DESCENDING;
            default:
                return '';
        }
    };
    MDCDataTable.prototype.getLinearProgressElement = function () {
        var el = this.root.querySelector("." + cssClasses.LINEAR_PROGRESS);
        if (!el) {
            throw new Error('MDCDataTable: linear progress element is not found.');
        }
        return el;
    };
    MDCDataTable.prototype.getLinearProgress = function () {
        if (!this.linearProgress) {
            var el = this.getLinearProgressElement();
            this.linearProgress = new MDCLinearProgress(el);
        }
        return this.linearProgress;
    };
    MDCDataTable.prototype.getRowIdByRowElement = function (rowElement) {
        return rowElement.getAttribute(dataAttributes.ROW_ID);
    };
    return MDCDataTable;
}(MDCComponent));
export { MDCDataTable };
//# sourceMappingURL=component.js.map