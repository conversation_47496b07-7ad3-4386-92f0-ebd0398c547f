{"name": "pacote", "version": "17.0.6", "description": "JavaScript package downloader", "author": "GitHub Inc.", "bin": {"pacote": "lib/bin.js"}, "license": "ISC", "main": "lib/index.js", "scripts": {"test": "tap", "snap": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "tap": {"timeout": 300, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "devDependencies": {"@npmcli/arborist": "^7.1.0", "@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "hosted-git-info": "^7.0.0", "mutate-fs": "^2.1.1", "nock": "^13.2.4", "npm-registry-mock": "^1.3.2", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "keywords": ["packages", "npm", "git"], "dependencies": {"@npmcli/git": "^5.0.0", "@npmcli/installed-package-contents": "^2.0.1", "@npmcli/promise-spawn": "^7.0.0", "@npmcli/run-script": "^7.0.0", "cacache": "^18.0.0", "fs-minipass": "^3.0.0", "minipass": "^7.0.2", "npm-package-arg": "^11.0.0", "npm-packlist": "^8.0.0", "npm-pick-manifest": "^9.0.0", "npm-registry-fetch": "^16.0.0", "proc-log": "^3.0.0", "promise-retry": "^2.0.1", "read-package-json": "^7.0.0", "read-package-json-fast": "^3.0.0", "sigstore": "^2.2.0", "ssri": "^10.0.0", "tar": "^6.1.11"}, "engines": {"node": "^16.14.0 || >=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/npm/pacote.git"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "windowsCI": false, "publish": "true"}}