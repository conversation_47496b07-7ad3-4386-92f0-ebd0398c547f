!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("@material/auto-init",[],e):"object"==typeof exports?exports["auto-init"]=e():(t.mdc=t.mdc||{},t.mdc["auto-init"]=e())}(this,function(){return n={},o.m=r={184:function(t,e,r){"use strict";var d=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.mdcAutoInit=void 0;var n=r(185),s=n.strings.AUTO_INIT_ATTR,v=n.strings.DATASET_AUTO_INIT_STATE,y=n.strings.INITIALIZED_STATE,m={},o=console.warn.bind(console);function i(t){var e,r;void 0===t&&(t=document);var n=[],o=Array.from(t.querySelectorAll("["+s+"]"));o=o.filter(function(t){return t.dataset[v]!==y});try{for(var i=d(o),u=i.next();!u.done;u=i.next()){var a=u.value,c=a.getAttribute(s);if(!c)throw new Error("(mdc-auto-init) Constructor name must be given.");var f=m[c];if("function"!=typeof f)throw new Error("(mdc-auto-init) Could not find constructor in registry for "+c);var l=f.attachTo(a);Object.defineProperty(a,c,{configurable:!0,enumerable:!1,value:l,writable:!1}),n.push(l),a.dataset[v]=y}}catch(t){e={error:t}}finally{try{u&&!u.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return function(t,e,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(t,{bubbles:r,detail:e}):(n=document.createEvent("CustomEvent")).initCustomEvent(t,r,!1,e),document.dispatchEvent(n)}("MDCAutoInit:End",{}),n}(e.mdcAutoInit=i).register=function(t,e,r){if(void 0===r&&(r=o),"function"!=typeof e)throw new Error("(mdc-auto-init) Invalid Constructor value: "+e+". Expected function.");var n=m[t];n&&r("(mdc-auto-init) Overriding registration for "+t+" with "+e+". Was: "+n),m[t]=e},i.deregister=function(t){delete m[t]},i.deregisterAll=function(){var e,t;try{for(var r=d(Object.keys(m)),n=r.next();!n.done;n=r.next()){var o=n.value;i.deregister(o)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},e.default=i},185:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=void 0,e.strings={AUTO_INIT_ATTR:"data-mdc-auto-init",DATASET_AUTO_INIT_STATE:"mdcAutoInitState",INITIALIZED_STATE:"initialized"}}},o.c=n,o.d=function(t,e,r){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=184);function o(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,o),e.l=!0,e.exports}var r,n});