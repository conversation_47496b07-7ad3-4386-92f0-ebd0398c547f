{"name": "@angular/forms", "version": "17.3.12", "description": "Angular - directives and services for creating forms", "author": "angular", "license": "MIT", "engines": {"node": "^18.13.0 || >=20.9.0"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "17.3.12", "@angular/common": "17.3.12", "@angular/platform-browser": "17.3.12", "rxjs": "^6.5.3 || ^7.4.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/forms"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/forms.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/forms.mjs", "esm": "./esm2022/forms.mjs", "default": "./fesm2022/forms.mjs"}}}