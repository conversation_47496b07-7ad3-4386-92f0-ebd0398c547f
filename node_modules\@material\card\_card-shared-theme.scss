//
// Copyright 2022 Google Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.

// stylelint-disable selector-class-pattern --
// Selector '.mdc-*' should only be used in this project.

@use './mixins' as card;
@use '@material/elevation/elevation-theme';
@use '@material/ripple/ripple-theme';
@use '@material/theme/state';
@use 'sass:map';

$ripple-target: '.mdc-card__ripple';

@mixin theme($theme, $resolver) {
  @include card.shape-radius(map.get($theme, container-shape));
  @include card.fill-color(map.get($theme, container-color));
  .mdc-card {
    &::after {
      // Adjust shape-radius for transparent card border for high-contrast mode;
      @include card.shape-radius(map.get($theme, container-shape));
    }
  }

  @include elevation-theme.overlay-container-color(
    map.get($theme, container-surface-tint-layer-color)
  );

  @include _container-elevation(
    $resolver,
    $shadow-color: map.get($theme, container-shadow-color),
    $map: (
      default: map.get($theme, container-elevation),
      hover: map.get($theme, hover-container-elevation),
      focus: map.get($theme, focus-container-elevation),
      pressed: map.get($theme, pressed-container-elevation)
    )
  );

  .mdc-card__primary-action {
    // States styles
    // TODO(b/191298796): Unique state layer colors for each interactive state is not supported. Ripple mixin currently only uses the hover state layer color and ignores focus and pressed colors.
    @include ripple-theme.theme-styles(
      (
        focus-state-layer-color: map.get($theme, focus-state-layer-color),
        focus-state-layer-opacity: map.get($theme, focus-state-layer-opacity),
        hover-state-layer-color: map.get($theme, hover-state-layer-color),
        hover-state-layer-opacity: map.get($theme, hover-state-layer-opacity),
        pressed-state-layer-color: map.get($theme, pressed-state-layer-color),
        pressed-state-layer-opacity:
          map.get($theme, pressed-state-layer-opacity),
      ),
      $ripple-target: $ripple-target
    );
  }
}

@mixin _container-elevation($resolver, $shadow-color, $map) {
  $elevation-resolver: map.get($resolver, elevation);
  .mdc-card {
    @if state.get-default-state($map) {
      @include elevation-theme.with-resolver(
        $elevation-resolver,
        $elevation: state.get-default-state($map),
        $shadow-color: $shadow-color
      );
    }

    @include ripple-theme.hover() {
      @if state.get-hover-state($map) {
        @include elevation-theme.with-resolver(
          $elevation-resolver,
          $elevation: state.get-hover-state($map),
          $shadow-color: $shadow-color
        );
      }
    }

    @include ripple-theme.focus() {
      @if state.get-focus-state($map) {
        @include elevation-theme.with-resolver(
          $elevation-resolver,
          $elevation: state.get-focus-state($map),
          $shadow-color: $shadow-color
        );
      }
    }

    @include ripple-theme.pressed() {
      @if state.get-pressed-state($map) {
        @include elevation-theme.with-resolver(
          $elevation-resolver,
          $elevation: state.get-pressed-state($map),
          $shadow-color: $shadow-color
        );
      }
    }
  }
}
