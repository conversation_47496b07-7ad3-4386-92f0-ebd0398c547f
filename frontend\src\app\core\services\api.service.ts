import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  private http = inject(HttpClient);
  private readonly baseUrl = environment.apiUrl;

  constructor() {}

  // Generic HTTP methods
  get<T>(endpoint: string, params?: any): Observable<ApiResponse<T>> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        if (params[key] !== null && params[key] !== undefined) {
          httpParams = httpParams.set(key, params[key].toString());
        }
      });
    }

    return this.http.get<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, { params: httpParams });
  }

  post<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    return this.http.post<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data);
  }

  put<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    return this.http.put<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data);
  }

  patch<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    return this.http.patch<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, data);
  }

  delete<T>(endpoint: string): Observable<ApiResponse<T>> {
    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}${endpoint}`);
  }

  // File upload
  upload<T>(endpoint: string, file: File, additionalData?: any): Observable<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    return this.http.post<ApiResponse<T>>(`${this.baseUrl}${endpoint}`, formData);
  }

  // Download file
  downloadFile(endpoint: string, filename?: string): Observable<Blob> {
    const headers = new HttpHeaders({
      'Accept': 'application/octet-stream'
    });

    return this.http.get(`${this.baseUrl}${endpoint}`, {
      headers,
      responseType: 'blob'
    });
  }

  // GraphQL query
  graphql<T>(query: string, variables?: any): Observable<{ data: T }> {
    return this.http.post<{ data: T }>(`${this.baseUrl}/graphql`, {
      query,
      variables
    });
  }

  // Health check
  healthCheck(service?: string): Observable<any> {
    const endpoint = service ? `/${service}/health` : '/health';
    return this.http.get(`${this.baseUrl}${endpoint}`);
  }

  // Utility methods for common patterns
  getPaginated<T>(endpoint: string, pagination: PaginationParams, filters?: any): Observable<ApiResponse<T>> {
    const params = { ...pagination, ...filters };
    return this.get<T>(endpoint, params);
  }

  search<T>(endpoint: string, query: string, limit?: number): Observable<ApiResponse<T>> {
    const params = { q: query, limit };
    return this.get<T>(`${endpoint}/search`, params);
  }
}
