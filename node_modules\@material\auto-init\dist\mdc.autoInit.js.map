{"version": 3, "sources": ["webpack://mdc.[name]/webpack/universalModuleDefinition", "webpack://mdc.[name]/webpack/bootstrap", "webpack://mdc.[name]/./packages/mdc-auto-init/constants.ts", "webpack://mdc.[name]/./packages/mdc-auto-init/index.ts"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,kDAA0C,gCAAgC;AAC1E;AACA;;AAEA;AACA;AACA;AACA,gEAAwD,kBAAkB;AAC1E;AACA,yDAAiD,cAAc;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAyC,iCAAiC;AAC1E,wHAAgH,mBAAmB,EAAE;AACrI;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;;AAGA;AACA;;;;;;;;;;;;;;AC7DG;;;;;;;;;;;;;;;;;;;;;;;;;AAEU,QAAO;AACJ,kBAAsB;AACb,2BAAoB;AAC1B,qBACjB;AAJqB,E;;;;;;;;;;;;;ACFpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOH,sCAAoC;AAE7B,IAAc,iBAAgD,YAAO,QAAvD;IAAyB,0BAAuB,YAAO,QAA9B;IAAmB,oBAAI,YAAO,QAAC;AAW7E,IAAc,WAAiC;AAET;AACtC,IAAkB,eAAU,QAAK,KAAK,KAAU;AAEhD,SAAa,KACQ,WAAc,WAAsB;AAApB;AAAA,uBAAoB;;AACvD,QAAU;AACV,QAAI,OAAkB,gBAAe,YAAE;AAChC,oBAAkB,YAAa;AAC3B,qBAAc;AACf,oBACL;AAHmC,SAA9B;AAIT,WAAM;AACA,gBAAW,SAAY,YAAgB;AACvC,cAAgB,gBAAU,WAAc,cAAO,OAAa;AAClE;AAEO,aAAc,cACxB;AAAC;AAEsE;AAGpE;;;AACH,SAAoB,YAA4B;;AAA3B;AAAA,eAA2B;;AAC9C,QAAgB,aAAM;AACtB,QAAS,QACA,MAAK,KAAK,KAAiB,iBAAc,MAAkB,iBAAM;AACrE,kBAAe,OAChB,UAAK;AAAK,eAAI,KAAQ,QAAyB,6BAAsB;AAAE,KAD9D;;AAGb,aAAmB,uBAAK,iFAAE;AAArB,gBAAU;AACb,gBAAc,WAAO,KAAa,aAAiB;AACnD,gBAAI,CAAS,UAAE;AACb,sBAAM,IAAS,MAAoD;AACpE;AAE8C;AAC/C,gBAAiB,cAAW,SAAW;AACvC,gBAAI,OAAkB,gBAAe,YAAE;AACrC,sBAAM,IAAS,MACX,gEACgB;AACrB;AAEsE;AACG;AACtC;AACpC,gBAAe,YAAc,YAAS,SAAO;AACvC,mBAAe,eAAK,MAAU;AACtB,8BAAM;AACR,4BAAO;AACZ,uBAAW;AACR,0BACP;AALmC;AAM5B,uBAAK,KAAY;AACvB,iBAAQ,QAAyB,2BAAqB;AAC3D;;;;;;;;;;AAEG,SAAkB,mBAAM;AAC5B,WACF;AAAC;AAiCO,sBAAW;AA/BuD;AACpC;AAC3B,YAAS,qBACK;AAC0B;AACrB,WAHP,EAG4B;AAAnB;AAAA,eAAmB;;AACjD,QAAI,OAAkB,gBAAe,YAAE;AACrC,cAAM,IAAS,MAAC,gDACD,cAAwB;AACxC;AACD,QAAmB,gBAAW,SAAgB;AAC9C,QAAiB,eAAE;AACb,aAAC,iDAA4D,2BAClD,0BAA2B;AAC3C;AACO,aAAe,iBACzB;AAAE;AAES,YAAW,aAAG,UAA8B;AACrD,WAAe,SACjB;AAAE;AAEgB;AACP,YAAc,gBAAG;;;AAC1B,aAA4B,kBAAM,OAAK,KAAU,sDAAE;AAA9C,gBAAmB;AACX,wBAAW,WAAgB;AACvC;;;;;;;;;AACH;AAAE;AAE+G;AACjH,kBAA2B,Y", "file": "mdc.autoInit.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"@material/auto-init\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"auto-init\"] = factory();\n\telse\n\t\troot[\"mdc\"] = root[\"mdc\"] || {}, root[\"mdc\"][\"auto-init\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./packages/mdc-auto-init/index.ts\");\n", "/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nexport const strings = {\n  AUTO_INIT_ATTR: 'data-mdc-auto-init',\n  DATASET_AUTO_INIT_STATE: 'mdcAutoInitState',\n  INITIALIZED_STATE: 'initialized',\n};\n", "/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n// tslint:disable:only-arrow-functions\n\nimport {MDCComponent} from '@material/base/component';\nimport {MDCFoundation} from '@material/base/foundation';\n\nimport {strings} from './constants';\n\nconst {AUTO_INIT_ATTR, DATASET_AUTO_INIT_STATE, INITIALIZED_STATE} = strings;\n\n/** MDC Attachable */\nexport interface MDCAttachable extends Function {\n  attachTo(root: HTMLElement): MDCComponent<MDCFoundation>;\n}\n\ninterface InternalComponentRegistry {\n  [key: string]: MDCAttachable;\n}\n\nconst registry: InternalComponentRegistry = {};\n\n// tslint:disable-next-line:no-console\nconst CONSOLE_WARN = console.warn.bind(console);\n\nfunction emit<T extends object>(\n    eventType: string, eventData: T, shouldBubble = false) {\n  let event;\n  if (typeof CustomEvent === 'function') {\n    event = new CustomEvent<T>(eventType, {\n      bubbles: shouldBubble,\n      detail: eventData,\n    });\n  } else {\n    event = document.createEvent('CustomEvent');\n    event.initCustomEvent(eventType, shouldBubble, false, eventData);\n  }\n\n  document.dispatchEvent(event);\n}\n\n/* istanbul ignore next: optional argument is not a branch statement */\n/**\n * Auto-initializes all MDC components on a page.\n */\nfunction mdcAutoInit(root: ParentNode = document) {\n  const components = [];\n  let nodes =\n      Array.from(root.querySelectorAll<HTMLElement>(`[${AUTO_INIT_ATTR}]`));\n  nodes = nodes.filter(\n      (node) => node.dataset[DATASET_AUTO_INIT_STATE] !== INITIALIZED_STATE);\n\n  for (const node of nodes) {\n    const ctorName = node.getAttribute(AUTO_INIT_ATTR);\n    if (!ctorName) {\n      throw new Error('(mdc-auto-init) Constructor name must be given.');\n    }\n\n    // tslint:disable-next-line:enforce-name-casing\n    const Constructor = registry[ctorName];\n    if (typeof Constructor !== 'function') {\n      throw new Error(\n          `(mdc-auto-init) Could not find constructor in registry for ${\n              ctorName}`);\n    }\n\n    // TODO: Should we make an eslint rule for an attachTo() static method?\n    // See https://github.com/Microsoft/TypeScript/issues/14600 for discussion\n    // of static interface support in TS\n    const component = Constructor.attachTo(node);\n    Object.defineProperty(node, ctorName, {\n      configurable: true,\n      enumerable: false,\n      value: component,\n      writable: false,\n    });\n    components.push(component);\n    node.dataset[DATASET_AUTO_INIT_STATE] = INITIALIZED_STATE;\n  }\n\n  emit('MDCAutoInit:End', {});\n  return components;\n}\n\n// Constructor is PascalCased because it is a direct reference to a class,\n// rather than an instance of a class.\nmdcAutoInit.register = function(\n    componentName: string,\n    // tslint:disable-next-line:enforce-name-casing\n    Constructor: MDCAttachable, warn = CONSOLE_WARN) {\n  if (typeof Constructor !== 'function') {\n    throw new Error(`(mdc-auto-init) Invalid Constructor value: ${\n        Constructor}. Expected function.`);\n  }\n  const registryValue = registry[componentName];\n  if (registryValue) {\n    warn(`(mdc-auto-init) Overriding registration for ${componentName} with ${\n        Constructor}. Was: ${registryValue}`);\n  }\n  registry[componentName] = Constructor;\n};\n\nmdcAutoInit.deregister = function(componentName: string) {\n  delete registry[componentName];\n};\n\n/** @nocollapse */\nmdcAutoInit.deregisterAll = function() {\n  for (const componentName of Object.keys(registry)) {\n    mdcAutoInit.deregister(componentName);\n  }\n};\n\n// tslint:disable-next-line:no-default-export Needed for backward compatibility with MDC Web v0.44.0 and earlier.\nexport default mdcAutoInit;\nexport {mdcAutoInit};\n"], "sourceRoot": ""}