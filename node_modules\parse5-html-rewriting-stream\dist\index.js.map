{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../lib/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAc,MAAM,QAAQ,CAAC;AAC1C,OAAO,EACH,SAAS,GAOZ,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAErE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgDG;AACH,MAAM,OAAO,eAAgB,SAAQ,SAAS;IAC1C,wDAAwD;IACxD;QACI,KAAK,CAAC,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5C,CAAC;IAEQ,eAAe,CAAC,KAAa;QAClC,4DAA4D;QAC5D,2DAA2D;QAC3D,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAC7B,OAAO,EAAE,CAAC;IACd,CAAC;IAEO,WAAW,CAAC,QAAwB;QACxC,MAAM,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;QAChE,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,GAAG,iBAAiB,CAAC;QACvD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,GAAG,iBAAiB,CAAC;QAEnD,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IAED,SAAS;IACU,oBAAoB,CAAC,SAAiB,EAAE,KAAe;QACtE,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE;YAC/C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAmB,CAAC,CAAC,CAAC;SAC7D;QAED,2DAA2D;QAC3D,2CAA2C;QAC3C,IAAI,CAAC,uBAAuB,CAAC,eAAe,GAAG,KAAK,CAAC;QACrD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAc;IACK,UAAU,CAAC,SAAiB,EAAE,KAAe;QAC5D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,kBAAmB,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,qEAAqE;IAC9D,WAAW,CAAC,KAAc;QAC7B,IAAI,GAAG,GAAG,aAAa,KAAK,CAAC,IAAI,EAAE,CAAC;QAEpC,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,EAAE;YACzB,GAAG,IAAI,YAAY,KAAK,CAAC,QAAQ,GAAG,CAAC;SACxC;aAAM,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,EAAE;YAChC,GAAG,IAAI,SAAS,CAAC;SACpB;QAED,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,EAAE;YACzB,GAAG,IAAI,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC;SACjC;QAED,GAAG,IAAI,GAAG,CAAC;QAEX,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,iEAAiE;IAC1D,YAAY,CAAC,KAAe;QAC/B,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;QAE9B,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;YAC5B,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;SAC3D;QAED,GAAG,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,+DAA+D;IACxD,UAAU,CAAC,KAAa;QAC3B,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,4DAA4D;IACrD,QAAQ,CAAC,EAAE,IAAI,EAAQ;QAC1B,IAAI,CAAC,IAAI,CACL,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB;YAC1C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,IAAI,CAAC;YAC5D,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CACzB,CAAC;IACN,CAAC;IAED,+DAA+D;IACxD,WAAW,CAAC,KAAc;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;IACtC,CAAC;IAED,sDAAsD;IAC/C,OAAO,CAAC,IAAY;QACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;CACJ"}