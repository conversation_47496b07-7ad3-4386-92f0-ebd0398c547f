!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("@material/animation",[],e):"object"==typeof exports?exports.animation=e():(t.mdc=t.mdc||{},t.mdc.animation=e())}(this,function(){return r={},i.m=n={182:function(t,e,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(t,e,n,r){void 0===r&&(r=n),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[n]}})}:function(t,e,n,r){void 0===r&&(r=n),t[r]=e[n]}),i=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)"default"!==n&&Object.prototype.hasOwnProperty.call(t,n)&&r(e,t,n);return i(e,t),e},a=this&&this.__exportStar||function(t,e){for(var n in t)"default"===n||Object.prototype.hasOwnProperty.call(e,n)||r(e,t,n)};Object.defineProperty(e,"__esModule",{value:!0}),e.util=void 0;var u=o(n(57));e.util=u,a(n(63),e),a(n(183),e),a(n(57),e)},183:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},57:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getCorrectEventName=e.getCorrectPropertyName=void 0;var a={animation:{prefixed:"-webkit-animation",standard:"animation"},transform:{prefixed:"-webkit-transform",standard:"transform"},transition:{prefixed:"-webkit-transition",standard:"transition"}},u={animationend:{cssProperty:"animation",prefixed:"webkitAnimationEnd",standard:"animationend"},animationiteration:{cssProperty:"animation",prefixed:"webkitAnimationIteration",standard:"animationiteration"},animationstart:{cssProperty:"animation",prefixed:"webkitAnimationStart",standard:"animationstart"},transitionend:{cssProperty:"transition",prefixed:"webkitTransitionEnd",standard:"transitionend"}};function c(t){return Boolean(t.document)&&"function"==typeof t.document.createElement}e.getCorrectPropertyName=function(t,e){if(c(t)&&e in a){var n=t.document.createElement("div"),r=a[e],i=r.standard,o=r.prefixed;return i in n.style?i:o}return e},e.getCorrectEventName=function(t,e){if(c(t)&&e in u){var n=t.document.createElement("div"),r=u[e],i=r.standard,o=r.prefixed;return r.cssProperty in n.style?i:o}return e}},63:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AnimationFrame=void 0;var r=(i.prototype.request=function(e,n){var r=this;this.cancel(e);var t=requestAnimationFrame(function(t){r.rafIDs.delete(e),n(t)});this.rafIDs.set(e,t)},i.prototype.cancel=function(t){var e=this.rafIDs.get(t);e&&(cancelAnimationFrame(e),this.rafIDs.delete(t))},i.prototype.cancelAll=function(){var n=this;this.rafIDs.forEach(function(t,e){n.cancel(e)})},i.prototype.getQueue=function(){var n=[];return this.rafIDs.forEach(function(t,e){n.push(e)}),n},i);function i(){this.rafIDs=new Map}e.AnimationFrame=r}},i.c=r,i.d=function(t,e,n){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)i.d(n,r,function(t){return e[t]}.bind(null,r));return n},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=182);function i(t){if(r[t])return r[t].exports;var e=r[t]={i:t,l:!1,exports:{}};return n[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}var n,r});