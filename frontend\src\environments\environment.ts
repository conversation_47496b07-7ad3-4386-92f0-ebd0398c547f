export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000',
  appName: 'Shop Management System',
  version: '1.0.0',
  features: {
    enablePWA: true,
    enableNotifications: true,
    enableOfflineMode: true,
    enableAnalytics: false
  },
  api: {
    timeout: 30000,
    retryAttempts: 3
  },
  auth: {
    tokenKey: 'shop_access_token',
    refreshTokenKey: 'shop_refresh_token',
    tokenExpirationBuffer: 300 // 5 minutes
  },
  ui: {
    defaultPageSize: 10,
    maxPageSize: 100,
    debounceTime: 300
  }
};
