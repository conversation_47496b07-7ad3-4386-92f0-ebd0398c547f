{"name": "copy-anything", "sideEffects": false, "version": "2.0.6", "description": "An optimised way to copy'ing an object. A small and simple integration", "module": "./dist/index.es.js", "main": "./dist/index.cjs", "types": "./dist/types/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs", "types": "./dist/types/index.d.ts"}}, "files": ["dist"], "scripts": {"test": "vitest run", "lint": "tsc --noEmit && eslint ./src --ext .ts", "build": "rollup -c ./scripts/build.js", "release": "npm run lint && del dist && npm run build && np"}, "dependencies": {"is-what": "^3.14.1"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.10.1", "@typescript-eslint/parser": "^5.10.1", "del-cli": "^4.0.1", "eslint": "^8.7.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-tree-shaking": "^1.10.0", "np": "^7.6.0", "prettier": "^2.5.1", "rollup": "^2.66.0", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^4.5.5", "vitest": "^0.2.1"}, "keywords": ["copy", "clone", "json-stringify", "stringify-parse", "object", "copy-objects", "clone-objects", "json-stringify-json-parse", "deep-clone", "deep-copy", "typescript", "ts"], "author": "<PERSON>", "funding": "https://github.com/sponsors/mesqueeb", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/copy-anything/issues"}, "homepage": "https://github.com/mesqueeb/copy-anything#readme", "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/copy-anything.git"}, "np": {"yarn": false, "branch": "legacy"}, "eslintConfig": {"ignorePatterns": ["node_modules", "dist", "scripts", "test"], "root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "tree-shaking"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-ignore": "off", "tree-shaking/no-side-effects-in-initialization": "error", "@typescript-eslint/ban-ts-comment": "off"}}}