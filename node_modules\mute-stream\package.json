{"name": "mute-stream", "version": "1.0.0", "main": "lib/index.js", "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.11.0", "tap": "^16.3.0"}, "scripts": {"test": "tap", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap", "posttest": "npm run lint"}, "repository": {"type": "git", "url": "https://github.com/npm/mute-stream.git"}, "keywords": ["mute", "stream", "pipe"], "author": "GitHub Inc.", "license": "ISC", "description": "Bytes go in, but they don't come out (when muted).", "files": ["bin/", "lib/"], "tap": {"statements": 70, "branches": 60, "functions": 81, "lines": 70, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.11.0"}}