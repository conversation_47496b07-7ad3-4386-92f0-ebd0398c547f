/**
 * Jasmine 2.0 standalone `boot.js` modified for Karma.
 * This file is registered in `index.js`. This version
 * does not include `HtmlReporter` setup.
 */
;(function (global) {
  /* global jasmineRequire */
  'use strict'

  /**
   * Require <PERSON>'s core files. Specifically, this requires and
   * attaches all of <PERSON>'s code to the `jasmine` reference.
   */
  var jasmine = jasmineRequire.core(jasmineRequire)

  /**
   * Obtain the public Jasmine API.
   */
  var jasmineInterface = jasmineRequire.interface(jasmine, jasmine.getEnv())

  /**
   * Setting up timing functions to be able to be overridden.
   * Certain browsers (Safari, IE 8, PhantomJS) require this hack.
   */
  /* eslint-disable no-self-assign */
  global.setTimeout = global.setTimeout
  global.setInterval = global.setInterval
  global.clearTimeout = global.clearTimeout
  global.clearInterval = global.clearInterval
  /* eslint-enable no-self-assign */

  /**
   * Add all of the Jasmine global/public interface to the proper
   * global, so a project can use the public interface directly.
   * For example, calling `describe` in specs instead of
   * `jasmine.getEnv().describe`.
   */
  for (var property in jasmineInterface) {
    if (Object.prototype.hasOwnProperty.call(jasmineInterface, property)) {
      global[property] = jasmineInterface[property]
    }
  }
}(typeof window !== 'undefined' ? window : global))
