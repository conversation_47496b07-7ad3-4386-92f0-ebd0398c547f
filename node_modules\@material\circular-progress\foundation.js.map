{"version": 3, "file": "foundation.js", "sourceRoot": "", "sources": ["foundation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,2BAA2B,CAAC;AAIxD,OAAO,EAAC,UAAU,EAAE,OAAO,EAAC,MAAM,aAAa,CAAC;AAEhD,uCAAuC;AACvC;IACI,iDAAyC;IA2B3C,uCAAY,OAA6C;eACvD,wCAAU,6BAA6B,CAAC,cAAc,GAAK,OAAO,EAAE;IACtE,CAAC;IA3BD,sBAAoB,2CAAU;aAA9B;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAED,sBAAoB,wCAAO;aAA3B;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAoB,+CAAc;aAAlC;YACE,OAAO;gBACL,QAAQ,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACzB,6BAA6B,EAAE,cAAM,OAAA,IAAI,EAAJ,CAAI;gBACzC,QAAQ,EAAE,cAAM,OAAA,KAAK,EAAL,CAAK;gBACrB,WAAW,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC5B,eAAe,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAChC,YAAY,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC7B,6BAA6B,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;aAC/C,CAAC;QACJ,CAAC;;;OAAA;IAWQ,4CAAI,GAAb;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC7D,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;QAC1E,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAElB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,YAAY,CACrB,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,MAAM;YACP,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,6BAA6B,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IAED;;;OAGG;IACH,sDAAc,GAAd,UAAe,WAAoB;QACjC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YACzD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;SACrD;IACH,CAAC;IAED,qDAAa,GAAb;QACE,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,mDAAW,GAAX,UAAY,KAAa;QACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAM,iBAAiB,GACnB,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAEtD,IAAI,CAAC,OAAO,CAAC,6BAA6B,CACtC,OAAO,CAAC,iBAAiB,EAAE,KAAG,iBAAmB,CAAC,CAAC;YACvD,IAAI,CAAC,OAAO,CAAC,YAAY,CACrB,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;SACtD;IACH,CAAC;IAED,mDAAW,GAAX;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,4CAAI,GAAJ;QACE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACH,6CAAK,GAAL;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACH,gDAAQ,GAAR;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACH,oCAAC;AAAD,CAAC,AAjHD,CACI,aAAa,GAgHhB;;AAED,iHAAiH;AACjH,eAAe,6BAA6B,CAAC"}