{
	// See https://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
	// Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
	// List of extensions which should be recommended for users of this workspace.
	"recommendations": [
		"CoenraadS.bracket-pair-colorizer-2",
		"dbaeumer.vscode-eslint",
		"fabiospampinato.vscode-terminals",
		"GitHub.vscode-pull-request-github",
		"VisualStudioExptTeam.vscodeintellicode"
	],
	// List of extensions recommended by VS Code that should not be recommended for users of this workspace.
	"unwantedRecommendations": []
}