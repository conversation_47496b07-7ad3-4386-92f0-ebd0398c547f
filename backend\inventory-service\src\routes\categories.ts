import express, { Request, Response, NextFunction } from 'express';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

/**
 * @swagger
 * /categories:
 *   get:
 *     summary: Get all categories
 *     tags: [Categories]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Categories retrieved successfully
 */
router.get('/', requireAuth, requirePermission('inventory:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { page, limit, search } = req.query;
  const skip = ((page as number) - 1) * (limit as number);

  const where: any = { isActive: true };

  if (search) {
    where.OR = [
      { name: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } },
    ];
  }

  const [categories, total] = await Promise.all([
    prisma.category.findMany({
      where,
      skip,
      take: limit as number,
      include: {
        _count: {
          select: {
            products: true
          }
        }
      },
      orderBy: { name: 'asc' }
    }),
    prisma.category.count({ where })
  ]);

  const categoriesResponse = categories.map(category => ({
    ...category,
    productCount: category._count.products,
  }));

  utils.sendSuccess(res, {
    categories: categoriesResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / (limit as number)),
    }
  });
}));

/**
 * @swagger
 * /categories/{id}:
 *   get:
 *     summary: Get category by ID
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Category retrieved successfully
 *       404:
 *         description: Category not found
 */
router.get('/:id', requireAuth, requirePermission('inventory:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const category = await prisma.category.findUnique({
    where: { id },
    include: {
      products: {
        where: { isActive: true },
        include: {
          stock: true
        }
      },
      _count: {
        select: {
          products: true
        }
      }
    }
  });

  if (!category) {
    utils.sendError(res, 'Category not found', 404);
    return;
  }

  const categoryResponse = {
    ...category,
    productCount: category._count.products,
  };

  utils.sendSuccess(res, { category: categoryResponse });
}));

/**
 * @swagger
 * /categories:
 *   post:
 *     summary: Create new category
 *     tags: [Categories]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: Category created successfully
 */
router.post('/', requireAuth, requirePermission('inventory:create'), validation.validateBody(validation.categoryCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { name, description } = req.body;

  // Check if category already exists
  const existingCategory = await prisma.category.findUnique({
    where: { name }
  });

  if (existingCategory) {
    utils.sendError(res, 'Category with this name already exists', 400);
    return;
  }

  // Create category
  const category = await prisma.category.create({
    data: {
      name,
      description,
    }
  });

  utils.sendSuccess(res, { category }, 'Category created successfully', 201);
}));

/**
 * @swagger
 * /categories/{id}:
 *   put:
 *     summary: Update category
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Category updated successfully
 */
router.put('/:id', requireAuth, requirePermission('inventory:update'), validation.validateParams(validation.idSchema), validation.validateBody(validation.categoryUpdateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const updateData = req.body;

  // Check if category exists
  const existingCategory = await prisma.category.findUnique({
    where: { id }
  });

  if (!existingCategory) {
    utils.sendError(res, 'Category not found', 404);
    return;
  }

  // Check for name conflicts
  if (updateData.name) {
    const conflictCategory = await prisma.category.findFirst({
      where: {
        AND: [
          { id: { not: id } },
          { name: updateData.name }
        ]
      }
    });

    if (conflictCategory) {
      utils.sendError(res, 'Category name already exists', 400);
      return;
    }
  }

  // Update category
  const category = await prisma.category.update({
    where: { id },
    data: updateData,
  });

  utils.sendSuccess(res, { category }, 'Category updated successfully');
}));

/**
 * @swagger
 * /categories/{id}:
 *   delete:
 *     summary: Delete category
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Category deleted successfully
 */
router.delete('/:id', requireAuth, requirePermission('inventory:delete'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Check if category exists
  const category = await prisma.category.findUnique({
    where: { id },
    include: {
      _count: {
        select: {
          products: true
        }
      }
    }
  });

  if (!category) {
    utils.sendError(res, 'Category not found', 404);
    return;
  }

  // Check if category has products
  if (category._count.products > 0) {
    utils.sendError(res, 'Cannot delete category with products. Move products to another category first.', 400);
    return;
  }

  // Soft delete by deactivating
  await prisma.category.update({
    where: { id },
    data: { isActive: false },
  });

  utils.sendSuccess(res, null, 'Category deleted successfully');
}));

/**
 * @swagger
 * /categories/list:
 *   get:
 *     summary: Get simple list of categories for dropdowns
 *     tags: [Categories]
 *     responses:
 *       200:
 *         description: Category list retrieved successfully
 */
router.get('/list', requireAuth, requirePermission('inventory:read'), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const categories = await prisma.category.findMany({
    where: { isActive: true },
    select: {
      id: true,
      name: true,
    },
    orderBy: { name: 'asc' }
  });

  utils.sendSuccess(res, { categories });
}));

export default router;
