export interface Product {
  id: string;
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  price: number;
  cost?: number;
  categoryId: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  category?: Category;
  stock?: Stock;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  products?: Product[];
  productCount?: number;
}

export interface Stock {
  id: string;
  productId: string;
  quantity: number;
  minThreshold: number;
  maxThreshold?: number;
  lastRestocked?: string;
  createdAt: string;
  updatedAt: string;
  product?: Product;
  status?: StockStatus;
  isLowStock?: boolean;
  isOutOfStock?: boolean;
}

export enum StockStatus {
  NORMAL = 'NORMAL',
  LOW = 'LOW',
  OUT = 'OUT'
}

export interface ProductWithStock extends Product {
  stock: Stock;
  stockStatus: StockStatus;
}

export interface ProductFilter {
  search?: string;
  categoryId?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  priceMin?: number;
  priceMax?: number;
}

export interface CategoryFilter {
  search?: string;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ProductConnection {
  products: ProductWithStock[];
  pagination: PaginationInfo;
}

export interface CategoryConnection {
  categories: Category[];
  pagination: PaginationInfo;
}

export interface StockSummary {
  totalProducts: number;
  lowStockCount: number;
  outOfStockCount: number;
  totalQuantity: number;
  totalCostValue: number;
  totalRetailValue: number;
  healthyStockCount: number;
  lowStockPercentage: number;
}

export interface CreateProductRequest {
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  price: number;
  cost?: number;
  categoryId: string;
  initialStock?: number;
  minThreshold?: number;
  maxThreshold?: number;
}

export interface UpdateProductRequest {
  name?: string;
  description?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  cost?: number;
  categoryId?: string;
  isActive?: boolean;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  isActive?: boolean;
}

export interface StockAdjustmentRequest {
  adjustment: number;
  reason: string;
  notes?: string;
}

export interface StockUpdateRequest {
  quantity: number;
  minThreshold?: number;
  maxThreshold?: number;
  reason?: string;
  notes?: string;
}
