# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [15.0.0-canary.7f224ddd4.0](https://github.com/material-components/material-components-web/compare/v14.0.0...v15.0.0-canary.7f224ddd4.0) (2023-12-28)


### Bug Fixes

* **fab:** Attribute `hidden` now correctly hides the radio button. ([c501884](https://github.com/material-components/material-components-web/commit/c5018840c89427aeb4be71472992538ef628c868))


### Features

* **fab:** Add support for container-surface-tint-layer-color ([e340b04](https://github.com/material-components/material-components-web/commit/e340b04c53f1b8db0951fc51a3e368231f39a781))
* **fab:** Make extended-light-theme tokens public ([736b7fd](https://github.com/material-components/material-components-web/commit/736b7fda40fce0e45eb95ca28df7ae9a7426b365))
* **fab:** Make light-theme tokens public ([b281a40](https://github.com/material-components/material-components-web/commit/b281a409a8d63105dcd7b00dd8cd4a3e274e3495))
