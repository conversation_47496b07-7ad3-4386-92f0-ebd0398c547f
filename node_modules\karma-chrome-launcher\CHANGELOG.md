# [3.2.0](https://github.com/karma-runner/karma-chrome-launcher/compare/v3.1.1...v3.2.0) (2023-04-20)


### Features

* add process.env.ProgramW6432 as root location for binaries ([12a73db](https://github.com/karma-runner/karma-chrome-launcher/commit/12a73dba261c78fdba4aff4dbb741ac87adfed01))

## [3.1.1](https://github.com/karma-runner/karma-chrome-launcher/compare/v3.1.0...v3.1.1) (2022-03-09)


### Bug Fixes

* artificially trigger a release ([83fdc3c](https://github.com/karma-runner/karma-chrome-launcher/commit/83fdc3ca5666374c677408ed0589e66b1124bec4))

<a name="3.1.0"></a>
# [3.1.0](https://github.com/karma-runner/karma-chrome-launcher/compare/v3.0.0...v3.1.0) (2019-08-13)


### Features

* add --disable-dev-shm-usage flag to headless ([137005d](https://github.com/karma-runner/karma-chrome-launcher/commit/137005d))



<a name="3.0.0"></a>
# [3.0.0](https://github.com/karma-runner/karma-chrome-launcher/compare/v2.2.0...v3.0.0) (2019-07-12)


### Features

* allow overriding of the default debug port ([26ae9f4](https://github.com/karma-runner/karma-chrome-launcher/commit/26ae9f4)), closes [#187](https://github.com/karma-runner/karma-chrome-launcher/issues/187)



<a name="2.2.0"></a>
# [2.2.0](https://github.com/karma-runner/karma-chrome-launcher/compare/v2.1.1...v2.2.0) (2017-06-23)


### Features

* add ChromiumHeadless ([7a12021](https://github.com/karma-runner/karma-chrome-launcher/commit/7a12021))



<a name="2.1.1"></a>
## [2.1.1](https://github.com/karma-runner/karma-chrome-launcher/compare/v2.1.0...v2.1.1) (2017-05-05)


### Bug Fixes

* add disable-renderer-backgrounding flag ([6bfc170](https://github.com/karma-runner/karma-chrome-launcher/commit/6bfc170)), closes [#123](https://github.com/karma-runner/karma-chrome-launcher/issues/123)



<a name="2.1.0"></a>
# [2.1.0](https://github.com/karma-runner/karma-chrome-launcher/compare/v2.0.0...v2.1.0) (2017-05-02)


### Features

* add support for custom user-data-dir in Chromium ([579fcfc](https://github.com/karma-runner/karma-chrome-launcher/commit/579fcfc)), closes [#89](https://github.com/karma-runner/karma-chrome-launcher/issues/89)
* add support for headless Chrome/ChromeCanary ([7446181](https://github.com/karma-runner/karma-chrome-launcher/commit/7446181))



<a name="2.0.0"></a>
# [2.0.0](https://github.com/karma-runner/karma-chrome-launcher/compare/v1.0.1...v2.0.0) (2016-08-18)


### Features

* Chromium support for Linux, Darwin and Windows ([33e8d82](https://github.com/karma-runner/karma-chrome-launcher/commit/33e8d82))



<a name="1.0.1"></a>
## [1.0.1](https://github.com/karma-runner/karma-chrome-launcher/compare/v0.2.3...v1.0.1) (2016-05-02)




<a name="0.2.3"></a>
## [0.2.3](https://github.com/karma-runner/karma-chrome-launcher/compare/v0.2.1...v0.2.3) (2016-03-22)


### Features

* **config:** Allow --user-data-dir to be set in launcher properties ([2c0b7f4](https://github.com/karma-runner/karma-chrome-launcher/commit/2c0b7f4))



<a name="0.2.2"></a>
## [0.2.2](https://github.com/karma-runner/karma-chrome-launcher/compare/v0.2.1...v0.2.2) (2015-12-01)

### Bug Fixes

* chrome will ignore command line flags it does not yet know about ([426e7c3](https://github.com/karma-runner/karma-chrome-launcher/commit/426e7c3))



<a name="0.2.1"></a>
## [0.2.1](https://github.com/karma-runner/karma-chrome-launcher/compare/v0.2.0...v0.2.1) (2015-10-07)


### Bug Fixes

* **ChromeCanary:** on Linux (archlinux), it can be named google-chrome-unstable ([28cb5b0](https://github.com/karma-runner/karma-chrome-launcher/commit/28cb5b0))



<a name"0.2.0"></a>
## 0.2.0 (2015-06-23)


#### Bug Fixes

* Use fs-access shim for file detection. ([6355ca88](https://github.com/karma-runner/karma-chrome-launcher/commit/6355ca88), closes [#53](https://github.com/karma-runner/karma-chrome-launcher/issues/53))


<a name"0.1.9"></a>
### 0.1.9 (2015-06-23)


<a name"0.1.8"></a>
### 0.1.8 (2015-06-23)


<a name"0.1.7"></a>
### 0.1.7 (2015-06-23)


<a name"0.1.6"></a>
### 0.1.6 (2015-06-23)


#### Bug Fixes

* **jsFlags:** un-quote --js-flags flag and automatically merge with presets ([4509c277](https://github.com/karma-runner/karma-chrome-launcher/commit/4509c277))


<a name"0.1.5"></a>
### 0.1.5 (2015-06-23)


#### Bug Fixes

* better error when DARTIUM_BIN not set ([27462e86](https://github.com/karma-runner/karma-chrome-launcher/commit/27462e86))


<a name"0.1.4"></a>
### 0.1.4 (2015-06-23)


#### Bug Fixes

* the translation is now disabled on launch ([b2f389ba](https://github.com/karma-runner/karma-chrome-launcher/commit/b2f389ba))


<a name"0.1.3"></a>
### 0.1.3 (2015-06-23)


#### Features

* **Launcher:** Add Dartium launcher. ([ae81c0c1](https://github.com/karma-runner/karma-chrome-launcher/commit/ae81c0c1))


<a name"0.1.2"></a>
### 0.1.2 (2015-06-23)


<a name"0.1.12"></a>
### 0.1.12 (2015-06-23)


<a name"0.1.11"></a>
### 0.1.11 (2015-06-23)


<a name"0.1.10"></a>
### 0.1.10 (2015-06-23)


#### Bug Fixes

* better error when DARTIUM_BIN not set ([27462e86](https://github.com/karma-runner/karma-chrome-launcher/commit/27462e86))
* the translation is now disabled on launch ([b2f389ba](https://github.com/karma-runner/karma-chrome-launcher/commit/b2f389ba))
* **jsFlags:** un-quote --js-flags flag and automatically merge with presets ([4509c277](https://github.com/karma-runner/karma-chrome-launcher/commit/4509c277))


#### Features

* disable popup blocking by default ([2cdace9e](https://github.com/karma-runner/karma-chrome-launcher/commit/2cdace9e))
* **Launcher:** Add Dartium launcher. ([ae81c0c1](https://github.com/karma-runner/karma-chrome-launcher/commit/ae81c0c1))


<a name"0.1.1"></a>
### 0.1.1 (2015-06-23)


#### Bug Fixes

* handle Chrome location on Windows ([62df3014](https://github.com/karma-runner/karma-chrome-launcher/commit/62df3014))
* correct Chrome path on Windows ([9ebd9974](https://github.com/karma-runner/karma-chrome-launcher/commit/9ebd9974), closes [#2](https://github.com/karma-runner/karma-chrome-launcher/issues/2))


<a name"0.1.0"></a>
## 0.1.0 (2015-06-23)


<a name"0.0.2"></a>
### 0.0.2 (2015-06-23)


#### Features

* allow passing custom flags ([4ebc7694](https://github.com/karma-runner/karma-chrome-launcher/commit/4ebc7694))
