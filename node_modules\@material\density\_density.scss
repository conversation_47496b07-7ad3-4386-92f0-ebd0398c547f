//
// Copyright 2019 Google Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
//

@use 'sass:list';
@use 'sass:map';
@use 'sass:meta';

$interval: 4px !default;
$minimum-scale: minimum !default;
$maximum-scale: maximum !default;
$supported-scales: (default, minimum, maximum) !default;
$supported-properties: (height, size, margin-top, margin-bottom, top) !default;
$default-scale: 0 !default;

///
/// Returns component property value based on given density config and density scale.
///
/// @param {Map} $density-config - Density configuration for component.
/// @param {Number | String} $density-scale - Density scale value for component. Examples: `-3`, `0` or `minimum`.
/// @param {Map} $property-name - Density scale map for the target component.
///
/// @example
///   mdc-density-prop-value(
///     $density-config: (
///       height: (
///         default: 36px,
///         maximum: 40px,
///         minimum: 24px,
///       ),
///     ),
///     $density-scale: minimum,
///     $property-name: height,
///   )
///   // 24px
///
/// @example
///   mdc-density-prop-value(
///     $density-config: (
///       height: (
///         default: 40px,
///         maximum: 60px,
///         minimum: 24px,
///       ),
///     ),
///     $density-scale: -2,
///     $property-name: height,
///   )
///   // 32px
///
/// @example
///   mdc-density-prop-value(
///     $density-config: (
///       height: (
///         default: 36px,
///         maximum: 40px,
///         minimum: 24px,
///       ),
///       width: (
///         default: 56px,
///         maximum: 64px,
///         minimum: 48px,
///       ),
///     ),
///     $density-scale: minimum,
///     $property-name: width,
///   )
///   // 48px
///
@function prop-value($density-config, $density-scale, $property-name) {
  @if (
    meta.type-of($density-scale) ==
      'string' and
      list.index($list: $supported-scales, $value: $density-scale) ==
      null
  ) {
    @error "mdc-density: Supported density scales #{$supported-scales}, but received #{$density-scale}.";
  }

  @if (
    list.index($list: $supported-properties, $value: $property-name) == null
  ) {
    @error "mdc-density: Supported density properties #{$supported-properties}," +
      "but received #{$property-name}.";
  }

  $value: null;
  $property-scale-map: map.get($density-config, $property-name);

  @if map.has-key($property-scale-map, $density-scale) {
    $value: map.get($property-scale-map, $density-scale);
  } @else {
    $value: map.get($property-scale-map, default) + $density-scale * $interval;
  }

  $min-value: map.get($property-scale-map, $minimum-scale);
  $max-value: map.get($property-scale-map, $maximum-scale);

  @if ($value < $min-value or $value > $max-value) {
    @error "mdc-density: #{$property-name} must be between #{$min-value} and " +
      "#{$max-value} (inclusive), but received #{$value}.";
  }

  @return $value;
}
