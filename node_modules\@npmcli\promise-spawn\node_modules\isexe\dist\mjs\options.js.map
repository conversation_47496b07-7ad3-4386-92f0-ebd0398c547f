{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../src/options.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface IsexeOptions {\n  /**\n   * Ignore errors arising from attempting to get file access status\n   * Note that EACCES is always ignored, because that just means\n   * it's not executable. If this is not set, then attempting to check\n   * the executable-ness of a nonexistent file will raise ENOENT, for\n   * example.\n   */\n  ignoreErrors?: boolean\n\n  /**\n   * effective uid when checking executable mode flags on posix\n   * Defaults to process.getuid()\n   */\n  uid?: number\n\n  /**\n   * effective gid when checking executable mode flags on posix\n   * Defaults to process.getgid()\n   */\n  gid?: number\n\n  /**\n   * effective group ID list to use when checking executable mode flags\n   * on posix\n   * Defaults to process.getgroups()\n   */\n  groups?: number[]\n\n  /**\n   * The ;-delimited path extension list for win32 implementation.\n   * Defaults to process.env.PATHEXT\n   */\n  pathExt?: string\n}\n"]}