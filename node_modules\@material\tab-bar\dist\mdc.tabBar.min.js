!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("@material/tab-bar",[],e):"object"==typeof exports?exports["tab-bar"]=e():(t.mdc=t.mdc||{},t.mdc["tab-bar"]=e())}(this,function(){return n={},o.m=r=[function(t,e,r){"use strict";(function(t){}).call(this,r(20))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapResourceUrl=e.isResourceUrl=e.createResourceUrl=e.TrustedResourceUrl=void 0,r(0);var o=r(4),i=r(9),a=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(t,e){this.privateDoNotAccessOrElseWrappedResourceUrl=t}var s=window.TrustedScriptURL;e.TrustedResourceUrl=null!=s?s:a,e.createResourceUrl=function(t){var e,r=t,n=null===(e=(0,i.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScriptURL(r);return null!=n?n:new a(r,o.secretToken)},e.isResourceUrl=function(t){return t instanceof e.TrustedResourceUrl},e.unwrapResourceUrl=function(t){var e;if(null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.isScriptURL(t))return t;if(t instanceof a)return t.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapHtml=e.isHtml=e.EMPTY_HTML=e.createHtml=e.SafeHtml=void 0,r(0);var n=r(4),o=r(9),i=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(t,e){this.privateDoNotAccessOrElseWrappedHtml=t}function s(t,e){return null!=e?e:new i(t,n.secretToken)}var c=window.TrustedHTML;e.SafeHtml=null!=c?c:i,e.createHtml=function(t){var e,r=t;return s(r,null===(e=(0,o.getTrustedTypesPolicy)())||void 0===e?void 0:e.createHTML(r))},e.EMPTY_HTML=function(){var t;return s("",null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.emptyHTML)}(),e.isHtml=function(t){return t instanceof e.SafeHtml},e.unwrapHtml=function(t){var e;if(null===(e=(0,o.getTrustedTypes)())||void 0===e?void 0:e.isHTML(t))return t;if(t instanceof i)return t.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},function(t,e,r){"use strict";function n(t){var e;try{e=new URL(t)}catch(t){return"https:"}return e.protocol}Object.defineProperty(e,"__esModule",{value:!0}),e.restrictivelySanitizeUrl=e.unwrapUrlOrSanitize=e.sanitizeJavascriptUrl=void 0,r(0);var o=["data:","http:","https:","mailto:","ftp:"];function i(t){if("javascript:"!==n(t))return t}e.sanitizeJavascriptUrl=i,e.unwrapUrlOrSanitize=function(t){return i(t)},e.restrictivelySanitizeUrl=function(t){var e=n(t);return void 0!==e&&-1!==o.indexOf(e.toLowerCase())?t:"about:invalid#zClosurez"}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ensureTokenIsValid=e.secretToken=void 0,e.secretToken={},e.ensureTokenIsValid=function(t){if(t!==e.secretToken)throw new Error("Bad secret")}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapScript=e.isScript=e.EMPTY_SCRIPT=e.createScript=e.SafeScript=void 0,r(0);var n=r(4),o=r(9),i=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(t,e){this.privateDoNotAccessOrElseWrappedScript=t}function s(t,e){return null!=e?e:new i(t,n.secretToken)}var c=window.TrustedScript;e.SafeScript=null!=c?c:i,e.createScript=function(t){var e,r=t;return s(r,null===(e=(0,o.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScript(r))},e.EMPTY_SCRIPT=function(){var t;return s("",null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.emptyScript)}(),e.isScript=function(t){return t instanceof e.SafeScript},e.unwrapScript=function(t){var e;if(null===(e=(0,o.getTrustedTypes)())||void 0===e?void 0:e.isScript(t))return t;if(t instanceof i)return t.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assertIsTemplateObject=void 0,e.assertIsTemplateObject=function(t,e,r){if(!Array.isArray(t)||!Array.isArray(t.raw)||!e&&1!==t.length)throw new TypeError(r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MDCFoundation=void 0;var n=(Object.defineProperty(o,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(o,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(o,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(o,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),o.prototype.init=function(){},o.prototype.destroy=function(){},o);function o(t){void 0===t&&(t={}),this.adapter=t}e.MDCFoundation=n,e.default=n},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapAttributePrefix=e.createAttributePrefix=e.SafeAttributePrefix=void 0,r(0);function i(){}var a=r(4);e.SafeAttributePrefix=i;var s,c=(o(l,s=i),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},l);function l(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=t,r}e.createAttributePrefix=function(t){return new c(t,a.secretToken)},e.unwrapAttributePrefix=function(t){if(t instanceof c)return t.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEST_ONLY=e.getTrustedTypesPolicy=e.getTrustedTypes=void 0;var n,o="google#safe";function i(){var t;return""!==o&&null!==(t=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==t?t:null}e.getTrustedTypes=i,e.getTrustedTypesPolicy=function(){var t,e;if(void 0===n)try{n=null!==(e=null===(t=i())||void 0===t?void 0:t.createPolicy(o,{createHTML:function(t){return t},createScript:function(t){return t},createScriptURL:function(t){return t}}))&&void 0!==e?e:null}catch(t){n=null}return n},e.TEST_ONLY={resetDefaults:function(){n=void 0,o="google#safe"},setTrustedTypesPolicyName:function(t){o=t}}},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyle=e.isStyle=e.createStyle=e.SafeStyle=void 0,r(0);function i(){}var a=r(4);e.SafeStyle=i;var s,c=(o(l,s=i),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},l);function l(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=t,r}e.createStyle=function(t){return new c(t,a.secretToken)},e.isStyle=function(t){return t instanceof c},e.unwrapStyle=function(t){if(t instanceof c)return t.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributePolicyAction=e.SanitizerTable=void 0;var n,o,i=(a.prototype.isAllowedElement=function(t){return"form"!==t.toLowerCase()&&(this.allowedElements.has(t)||this.elementPolicies.has(t))},a.prototype.getAttributePolicy=function(t,e){var r=this.elementPolicies.get(e);return(null==r?void 0:r.has(t))?r.get(t):this.allowedGlobalAttributes.has(t)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(t)||{policyAction:n.DROP}},a);function a(t,e,r,n){this.allowedElements=t,this.elementPolicies=e,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}e.SanitizerTable=i,(o=n=e.AttributePolicyAction||(e.AttributePolicyAction={}))[o.DROP=0]="DROP",o[o.KEEP=1]="KEEP",o[o.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",o[o.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",o[o.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.isStyleSheet=e.createStyleSheet=e.SafeStyleSheet=void 0,r(0);function i(){}var a=r(4);e.SafeStyleSheet=i;var s,c=(o(l,s=i),l.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},l);function l(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=t,r}e.createStyleSheet=function(t){return new c(t,a.secretToken)},e.isStyleSheet=function(t){return t instanceof c},e.unwrapStyleSheet=function(t){if(t instanceof c)return t.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},function(t,e,r){"use strict";var o=this&&this.__makeTemplateObject||function(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t},i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},a=this&&this.__spreadArray||function(t,e){for(var r=0,n=e.length,o=t.length;r<n;r++,o++)t[o]=e[r];return t};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCComponent=void 0;var s=r(17),c=r(18),n=r(7);var l,u,f=(d.attachTo=function(t){return new d(t,new n.MDCFoundation({}))},d.prototype.initialize=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},d.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},d.prototype.initialSyncWithDOM=function(){},d.prototype.destroy=function(){this.foundation.destroy()},d.prototype.listen=function(t,e,r){this.root.addEventListener(t,e,r)},d.prototype.unlisten=function(t,e,r){this.root.removeEventListener(t,e,r)},d.prototype.emit=function(t,e,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(t,{bubbles:r,detail:e}):(n=document.createEvent("CustomEvent")).initCustomEvent(t,r,!1,e),this.root.dispatchEvent(n)},d.prototype.safeSetAttribute=function(t,e,r){if("tabindex"===e.toLowerCase())t.tabIndex=Number(r);else if(0===e.indexOf("data-")){var n=function(t){return String(t).replace(/\-([a-z])/g,function(t,e){return e.toUpperCase()})}(e.replace(/^data-/,""));t.dataset[n]=r}else c.safeElement.setPrefixedAttribute([s.safeAttrPrefix(l=l||o(["aria-"],["aria-"])),s.safeAttrPrefix(u=u||o(["role"],["role"]))],t,e,r)},d);function d(t,e){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=t,this.initialize.apply(this,a([],i(r))),this.foundation=void 0===e?this.getDefaultFoundation():e,this.foundation.init(),this.initialSyncWithDOM()}e.MDCComponent=f,e.default=f},function(t,e,r){"use strict";var p=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},f=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.HtmlSanitizerImpl=void 0,r(0);var n=r(2),o=r(4),h=r(3),c=r(23),v=r(24),i=r(16),y=r(11),a=(s.prototype.sanitizeAssertUnchanged=function(t){this.changes=[];var e=this.sanitize(t);if(0===this.changes.length)return e;throw new Error("")},s.prototype.sanitize=function(t){var e=document.createElement("span");e.appendChild(this.sanitizeToFragment(t));var r=(new XMLSerializer).serializeToString(e);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},s.prototype.sanitizeToFragment=function(t){for(var e=this,r=(0,c.createInertFragment)(t),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(t){return e.nodeFilter(t)},!1),o=n.nextNode(),i=document.createDocumentFragment(),a=i;null!==o;){var s=void 0;if((0,v.isText)(o))s=this.sanitizeTextNode(o);else{if(!(0,v.isElement)(o))throw new Error("Node is not of type text or element");s=this.sanitizeElementNode(o)}if(a.appendChild(s),o=n.firstChild())a=s;else for(;!(o=n.nextSibling())&&(o=n.parentNode());)a=a.parentNode}return i},s.prototype.sanitizeTextNode=function(t){return document.createTextNode(t.data)},s.prototype.sanitizeElementNode=function(t){var e,r,n=(0,v.getNodeName)(t),o=document.createElement(n),i=t.attributes;try{for(var a=p(i),s=a.next();!s.done;s=a.next()){var c=s.value,l=c.name,u=c.value,f=this.sanitizerTable.getAttributePolicy(l,n);if(this.satisfiesAllConditions(f.conditions,i))switch(f.policyAction){case y.AttributePolicyAction.KEEP:o.setAttribute(l,u);break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var d=(0,h.restrictivelySanitizeUrl)(u);d!==u&&this.recordChange("Url in attribute ".concat(l,' was modified during sanitization. Original url:"').concat(u,'" was sanitized to: "').concat(d,'"')),o.setAttribute(l,d);break;case y.AttributePolicyAction.KEEP_AND_NORMALIZE:o.setAttribute(l,u.toLowerCase());break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:o.setAttribute(l,u);break;case y.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(l," was dropped"));break;default:b(f.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(l,"."))}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return o},s.prototype.nodeFilter=function(t){if((0,v.isText)(t))return NodeFilter.FILTER_ACCEPT;if(!(0,v.isElement)(t))return NodeFilter.FILTER_REJECT;var e=(0,v.getNodeName)(t);return null===e?(this.recordChange("Node name was null for node: ".concat(t)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(e)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(e," was dropped")),NodeFilter.FILTER_REJECT)},s.prototype.recordChange=function(t){0===this.changes.length&&this.changes.push("")},s.prototype.satisfiesAllConditions=function(t,e){var r,n,o;if(!t)return!0;try{for(var i=p(t),a=i.next();!a.done;a=i.next()){var s=f(a.value,2),c=s[0],l=s[1],u=null===(o=e.getNamedItem(c))||void 0===o?void 0:o.value;if(u&&!l.has(u))return!1}}catch(t){r={error:t}}finally{try{a&&!a.done&&(n=i.return)&&n.call(i)}finally{if(r)throw r.error}}return!0},s);function s(t,e){this.sanitizerTable=t,this.changes=[],(0,o.ensureTokenIsValid)(e)}e.HtmlSanitizerImpl=a;var l=function(){return new a(i.defaultSanitizerTable,o.secretToken)}();function b(t,e){throw void 0===e&&(e="unexpected value ".concat(t,"!")),new Error(e)}e.sanitizeHtml=function(t){return l.sanitize(t)},e.sanitizeHtmlAssertUnchanged=function(t){return l.sanitizeAssertUnchanged(t)},e.sanitizeHtmlToFragment=function(t){return l.sanitizeToFragment(t)}},function(t,e,r){"use strict";var o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},i=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||((n=n||Array.prototype.slice.call(e,0,o))[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.setPrefixedAttribute=e.buildPrefixedAttributeSetter=e.insertAdjacentHtml=e.setCssText=e.setOuterHtml=e.setInnerHtml=void 0;var a=r(8),s=r(2),n=r(10);function c(t,e,r,n){if(0===t.length)throw new Error("No prefixes are provided");var o=t.map(function(t){return(0,a.unwrapAttributePrefix)(t)}),i=r.toLowerCase();if(o.every(function(t){return 0!==i.indexOf(t)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));e.setAttribute(r,n)}function l(t){if("script"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}e.setInnerHtml=function(t,e){!function(t){return void 0!==t.tagName}(t)||l(t),t.innerHTML=(0,s.unwrapHtml)(e)},e.setOuterHtml=function(t,e){var r=t.parentElement;null!==r&&l(r),t.outerHTML=(0,s.unwrapHtml)(e)},e.setCssText=function(t,e){t.style.cssText=(0,n.unwrapStyle)(e)},e.insertAdjacentHtml=function(t,e,r){var n="beforebegin"===e||"afterend"===e?t.parentElement:t;null!==n&&l(n),t.insertAdjacentHTML(e,(0,s.unwrapHtml)(r))},e.buildPrefixedAttributeSetter=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=i([t],o(e),!1);return function(t,e,r){c(n,t,e,r)}},e.setPrefixedAttribute=c},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defaultSanitizerTable=void 0;var n=r(11);e.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.SafeStyleSheet=e.isStyleSheet=e.unwrapStyle=e.SafeStyle=e.isStyle=e.unwrapScript=e.SafeScript=e.isScript=e.EMPTY_SCRIPT=e.unwrapResourceUrl=e.TrustedResourceUrl=e.isResourceUrl=e.unwrapHtml=e.SafeHtml=e.isHtml=e.EMPTY_HTML=e.unwrapAttributePrefix=e.SafeAttributePrefix=e.safeStyleSheet=e.concatStyleSheets=e.safeStyle=e.concatStyles=e.scriptFromJson=e.safeScriptWithArgs=e.safeScript=e.concatScripts=e.trustedResourceUrl=e.replaceFragment=e.blobUrlFromScript=e.appendParams=e.HtmlSanitizerBuilder=e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.htmlEscape=e.createScriptSrc=e.createScript=e.concatHtmls=e.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(e,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var o=r(22);Object.defineProperty(e,"concatHtmls",{enumerable:!0,get:function(){return o.concatHtmls}}),Object.defineProperty(e,"createScript",{enumerable:!0,get:function(){return o.createScript}}),Object.defineProperty(e,"createScriptSrc",{enumerable:!0,get:function(){return o.createScriptSrc}}),Object.defineProperty(e,"htmlEscape",{enumerable:!0,get:function(){return o.htmlEscape}});var i=r(14);Object.defineProperty(e,"sanitizeHtml",{enumerable:!0,get:function(){return i.sanitizeHtml}}),Object.defineProperty(e,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return i.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(e,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return i.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(e,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var s=r(26);Object.defineProperty(e,"appendParams",{enumerable:!0,get:function(){return s.appendParams}}),Object.defineProperty(e,"blobUrlFromScript",{enumerable:!0,get:function(){return s.blobUrlFromScript}}),Object.defineProperty(e,"replaceFragment",{enumerable:!0,get:function(){return s.replaceFragment}}),Object.defineProperty(e,"trustedResourceUrl",{enumerable:!0,get:function(){return s.trustedResourceUrl}});var c=r(27);Object.defineProperty(e,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(e,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(e,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(e,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var l=r(28);Object.defineProperty(e,"concatStyles",{enumerable:!0,get:function(){return l.concatStyles}}),Object.defineProperty(e,"safeStyle",{enumerable:!0,get:function(){return l.safeStyle}});var u=r(29);Object.defineProperty(e,"concatStyleSheets",{enumerable:!0,get:function(){return u.concatStyleSheets}}),Object.defineProperty(e,"safeStyleSheet",{enumerable:!0,get:function(){return u.safeStyleSheet}});var f=r(8);Object.defineProperty(e,"SafeAttributePrefix",{enumerable:!0,get:function(){return f.SafeAttributePrefix}}),Object.defineProperty(e,"unwrapAttributePrefix",{enumerable:!0,get:function(){return f.unwrapAttributePrefix}});var d=r(2);Object.defineProperty(e,"EMPTY_HTML",{enumerable:!0,get:function(){return d.EMPTY_HTML}}),Object.defineProperty(e,"isHtml",{enumerable:!0,get:function(){return d.isHtml}}),Object.defineProperty(e,"SafeHtml",{enumerable:!0,get:function(){return d.SafeHtml}}),Object.defineProperty(e,"unwrapHtml",{enumerable:!0,get:function(){return d.unwrapHtml}});var p=r(1);Object.defineProperty(e,"isResourceUrl",{enumerable:!0,get:function(){return p.isResourceUrl}}),Object.defineProperty(e,"TrustedResourceUrl",{enumerable:!0,get:function(){return p.TrustedResourceUrl}}),Object.defineProperty(e,"unwrapResourceUrl",{enumerable:!0,get:function(){return p.unwrapResourceUrl}});var h=r(5);Object.defineProperty(e,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return h.EMPTY_SCRIPT}}),Object.defineProperty(e,"isScript",{enumerable:!0,get:function(){return h.isScript}}),Object.defineProperty(e,"SafeScript",{enumerable:!0,get:function(){return h.SafeScript}}),Object.defineProperty(e,"unwrapScript",{enumerable:!0,get:function(){return h.unwrapScript}});var v=r(10);Object.defineProperty(e,"isStyle",{enumerable:!0,get:function(){return v.isStyle}}),Object.defineProperty(e,"SafeStyle",{enumerable:!0,get:function(){return v.SafeStyle}}),Object.defineProperty(e,"unwrapStyle",{enumerable:!0,get:function(){return v.unwrapStyle}});var y=r(12);Object.defineProperty(e,"isStyleSheet",{enumerable:!0,get:function(){return y.isStyleSheet}}),Object.defineProperty(e,"SafeStyleSheet",{enumerable:!0,get:function(){return y.SafeStyleSheet}}),Object.defineProperty(e,"unwrapStyleSheet",{enumerable:!0,get:function(){return y.unwrapStyleSheet}})},function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(e,r);o&&("get"in o?e.__esModule:!o.writable&&!o.configurable)||(o={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,n,o)}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&n(e,t,r);return o(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.safeWorker=e.safeWindow=e.safeServiceWorkerContainer=e.safeRange=e.safeLocation=e.safeGlobal=e.safeDomParser=e.safeDocument=e.safeStyleEl=e.safeScriptEl=e.safeObjectEl=e.safeLinkEl=e.safeInputEl=e.safeIframeEl=e.safeFormEl=e.safeEmbedEl=e.safeElement=e.safeButtonEl=e.safeAreaEl=e.safeAnchorEl=void 0,e.safeAnchorEl=i(r(30)),e.safeAreaEl=i(r(31)),e.safeButtonEl=i(r(32)),e.safeElement=i(r(15)),e.safeEmbedEl=i(r(33)),e.safeFormEl=i(r(34)),e.safeIframeEl=i(r(35)),e.safeInputEl=i(r(36)),e.safeLinkEl=i(r(37)),e.safeObjectEl=i(r(38)),e.safeScriptEl=i(r(39)),e.safeStyleEl=i(r(40)),e.safeDocument=i(r(41)),e.safeDomParser=i(r(42)),e.safeGlobal=i(r(43)),e.safeLocation=i(r(44)),e.safeRange=i(r(45)),e.safeServiceWorkerContainer=i(r(46)),e.safeWindow=i(r(47)),e.safeWorker=i(r(48))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);e.safeAttrPrefix=function(t){var e=t[0].toLowerCase();return(0,n.createAttributePrefix)(e)}},function(t,e){var r,n,o=t.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(r===setTimeout)return setTimeout(e,0);if((r===i||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:i}catch(t){r=i}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(t){n=a}}();var c,l=[],u=!1,f=-1;function d(){u&&c&&(u=!1,c.length?l=c.concat(l):f=-1,l.length&&p())}function p(){if(!u){var t=s(d);u=!0;for(var e=l.length;e;){for(c=l,l=[];++f<e;)c&&c[f].run();f=-1,e=l.length}c=null,u=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(t)}}function h(t,e){this.fun=t,this.array=e}function v(){}o.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];l.push(new h(t,e)),1!==l.length||u||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=v,o.addListener=v,o.once=v,o.off=v,o.removeListener=v,o.removeAllListeners=v,o.emit=v,o.prependListener=v,o.prependOnceListener=v,o.listeners=function(t){return[]},o.binding=function(t){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(t){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SECURITY_SENSITIVE_ATTRIBUTES=void 0,e.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatHtmls=e.createScriptSrc=e.createScript=e.htmlEscape=void 0;var i=r(2),a=r(1),o=r(5);function s(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}e.htmlEscape=function(t,e){void 0===e&&(e={});var r=s(t);return e.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),e.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),e.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,i.createHtml)(r)},e.createScript=function(t,e){void 0===e&&(e={});var r=(0,o.unwrapScript)(t).toString(),n="<script";return e.id&&(n+=' id="'.concat(s(e.id),'"')),e.nonce&&(n+=' nonce="'.concat(s(e.nonce),'"')),e.type&&(n+=' type="'.concat(s(e.type),'"')),n+=">".concat(r,"<\/script>"),(0,i.createHtml)(n)},e.createScriptSrc=function(t,e,r){var n=(0,a.unwrapResourceUrl)(t).toString(),o='<script src="'.concat(s(n),'"');return e&&(o+=" async"),r&&(o+=' nonce="'.concat(s(r),'"')),o+="><\/script>",(0,i.createHtml)(o)},e.concatHtmls=function(t){return(0,i.createHtml)(t.map(i.unwrapHtml).join(""))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createInertFragment=void 0;var n=r(15),o=r(2);e.createInertFragment=function(t){var e=document.createElement("template"),r=(0,o.createHtml)(t);return(0,n.setInnerHtml)(e,r),e.content}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isElement=e.isText=e.getNodeName=void 0,e.getNodeName=function(t){var e=t.nodeName;return"string"==typeof e?e:"FORM"},e.isText=function(t){return t.nodeType===Node.TEXT_NODE},e.isElement=function(t){var e=t.nodeType;return e===Node.ELEMENT_NODE||"number"!=typeof e}},function(t,e,r){"use strict";var A=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},E=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlSanitizerBuilder=void 0;var n=r(4),o=r(14),i=r(16),O=r(11),a=(s.prototype.onlyAllowElements=function(t){var e,r,n=new Set,o=new Map;try{for(var i=A(t),a=i.next();!a.done;a=i.next()){var s=a.value;if(s=s.toUpperCase(),!this.sanitizerTable.isAllowedElement(s))throw new Error("Element: ".concat(s,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(s);void 0!==c?o.set(s,c):n.add(s)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}return this.sanitizerTable=new O.SanitizerTable(n,o,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},s.prototype.onlyAllowAttributes=function(t){var e,r,n,o,i,a,s=new Set,c=new Map,l=new Map;try{for(var u=A(t),f=u.next();!f.done;f=u.next()){var d=f.value;this.sanitizerTable.allowedGlobalAttributes.has(d)&&s.add(d),this.sanitizerTable.globalAttributePolicies.has(d)&&c.set(d,this.sanitizerTable.globalAttributePolicies.get(d))}}catch(t){e={error:t}}finally{try{f&&!f.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}try{for(var p=A(this.sanitizerTable.elementPolicies.entries()),h=p.next();!h.done;h=p.next()){var v=E(h.value,2),y=v[0],b=v[1],m=new Map;try{for(var S=(i=void 0,A(b.entries())),g=S.next();!g.done;g=S.next()){var T=E(g.value,2),_=(d=T[0],T[1]);t.has(d)&&m.set(d,_)}}catch(t){i={error:t}}finally{try{g&&!g.done&&(a=S.return)&&a.call(S)}finally{if(i)throw i.error}}l.set(y,m)}}catch(t){n={error:t}}finally{try{h&&!h.done&&(o=p.return)&&o.call(p)}finally{if(n)throw n.error}}return this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,l,s,c),this},s.prototype.allowDataAttributes=function(t){var e,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var o=A(t),i=o.next();!i.done;i=o.next()){var a=i.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));n.add(a)}}catch(t){e={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},s.prototype.allowStyleAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("style",{policyAction:O.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowClassAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("class",{policyAction:O.AttributePolicyAction.KEEP}),this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowIdAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("id",{policyAction:O.AttributePolicyAction.KEEP}),this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new o.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},s);function s(){this.calledBuild=!1,this.sanitizerTable=i.defaultSanitizerTable}e.HtmlSanitizerBuilder=a},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.blobUrlFromScript=e.replaceFragment=e.appendParams=e.trustedResourceUrl=void 0,r(0);var s=r(1),n=r(5);r(6);e.trustedResourceUrl=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(0===e.length)return(0,s.createResourceUrl)(t[0]);t[0].toLowerCase();for(var n=[t[0]],o=0;o<e.length;o++)n.push(encodeURIComponent(e[o])),n.push(t[o+1]);return(0,s.createResourceUrl)(n.join(""))},e.appendParams=function(t,e){var i=(0,s.unwrapResourceUrl)(t).toString();if(/#/.test(i)){throw new Error("")}var a=/\?/.test(i)?"&":"?";return e.forEach(function(t,e){for(var r=t instanceof Array?t:[t],n=0;n<r.length;n++){var o=r[n];null!=o&&(i+=a+encodeURIComponent(e)+"="+encodeURIComponent(String(o)),a="&")}}),(0,s.createResourceUrl)(i)};var o=/[^#]*/;e.replaceFragment=function(t,e){var r=(0,s.unwrapResourceUrl)(t).toString();return(0,s.createResourceUrl)(o.exec(r)[0]+"#"+e)},e.blobUrlFromScript=function(t){var e=(0,n.unwrapScript)(t).toString(),r=new Blob([e],{type:"text/javascript"});return(0,s.createResourceUrl)(URL.createObjectURL(r))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeScriptWithArgs=e.scriptFromJson=e.concatScripts=e.safeScript=void 0,r(0);var o=r(5);r(6);function i(t){return(0,o.createScript)(JSON.stringify(t).replace(/</g,"\\x3c"))}e.safeScript=function(t){return(0,o.createScript)(t[0])},e.concatScripts=function(t){return(0,o.createScript)(t.map(o.unwrapScript).join(""))},e.scriptFromJson=i,e.safeScriptWithArgs=function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=t.map(function(t){return i(t).toString()});return(0,o.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyles=e.safeStyle=void 0,r(0);r(6);var n=r(10);e.safeStyle=function(t){var e=t[0];return(0,n.createStyle)(e)},e.concatStyles=function(t){return(0,n.createStyle)(t.map(n.unwrapStyle).join(""))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyleSheets=e.safeStyleSheet=void 0,r(0);r(6);var n=r(12);e.safeStyleSheet=function(t){var e=t[0];return(0,n.createStyleSheet)(e)},e.concatStyleSheets=function(t){return(0,n.createStyleSheet)(t.map(n.unwrapStyleSheet).join(""))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var n=r(3);e.setFormaction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=void 0;var n=r(1);e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setAction=void 0;var n=r(3);e.setAction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.action=r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrcdoc=e.setSrc=void 0;var n=r(2),o=r(1);e.setSrc=function(t,e){t.src=(0,o.unwrapResourceUrl)(e).toString()},e.setSrcdoc=function(t,e){t.srcdoc=(0,n.unwrapHtml)(e)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var n=r(3);e.setFormaction=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHrefAndRel=void 0;var o=r(3),i=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];e.setHrefAndRel=function(t,e,r){if(e instanceof i.TrustedResourceUrl)t.href=(0,i.unwrapResourceUrl)(e).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,o.unwrapUrlOrSanitize)(e);if(void 0===n)return;t.href=n}t.rel=r}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setData=void 0;var n=r(1);e.setData=function(t,e){t.data=(0,n.unwrapResourceUrl)(e)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=e.setTextContent=void 0;var n=r(1),o=r(5);function i(t){var e=function(t){var e,r=t.document,n=null===(e=r.querySelector)||void 0===e?void 0:e.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(t.ownerDocument&&t.ownerDocument.defaultView||window);e&&t.setAttribute("nonce",e)}e.setTextContent=function(t,e){t.textContent=(0,o.unwrapScript)(e),i(t)},e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e),i(t)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setTextContent=void 0;var n=r(12);e.setTextContent=function(t,e){t.textContent=(0,n.unwrapStyleSheet)(e)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.execCommandInsertHtml=e.execCommand=e.write=void 0;var i=r(2);e.write=function(t,e){t.write((0,i.unwrapHtml)(e))},e.execCommand=function(t,e,r){var n=String(e),o=r;return"inserthtml"===n.toLowerCase()&&(o=(0,i.unwrapHtml)(r)),t.execCommand(n,!1,o)},e.execCommandInsertHtml=function(t,e){return t.execCommand("insertHTML",!1,(0,i.unwrapHtml)(e))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseFromString=e.parseHtml=void 0;var n=r(2);function o(t,e,r){return t.parseFromString((0,n.unwrapHtml)(e),r)}e.parseHtml=function(t,e){return o(t,e,"text/html")},e.parseFromString=o},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.globalEval=void 0;var o=r(5);e.globalEval=function(t,e){var r=(0,o.unwrapScript)(e),n=t.eval(r);return n===r&&(n=t.eval(r.toString())),n}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assign=e.replace=e.setHref=void 0;var n=r(3);e.setHref=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)},e.replace=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&t.replace(r)},e.assign=function(t,e){var r=(0,n.unwrapUrlOrSanitize)(e);void 0!==r&&t.assign(r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createContextualFragment=void 0;var n=r(2);e.createContextualFragment=function(t,e){return t.createContextualFragment((0,n.unwrapHtml)(e))}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.register=void 0;var n=r(1);e.register=function(t,e,r){return t.register((0,n.unwrapResourceUrl)(e),r)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.open=void 0;var i=r(3);e.open=function(t,e,r,n){var o=(0,i.unwrapUrlOrSanitize)(e);return void 0!==o?t.open(o,r,n):null}},function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a},o=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var n,o=0,i=e.length;o<i;o++)!n&&o in e||((n=n||Array.prototype.slice.call(e,0,o))[o]=e[o]);return t.concat(n||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.importScripts=e.createShared=e.create=void 0;var i=r(1);e.create=function(t,e){return new Worker((0,i.unwrapResourceUrl)(t),e)},e.createShared=function(t,e){return new SharedWorker((0,i.unwrapResourceUrl)(t),e)},e.importScripts=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.importScripts.apply(t,o([],n(e.map(function(t){return(0,i.unwrapResourceUrl)(t)})),!1))}},function(t,e,r){"use strict";function n(t,e){return(t.matches||t.webkitMatchesSelector||t.msMatchesSelector).call(t,e)}Object.defineProperty(e,"__esModule",{value:!0}),e.estimateScrollWidth=e.matches=e.closest=void 0,e.closest=function(t,e){if(t.closest)return t.closest(e);for(var r=t;r;){if(n(r,e))return r;r=r.parentElement}return null},e.matches=n,e.estimateScrollWidth=function(t){var e=t;if(null!==e.offsetParent)return e.scrollWidth;var r=e.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},function(t,e,r){"use strict";var a;Object.defineProperty(e,"__esModule",{value:!0}),e.getNormalizedEventCoords=e.supportsCssVariables=void 0,e.supportsCssVariables=function(t,e){void 0===e&&(e=!1);var r,n=t.CSS;if("boolean"==typeof a&&!e)return a;if(!(n&&"function"==typeof n.supports))return!1;var o=n.supports("--css-vars","yes"),i=n.supports("(--css-vars: yes)")&&n.supports("color","#00000000");return r=o||i,e||(a=r),r},e.getNormalizedEventCoords=function(t,e,r){if(!t)return{x:0,y:0};var n,o,i=e.x,a=e.y,s=i+r.left,c=a+r.top;if("touchstart"===t.type){var l=t;n=l.changedTouches[0].pageX-s,o=l.changedTouches[0].pageY-c}else{var u=t;n=u.pageX-s,o=u.pageY-c}return{x:n,y:o}}},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},a=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCRippleFoundation=void 0;var s,c=r(7),l=r(54),u=r(50),f=["touchstart","pointerdown","mousedown","keydown"],d=["touchend","pointerup","mouseup","contextmenu"],p=[],h=(s=c.MDCFoundation,o(v,s),Object.defineProperty(v,"cssClasses",{get:function(){return l.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(v,"strings",{get:function(){return l.strings},enumerable:!1,configurable:!0}),Object.defineProperty(v,"numbers",{get:function(){return l.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(v,"defaultAdapter",{get:function(){return{addClass:function(){},browserSupportsCssVars:function(){return!0},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},containsEventTarget:function(){return!0},deregisterDocumentInteractionHandler:function(){},deregisterInteractionHandler:function(){},deregisterResizeHandler:function(){},getWindowPageOffset:function(){return{x:0,y:0}},isSurfaceActive:function(){return!0},isSurfaceDisabled:function(){return!0},isUnbounded:function(){return!0},registerDocumentInteractionHandler:function(){},registerInteractionHandler:function(){},registerResizeHandler:function(){},removeClass:function(){},updateCssVariable:function(){}}},enumerable:!1,configurable:!0}),v.prototype.init=function(){var t=this,e=this.supportsPressRipple();if(this.registerRootHandlers(e),e){var r=v.cssClasses,n=r.ROOT,o=r.UNBOUNDED;requestAnimationFrame(function(){t.adapter.addClass(n),t.adapter.isUnbounded()&&(t.adapter.addClass(o),t.layoutInternal())})}},v.prototype.destroy=function(){var t=this;if(this.supportsPressRipple()){this.activationTimer&&(clearTimeout(this.activationTimer),this.activationTimer=0,this.adapter.removeClass(v.cssClasses.FG_ACTIVATION)),this.fgDeactivationRemovalTimer&&(clearTimeout(this.fgDeactivationRemovalTimer),this.fgDeactivationRemovalTimer=0,this.adapter.removeClass(v.cssClasses.FG_DEACTIVATION));var e=v.cssClasses,r=e.ROOT,n=e.UNBOUNDED;requestAnimationFrame(function(){t.adapter.removeClass(r),t.adapter.removeClass(n),t.removeCssVars()})}this.deregisterRootHandlers(),this.deregisterDeactivationHandlers()},v.prototype.activate=function(t){this.activateImpl(t)},v.prototype.deactivate=function(){this.deactivateImpl()},v.prototype.layout=function(){var t=this;this.layoutFrame&&cancelAnimationFrame(this.layoutFrame),this.layoutFrame=requestAnimationFrame(function(){t.layoutInternal(),t.layoutFrame=0})},v.prototype.setUnbounded=function(t){var e=v.cssClasses.UNBOUNDED;t?this.adapter.addClass(e):this.adapter.removeClass(e)},v.prototype.handleFocus=function(){var t=this;requestAnimationFrame(function(){t.adapter.addClass(v.cssClasses.BG_FOCUSED)})},v.prototype.handleBlur=function(){var t=this;requestAnimationFrame(function(){t.adapter.removeClass(v.cssClasses.BG_FOCUSED)})},v.prototype.supportsPressRipple=function(){return this.adapter.browserSupportsCssVars()},v.prototype.defaultActivationState=function(){return{activationEvent:void 0,hasDeactivationUXRun:!1,isActivated:!1,isProgrammatic:!1,wasActivatedByPointer:!1,wasElementMadeActive:!1}},v.prototype.registerRootHandlers=function(t){var e,r;if(t){try{for(var n=a(f),o=n.next();!o.done;o=n.next()){var i=o.value;this.adapter.registerInteractionHandler(i,this.activateHandler)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}this.adapter.isUnbounded()&&this.adapter.registerResizeHandler(this.resizeHandler)}this.adapter.registerInteractionHandler("focus",this.focusHandler),this.adapter.registerInteractionHandler("blur",this.blurHandler)},v.prototype.registerDeactivationHandlers=function(t){var e,r;if("keydown"===t.type)this.adapter.registerInteractionHandler("keyup",this.deactivateHandler);else try{for(var n=a(d),o=n.next();!o.done;o=n.next()){var i=o.value;this.adapter.registerDocumentInteractionHandler(i,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},v.prototype.deregisterRootHandlers=function(){var e,t;try{for(var r=a(f),n=r.next();!n.done;n=r.next()){var o=n.value;this.adapter.deregisterInteractionHandler(o,this.activateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}this.adapter.deregisterInteractionHandler("focus",this.focusHandler),this.adapter.deregisterInteractionHandler("blur",this.blurHandler),this.adapter.isUnbounded()&&this.adapter.deregisterResizeHandler(this.resizeHandler)},v.prototype.deregisterDeactivationHandlers=function(){var e,t;this.adapter.deregisterInteractionHandler("keyup",this.deactivateHandler);try{for(var r=a(d),n=r.next();!n.done;n=r.next()){var o=n.value;this.adapter.deregisterDocumentInteractionHandler(o,this.deactivateHandler)}}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}},v.prototype.removeCssVars=function(){var e=this,r=v.strings;Object.keys(r).forEach(function(t){0===t.indexOf("VAR_")&&e.adapter.updateCssVariable(r[t],null)})},v.prototype.activateImpl=function(t){var e=this;if(!this.adapter.isSurfaceDisabled()){var r=this.activationState;if(!r.isActivated){var n=this.previousActivationEvent;n&&void 0!==t&&n.type!==t.type||(r.isActivated=!0,r.isProgrammatic=void 0===t,r.activationEvent=t,r.wasActivatedByPointer=!r.isProgrammatic&&void 0!==t&&("mousedown"===t.type||"touchstart"===t.type||"pointerdown"===t.type),void 0!==t&&0<p.length&&p.some(function(t){return e.adapter.containsEventTarget(t)})?this.resetActivationState():(void 0!==t&&(p.push(t.target),this.registerDeactivationHandlers(t)),r.wasElementMadeActive=this.checkElementMadeActive(t),r.wasElementMadeActive&&this.animateActivation(),requestAnimationFrame(function(){p=[],r.wasElementMadeActive||void 0===t||" "!==t.key&&32!==t.keyCode||(r.wasElementMadeActive=e.checkElementMadeActive(t),r.wasElementMadeActive&&e.animateActivation()),r.wasElementMadeActive||(e.activationState=e.defaultActivationState())})))}}},v.prototype.checkElementMadeActive=function(t){return void 0===t||"keydown"!==t.type||this.adapter.isSurfaceActive()},v.prototype.animateActivation=function(){var t=this,e=v.strings,r=e.VAR_FG_TRANSLATE_START,n=e.VAR_FG_TRANSLATE_END,o=v.cssClasses,i=o.FG_DEACTIVATION,a=o.FG_ACTIVATION,s=v.numbers.DEACTIVATION_TIMEOUT_MS;this.layoutInternal();var c="",l="";if(!this.adapter.isUnbounded()){var u=this.getFgTranslationCoordinates(),f=u.startPoint,d=u.endPoint;c=f.x+"px, "+f.y+"px",l=d.x+"px, "+d.y+"px"}this.adapter.updateCssVariable(r,c),this.adapter.updateCssVariable(n,l),clearTimeout(this.activationTimer),clearTimeout(this.fgDeactivationRemovalTimer),this.rmBoundedActivationClasses(),this.adapter.removeClass(i),this.adapter.computeBoundingRect(),this.adapter.addClass(a),this.activationTimer=setTimeout(function(){t.activationTimerCallback()},s)},v.prototype.getFgTranslationCoordinates=function(){var t,e=this.activationState,r=e.activationEvent;return{startPoint:t={x:(t=e.wasActivatedByPointer?u.getNormalizedEventCoords(r,this.adapter.getWindowPageOffset(),this.adapter.computeBoundingRect()):{x:this.frame.width/2,y:this.frame.height/2}).x-this.initialSize/2,y:t.y-this.initialSize/2},endPoint:{x:this.frame.width/2-this.initialSize/2,y:this.frame.height/2-this.initialSize/2}}},v.prototype.runDeactivationUXLogicIfReady=function(){var t=this,e=v.cssClasses.FG_DEACTIVATION,r=this.activationState,n=r.hasDeactivationUXRun,o=r.isActivated;!n&&o||!this.activationAnimationHasEnded||(this.rmBoundedActivationClasses(),this.adapter.addClass(e),this.fgDeactivationRemovalTimer=setTimeout(function(){t.adapter.removeClass(e)},l.numbers.FG_DEACTIVATION_MS))},v.prototype.rmBoundedActivationClasses=function(){var t=v.cssClasses.FG_ACTIVATION;this.adapter.removeClass(t),this.activationAnimationHasEnded=!1,this.adapter.computeBoundingRect()},v.prototype.resetActivationState=function(){var t=this;this.previousActivationEvent=this.activationState.activationEvent,this.activationState=this.defaultActivationState(),setTimeout(function(){return t.previousActivationEvent=void 0},v.numbers.TAP_DELAY_MS)},v.prototype.deactivateImpl=function(){var t=this,e=this.activationState;if(e.isActivated){var r=i({},e);e.isProgrammatic?(requestAnimationFrame(function(){t.animateDeactivation(r)}),this.resetActivationState()):(this.deregisterDeactivationHandlers(),requestAnimationFrame(function(){t.activationState.hasDeactivationUXRun=!0,t.animateDeactivation(r),t.resetActivationState()}))}},v.prototype.animateDeactivation=function(t){var e=t.wasActivatedByPointer,r=t.wasElementMadeActive;(e||r)&&this.runDeactivationUXLogicIfReady()},v.prototype.layoutInternal=function(){var t=this;this.frame=this.adapter.computeBoundingRect();var e=Math.max(this.frame.height,this.frame.width);this.maxRadius=this.adapter.isUnbounded()?e:Math.sqrt(Math.pow(t.frame.width,2)+Math.pow(t.frame.height,2))+v.numbers.PADDING;var r=Math.floor(e*v.numbers.INITIAL_ORIGIN_SCALE);this.adapter.isUnbounded()&&r%2!=0?this.initialSize=r-1:this.initialSize=r,this.fgScale=""+this.maxRadius/this.initialSize,this.updateLayoutCssVars()},v.prototype.updateLayoutCssVars=function(){var t=v.strings,e=t.VAR_FG_SIZE,r=t.VAR_LEFT,n=t.VAR_TOP,o=t.VAR_FG_SCALE;this.adapter.updateCssVariable(e,this.initialSize+"px"),this.adapter.updateCssVariable(o,this.fgScale),this.adapter.isUnbounded()&&(this.unboundedCoords={left:Math.round(this.frame.width/2-this.initialSize/2),top:Math.round(this.frame.height/2-this.initialSize/2)},this.adapter.updateCssVariable(r,this.unboundedCoords.left+"px"),this.adapter.updateCssVariable(n,this.unboundedCoords.top+"px"))},v);function v(t){var e=s.call(this,i(i({},v.defaultAdapter),t))||this;return e.activationAnimationHasEnded=!1,e.activationTimer=0,e.fgDeactivationRemovalTimer=0,e.fgScale="0",e.frame={width:0,height:0},e.initialSize=0,e.layoutFrame=0,e.maxRadius=0,e.unboundedCoords={left:0,top:0},e.activationState=e.defaultActivationState(),e.activationTimerCallback=function(){e.activationAnimationHasEnded=!0,e.runDeactivationUXLogicIfReady()},e.activateHandler=function(t){e.activateImpl(t)},e.deactivateHandler=function(){e.deactivateImpl()},e.focusHandler=function(){e.handleFocus()},e.blurHandler=function(){e.handleBlur()},e.resizeHandler=function(){e.layout()},e}e.MDCRippleFoundation=h,e.default=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.applyPassive=void 0,e.applyPassive=function(t){return void 0===t&&(t=window),!!function(t){void 0===t&&(t=window);var e=!1;try{var r={get passive(){return!(e=!0)}},n=function(){};t.document.addEventListener("test",n,r),t.document.removeEventListener("test",n,r)}catch(t){e=!1}return e}(t)&&{passive:!0}}},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),a=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),s=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&i(e,t,r);return a(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCRipple=void 0;var c,l=r(13),u=r(52),f=r(49),d=r(51),p=s(r(50)),h=(c=l.MDCComponent,o(v,c),v.attachTo=function(t,e){void 0===e&&(e={isUnbounded:void 0});var r=new v(t);return void 0!==e.isUnbounded&&(r.unbounded=e.isUnbounded),r},v.createAdapter=function(r){return{addClass:function(t){r.root.classList.add(t)},browserSupportsCssVars:function(){return p.supportsCssVariables(window)},computeBoundingRect:function(){return r.root.getBoundingClientRect()},containsEventTarget:function(t){return r.root.contains(t)},deregisterDocumentInteractionHandler:function(t,e){document.documentElement.removeEventListener(t,e,u.applyPassive())},deregisterInteractionHandler:function(t,e){r.root.removeEventListener(t,e,u.applyPassive())},deregisterResizeHandler:function(t){window.removeEventListener("resize",t)},getWindowPageOffset:function(){return{x:window.pageXOffset,y:window.pageYOffset}},isSurfaceActive:function(){return f.matches(r.root,":active")},isSurfaceDisabled:function(){return Boolean(r.disabled)},isUnbounded:function(){return Boolean(r.unbounded)},registerDocumentInteractionHandler:function(t,e){document.documentElement.addEventListener(t,e,u.applyPassive())},registerInteractionHandler:function(t,e){r.root.addEventListener(t,e,u.applyPassive())},registerResizeHandler:function(t){window.addEventListener("resize",t)},removeClass:function(t){r.root.classList.remove(t)},updateCssVariable:function(t,e){r.root.style.setProperty(t,e)}}},Object.defineProperty(v.prototype,"unbounded",{get:function(){return Boolean(this.isUnbounded)},set:function(t){this.isUnbounded=Boolean(t),this.setUnbounded()},enumerable:!1,configurable:!0}),v.prototype.activate=function(){this.foundation.activate()},v.prototype.deactivate=function(){this.foundation.deactivate()},v.prototype.layout=function(){this.foundation.layout()},v.prototype.getDefaultFoundation=function(){return new d.MDCRippleFoundation(v.createAdapter(this))},v.prototype.initialSyncWithDOM=function(){var t=this.root;this.isUnbounded="mdcRippleIsUnbounded"in t.dataset},v.prototype.setUnbounded=function(){this.foundation.setUnbounded(Boolean(this.isUnbounded))},v);function v(){var t=null!==c&&c.apply(this,arguments)||this;return t.disabled=!1,t}e.MDCRipple=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.numbers=e.strings=e.cssClasses=void 0,e.cssClasses={BG_FOCUSED:"mdc-ripple-upgraded--background-focused",FG_ACTIVATION:"mdc-ripple-upgraded--foreground-activation",FG_DEACTIVATION:"mdc-ripple-upgraded--foreground-deactivation",ROOT:"mdc-ripple-upgraded",UNBOUNDED:"mdc-ripple-upgraded--unbounded"},e.strings={VAR_FG_SCALE:"--mdc-ripple-fg-scale",VAR_FG_SIZE:"--mdc-ripple-fg-size",VAR_FG_TRANSLATE_END:"--mdc-ripple-fg-translate-end",VAR_FG_TRANSLATE_START:"--mdc-ripple-fg-translate-start",VAR_LEFT:"--mdc-ripple-left",VAR_TOP:"--mdc-ripple-top"},e.numbers={DEACTIVATION_TIMEOUT_MS:225,FG_DEACTIVATION_MS:150,INITIAL_ORIGIN_SCALE:.6,PADDING:10,TAP_DELAY_MS:300}},,,,,function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabIndicatorFoundation=void 0;var a,s=r(7),c=r(78),l=(a=s.MDCFoundation,o(u,a),Object.defineProperty(u,"cssClasses",{get:function(){return c.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(u,"strings",{get:function(){return c.strings},enumerable:!1,configurable:!0}),Object.defineProperty(u,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},computeContentClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},setContentStyleProperty:function(){}}},enumerable:!1,configurable:!0}),u.prototype.computeContentClientRect=function(){return this.adapter.computeContentClientRect()},u);function u(t){return a.call(this,i(i({},u.defaultAdapter),t))||this}e.MDCTabIndicatorFoundation=l,e.default=l},,,,,,,,,,,,,function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabScrollerRTL=void 0;function n(t){this.adapter=t}e.MDCTabScrollerRTL=n,e.default=n},,,,,function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCFadingTabIndicatorFoundation=void 0;var i,a=r(59),s=(i=a.MDCTabIndicatorFoundation,o(c,i),c.prototype.activate=function(){this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c.prototype.deactivate=function(){this.adapter.removeClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c);function c(){return null!==i&&i.apply(this,arguments)||this}e.MDCFadingTabIndicatorFoundation=s,e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.cssClasses=void 0;e.cssClasses={ACTIVE:"mdc-tab-indicator--active",FADE:"mdc-tab-indicator--fade",NO_TRANSITION:"mdc-tab-indicator--no-transition"};e.strings={CONTENT_SELECTOR:".mdc-tab-indicator__content"}},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCSlidingTabIndicatorFoundation=void 0;var i,a=r(59),s=(i=a.MDCTabIndicatorFoundation,o(c,i),c.prototype.activate=function(t){if(t){var e=this.computeContentClientRect(),r=t.width/e.width,n=t.left-e.left;this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.NO_TRANSITION),this.adapter.setContentStyleProperty("transform","translateX("+n+"px) scaleX("+r+")"),this.computeContentClientRect(),this.adapter.removeClass(a.MDCTabIndicatorFoundation.cssClasses.NO_TRANSITION),this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE),this.adapter.setContentStyleProperty("transform","")}else this.adapter.addClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c.prototype.deactivate=function(){this.adapter.removeClass(a.MDCTabIndicatorFoundation.cssClasses.ACTIVE)},c);function c(){return null!==i&&i.apply(this,arguments)||this}e.MDCSlidingTabIndicatorFoundation=s,e.default=s},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.cssClasses=void 0;e.cssClasses={ANIMATING:"mdc-tab-scroller--animating",SCROLL_AREA_SCROLL:"mdc-tab-scroller__scroll-area--scroll",SCROLL_TEST:"mdc-tab-scroller__test"};e.strings={AREA_SELECTOR:".mdc-tab-scroller__scroll-area",CONTENT_SELECTOR:".mdc-tab-scroller__scroll-content"}},,,,,,function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabIndicator=void 0;var i,a=r(13),s=r(77),c=r(59),l=r(79),u=(i=a.MDCComponent,o(f,i),f.attachTo=function(t){return new f(t)},f.prototype.initialize=function(){this.content=this.root.querySelector(c.MDCTabIndicatorFoundation.strings.CONTENT_SELECTOR)},f.prototype.computeContentClientRect=function(){return this.foundation.computeContentClientRect()},f.prototype.getDefaultFoundation=function(){var r=this,t={addClass:function(t){r.root.classList.add(t)},removeClass:function(t){r.root.classList.remove(t)},computeContentClientRect:function(){return r.content.getBoundingClientRect()},setContentStyleProperty:function(t,e){r.content.style.setProperty(t,e)}};return this.root.classList.contains(c.MDCTabIndicatorFoundation.cssClasses.FADE)?new s.MDCFadingTabIndicatorFoundation(t):new l.MDCSlidingTabIndicatorFoundation(t)},f.prototype.activate=function(t){this.foundation.activate(t)},f.prototype.deactivate=function(){this.foundation.deactivate()},f);function f(){return null!==i&&i.apply(this,arguments)||this}e.MDCTabIndicator=u},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabFoundation=void 0;var a,s=r(7),c=r(96),l=(a=s.MDCFoundation,o(u,a),Object.defineProperty(u,"cssClasses",{get:function(){return c.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(u,"strings",{get:function(){return c.strings},enumerable:!1,configurable:!0}),Object.defineProperty(u,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},setAttr:function(){},activateIndicator:function(){},deactivateIndicator:function(){},notifyInteracted:function(){},getOffsetLeft:function(){return 0},getOffsetWidth:function(){return 0},getContentOffsetLeft:function(){return 0},getContentOffsetWidth:function(){return 0},focus:function(){},isFocused:function(){return!1}}},enumerable:!1,configurable:!0}),u.prototype.handleClick=function(){this.adapter.notifyInteracted()},u.prototype.isActive=function(){return this.adapter.hasClass(c.cssClasses.ACTIVE)},u.prototype.setFocusOnActivate=function(t){this.focusOnActivate=t},u.prototype.activate=function(t){this.adapter.addClass(c.cssClasses.ACTIVE),this.adapter.setAttr(c.strings.ARIA_SELECTED,"true"),this.adapter.setAttr(c.strings.TABINDEX,"0"),this.adapter.activateIndicator(t),this.focusOnActivate&&!this.adapter.isFocused()&&this.adapter.focus()},u.prototype.deactivate=function(){this.isActive()&&(this.adapter.removeClass(c.cssClasses.ACTIVE),this.adapter.setAttr(c.strings.ARIA_SELECTED,"false"),this.adapter.setAttr(c.strings.TABINDEX,"-1"),this.adapter.deactivateIndicator())},u.prototype.computeDimensions=function(){var t=this.adapter.getOffsetWidth(),e=this.adapter.getOffsetLeft(),r=this.adapter.getContentOffsetWidth(),n=this.adapter.getContentOffsetLeft();return{contentLeft:e+n,contentRight:e+n+r,rootLeft:e,rootRight:e+t}},u);function u(t){var e=a.call(this,i(i({},u.defaultAdapter),t))||this;return e.focusOnActivate=!0,e}e.MDCTabFoundation=l,e.default=l},,,,,,,,,function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.cssClasses=void 0;e.cssClasses={ACTIVE:"mdc-tab--active"};e.strings={ARIA_SELECTED:"aria-selected",CONTENT_SELECTOR:".mdc-tab__content",INTERACTED_EVENT:"MDCTab:interacted",RIPPLE_SELECTOR:".mdc-tab__ripple",TABINDEX:"tabIndex",TAB_INDICATOR_SELECTOR:".mdc-tab-indicator"}},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},a=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,o,i=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(n=i.next()).done;)a.push(n.value)}catch(t){o={error:t}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabScrollerFoundation=void 0;var s,c=r(7),l=r(80),u=r(114),f=r(115),d=r(116),p=(s=c.MDCFoundation,o(h,s),Object.defineProperty(h,"cssClasses",{get:function(){return l.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(h,"strings",{get:function(){return l.strings},enumerable:!1,configurable:!0}),Object.defineProperty(h,"defaultAdapter",{get:function(){return{eventTargetMatchesSelector:function(){return!1},addClass:function(){},removeClass:function(){},addScrollAreaClass:function(){},setScrollAreaStyleProperty:function(){},setScrollContentStyleProperty:function(){},getScrollContentStyleValue:function(){return""},setScrollAreaScrollLeft:function(){},getScrollAreaScrollLeft:function(){return 0},getScrollContentOffsetWidth:function(){return 0},getScrollAreaOffsetWidth:function(){return 0},computeScrollAreaClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},computeScrollContentClientRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},computeHorizontalScrollbarHeight:function(){return 0}}},enumerable:!1,configurable:!0}),h.prototype.init=function(){var t=this.adapter.computeHorizontalScrollbarHeight();this.adapter.setScrollAreaStyleProperty("margin-bottom",-t+"px"),this.adapter.addScrollAreaClass(h.cssClasses.SCROLL_AREA_SCROLL)},h.prototype.getScrollPosition=function(){if(this.isRTL())return this.computeCurrentScrollPositionRTL();var t=this.calculateCurrentTranslateX();return this.adapter.getScrollAreaScrollLeft()-t},h.prototype.handleInteraction=function(){this.isAnimating&&this.stopScrollAnimation()},h.prototype.handleTransitionEnd=function(t){var e=t.target;this.isAnimating&&this.adapter.eventTargetMatchesSelector(e,h.strings.CONTENT_SELECTOR)&&(this.isAnimating=!1,this.adapter.removeClass(h.cssClasses.ANIMATING))},h.prototype.incrementScroll=function(t){0!==t&&this.animate(this.getIncrementScrollOperation(t))},h.prototype.incrementScrollImmediate=function(t){if(0!==t){var e=this.getIncrementScrollOperation(t);0!==e.scrollDelta&&(this.stopScrollAnimation(),this.adapter.setScrollAreaScrollLeft(e.finalScrollPosition))}},h.prototype.scrollTo=function(t){this.isRTL()?this.scrollToImplRTL(t):this.scrollToImpl(t)},h.prototype.getRTLScroller=function(){return this.rtlScrollerInstance||(this.rtlScrollerInstance=this.rtlScrollerFactory()),this.rtlScrollerInstance},h.prototype.calculateCurrentTranslateX=function(){var t=this.adapter.getScrollContentStyleValue("transform");if("none"===t)return 0;var e=/\((.+?)\)/.exec(t);if(!e)return 0;var r=e[1],n=a(r.split(","),6),o=(n[0],n[1],n[2],n[3],n[4]);return n[5],parseFloat(o)},h.prototype.clampScrollValue=function(t){var e=this.calculateScrollEdges();return Math.min(Math.max(e.left,t),e.right)},h.prototype.computeCurrentScrollPositionRTL=function(){var t=this.calculateCurrentTranslateX();return this.getRTLScroller().getScrollPositionRTL(t)},h.prototype.calculateScrollEdges=function(){return{left:0,right:this.adapter.getScrollContentOffsetWidth()-this.adapter.getScrollAreaOffsetWidth()}},h.prototype.scrollToImpl=function(t){var e=this.getScrollPosition(),r=this.clampScrollValue(t),n=r-e;this.animate({finalScrollPosition:r,scrollDelta:n})},h.prototype.scrollToImplRTL=function(t){var e=this.getRTLScroller().scrollToRTL(t);this.animate(e)},h.prototype.getIncrementScrollOperation=function(t){if(this.isRTL())return this.getRTLScroller().incrementScrollRTL(t);var e=this.getScrollPosition(),r=t+e,n=this.clampScrollValue(r);return{finalScrollPosition:n,scrollDelta:n-e}},h.prototype.animate=function(t){var e=this;0!==t.scrollDelta&&(this.stopScrollAnimation(),this.adapter.setScrollAreaScrollLeft(t.finalScrollPosition),this.adapter.setScrollContentStyleProperty("transform","translateX("+t.scrollDelta+"px)"),this.adapter.computeScrollAreaClientRect(),requestAnimationFrame(function(){e.adapter.addClass(h.cssClasses.ANIMATING),e.adapter.setScrollContentStyleProperty("transform","none")}),this.isAnimating=!0)},h.prototype.stopScrollAnimation=function(){this.isAnimating=!1;var t=this.getAnimatingScrollPosition();this.adapter.removeClass(h.cssClasses.ANIMATING),this.adapter.setScrollContentStyleProperty("transform","translateX(0px)"),this.adapter.setScrollAreaScrollLeft(t)},h.prototype.getAnimatingScrollPosition=function(){var t=this.calculateCurrentTranslateX(),e=this.adapter.getScrollAreaScrollLeft();return this.isRTL()?this.getRTLScroller().getAnimatingScrollPosition(e,t):e-t},h.prototype.rtlScrollerFactory=function(){var t=this.adapter.getScrollAreaScrollLeft();this.adapter.setScrollAreaScrollLeft(t-1);var e=this.adapter.getScrollAreaScrollLeft();if(e<0)return this.adapter.setScrollAreaScrollLeft(t),new f.MDCTabScrollerRTLNegative(this.adapter);var r=this.adapter.computeScrollAreaClientRect(),n=this.adapter.computeScrollContentClientRect(),o=Math.round(n.right-r.right);return this.adapter.setScrollAreaScrollLeft(t),o===e?new d.MDCTabScrollerRTLReverse(this.adapter):new u.MDCTabScrollerRTLDefault(this.adapter)},h.prototype.isRTL=function(){return"rtl"===this.adapter.getScrollContentStyleValue("direction")},h);function h(t){var e=s.call(this,i(i({},h.defaultAdapter),t))||this;return e.isAnimating=!1,e}e.MDCTabScrollerFoundation=p,e.default=p},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.computeHorizontalScrollbarHeight=void 0;var o,i=r(80);e.computeHorizontalScrollbarHeight=function(t,e){if(void 0===e&&(e=!0),e&&void 0!==o)return o;var r=t.createElement("div");r.classList.add(i.cssClasses.SCROLL_TEST),t.body.appendChild(r);var n=r.offsetHeight-r.clientHeight;return t.body.removeChild(r),e&&(o=n),n}},,,,,,,,,,,,,,function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTab=void 0;var i,a=r(13),s=r(53),c=r(51),l=r(86),u=r(87),f=(i=a.MDCComponent,o(d,i),d.attachTo=function(t){return new d(t)},d.prototype.initialize=function(t,e){void 0===t&&(t=function(t,e){return new s.MDCRipple(t,e)}),void 0===e&&(e=function(t){return new l.MDCTabIndicator(t)}),this.id=this.root.id;var r=new c.MDCRippleFoundation(s.MDCRipple.createAdapter(this));this.ripple=t(this.root,r);var n=this.root.querySelector(u.MDCTabFoundation.strings.TAB_INDICATOR_SELECTOR);this.tabIndicator=e(n),this.content=this.root.querySelector(u.MDCTabFoundation.strings.CONTENT_SELECTOR)},d.prototype.initialSyncWithDOM=function(){var t=this;this.handleClick=function(){t.foundation.handleClick()},this.listen("click",this.handleClick)},d.prototype.destroy=function(){this.unlisten("click",this.handleClick),this.ripple.destroy(),i.prototype.destroy.call(this)},d.prototype.getDefaultFoundation=function(){var r=this,t={setAttr:function(t,e){r.safeSetAttribute(r.root,t,e)},addClass:function(t){r.root.classList.add(t)},removeClass:function(t){r.root.classList.remove(t)},hasClass:function(t){return r.root.classList.contains(t)},activateIndicator:function(t){r.tabIndicator.activate(t)},deactivateIndicator:function(){r.tabIndicator.deactivate()},notifyInteracted:function(){r.emit(u.MDCTabFoundation.strings.INTERACTED_EVENT,{tabId:r.id},!0)},getOffsetLeft:function(){return r.root.offsetLeft},getOffsetWidth:function(){return r.root.offsetWidth},getContentOffsetLeft:function(){return r.content.offsetLeft},getContentOffsetWidth:function(){return r.content.offsetWidth},focus:function(){r.root.focus()},isFocused:function(){return r.root===document.activeElement}};return new u.MDCTabFoundation(t)},Object.defineProperty(d.prototype,"active",{get:function(){return this.foundation.isActive()},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"focusOnActivate",{set:function(t){this.foundation.setFocusOnActivate(t)},enumerable:!1,configurable:!0}),d.prototype.activate=function(t){this.foundation.activate(t)},d.prototype.deactivate=function(){this.foundation.deactivate()},d.prototype.computeIndicatorClientRect=function(){return this.tabIndicator.computeContentClientRect()},d.prototype.computeDimensions=function(){return this.foundation.computeDimensions()},d.prototype.focus=function(){this.root.focus()},d);function d(){return null!==i&&i.apply(this,arguments)||this}e.MDCTab=f},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),a=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),s=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&i(e,t,r);return a(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabScroller=void 0;var c,l=r(13),u=r(52),f=r(49),d=r(97),p=s(r(98)),h=(c=l.MDCComponent,o(v,c),v.attachTo=function(t){return new v(t)},v.prototype.initialize=function(){this.area=this.root.querySelector(d.MDCTabScrollerFoundation.strings.AREA_SELECTOR),this.content=this.root.querySelector(d.MDCTabScrollerFoundation.strings.CONTENT_SELECTOR)},v.prototype.initialSyncWithDOM=function(){var e=this;this.handleInteraction=function(){e.foundation.handleInteraction()},this.handleTransitionEnd=function(t){e.foundation.handleTransitionEnd(t)},this.area.addEventListener("wheel",this.handleInteraction,u.applyPassive()),this.area.addEventListener("touchstart",this.handleInteraction,u.applyPassive()),this.area.addEventListener("pointerdown",this.handleInteraction,u.applyPassive()),this.area.addEventListener("mousedown",this.handleInteraction,u.applyPassive()),this.area.addEventListener("keydown",this.handleInteraction,u.applyPassive()),this.content.addEventListener("transitionend",this.handleTransitionEnd)},v.prototype.destroy=function(){c.prototype.destroy.call(this),this.area.removeEventListener("wheel",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("touchstart",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("pointerdown",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("mousedown",this.handleInteraction,u.applyPassive()),this.area.removeEventListener("keydown",this.handleInteraction,u.applyPassive()),this.content.removeEventListener("transitionend",this.handleTransitionEnd)},v.prototype.getDefaultFoundation=function(){var r=this,t={eventTargetMatchesSelector:function(t,e){return f.matches(t,e)},addClass:function(t){r.root.classList.add(t)},removeClass:function(t){r.root.classList.remove(t)},addScrollAreaClass:function(t){r.area.classList.add(t)},setScrollAreaStyleProperty:function(t,e){r.area.style.setProperty(t,e)},setScrollContentStyleProperty:function(t,e){r.content.style.setProperty(t,e)},getScrollContentStyleValue:function(t){return window.getComputedStyle(r.content).getPropertyValue(t)},setScrollAreaScrollLeft:function(t){return r.area.scrollLeft=t},getScrollAreaScrollLeft:function(){return r.area.scrollLeft},getScrollContentOffsetWidth:function(){return r.content.offsetWidth},getScrollAreaOffsetWidth:function(){return r.area.offsetWidth},computeScrollAreaClientRect:function(){return r.area.getBoundingClientRect()},computeScrollContentClientRect:function(){return r.content.getBoundingClientRect()},computeHorizontalScrollbarHeight:function(){return p.computeHorizontalScrollbarHeight(document)}};return new d.MDCTabScrollerFoundation(t)},v.prototype.getScrollPosition=function(){return this.foundation.getScrollPosition()},v.prototype.getScrollContentWidth=function(){return this.content.offsetWidth},v.prototype.incrementScroll=function(t){this.foundation.incrementScroll(t)},v.prototype.scrollTo=function(t){this.foundation.scrollTo(t)},v);function v(){return null!==c&&c.apply(this,arguments)||this}e.MDCTabScroller=h},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabScrollerRTLDefault=void 0;var i,a=r(72),s=(i=a.MDCTabScrollerRTL,o(c,i),c.prototype.getScrollPositionRTL=function(){var t=this.adapter.getScrollAreaScrollLeft(),e=this.calculateScrollEdges().right;return Math.round(e-t)},c.prototype.scrollToRTL=function(t){var e=this.calculateScrollEdges(),r=this.adapter.getScrollAreaScrollLeft(),n=this.clampScrollValue(e.right-t);return{finalScrollPosition:n,scrollDelta:n-r}},c.prototype.incrementScrollRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(e-t);return{finalScrollPosition:r,scrollDelta:r-e}},c.prototype.getAnimatingScrollPosition=function(t){return t},c.prototype.calculateScrollEdges=function(){return{left:0,right:this.adapter.getScrollContentOffsetWidth()-this.adapter.getScrollAreaOffsetWidth()}},c.prototype.clampScrollValue=function(t){var e=this.calculateScrollEdges();return Math.min(Math.max(e.left,t),e.right)},c);function c(){return null!==i&&i.apply(this,arguments)||this}e.MDCTabScrollerRTLDefault=s,e.default=s},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabScrollerRTLNegative=void 0;var i,a=r(72),s=(i=a.MDCTabScrollerRTL,o(c,i),c.prototype.getScrollPositionRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft();return Math.round(t-e)},c.prototype.scrollToRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(-t);return{finalScrollPosition:r,scrollDelta:r-e}},c.prototype.incrementScrollRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(e-t);return{finalScrollPosition:r,scrollDelta:r-e}},c.prototype.getAnimatingScrollPosition=function(t,e){return t-e},c.prototype.calculateScrollEdges=function(){var t=this.adapter.getScrollContentOffsetWidth();return{left:this.adapter.getScrollAreaOffsetWidth()-t,right:0}},c.prototype.clampScrollValue=function(t){var e=this.calculateScrollEdges();return Math.max(Math.min(e.right,t),e.left)},c);function c(){return null!==i&&i.apply(this,arguments)||this}e.MDCTabScrollerRTLNegative=s,e.default=s},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabScrollerRTLReverse=void 0;var i,a=r(72),s=(i=a.MDCTabScrollerRTL,o(c,i),c.prototype.getScrollPositionRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft();return Math.round(e-t)},c.prototype.scrollToRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(t);return{finalScrollPosition:r,scrollDelta:e-r}},c.prototype.incrementScrollRTL=function(t){var e=this.adapter.getScrollAreaScrollLeft(),r=this.clampScrollValue(e+t);return{finalScrollPosition:r,scrollDelta:e-r}},c.prototype.getAnimatingScrollPosition=function(t,e){return t+e},c.prototype.calculateScrollEdges=function(){return{left:this.adapter.getScrollContentOffsetWidth()-this.adapter.getScrollAreaOffsetWidth(),right:0}},c.prototype.clampScrollValue=function(t){var e=this.calculateScrollEdges();return Math.min(Math.max(e.right,t),e.left)},c);function c(){return null!==i&&i.apply(this,arguments)||this}e.MDCTabScrollerRTLReverse=s,e.default=s},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__assign||function(){return(i=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var o in e=arguments[r])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabBarFoundation=void 0;var a=r(7),l=r(170),s=new Set;s.add(l.strings.ARROW_LEFT_KEY),s.add(l.strings.ARROW_RIGHT_KEY),s.add(l.strings.END_KEY),s.add(l.strings.HOME_KEY),s.add(l.strings.ENTER_KEY),s.add(l.strings.SPACE_KEY);var c=new Map;c.set(l.numbers.ARROW_LEFT_KEYCODE,l.strings.ARROW_LEFT_KEY),c.set(l.numbers.ARROW_RIGHT_KEYCODE,l.strings.ARROW_RIGHT_KEY),c.set(l.numbers.END_KEYCODE,l.strings.END_KEY),c.set(l.numbers.HOME_KEYCODE,l.strings.HOME_KEY),c.set(l.numbers.ENTER_KEYCODE,l.strings.ENTER_KEY),c.set(l.numbers.SPACE_KEYCODE,l.strings.SPACE_KEY);var u,f=(u=a.MDCFoundation,o(d,u),Object.defineProperty(d,"strings",{get:function(){return l.strings},enumerable:!1,configurable:!0}),Object.defineProperty(d,"numbers",{get:function(){return l.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(d,"defaultAdapter",{get:function(){return{scrollTo:function(){},incrementScroll:function(){},getScrollPosition:function(){return 0},getScrollContentWidth:function(){return 0},getOffsetWidth:function(){return 0},isRTL:function(){return!1},setActiveTab:function(){},activateTabAtIndex:function(){},deactivateTabAtIndex:function(){},focusTabAtIndex:function(){},getTabIndicatorClientRectAtIndex:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},getTabDimensionsAtIndex:function(){return{rootLeft:0,rootRight:0,contentLeft:0,contentRight:0}},getPreviousActiveTabIndex:function(){return-1},getFocusedTabIndex:function(){return-1},getIndexOfTabById:function(){return-1},getTabListLength:function(){return 0},notifyTabActivated:function(){}}},enumerable:!1,configurable:!0}),d.prototype.setUseAutomaticActivation=function(t){this.useAutomaticActivation=t},d.prototype.activateTab=function(t){var e,r=this.adapter.getPreviousActiveTabIndex();this.indexIsInRange(t)&&t!==r&&(-1!==r&&(this.adapter.deactivateTabAtIndex(r),e=this.adapter.getTabIndicatorClientRectAtIndex(r)),this.adapter.activateTabAtIndex(t,e),this.scrollIntoView(t),this.adapter.notifyTabActivated(t))},d.prototype.handleKeyDown=function(t){var e=this.getKeyFromEvent(t);if(void 0!==e&&(this.isActivationKey(e)||t.preventDefault(),!this.useAutomaticActivation||!this.isActivationKey(e))){var r=this.adapter.getFocusedTabIndex();if(this.isActivationKey(e))this.adapter.setActiveTab(r);else{var n=this.determineTargetFromKey(r,e);this.adapter.focusTabAtIndex(n),this.scrollIntoView(n),this.useAutomaticActivation&&this.adapter.setActiveTab(n)}}},d.prototype.handleTabInteraction=function(t){this.adapter.setActiveTab(this.adapter.getIndexOfTabById(t.detail.tabId))},d.prototype.scrollIntoView=function(t){this.indexIsInRange(t)&&(0!==t?t!==this.adapter.getTabListLength()-1?this.isRTL()?this.scrollIntoViewImplRTL(t):this.scrollIntoViewImpl(t):this.adapter.scrollTo(this.adapter.getScrollContentWidth()):this.adapter.scrollTo(0))},d.prototype.determineTargetFromKey=function(t,e){var r=this.isRTL(),n=this.adapter.getTabListLength()-1,o=e===l.strings.END_KEY,i=e===l.strings.ARROW_LEFT_KEY&&!r||e===l.strings.ARROW_RIGHT_KEY&&r,a=e===l.strings.ARROW_RIGHT_KEY&&!r||e===l.strings.ARROW_LEFT_KEY&&r,s=t;return o?s=n:i?s-=1:a?s+=1:s=0,s<0?s=n:n<s&&(s=0),s},d.prototype.calculateScrollIncrement=function(t,e,r,n){var o=this.adapter.getTabDimensionsAtIndex(e),i=o.contentLeft-r-n,a=o.contentRight-r-l.numbers.EXTRA_SCROLL_AMOUNT,s=i+l.numbers.EXTRA_SCROLL_AMOUNT;return e<t?Math.min(a,0):Math.max(s,0)},d.prototype.calculateScrollIncrementRTL=function(t,e,r,n,o){var i=this.adapter.getTabDimensionsAtIndex(e),a=o-i.contentLeft-r,s=o-i.contentRight-r-n+l.numbers.EXTRA_SCROLL_AMOUNT,c=a-l.numbers.EXTRA_SCROLL_AMOUNT;return t<e?Math.max(s,0):Math.min(c,0)},d.prototype.findAdjacentTabIndexClosestToEdge=function(t,e,r,n){var o=e.rootLeft-r,i=e.rootRight-r-n,a=o+i;return o<0||a<0?t-1:0<i||0<a?t+1:-1},d.prototype.findAdjacentTabIndexClosestToEdgeRTL=function(t,e,r,n,o){var i=o-e.rootLeft-n-r,a=o-e.rootRight-r,s=i+a;return 0<i||0<s?t+1:a<0||s<0?t-1:-1},d.prototype.getKeyFromEvent=function(t){return s.has(t.key)?t.key:c.get(t.keyCode)},d.prototype.isActivationKey=function(t){return t===l.strings.SPACE_KEY||t===l.strings.ENTER_KEY},d.prototype.indexIsInRange=function(t){return 0<=t&&t<this.adapter.getTabListLength()},d.prototype.isRTL=function(){return this.adapter.isRTL()},d.prototype.scrollIntoViewImpl=function(t){var e=this.adapter.getScrollPosition(),r=this.adapter.getOffsetWidth(),n=this.adapter.getTabDimensionsAtIndex(t),o=this.findAdjacentTabIndexClosestToEdge(t,n,e,r);if(this.indexIsInRange(o)){var i=this.calculateScrollIncrement(t,o,e,r);this.adapter.incrementScroll(i)}},d.prototype.scrollIntoViewImplRTL=function(t){var e=this.adapter.getScrollPosition(),r=this.adapter.getOffsetWidth(),n=this.adapter.getTabDimensionsAtIndex(t),o=this.adapter.getScrollContentWidth(),i=this.findAdjacentTabIndexClosestToEdgeRTL(t,n,e,r,o);if(this.indexIsInRange(i)){var a=this.calculateScrollIncrementRTL(t,i,e,r,o);this.adapter.incrementScroll(a)}},d);function d(t){var e=u.call(this,i(i({},d.defaultAdapter),t))||this;return e.useAutomaticActivation=!1,e}e.MDCTabBarFoundation=f,e.default=f},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.strings=e.numbers=void 0;e.strings={ARROW_LEFT_KEY:"ArrowLeft",ARROW_RIGHT_KEY:"ArrowRight",END_KEY:"End",ENTER_KEY:"Enter",HOME_KEY:"Home",SPACE_KEY:"Space",TAB_ACTIVATED_EVENT:"MDCTabBar:activated",TAB_SCROLLER_SELECTOR:".mdc-tab-scroller",TAB_SELECTOR:".mdc-tab"};e.numbers={ARROW_LEFT_KEYCODE:37,ARROW_RIGHT_KEYCODE:39,END_KEYCODE:35,ENTER_KEYCODE:13,EXTRA_SCROLL_AMOUNT:20,HOME_KEYCODE:36,SPACE_KEYCODE:32}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(t,e,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(t,e,r,n){void 0===n&&(n=r),Object.defineProperty(t,n,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,n){void 0===n&&(n=r),t[n]=e[r]}),o=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||n(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),o(r(295),e),o(r(296),e),o(r(170),e),o(r(169),e),o(r(297),e)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},function(t,e,r){"use strict";var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),i=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTabBar=void 0;var a,s=r(13),c=r(113),l=r(112),u=r(87),f=r(169),d=f.MDCTabBarFoundation.strings,p=0,h=(a=s.MDCComponent,o(v,a),v.attachTo=function(t){return new v(t)},Object.defineProperty(v.prototype,"focusOnActivate",{set:function(t){var e,r;try{for(var n=i(this.tabList),o=n.next();!o.done;o=n.next())o.value.focusOnActivate=t}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}},enumerable:!1,configurable:!0}),Object.defineProperty(v.prototype,"useAutomaticActivation",{set:function(t){this.foundation.setUseAutomaticActivation(t)},enumerable:!1,configurable:!0}),v.prototype.initialize=function(t,e){void 0===t&&(t=function(t){return new l.MDCTab(t)}),void 0===e&&(e=function(t){return new c.MDCTabScroller(t)}),this.tabList=this.instantiateTabs(t),this.tabScroller=this.instantiatetabScroller(e)},v.prototype.initialSyncWithDOM=function(){var e=this;this.handleTabInteraction=function(t){e.foundation.handleTabInteraction(t)},this.handleKeyDown=function(t){e.foundation.handleKeyDown(t)},this.listen(u.MDCTabFoundation.strings.INTERACTED_EVENT,this.handleTabInteraction),this.listen("keydown",this.handleKeyDown);for(var t=0;t<this.tabList.length;t++)if(this.tabList[t].active){this.scrollIntoView(t);break}},v.prototype.destroy=function(){var e,t;a.prototype.destroy.call(this),this.unlisten(u.MDCTabFoundation.strings.INTERACTED_EVENT,this.handleTabInteraction),this.unlisten("keydown",this.handleKeyDown);try{for(var r=i(this.tabList),n=r.next();!n.done;n=r.next())n.value.destroy()}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}this.tabScroller&&this.tabScroller.destroy()},v.prototype.getDefaultFoundation=function(){var r=this,t={scrollTo:function(t){r.tabScroller.scrollTo(t)},incrementScroll:function(t){r.tabScroller.incrementScroll(t)},getScrollPosition:function(){return r.tabScroller.getScrollPosition()},getScrollContentWidth:function(){return r.tabScroller.getScrollContentWidth()},getOffsetWidth:function(){return r.root.offsetWidth},isRTL:function(){return"rtl"===window.getComputedStyle(r.root).getPropertyValue("direction")},setActiveTab:function(t){r.foundation.activateTab(t)},activateTabAtIndex:function(t,e){r.tabList[t].activate(e)},deactivateTabAtIndex:function(t){r.tabList[t].deactivate()},focusTabAtIndex:function(t){r.tabList[t].focus()},getTabIndicatorClientRectAtIndex:function(t){return r.tabList[t].computeIndicatorClientRect()},getTabDimensionsAtIndex:function(t){return r.tabList[t].computeDimensions()},getPreviousActiveTabIndex:function(){for(var t=0;t<r.tabList.length;t++)if(r.tabList[t].active)return t;return-1},getFocusedTabIndex:function(){var t=r.getTabElements(),e=document.activeElement;return t.indexOf(e)},getIndexOfTabById:function(t){for(var e=0;e<r.tabList.length;e++)if(r.tabList[e].id===t)return e;return-1},getTabListLength:function(){return r.tabList.length},notifyTabActivated:function(t){r.emit(d.TAB_ACTIVATED_EVENT,{index:t},!0)}};return new f.MDCTabBarFoundation(t)},v.prototype.activateTab=function(t){this.foundation.activateTab(t)},v.prototype.scrollIntoView=function(t){this.foundation.scrollIntoView(t)},v.prototype.getTabElements=function(){return Array.from(this.root.querySelectorAll(d.TAB_SELECTOR))},v.prototype.instantiateTabs=function(e){return this.getTabElements().map(function(t){return t.id=t.id||"mdc-tab-"+ ++p,e(t)})},v.prototype.instantiatetabScroller=function(t){var e=this.root.querySelector(d.TAB_SCROLLER_SELECTOR);return e?t(e):null},v);function v(){return null!==a&&a.apply(this,arguments)||this}e.MDCTabBar=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})}],o.c=n,o.d=function(t,e,r){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=294);function o(t){if(n[t])return n[t].exports;var e=n[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,o),e.l=!0,e.exports}var r,n});