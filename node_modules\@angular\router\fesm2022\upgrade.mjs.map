{"version": 3, "file": "upgrade.mjs", "sources": ["../../../../../../packages/router/upgrade/src/upgrade.ts", "../../../../../../packages/router/upgrade/public_api.ts", "../../../../../../packages/router/upgrade/index.ts", "../../../../../../packages/router/upgrade/upgrade.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {Location} from '@angular/common';\nimport {APP_BOOTSTRAP_LISTENER, ComponentRef, InjectionToken} from '@angular/core';\nimport {Router, ɵRestoredState as RestoredState} from '@angular/router';\nimport {UpgradeModule} from '@angular/upgrade/static';\n\n/**\n * Creates an initializer that sets up `ngRoute` integration\n * along with setting up the Angular router.\n *\n * @usageNotes\n *\n * <code-example language=\"typescript\">\n * @NgModule({\n *  imports: [\n *   RouterModule.forRoot(SOME_ROUTES),\n *   UpgradeModule\n * ],\n * providers: [\n *   RouterUpgradeInitializer\n * ]\n * })\n * export class AppModule {\n *   ngDoBootstrap() {}\n * }\n * </code-example>\n *\n * @publicApi\n */\nexport const RouterUpgradeInitializer = {\n  provide: APP_BOOTSTRAP_LISTENER,\n  multi: true,\n  useFactory: locationSyncBootstrapListener as (ngUpgrade: UpgradeModule) => () => void,\n  deps: [UpgradeModule],\n};\n\n/**\n * @internal\n */\nexport function locationSyncBootstrapListener(ngUpgrade: UpgradeModule) {\n  return () => {\n    setUpLocationSync(ngUpgrade);\n  };\n}\n\n/**\n * Sets up a location change listener to trigger `history.pushState`.\n * Works around the problem that `onPopState` does not trigger `history.pushState`.\n * Must be called *after* calling `UpgradeModule.bootstrap`.\n *\n * @param ngUpgrade The upgrade NgModule.\n * @param urlType The location strategy.\n * @see {@link HashLocationStrategy}\n * @see {@link PathLocationStrategy}\n *\n * @publicApi\n */\nexport function setUpLocationSync(ngUpgrade: UpgradeModule, urlType: 'path' | 'hash' = 'path') {\n  if (!ngUpgrade.$injector) {\n    throw new Error(`\n        RouterUpgradeInitializer can be used only after UpgradeModule.bootstrap has been called.\n        Remove RouterUpgradeInitializer and call setUpLocationSync after UpgradeModule.bootstrap.\n      `);\n  }\n\n  const router: Router = ngUpgrade.injector.get(Router);\n  const location: Location = ngUpgrade.injector.get(Location);\n\n  ngUpgrade.$injector\n    .get('$rootScope')\n    .$on(\n      '$locationChangeStart',\n      (\n        event: any,\n        newUrl: string,\n        oldUrl: string,\n        newState?: {[k: string]: unknown} | RestoredState,\n        oldState?: {[k: string]: unknown} | RestoredState,\n      ) => {\n        // Navigations coming from Angular router have a navigationId state\n        // property. Don't trigger Angular router navigation again if it is\n        // caused by a URL change from the current Angular router\n        // navigation.\n        const currentNavigationId = router.getCurrentNavigation()?.id;\n        const newStateNavigationId = newState?.navigationId;\n        if (newStateNavigationId !== undefined && newStateNavigationId === currentNavigationId) {\n          return;\n        }\n\n        let url;\n        if (urlType === 'path') {\n          url = resolveUrl(newUrl);\n        } else if (urlType === 'hash') {\n          // Remove the first hash from the URL\n          const hashIdx = newUrl.indexOf('#');\n          url = resolveUrl(newUrl.substring(0, hashIdx) + newUrl.substring(hashIdx + 1));\n        } else {\n          throw 'Invalid URLType passed to setUpLocationSync: ' + urlType;\n        }\n        const path = location.normalize(url.pathname);\n        router.navigateByUrl(path + url.search + url.hash);\n      },\n    );\n}\n\n/**\n * Normalizes and parses a URL.\n *\n * - Normalizing means that a relative URL will be resolved into an absolute URL in the context of\n *   the application document.\n * - Parsing means that the anchor's `protocol`, `hostname`, `port`, `pathname` and related\n *   properties are all populated to reflect the normalized URL.\n *\n * While this approach has wide compatibility, it doesn't work as expected on IE. On IE, normalizing\n * happens similar to other browsers, but the parsed components will not be set. (E.g. if you assign\n * `a.href = 'foo'`, then `a.protocol`, `a.host`, etc. will not be correctly updated.)\n * We work around that by performing the parsing in a 2nd step by taking a previously normalized URL\n * and assigning it again. This correctly populates all properties.\n *\n * See\n * https://github.com/angular/angular.js/blob/2c7400e7d07b0f6cec1817dab40b9250ce8ebce6/src/ng/urlUtils.js#L26-L33\n * for more info.\n */\nlet anchor: HTMLAnchorElement | undefined;\nfunction resolveUrl(url: string): {pathname: string; search: string; hash: string} {\n  anchor ??= document.createElement('a');\n\n  anchor.setAttribute('href', url);\n  anchor.setAttribute('href', anchor.href);\n\n  return {\n    // IE does not start `pathname` with `/` like other browsers.\n    pathname: `/${anchor.pathname.replace(/^\\//, '')}`,\n    search: anchor.search,\n    hash: anchor.hash,\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/upgrade';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;;;;;;AAaA;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACU,MAAA,wBAAwB,GAAG;AACtC,IAAA,OAAO,EAAE,sBAAsB;AAC/B,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,UAAU,EAAE,6BAAyE;IACrF,IAAI,EAAE,CAAC,aAAa,CAAC;EACrB;AAEF;;AAEG;AACG,SAAU,6BAA6B,CAAC,SAAwB,EAAA;AACpE,IAAA,OAAO,MAAK;QACV,iBAAiB,CAAC,SAAS,CAAC,CAAC;AAC/B,KAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;AAWG;SACa,iBAAiB,CAAC,SAAwB,EAAE,UAA2B,MAAM,EAAA;AAC3F,IAAA,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;QACxB,MAAM,IAAI,KAAK,CAAC,CAAA;;;AAGb,MAAA,CAAA,CAAC,CAAC;KACN;IAED,MAAM,MAAM,GAAW,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACtD,MAAM,QAAQ,GAAa,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAE5D,IAAA,SAAS,CAAC,SAAS;SAChB,GAAG,CAAC,YAAY,CAAC;AACjB,SAAA,GAAG,CACF,sBAAsB,EACtB,CACE,KAAU,EACV,MAAc,EACd,MAAc,EACd,QAAiD,EACjD,QAAiD,KAC/C;;;;;QAKF,MAAM,mBAAmB,GAAG,MAAM,CAAC,oBAAoB,EAAE,EAAE,EAAE,CAAC;AAC9D,QAAA,MAAM,oBAAoB,GAAG,QAAQ,EAAE,YAAY,CAAC;QACpD,IAAI,oBAAoB,KAAK,SAAS,IAAI,oBAAoB,KAAK,mBAAmB,EAAE;YACtF,OAAO;SACR;AAED,QAAA,IAAI,GAAG,CAAC;AACR,QAAA,IAAI,OAAO,KAAK,MAAM,EAAE;AACtB,YAAA,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;SAC1B;AAAM,aAAA,IAAI,OAAO,KAAK,MAAM,EAAE;;YAE7B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACpC,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;SAChF;aAAM;YACL,MAAM,+CAA+C,GAAG,OAAO,CAAC;SACjE;QACD,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC9C,QAAA,MAAM,CAAC,aAAa,CAAC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACrD,KAAC,CACF,CAAC;AACN,CAAC;AAED;;;;;;;;;;;;;;;;;AAiBG;AACH,IAAI,MAAqC,CAAC;AAC1C,SAAS,UAAU,CAAC,GAAW,EAAA;AAC7B,IAAA,MAAM,KAAK,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAEvC,IAAA,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;IAEzC,OAAO;;AAEL,QAAA,QAAQ,EAAE,CAAA,CAAA,EAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAE,CAAA;QAClD,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,IAAI,EAAE,MAAM,CAAC,IAAI;KAClB,CAAC;AACJ;;ACvIA;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}