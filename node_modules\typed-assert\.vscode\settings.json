{
  "editor.fontLigatures": true,
  "editor.suggestSelection": "recentlyUsed",
  "editor.tabSize": 2,
  "emmet.showAbbreviationSuggestions": false,
  "emmet.showExpandedAbbreviation": "never",
  "emmet.excludeLanguages": [
    "typescript",
    "javascript",
  ],
  "eslint.enable": true,
  "eslint.packageManager": "npm",
  "eslint.validate": [
    "javascript",
    "typescript",
  ],
  "eslint.workingDirectories": [
    {
      "directory": ".",
      "changeProcessCWD": true
    }
  ],
  "eslint.lintTask.enable": true,
  "files.associations": {
    "*.md": "markdown",
    "*.d.ts": "typescript"
  },
  "files.autoSave": "off",
  "javascript.updateImportsOnFileMove.enabled": "always",
  "[javascript]": {
    "editor.formatOnSave": false
  },
  "[typescript]": {
    "editor.formatOnSave": false
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.suggest.paths": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "editor.suggest.showSnippets": false,
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "editor.snippetSuggestions": "none"
}