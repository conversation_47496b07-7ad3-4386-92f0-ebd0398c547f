{"name": "typed-assert", "version": "1.0.9", "description": "typesafe assertion library for TypeScript 3.7+", "main": "build/index.js", "scripts": {"test:src": "jest src/**/*.test.ts", "test:build": "jest build/**/*.test.js", "clean:doc": "rm -rf doc && rm -rf API.md", "build:doc": "npm run clean:doc && typedoc && sed s/README.md/API.md/ doc/README.md > API.md && rm -rf doc", "clean:ts": "rm -rf build", "build:ts": "npm run clean:ts && tsc -p .", "build": "npm run build:doc && npm run build:ts"}, "repository": {"type": "git", "url": "git+https://github.com/elierotenberg/typed-assert.git"}, "keywords": ["assert", "typescript"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/elier<PERSON>nberg/typed-assert/issues"}, "homepage": "https://github.com/elierotenberg/typed-assert#readme", "devDependencies": {"@types/jest": "^27.4.0", "@types/node": "^17.0.18", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.5.1", "prettier": "^2.5.1", "ts-jest": "^27.1.3", "typedoc": "^0.22.11", "typedoc-plugin-markdown": "^3.11.14", "typescript": "^4.5.5"}}