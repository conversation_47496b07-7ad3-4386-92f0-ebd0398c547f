import express, { Response, NextFunction } from 'express';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

/**
 * @swagger
 * /stock:
 *   get:
 *     summary: Get stock levels for all products
 *     tags: [Stock]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: lowStock
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: categoryId
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Stock levels retrieved successfully
 */
router.get('/', requireAuth, requirePermission('inventory:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { page, limit, lowStock, categoryId } = req.query;
  const skip = ((page as number) - 1) * (limit as number);

  const where: any = {
    product: {
      isActive: true
    }
  };

  if (categoryId) {
    where.product.categoryId = categoryId as string;
  }

  if (lowStock === 'true') {
    // Find products where quantity <= minThreshold
    where.quantity = {
      lte: (prisma as any).raw('stock.min_threshold')
    };
  }

  const [stockItems, total] = await Promise.all([
    prisma.stock.findMany({
      where,
      skip,
      take: limit as number,
      include: {
        product: {
          include: {
            category: true
          }
        }
      },
      orderBy: {
        product: {
          name: 'asc'
        }
      }
    }),
    prisma.stock.count({ where })
  ]);

  // Add stock status to each item
  const stockResponse = stockItems.map(stock => ({
    ...stock,
    status: stock.quantity <= stock.minThreshold ? 'low' :
            stock.quantity === 0 ? 'out' : 'normal',
    isLowStock: stock.quantity <= stock.minThreshold,
    isOutOfStock: stock.quantity === 0,
  }));

  utils.sendSuccess(res, {
    stock: stockResponse,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / (limit as number)),
    }
  });
}));

/**
 * @swagger
 * /stock/{productId}:
 *   get:
 *     summary: Get stock level for specific product
 *     tags: [Stock]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Stock level retrieved successfully
 *       404:
 *         description: Product not found
 */
router.get('/:productId', requireAuth, requirePermission('inventory:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { productId } = req.params;

  const stock = await prisma.stock.findUnique({
    where: { productId },
    include: {
      product: {
        include: {
          category: true
        }
      }
    }
  });

  if (!stock) {
    utils.sendError(res, 'Stock record not found', 404);
    return;
  }

  const stockResponse = {
    ...stock,
    status: stock.quantity <= stock.minThreshold ? 'low' :
            stock.quantity === 0 ? 'out' : 'normal',
    isLowStock: stock.quantity <= stock.minThreshold,
    isOutOfStock: stock.quantity === 0,
  };

  utils.sendSuccess(res, { stock: stockResponse });
}));

/**
 * @swagger
 * /stock/{productId}/adjust:
 *   put:
 *     summary: Adjust stock level for a product
 *     tags: [Stock]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - adjustment
 *               - reason
 *             properties:
 *               adjustment:
 *                 type: integer
 *                 description: Positive for increase, negative for decrease
 *               reason:
 *                 type: string
 *                 enum: [restock, sale, damage, theft, correction, return]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stock adjusted successfully
 */
router.put('/:productId/adjust', requireAuth, requirePermission('inventory:update'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { productId } = req.params;
  const { adjustment, reason, notes } = req.body;

  if (!adjustment || typeof adjustment !== 'number') {
    utils.sendError(res, 'Valid adjustment amount required', 400);
    return;
  }

  if (!reason) {
    utils.sendError(res, 'Reason for adjustment required', 400);
    return;
  }

  // Get current stock
  const currentStock = await prisma.stock.findUnique({
    where: { productId },
    include: {
      product: true
    }
  });

  if (!currentStock) {
    utils.sendError(res, 'Stock record not found', 404);
    return;
  }

  const newQuantity = currentStock.quantity + adjustment;

  if (newQuantity < 0) {
    utils.sendError(res, 'Adjustment would result in negative stock', 400);
    return;
  }

  // Update stock
  const updatedStock = await prisma.stock.update({
    where: { productId },
    data: {
      quantity: newQuantity,
      lastRestocked: adjustment > 0 ? new Date() : currentStock.lastRestocked,
    },
    include: {
      product: {
        include: {
          category: true
        }
      }
    }
  });

  // Log the adjustment
  console.log(`Stock adjustment: Product ${currentStock.product.name}, ${adjustment > 0 ? '+' : ''}${adjustment}, Reason: ${reason}, User: ${req.user?.userId}`);

  const stockResponse = {
    ...updatedStock,
    status: updatedStock.quantity <= updatedStock.minThreshold ? 'low' :
            updatedStock.quantity === 0 ? 'out' : 'normal',
    isLowStock: updatedStock.quantity <= updatedStock.minThreshold,
    isOutOfStock: updatedStock.quantity === 0,
    previousQuantity: currentStock.quantity,
    adjustment,
  };

  utils.sendSuccess(res, { stock: stockResponse }, 'Stock adjusted successfully');
}));

/**
 * @swagger
 * /stock/{productId}/set:
 *   put:
 *     summary: Set exact stock level for a product
 *     tags: [Stock]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity
 *             properties:
 *               quantity:
 *                 type: integer
 *                 minimum: 0
 *               minThreshold:
 *                 type: integer
 *                 minimum: 0
 *               maxThreshold:
 *                 type: integer
 *                 minimum: 0
 *               reason:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stock level set successfully
 */
router.put('/:productId/set', requireAuth, requirePermission('inventory:update'), validation.validateParams(validation.idSchema), validation.validateBody(validation.stockUpdateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { productId } = req.params;
  const { quantity, minThreshold, maxThreshold, reason, notes } = req.body;

  // Check if stock record exists
  const currentStock = await prisma.stock.findUnique({
    where: { productId },
    include: {
      product: true
    }
  });

  if (!currentStock) {
    utils.sendError(res, 'Stock record not found', 404);
    return;
  }

  // Validate thresholds
  if (maxThreshold && minThreshold && maxThreshold <= minThreshold) {
    utils.sendError(res, 'Max threshold must be greater than min threshold', 400);
    return;
  }

  // Update stock
  const updateData: any = { quantity };
  if (minThreshold !== undefined) updateData.minThreshold = minThreshold;
  if (maxThreshold !== undefined) updateData.maxThreshold = maxThreshold;
  if (quantity > currentStock.quantity) updateData.lastRestocked = new Date();

  const updatedStock = await prisma.stock.update({
    where: { productId },
    data: updateData,
    include: {
      product: {
        include: {
          category: true
        }
      }
    }
  });

  // Log the change
  console.log(`Stock set: Product ${currentStock.product.name}, ${currentStock.quantity} -> ${quantity}, Reason: ${reason || 'Manual update'}, User: ${req.user?.userId}`);

  const stockResponse = {
    ...updatedStock,
    status: updatedStock.quantity <= updatedStock.minThreshold ? 'low' :
            updatedStock.quantity === 0 ? 'out' : 'normal',
    isLowStock: updatedStock.quantity <= updatedStock.minThreshold,
    isOutOfStock: updatedStock.quantity === 0,
    previousQuantity: currentStock.quantity,
  };

  utils.sendSuccess(res, { stock: stockResponse }, 'Stock level set successfully');
}));

/**
 * @swagger
 * /stock/low:
 *   get:
 *     summary: Get products with low stock
 *     tags: [Stock]
 *     responses:
 *       200:
 *         description: Low stock products retrieved successfully
 */
router.get('/low', requireAuth, requirePermission('inventory:read'), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const lowStockItems = await prisma.stock.findMany({
    where: {
      quantity: {
        lte: (prisma as any).raw('stock.min_threshold')
      },
      product: {
        isActive: true
      }
    },
    include: {
      product: {
        include: {
          category: true
        }
      }
    },
    orderBy: {
      quantity: 'asc'
    }
  });

  const stockResponse = lowStockItems.map(stock => ({
    ...stock,
    status: stock.quantity === 0 ? 'out' : 'low',
    isLowStock: true,
    isOutOfStock: stock.quantity === 0,
  }));

  utils.sendSuccess(res, {
    lowStockItems: stockResponse,
    count: stockResponse.length
  });
}));

/**
 * @swagger
 * /stock/summary:
 *   get:
 *     summary: Get stock summary statistics
 *     tags: [Stock]
 *     responses:
 *       200:
 *         description: Stock summary retrieved successfully
 */
router.get('/summary', requireAuth, requirePermission('inventory:read'), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const [
    totalProducts,
    lowStockCount,
    outOfStockCount,
    totalValue
  ] = await Promise.all([
    prisma.stock.count({
      where: {
        product: { isActive: true }
      }
    }),
    prisma.stock.count({
      where: {
        quantity: {
          lte: (prisma as any).raw('stock.min_threshold')
        },
        product: { isActive: true }
      }
    }),
    prisma.stock.count({
      where: {
        quantity: 0,
        product: { isActive: true }
      }
    }),
    prisma.stock.aggregate({
      where: {
        product: { isActive: true }
      },
      _sum: {
        quantity: true
      }
    })
  ]);

  // Calculate total inventory value
  const inventoryValue = await prisma.stock.findMany({
    where: {
      product: { isActive: true }
    },
    include: {
      product: {
        select: {
          cost: true,
          price: true
        }
      }
    }
  });

  const totalCostValue = inventoryValue.reduce((sum, item) => {
    return sum + (item.quantity * (item.product.cost || 0));
  }, 0);

  const totalRetailValue = inventoryValue.reduce((sum, item) => {
    return sum + (item.quantity * item.product.price);
  }, 0);

  const summary = {
    totalProducts,
    lowStockCount,
    outOfStockCount,
    totalQuantity: totalValue._sum.quantity || 0,
    totalCostValue: totalCostValue,
    totalRetailValue: totalRetailValue,
    healthyStockCount: totalProducts - lowStockCount,
    lowStockPercentage: totalProducts > 0 ? ((lowStockCount / totalProducts) * 100).toFixed(2) : 0,
  };

  utils.sendSuccess(res, { summary });
}));

export default router;
