@forward "@material/base" as mdc-base-*;
@forward "@material/feature-targeting" as mdc-feature-*;
@forward "@material/theme/all-theme-deprecated" as mdc-theme-* hide $mdc-theme-red-50, $mdc-theme-red-100, $mdc-theme-red-200, $mdc-theme-red-300, $mdc-theme-red-400, $mdc-theme-red-500, $mdc-theme-red-600, $mdc-theme-red-700, $mdc-theme-red-800, $mdc-theme-red-900, $mdc-theme-red-a100, $mdc-theme-red-a200, $mdc-theme-red-a400, $mdc-theme-red-a700, $mdc-theme-pink-50, $mdc-theme-pink-100, $mdc-theme-pink-200, $mdc-theme-pink-300, $mdc-theme-pink-400, $mdc-theme-pink-500, $mdc-theme-pink-600, $mdc-theme-pink-700, $mdc-theme-pink-800, $mdc-theme-pink-900, $mdc-theme-pink-a100, $mdc-theme-pink-a200, $mdc-theme-pink-a400, $mdc-theme-pink-a700, $mdc-theme-purple-50, $mdc-theme-purple-100, $mdc-theme-purple-200, $mdc-theme-purple-300, $mdc-theme-purple-400, $mdc-theme-purple-500, $mdc-theme-purple-600, $mdc-theme-purple-700, $mdc-theme-purple-800, $mdc-theme-purple-900, $mdc-theme-purple-a100, $mdc-theme-purple-a200, $mdc-theme-purple-a400, $mdc-theme-purple-a700, $mdc-theme-deep-purple-50, $mdc-theme-deep-purple-100, $mdc-theme-deep-purple-200, $mdc-theme-deep-purple-300, $mdc-theme-deep-purple-400, $mdc-theme-deep-purple-500, $mdc-theme-deep-purple-600, $mdc-theme-deep-purple-700, $mdc-theme-deep-purple-800, $mdc-theme-deep-purple-900, $mdc-theme-deep-purple-a100, $mdc-theme-deep-purple-a200, $mdc-theme-deep-purple-a400, $mdc-theme-deep-purple-a700, $mdc-theme-indigo-50, $mdc-theme-indigo-100, $mdc-theme-indigo-200, $mdc-theme-indigo-300, $mdc-theme-indigo-400, $mdc-theme-indigo-500, $mdc-theme-indigo-600, $mdc-theme-indigo-700, $mdc-theme-indigo-800, $mdc-theme-indigo-900, $mdc-theme-indigo-a100, $mdc-theme-indigo-a200, $mdc-theme-indigo-a400, $mdc-theme-indigo-a700, $mdc-theme-blue-50, $mdc-theme-blue-100, $mdc-theme-blue-200, $mdc-theme-blue-300, $mdc-theme-blue-400, $mdc-theme-blue-500, $mdc-theme-blue-600, $mdc-theme-blue-700, $mdc-theme-blue-800, $mdc-theme-blue-900, $mdc-theme-blue-a100, $mdc-theme-blue-a200, $mdc-theme-blue-a400, $mdc-theme-blue-a700, $mdc-theme-light-blue-50, $mdc-theme-light-blue-100, $mdc-theme-light-blue-200, $mdc-theme-light-blue-300, $mdc-theme-light-blue-400, $mdc-theme-light-blue-500, $mdc-theme-light-blue-600, $mdc-theme-light-blue-700, $mdc-theme-light-blue-800, $mdc-theme-light-blue-900, $mdc-theme-light-blue-a100, $mdc-theme-light-blue-a200, $mdc-theme-light-blue-a400, $mdc-theme-light-blue-a700, $mdc-theme-cyan-50, $mdc-theme-cyan-100, $mdc-theme-cyan-200, $mdc-theme-cyan-300, $mdc-theme-cyan-400, $mdc-theme-cyan-500, $mdc-theme-cyan-600, $mdc-theme-cyan-700, $mdc-theme-cyan-800, $mdc-theme-cyan-900, $mdc-theme-cyan-a100, $mdc-theme-cyan-a200, $mdc-theme-cyan-a400, $mdc-theme-cyan-a700, $mdc-theme-teal-50, $mdc-theme-teal-100, $mdc-theme-teal-200, $mdc-theme-teal-300, $mdc-theme-teal-400, $mdc-theme-teal-500, $mdc-theme-teal-600, $mdc-theme-teal-700, $mdc-theme-teal-800, $mdc-theme-teal-900, $mdc-theme-teal-a100, $mdc-theme-teal-a200, $mdc-theme-teal-a400, $mdc-theme-teal-a700, $mdc-theme-green-50, $mdc-theme-green-100, $mdc-theme-green-200, $mdc-theme-green-300, $mdc-theme-green-400, $mdc-theme-green-500, $mdc-theme-green-600, $mdc-theme-green-700, $mdc-theme-green-800, $mdc-theme-green-900, $mdc-theme-green-a100, $mdc-theme-green-a200, $mdc-theme-green-a400, $mdc-theme-green-a700, $mdc-theme-light-green-50, $mdc-theme-light-green-100, $mdc-theme-light-green-200, $mdc-theme-light-green-300, $mdc-theme-light-green-400, $mdc-theme-light-green-500, $mdc-theme-light-green-600, $mdc-theme-light-green-700, $mdc-theme-light-green-800, $mdc-theme-light-green-900, $mdc-theme-light-green-a100, $mdc-theme-light-green-a200, $mdc-theme-light-green-a400, $mdc-theme-light-green-a700, $mdc-theme-lime-50, $mdc-theme-lime-100, $mdc-theme-lime-200, $mdc-theme-lime-300, $mdc-theme-lime-400, $mdc-theme-lime-500, $mdc-theme-lime-600, $mdc-theme-lime-700, $mdc-theme-lime-800, $mdc-theme-lime-900, $mdc-theme-lime-a100, $mdc-theme-lime-a200, $mdc-theme-lime-a400, $mdc-theme-lime-a700, $mdc-theme-yellow-50, $mdc-theme-yellow-100, $mdc-theme-yellow-200, $mdc-theme-yellow-300, $mdc-theme-yellow-400, $mdc-theme-yellow-500, $mdc-theme-yellow-600, $mdc-theme-yellow-700, $mdc-theme-yellow-800, $mdc-theme-yellow-900, $mdc-theme-yellow-a100, $mdc-theme-yellow-a200, $mdc-theme-yellow-a400, $mdc-theme-yellow-a700, $mdc-theme-amber-50, $mdc-theme-amber-100, $mdc-theme-amber-200, $mdc-theme-amber-300, $mdc-theme-amber-400, $mdc-theme-amber-500, $mdc-theme-amber-600, $mdc-theme-amber-700, $mdc-theme-amber-800, $mdc-theme-amber-900, $mdc-theme-amber-a100, $mdc-theme-amber-a200, $mdc-theme-amber-a400, $mdc-theme-amber-a700, $mdc-theme-orange-50, $mdc-theme-orange-100, $mdc-theme-orange-200, $mdc-theme-orange-300, $mdc-theme-orange-400, $mdc-theme-orange-500, $mdc-theme-orange-600, $mdc-theme-orange-700, $mdc-theme-orange-800, $mdc-theme-orange-900, $mdc-theme-orange-a100, $mdc-theme-orange-a200, $mdc-theme-orange-a400, $mdc-theme-orange-a700, $mdc-theme-deep-orange-50, $mdc-theme-deep-orange-100, $mdc-theme-deep-orange-200, $mdc-theme-deep-orange-300, $mdc-theme-deep-orange-400, $mdc-theme-deep-orange-500, $mdc-theme-deep-orange-600, $mdc-theme-deep-orange-700, $mdc-theme-deep-orange-800, $mdc-theme-deep-orange-900, $mdc-theme-deep-orange-a100, $mdc-theme-deep-orange-a200, $mdc-theme-deep-orange-a400, $mdc-theme-deep-orange-a700, $mdc-theme-brown-50, $mdc-theme-brown-100, $mdc-theme-brown-200, $mdc-theme-brown-300, $mdc-theme-brown-400, $mdc-theme-brown-500, $mdc-theme-brown-600, $mdc-theme-brown-700, $mdc-theme-brown-800, $mdc-theme-brown-900, $mdc-theme-grey-50, $mdc-theme-grey-100, $mdc-theme-grey-200, $mdc-theme-grey-300, $mdc-theme-grey-400, $mdc-theme-grey-500, $mdc-theme-grey-600, $mdc-theme-grey-700, $mdc-theme-grey-800, $mdc-theme-grey-900, $mdc-theme-blue-grey-50, $mdc-theme-blue-grey-100, $mdc-theme-blue-grey-200, $mdc-theme-blue-grey-300, $mdc-theme-blue-grey-400, $mdc-theme-blue-grey-500, $mdc-theme-blue-grey-600, $mdc-theme-blue-grey-700, $mdc-theme-blue-grey-800, $mdc-theme-blue-grey-900;
@forward "@material/animation" as mdc-animation-* hide mdc-animation-enter, mdc-animation-exit-permanent, mdc-animation-exit-temporary, mdc-animation-standard;
@forward "./index" as mdc-elevation-* hide mdc-elevation-elevation;
@forward "./index" as mdc-* show mdc-elevation;
