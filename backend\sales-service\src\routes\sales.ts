import express, { Response, NextFunction } from 'express';
import axios from 'axios';
import { prisma, validation, utils } from '@shop/shared';
import { AuthenticatedRequest } from '@shop/shared/src/types';

const router = express.Router();

// Authentication middleware
const requireAuth = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  if (!req.user) {
    utils.sendError(res, 'Authentication required', 401);
    return;
  }
  next();
};

// Permission middleware
const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.permissions.includes(permission)) {
      utils.sendError(res, 'Insufficient permissions', 403);
      return;
    }
    next();
  };
};

// Service URLs
const INVENTORY_SERVICE_URL = process.env.INVENTORY_SERVICE_URL || 'http://localhost:3002';

/**
 * @swagger
 * /sales:
 *   get:
 *     summary: Get all sales
 *     tags: [Sales]
 */
router.get('/', requireAuth, requirePermission('sales:read'), validation.validateQuery(validation.paginationSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { page, limit, userId, customerId, dateFrom, dateTo } = req.query;
  const skip = ((page as number) - 1) * (limit as number);

  const where: any = {};

  if (userId) where.userId = userId as string;
  if (customerId) where.customerId = customerId as string;

  if (dateFrom || dateTo) {
    where.createdAt = {};
    if (dateFrom) where.createdAt.gte = new Date(dateFrom as string);
    if (dateTo) where.createdAt.lte = new Date(dateTo as string);
  }

  // Non-admin users can only see their own sales
  if (!req.user?.roles.includes('admin')) {
    where.userId = req.user?.userId;
  }

  const [sales, total] = await Promise.all([
    prisma.sale.findMany({
      where,
      skip,
      take: limit as number,
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        },
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            phone: true,
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        },
        payments: true,
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.sale.count({ where })
  ]);

  utils.sendSuccess(res, {
    sales,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / (limit as number)),
    }
  });
}));

/**
 * @swagger
 * /sales/{id}:
 *   get:
 *     summary: Get sale by ID
 *     tags: [Sales]
 */
router.get('/:id', requireAuth, requirePermission('sales:read'), validation.validateParams(validation.idSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const sale = await prisma.sale.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          username: true,
        }
      },
      customer: true,
      items: {
        include: {
          product: true
        }
      },
      payments: true,
      receipt: true,
    }
  });

  if (!sale) {
    utils.sendError(res, 'Sale not found', 404);
    return;
  }

  // Non-admin users can only see their own sales
  if (!req.user?.roles.includes('admin') && sale.userId !== req.user?.userId) {
    utils.sendError(res, 'Access denied', 403);
    return;
  }

  utils.sendSuccess(res, { sale });
}));

/**
 * @swagger
 * /sales:
 *   post:
 *     summary: Process a new sale
 *     tags: [Sales]
 */
router.post('/', requireAuth, requirePermission('sales:create'), validation.validateBody(validation.saleCreateSchema), utils.handleAsyncError(async (req: AuthenticatedRequest, res: Response) => {
  const { customerId, items, discount = 0, tax = 0, payments = [] } = req.body;
  const userId = req.user?.userId;

  if (!userId) {
    utils.sendError(res, 'User ID required', 400);
    return;
  }

  // Validate items and check stock availability
  const productIds = items.map((item: any) => item.productId);
  
  try {
    // Check product availability via inventory service
    const stockCheckPromises = productIds.map(async (productId: string) => {
      const response = await axios.get(`${INVENTORY_SERVICE_URL}/stock/${productId}`, {
        headers: {
          'x-user-id': req.user?.userId,
          'x-user-roles': JSON.stringify(req.user?.roles),
          'x-user-permissions': JSON.stringify(req.user?.permissions),
        }
      });
      return response.data.data.stock;
    });

    const stockLevels = await Promise.all(stockCheckPromises);
    
    // Validate stock availability
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const stock = stockLevels[i];
      
      if (!stock || stock.quantity < item.quantity) {
        utils.sendError(res, `Insufficient stock for product ${item.productId}. Available: ${stock?.quantity || 0}, Requested: ${item.quantity}`, 400);
        return;
      }
    }
  } catch (error: any) {
    console.error('Stock check failed:', error.message);
    utils.sendError(res, 'Unable to verify stock levels', 500);
    return;
  }

  // Calculate totals
  const subtotal = items.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0);
  const total = subtotal - discount + tax;

  // Validate payments
  if (payments.length > 0) {
    const totalPayments = payments.reduce((sum: number, payment: any) => sum + payment.amount, 0);
    if (Math.abs(totalPayments - total) > 0.01) {
      utils.sendError(res, 'Payment amount does not match sale total', 400);
      return;
    }
  }

  // Generate sale number
  const saleNumber = utils.generateSaleNumber();

  try {
    // Create sale with transaction
    const sale = await prisma.$transaction(async (tx) => {
      // Create sale
      const newSale = await tx.sale.create({
        data: {
          saleNumber,
          userId,
          customerId,
          subtotal,
          tax,
          discount,
          total,
          status: 'completed',
        }
      });

      // Create sale items
      const saleItems = await Promise.all(
        items.map((item: any) =>
          tx.saleItem.create({
            data: {
              saleId: newSale.id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              total: item.quantity * item.unitPrice,
            }
          })
        )
      );

      // Create payments
      const salePayments = await Promise.all(
        payments.map((payment: any) =>
          tx.payment.create({
            data: {
              saleId: newSale.id,
              method: payment.method,
              amount: payment.amount,
              reference: payment.reference,
              status: 'completed',
            }
          })
        )
      );

      return { ...newSale, items: saleItems, payments: salePayments };
    });

    // Update inventory levels via inventory service
    try {
      const inventoryUpdatePromises = items.map(async (item: any) => {
        await axios.put(`${INVENTORY_SERVICE_URL}/stock/${item.productId}/adjust`, {
          adjustment: -item.quantity,
          reason: 'sale',
          notes: `Sale ${saleNumber}`,
        }, {
          headers: {
            'x-user-id': req.user?.userId,
            'x-user-roles': JSON.stringify(req.user?.roles),
            'x-user-permissions': JSON.stringify(req.user?.permissions),
          }
        });
      });

      await Promise.all(inventoryUpdatePromises);
    } catch (error: any) {
      console.error('Inventory update failed:', error.message);
      // Note: In a production system, you might want to implement compensation logic here
    }

    // Fetch complete sale data
    const completeSale = await prisma.sale.findUnique({
      where: { id: sale.id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            username: true,
          }
        },
        customer: true,
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        },
        payments: true,
      }
    });

    utils.sendSuccess(res, { sale: completeSale }, 'Sale processed successfully', 201);
  } catch (error) {
    console.error('Sale creation failed:', error);
    utils.sendError(res, 'Failed to process sale', 500);
  }
}));

export default router;
