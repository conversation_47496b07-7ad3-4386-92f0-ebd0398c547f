{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_visit", "_default", "exports", "default", "declare", "types", "t", "traverse", "assertVersion", "name", "visitor", "visitors", "merge", "getVisitor", "CallExpression", "path", "_this$availableHelper", "availableHelper", "call", "callee", "get", "isMemberExpression", "obj", "isIdentifier", "helper", "addHelper", "isArrowFunctionExpression", "replaceWith", "body", "callExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport type { types as t } from \"@babel/core\";\nimport { getVisitor } from \"./regenerator/visit.ts\";\n\nexport default declare(({ types: t, traverse, assertVersion }) => {\n  assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-regenerator\",\n\n    visitor: traverse.visitors.merge([\n      getVisitor(t),\n      {\n        // We visit CallExpression so that we always transform\n        // regeneratorRuntime.*() before babel-plugin-polyfill-regenerator.\n        CallExpression(path) {\n          if (!process.env.BABEL_8_BREAKING) {\n            if (!this.availableHelper?.(\"regeneratorRuntime\")) {\n              // When using an older @babel/helpers version, fallback\n              // to the old behavior.\n              return;\n            }\n          }\n\n          const callee = path.get(\"callee\");\n          if (!callee.isMemberExpression()) return;\n\n          const obj = callee.get(\"object\");\n          if (obj.isIdentifier({ name: \"regeneratorRuntime\" })) {\n            const helper = this.addHelper(\"regeneratorRuntime\") as\n              | t.Identifier\n              | t.ArrowFunctionExpression;\n\n            if (!process.env.BABEL_8_BREAKING) {\n              if (\n                // It's necessary to avoid the IIFE when using older Babel versions.\n                t.isArrowFunctionExpression(helper)\n              ) {\n                obj.replaceWith(helper.body);\n                return;\n              }\n            }\n\n            obj.replaceWith(t.callExpression(helper, []));\n          }\n        },\n      },\n    ]),\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAAoD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAErC,IAAAC,0BAAO,EAAC,CAAC;EAAEC,KAAK,EAAEC,CAAC;EAAEC,QAAQ;EAAEC;AAAc,CAAC,KAAK;EAChEA,aAAa,CAAkB,CAAE,CAAC;EAElC,OAAO;IACLC,IAAI,EAAE,uBAAuB;IAE7BC,OAAO,EAAEH,QAAQ,CAACI,QAAQ,CAACC,KAAK,CAAC,CAC/B,IAAAC,iBAAU,EAACP,CAAC,CAAC,EACb;MAGEQ,cAAcA,CAACC,IAAI,EAAE;QACgB;UAAA,IAAAC,qBAAA;UACjC,IAAI,GAAAA,qBAAA,GAAC,IAAI,CAACC,eAAe,aAApBD,qBAAA,CAAAE,IAAA,KAAI,EAAmB,oBAAoB,CAAC,GAAE;YAGjD;UACF;QACF;QAEA,MAAMC,MAAM,GAAGJ,IAAI,CAACK,GAAG,CAAC,QAAQ,CAAC;QACjC,IAAI,CAACD,MAAM,CAACE,kBAAkB,CAAC,CAAC,EAAE;QAElC,MAAMC,GAAG,GAAGH,MAAM,CAACC,GAAG,CAAC,QAAQ,CAAC;QAChC,IAAIE,GAAG,CAACC,YAAY,CAAC;UAAEd,IAAI,EAAE;QAAqB,CAAC,CAAC,EAAE;UACpD,MAAMe,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,oBAAoB,CAErB;UAEM;YACjC,IAEEnB,CAAC,CAACoB,yBAAyB,CAACF,MAAM,CAAC,EACnC;cACAF,GAAG,CAACK,WAAW,CAACH,MAAM,CAACI,IAAI,CAAC;cAC5B;YACF;UACF;UAEAN,GAAG,CAACK,WAAW,CAACrB,CAAC,CAACuB,cAAc,CAACL,MAAM,EAAE,EAAE,CAAC,CAAC;QAC/C;MACF;IACF,CAAC,CACF;EACH,CAAC;AACH,CAAC,CAAC", "ignoreList": []}