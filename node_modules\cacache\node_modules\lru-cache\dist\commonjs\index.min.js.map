{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/**\n * @module LRUCache\n */\n\n// module-private names and types\ntype Perf = { now: () => number }\nconst perf: Perf =\n  typeof performance === 'object' &&\n  performance &&\n  typeof performance.now === 'function'\n    ? performance\n    : Date\n\nconst warned = new Set<string>()\n\n// either a function or a class\ntype ForC = ((...a: any[]) => any) | { new (...a: any[]): any }\n\n/* c8 ignore start */\nconst PROCESS = (\n  typeof process === 'object' && !!process ? process : {}\n) as { [k: string]: any }\n/* c8 ignore start */\n\nconst emitWarning = (\n  msg: string,\n  type: string,\n  code: string,\n  fn: ForC\n) => {\n  typeof PROCESS.emitWarning === 'function'\n    ? PROCESS.emitWarning(msg, type, code, fn)\n    : console.error(`[${code}] ${type}: ${msg}`)\n}\n\nlet AC = globalThis.AbortController\nlet AS = globalThis.AbortSignal\n\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n  //@ts-ignore\n  AS = class AbortSignal {\n    onabort?: (...a: any[]) => any\n    _onabort: ((...a: any[]) => any)[] = []\n    reason?: any\n    aborted: boolean = false\n    addEventListener(_: string, fn: (...a: any[]) => any) {\n      this._onabort.push(fn)\n    }\n  }\n  //@ts-ignore\n  AC = class AbortController {\n    constructor() {\n      warnACPolyfill()\n    }\n    signal = new AS()\n    abort(reason: any) {\n      if (this.signal.aborted) return\n      //@ts-ignore\n      this.signal.reason = reason\n      //@ts-ignore\n      this.signal.aborted = true\n      //@ts-ignore\n      for (const fn of this.signal._onabort) {\n        fn(reason)\n      }\n      this.signal.onabort?.(reason)\n    }\n  }\n  let printACPolyfillWarning =\n    PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1'\n  const warnACPolyfill = () => {\n    if (!printACPolyfillWarning) return\n    printACPolyfillWarning = false\n    emitWarning(\n      'AbortController is not defined. If using lru-cache in ' +\n        'node 14, load an AbortController polyfill from the ' +\n        '`node-abort-controller` package. A minimal polyfill is ' +\n        'provided for use by LRUCache.fetch(), but it should not be ' +\n        'relied upon in other contexts (eg, passing it to other APIs that ' +\n        'use AbortController/AbortSignal might have undesirable effects). ' +\n        'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.',\n      'NO_ABORT_CONTROLLER',\n      'ENOTSUP',\n      warnACPolyfill\n    )\n  }\n}\n/* c8 ignore stop */\n\nconst shouldWarn = (code: string) => !warned.has(code)\n\nconst TYPE = Symbol('type')\nexport type PosInt = number & { [TYPE]: 'Positive Integer' }\nexport type Index = number & { [TYPE]: 'LRUCache Index' }\n\nconst isPosInt = (n: any): n is PosInt =>\n  n && n === Math.floor(n) && n > 0 && isFinite(n)\n\nexport type UintArray = Uint8Array | Uint16Array | Uint32Array\nexport type NumberArray = UintArray | number[]\n\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max: number) =>\n  !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n    ? Uint8Array\n    : max <= Math.pow(2, 16)\n    ? Uint16Array\n    : max <= Math.pow(2, 32)\n    ? Uint32Array\n    : max <= Number.MAX_SAFE_INTEGER\n    ? ZeroArray\n    : null\n/* c8 ignore stop */\n\nclass ZeroArray extends Array<number> {\n  constructor(size: number) {\n    super(size)\n    this.fill(0)\n  }\n}\nexport type { ZeroArray }\nexport type { Stack }\n\nexport type StackLike = Stack | Index[]\nclass Stack {\n  heap: NumberArray\n  length: number\n  // private constructor\n  static #constructing: boolean = false\n  static create(max: number): StackLike {\n    const HeapCls = getUintArray(max)\n    if (!HeapCls) return []\n    Stack.#constructing = true\n    const s = new Stack(max, HeapCls)\n    Stack.#constructing = false\n    return s\n  }\n  constructor(\n    max: number,\n    HeapCls: { new (n: number): NumberArray }\n  ) {\n    /* c8 ignore start */\n    if (!Stack.#constructing) {\n      throw new TypeError('instantiate Stack using Stack.create(n)')\n    }\n    /* c8 ignore stop */\n    this.heap = new HeapCls(max)\n    this.length = 0\n  }\n  push(n: Index) {\n    this.heap[this.length++] = n\n  }\n  pop(): Index {\n    return this.heap[--this.length] as Index\n  }\n}\n\n/**\n * Promise representing an in-progress {@link LRUCache#fetch} call\n */\nexport type BackgroundFetch<V> = Promise<V | undefined> & {\n  __returned: BackgroundFetch<V> | undefined\n  __abortController: AbortController\n  __staleWhileFetching: V | undefined\n}\n\nexport type DisposeTask<K, V> = [\n  value: V,\n  key: K,\n  reason: LRUCache.DisposeReason\n]\n\nexport namespace LRUCache {\n  /**\n   * An integer greater than 0, reflecting the calculated size of items\n   */\n  export type Size = number\n\n  /**\n   * Integer greater than 0, representing some number of milliseconds, or the\n   * time at which a TTL started counting from.\n   */\n  export type Milliseconds = number\n\n  /**\n   * An integer greater than 0, reflecting a number of items\n   */\n  export type Count = number\n\n  /**\n   * The reason why an item was removed from the cache, passed\n   * to the {@link Disposer} methods.\n   *\n   * - `evict`: The item was evicted because it is the least recently used,\n   *   and the cache is full.\n   * - `set`: A new value was set, overwriting the old value being disposed.\n   * - `delete`: The item was explicitly deleted, either by calling\n   *   {@link LRUCache#delete}, {@link LRUCache#clear}, or\n   *   {@link LRUCache#set} with an undefined value.\n   * - `expire`: The item was removed due to exceeding its TTL.\n   * - `fetch`: A {@link OptionsBase#fetchMethod} operation returned\n   *   `undefined` or was aborted, causing the item to be deleted.\n   */\n  export type DisposeReason =\n    | 'evict'\n    | 'set'\n    | 'delete'\n    | 'expire'\n    | 'fetch'\n  /**\n   * A method called upon item removal, passed as the\n   * {@link OptionsBase.dispose} and/or\n   * {@link OptionsBase.disposeAfter} options.\n   */\n  export type Disposer<K, V> = (\n    value: V,\n    key: K,\n    reason: DisposeReason\n  ) => void\n\n  /**\n   * A function that returns the effective calculated size\n   * of an entry in the cache.\n   */\n  export type SizeCalculator<K, V> = (value: V, key: K) => Size\n\n  /**\n   * Options provided to the\n   * {@link OptionsBase.fetchMethod} function.\n   */\n  export interface FetcherOptions<K, V, FC = unknown> {\n    signal: AbortSignal\n    options: FetcherFetchOptions<K, V, FC>\n    /**\n     * Object provided in the {@link FetchOptions.context} option to\n     * {@link LRUCache#fetch}\n     */\n    context: FC\n  }\n\n  /**\n   * Occasionally, it may be useful to track the internal behavior of the\n   * cache, particularly for logging, debugging, or for behavior within the\n   * `fetchMethod`. To do this, you can pass a `status` object to the\n   * {@link LRUCache#fetch}, {@link LRUCache#get}, {@link LRUCache#set},\n   * {@link LRUCache#memo}, and {@link LRUCache#has} methods.\n   *\n   * The `status` option should be a plain JavaScript object. The following\n   * fields will be set on it appropriately, depending on the situation.\n   */\n  export interface Status<V> {\n    /**\n     * The status of a set() operation.\n     *\n     * - add: the item was not found in the cache, and was added\n     * - update: the item was in the cache, with the same value provided\n     * - replace: the item was in the cache, and replaced\n     * - miss: the item was not added to the cache for some reason\n     */\n    set?: 'add' | 'update' | 'replace' | 'miss'\n\n    /**\n     * the ttl stored for the item, or undefined if ttls are not used.\n     */\n    ttl?: Milliseconds\n\n    /**\n     * the start time for the item, or undefined if ttls are not used.\n     */\n    start?: Milliseconds\n\n    /**\n     * The timestamp used for TTL calculation\n     */\n    now?: Milliseconds\n\n    /**\n     * the remaining ttl for the item, or undefined if ttls are not used.\n     */\n    remainingTTL?: Milliseconds\n\n    /**\n     * The calculated size for the item, if sizes are used.\n     */\n    entrySize?: Size\n\n    /**\n     * The total calculated size of the cache, if sizes are used.\n     */\n    totalCalculatedSize?: Size\n\n    /**\n     * A flag indicating that the item was not stored, due to exceeding the\n     * {@link OptionsBase.maxEntrySize}\n     */\n    maxEntrySizeExceeded?: true\n\n    /**\n     * The old value, specified in the case of `set:'update'` or\n     * `set:'replace'`\n     */\n    oldValue?: V\n\n    /**\n     * The results of a {@link LRUCache#has} operation\n     *\n     * - hit: the item was found in the cache\n     * - stale: the item was found in the cache, but is stale\n     * - miss: the item was not found in the cache\n     */\n    has?: 'hit' | 'stale' | 'miss'\n\n    /**\n     * The status of a {@link LRUCache#fetch} operation.\n     * Note that this can change as the underlying fetch() moves through\n     * various states.\n     *\n     * - inflight: there is another fetch() for this key which is in process\n     * - get: there is no {@link OptionsBase.fetchMethod}, so\n     *   {@link LRUCache#get} was called.\n     * - miss: the item is not in cache, and will be fetched.\n     * - hit: the item is in the cache, and was resolved immediately.\n     * - stale: the item is in the cache, but stale.\n     * - refresh: the item is in the cache, and not stale, but\n     *   {@link FetchOptions.forceRefresh} was specified.\n     */\n    fetch?: 'get' | 'inflight' | 'miss' | 'hit' | 'stale' | 'refresh'\n\n    /**\n     * The {@link OptionsBase.fetchMethod} was called\n     */\n    fetchDispatched?: true\n\n    /**\n     * The cached value was updated after a successful call to\n     * {@link OptionsBase.fetchMethod}\n     */\n    fetchUpdated?: true\n\n    /**\n     * The reason for a fetch() rejection.  Either the error raised by the\n     * {@link OptionsBase.fetchMethod}, or the reason for an\n     * AbortSignal.\n     */\n    fetchError?: Error\n\n    /**\n     * The fetch received an abort signal\n     */\n    fetchAborted?: true\n\n    /**\n     * The abort signal received was ignored, and the fetch was allowed to\n     * continue.\n     */\n    fetchAbortIgnored?: true\n\n    /**\n     * The fetchMethod promise resolved successfully\n     */\n    fetchResolved?: true\n\n    /**\n     * The fetchMethod promise was rejected\n     */\n    fetchRejected?: true\n\n    /**\n     * The status of a {@link LRUCache#get} operation.\n     *\n     * - fetching: The item is currently being fetched.  If a previous value\n     *   is present and allowed, that will be returned.\n     * - stale: The item is in the cache, and is stale.\n     * - hit: the item is in the cache\n     * - miss: the item is not in the cache\n     */\n    get?: 'stale' | 'hit' | 'miss'\n\n    /**\n     * A fetch or get operation returned a stale value.\n     */\n    returnedStale?: true\n  }\n\n  /**\n   * options which override the options set in the LRUCache constructor\n   * when calling {@link LRUCache#fetch}.\n   *\n   * This is the union of {@link GetOptions} and {@link SetOptions}, plus\n   * {@link OptionsBase.noDeleteOnFetchRejection},\n   * {@link OptionsBase.allowStaleOnFetchRejection},\n   * {@link FetchOptions.forceRefresh}, and\n   * {@link FetcherOptions.context}\n   *\n   * Any of these may be modified in the {@link OptionsBase.fetchMethod}\n   * function, but the {@link GetOptions} fields will of course have no\n   * effect, as the {@link LRUCache#get} call already happened by the time\n   * the fetchMethod is called.\n   */\n  export interface FetcherFetchOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n      | 'noDeleteOnFetchRejection'\n      | 'allowStaleOnFetchRejection'\n      | 'ignoreFetchAbort'\n      | 'allowStaleOnFetchAbort'\n    > {\n    status?: Status<V>\n    size?: Size\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#fetch} method.\n   */\n  export interface FetchOptions<K, V, FC>\n    extends FetcherFetchOptions<K, V, FC> {\n    /**\n     * Set to true to force a re-load of the existing data, even if it\n     * is not yet stale.\n     */\n    forceRefresh?: boolean\n    /**\n     * Context provided to the {@link OptionsBase.fetchMethod} as\n     * the {@link FetcherOptions.context} param.\n     *\n     * If the FC type is specified as unknown (the default),\n     * undefined or void, then this is optional.  Otherwise, it will\n     * be required.\n     */\n    context?: FC\n    signal?: AbortSignal\n    status?: Status<V>\n  }\n  /**\n   * Options provided to {@link LRUCache#fetch} when the FC type is something\n   * other than `unknown`, `undefined`, or `void`\n   */\n  export interface FetchOptionsWithContext<K, V, FC>\n    extends FetchOptions<K, V, FC> {\n    context: FC\n  }\n  /**\n   * Options provided to {@link LRUCache#fetch} when the FC type is\n   * `undefined` or `void`\n   */\n  export interface FetchOptionsNoContext<K, V>\n    extends FetchOptions<K, V, undefined> {\n    context?: undefined\n  }\n\n  export interface MemoOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n      | 'noDeleteOnFetchRejection'\n      | 'allowStaleOnFetchRejection'\n      | 'ignoreFetchAbort'\n      | 'allowStaleOnFetchAbort'\n    > {\n    /**\n     * Set to true to force a re-load of the existing data, even if it\n     * is not yet stale.\n     */\n    forceRefresh?: boolean\n    /**\n     * Context provided to the {@link OptionsBase.memoMethod} as\n     * the {@link MemoizerOptions.context} param.\n     *\n     * If the FC type is specified as unknown (the default),\n     * undefined or void, then this is optional.  Otherwise, it will\n     * be required.\n     */\n    context?: FC\n    status?: Status<V>\n  }\n  /**\n   * Options provided to {@link LRUCache#memo} when the FC type is something\n   * other than `unknown`, `undefined`, or `void`\n   */\n  export interface MemoOptionsWithContext<K, V, FC>\n    extends MemoOptions<K, V, FC> {\n    context: FC\n  }\n  /**\n   * Options provided to {@link LRUCache#memo} when the FC type is\n   * `undefined` or `void`\n   */\n  export interface MemoOptionsNoContext<K, V>\n    extends MemoOptions<K, V, undefined> {\n    context?: undefined\n  }\n\n  /**\n   * Options provided to the\n   * {@link OptionsBase.memoMethod} function.\n   */\n  export interface MemoizerOptions<K, V, FC = unknown> {\n    options: MemoizerMemoOptions<K, V, FC>\n    /**\n     * Object provided in the {@link MemoOptions.context} option to\n     * {@link LRUCache#memo}\n     */\n    context: FC\n  }\n\n  /**\n   * options which override the options set in the LRUCache constructor\n   * when calling {@link LRUCache#memo}.\n   *\n   * This is the union of {@link GetOptions} and {@link SetOptions}, plus\n   * {@link MemoOptions.forceRefresh}, and\n   * {@link MemoerOptions.context}\n   *\n   * Any of these may be modified in the {@link OptionsBase.memoMethod}\n   * function, but the {@link GetOptions} fields will of course have no\n   * effect, as the {@link LRUCache#get} call already happened by the time\n   * the memoMethod is called.\n   */\n  export interface MemoizerMemoOptions<K, V, FC = unknown>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      | 'allowStale'\n      | 'updateAgeOnGet'\n      | 'noDeleteOnStaleGet'\n      | 'sizeCalculation'\n      | 'ttl'\n      | 'noDisposeOnSet'\n      | 'noUpdateTTL'\n    > {\n    status?: Status<V>\n    size?: Size\n    start?: Milliseconds\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#has} method.\n   */\n  export interface HasOptions<K, V, FC>\n    extends Pick<OptionsBase<K, V, FC>, 'updateAgeOnHas'> {\n    status?: Status<V>\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#get} method.\n   */\n  export interface GetOptions<K, V, FC>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      'allowStale' | 'updateAgeOnGet' | 'noDeleteOnStaleGet'\n    > {\n    status?: Status<V>\n  }\n\n  /**\n   * Options that may be passed to the {@link LRUCache#peek} method.\n   */\n  export interface PeekOptions<K, V, FC>\n    extends Pick<OptionsBase<K, V, FC>, 'allowStale'> {}\n\n  /**\n   * Options that may be passed to the {@link LRUCache#set} method.\n   */\n  export interface SetOptions<K, V, FC>\n    extends Pick<\n      OptionsBase<K, V, FC>,\n      'sizeCalculation' | 'ttl' | 'noDisposeOnSet' | 'noUpdateTTL'\n    > {\n    /**\n     * If size tracking is enabled, then setting an explicit size\n     * in the {@link LRUCache#set} call will prevent calling the\n     * {@link OptionsBase.sizeCalculation} function.\n     */\n    size?: Size\n    /**\n     * If TTL tracking is enabled, then setting an explicit start\n     * time in the {@link LRUCache#set} call will override the\n     * default time from `performance.now()` or `Date.now()`.\n     *\n     * Note that it must be a valid value for whichever time-tracking\n     * method is in use.\n     */\n    start?: Milliseconds\n    status?: Status<V>\n  }\n\n  /**\n   * The type signature for the {@link OptionsBase.fetchMethod} option.\n   */\n  export type Fetcher<K, V, FC = unknown> = (\n    key: K,\n    staleValue: V | undefined,\n    options: FetcherOptions<K, V, FC>\n  ) => Promise<V | undefined | void> | V | undefined | void\n\n  /**\n   * the type signature for the {@link OptionsBase.memoMethod} option.\n   */\n  export type Memoizer<K, V, FC = unknown> = (\n    key: K,\n    staleValue: V | undefined,\n    options: MemoizerOptions<K, V, FC>\n  ) => V\n\n  /**\n   * Options which may be passed to the {@link LRUCache} constructor.\n   *\n   * Most of these may be overridden in the various options that use\n   * them.\n   *\n   * Despite all being technically optional, the constructor requires that\n   * a cache is at minimum limited by one or more of {@link OptionsBase.max},\n   * {@link OptionsBase.ttl}, or {@link OptionsBase.maxSize}.\n   *\n   * If {@link OptionsBase.ttl} is used alone, then it is strongly advised\n   * (and in fact required by the type definitions here) that the cache\n   * also set {@link OptionsBase.ttlAutopurge}, to prevent potentially\n   * unbounded storage.\n   *\n   * All options are also available on the {@link LRUCache} instance, making\n   * it safe to pass an LRUCache instance as the options argumemnt to\n   * make another empty cache of the same type.\n   *\n   * Some options are marked as read-only, because changing them after\n   * instantiation is not safe. Changing any of the other options will of\n   * course only have an effect on subsequent method calls.\n   */\n  export interface OptionsBase<K, V, FC> {\n    /**\n     * The maximum number of items to store in the cache before evicting\n     * old entries. This is read-only on the {@link LRUCache} instance,\n     * and may not be overridden.\n     *\n     * If set, then storage space will be pre-allocated at construction\n     * time, and the cache will perform significantly faster.\n     *\n     * Note that significantly fewer items may be stored, if\n     * {@link OptionsBase.maxSize} and/or {@link OptionsBase.ttl} are also\n     * set.\n     *\n     * **It is strongly recommended to set a `max` to prevent unbounded growth\n     * of the cache.**\n     */\n    max?: Count\n\n    /**\n     * Max time in milliseconds for items to live in cache before they are\n     * considered stale.  Note that stale items are NOT preemptively removed by\n     * default, and MAY live in the cache, contributing to its LRU max, long\n     * after they have expired, unless {@link OptionsBase.ttlAutopurge} is\n     * set.\n     *\n     * If set to `0` (the default value), then that means \"do not track\n     * TTL\", not \"expire immediately\".\n     *\n     * Also, as this cache is optimized for LRU/MRU operations, some of\n     * the staleness/TTL checks will reduce performance, as they will incur\n     * overhead by deleting items.\n     *\n     * This is not primarily a TTL cache, and does not make strong TTL\n     * guarantees. There is no pre-emptive pruning of expired items, but you\n     * _may_ set a TTL on the cache, and it will treat expired items as missing\n     * when they are fetched, and delete them.\n     *\n     * Optional, but must be a non-negative integer in ms if specified.\n     *\n     * This may be overridden by passing an options object to `cache.set()`.\n     *\n     * At least one of `max`, `maxSize`, or `TTL` is required. This must be a\n     * positive integer if set.\n     *\n     * Even if ttl tracking is enabled, **it is strongly recommended to set a\n     * `max` to prevent unbounded growth of the cache.**\n     *\n     * If ttl tracking is enabled, and `max` and `maxSize` are not set,\n     * and `ttlAutopurge` is not set, then a warning will be emitted\n     * cautioning about the potential for unbounded memory consumption.\n     * (The TypeScript definitions will also discourage this.)\n     */\n    ttl?: Milliseconds\n\n    /**\n     * Minimum amount of time in ms in which to check for staleness.\n     * Defaults to 1, which means that the current time is checked\n     * at most once per millisecond.\n     *\n     * Set to 0 to check the current time every time staleness is tested.\n     * (This reduces performance, and is theoretically unnecessary.)\n     *\n     * Setting this to a higher value will improve performance somewhat\n     * while using ttl tracking, albeit at the expense of keeping stale\n     * items around a bit longer than their TTLs would indicate.\n     *\n     * @default 1\n     */\n    ttlResolution?: Milliseconds\n\n    /**\n     * Preemptively remove stale items from the cache.\n     *\n     * Note that this may *significantly* degrade performance, especially if\n     * the cache is storing a large number of items. It is almost always best\n     * to just leave the stale items in the cache, and let them fall out as new\n     * items are added.\n     *\n     * Note that this means that {@link OptionsBase.allowStale} is a bit\n     * pointless, as stale items will be deleted almost as soon as they\n     * expire.\n     *\n     * Use with caution!\n     */\n    ttlAutopurge?: boolean\n\n    /**\n     * When using time-expiring entries with `ttl`, setting this to `true` will\n     * make each item's age reset to 0 whenever it is retrieved from cache with\n     * {@link LRUCache#get}, causing it to not expire. (It can still fall out\n     * of cache based on recency of use, of course.)\n     *\n     * Has no effect if {@link OptionsBase.ttl} is not set.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     */\n    updateAgeOnGet?: boolean\n\n    /**\n     * When using time-expiring entries with `ttl`, setting this to `true` will\n     * make each item's age reset to 0 whenever its presence in the cache is\n     * checked with {@link LRUCache#has}, causing it to not expire. (It can\n     * still fall out of cache based on recency of use, of course.)\n     *\n     * Has no effect if {@link OptionsBase.ttl} is not set.\n     */\n    updateAgeOnHas?: boolean\n\n    /**\n     * Allow {@link LRUCache#get} and {@link LRUCache#fetch} calls to return\n     * stale data, if available.\n     *\n     * By default, if you set `ttl`, stale items will only be deleted from the\n     * cache when you `get(key)`. That is, it's not preemptively pruning items,\n     * unless {@link OptionsBase.ttlAutopurge} is set.\n     *\n     * If you set `allowStale:true`, it'll return the stale value *as well as*\n     * deleting it. If you don't set this, then it'll return `undefined` when\n     * you try to get a stale entry.\n     *\n     * Note that when a stale entry is fetched, _even if it is returned due to\n     * `allowStale` being set_, it is removed from the cache immediately. You\n     * can suppress this behavior by setting\n     * {@link OptionsBase.noDeleteOnStaleGet}, either in the constructor, or in\n     * the options provided to {@link LRUCache#get}.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     * The `cache.has()` method will always return `false` for stale items.\n     *\n     * Only relevant if a ttl is set.\n     */\n    allowStale?: boolean\n\n    /**\n     * Function that is called on items when they are dropped from the\n     * cache, as `dispose(value, key, reason)`.\n     *\n     * This can be handy if you want to close file descriptors or do\n     * other cleanup tasks when items are no longer stored in the cache.\n     *\n     * **NOTE**: It is called _before_ the item has been fully removed\n     * from the cache, so if you want to put it right back in, you need\n     * to wait until the next tick. If you try to add it back in during\n     * the `dispose()` function call, it will break things in subtle and\n     * weird ways.\n     *\n     * Unlike several other options, this may _not_ be overridden by\n     * passing an option to `set()`, for performance reasons.\n     *\n     * The `reason` will be one of the following strings, corresponding\n     * to the reason for the item's deletion:\n     *\n     * - `evict` Item was evicted to make space for a new addition\n     * - `set` Item was overwritten by a new value\n     * - `expire` Item expired its TTL\n     * - `fetch` Item was deleted due to a failed or aborted fetch, or a\n     *   fetchMethod returning `undefined.\n     * - `delete` Item was removed by explicit `cache.delete(key)`,\n     *   `cache.clear()`, or `cache.set(key, undefined)`.\n     */\n    dispose?: Disposer<K, V>\n\n    /**\n     * The same as {@link OptionsBase.dispose}, but called *after* the entry\n     * is completely removed and the cache is once again in a clean state.\n     *\n     * It is safe to add an item right back into the cache at this point.\n     * However, note that it is *very* easy to inadvertently create infinite\n     * recursion this way.\n     */\n    disposeAfter?: Disposer<K, V>\n\n    /**\n     * Set to true to suppress calling the\n     * {@link OptionsBase.dispose} function if the entry key is\n     * still accessible within the cache.\n     *\n     * This may be overridden by passing an options object to\n     * {@link LRUCache#set}.\n     *\n     * Only relevant if `dispose` or `disposeAfter` are set.\n     */\n    noDisposeOnSet?: boolean\n\n    /**\n     * Boolean flag to tell the cache to not update the TTL when setting a new\n     * value for an existing key (ie, when updating a value rather than\n     * inserting a new value).  Note that the TTL value is _always_ set (if\n     * provided) when adding a new entry into the cache.\n     *\n     * Has no effect if a {@link OptionsBase.ttl} is not set.\n     *\n     * May be passed as an option to {@link LRUCache#set}.\n     */\n    noUpdateTTL?: boolean\n\n    /**\n     * Set to a positive integer to track the sizes of items added to the\n     * cache, and automatically evict items in order to stay below this size.\n     * Note that this may result in fewer than `max` items being stored.\n     *\n     * Attempting to add an item to the cache whose calculated size is greater\n     * that this amount will be a no-op. The item will not be cached, and no\n     * other items will be evicted.\n     *\n     * Optional, must be a positive integer if provided.\n     *\n     * Sets `maxEntrySize` to the same value, unless a different value is\n     * provided for `maxEntrySize`.\n     *\n     * At least one of `max`, `maxSize`, or `TTL` is required. This must be a\n     * positive integer if set.\n     *\n     * Even if size tracking is enabled, **it is strongly recommended to set a\n     * `max` to prevent unbounded growth of the cache.**\n     *\n     * Note also that size tracking can negatively impact performance,\n     * though for most cases, only minimally.\n     */\n    maxSize?: Size\n\n    /**\n     * The maximum allowed size for any single item in the cache.\n     *\n     * If a larger item is passed to {@link LRUCache#set} or returned by a\n     * {@link OptionsBase.fetchMethod} or {@link OptionsBase.memoMethod}, then\n     * it will not be stored in the cache.\n     *\n     * Attempting to add an item whose calculated size is greater than\n     * this amount will not cache the item or evict any old items, but\n     * WILL delete an existing value if one is already present.\n     *\n     * Optional, must be a positive integer if provided. Defaults to\n     * the value of `maxSize` if provided.\n     */\n    maxEntrySize?: Size\n\n    /**\n     * A function that returns a number indicating the item's size.\n     *\n     * Requires {@link OptionsBase.maxSize} to be set.\n     *\n     * If not provided, and {@link OptionsBase.maxSize} or\n     * {@link OptionsBase.maxEntrySize} are set, then all\n     * {@link LRUCache#set} calls **must** provide an explicit\n     * {@link SetOptions.size} or sizeCalculation param.\n     */\n    sizeCalculation?: SizeCalculator<K, V>\n\n    /**\n     * Method that provides the implementation for {@link LRUCache#fetch}\n     *\n     * ```ts\n     * fetchMethod(key, staleValue, { signal, options, context })\n     * ```\n     *\n     * If `fetchMethod` is not provided, then `cache.fetch(key)` is equivalent\n     * to `Promise.resolve(cache.get(key))`.\n     *\n     * If at any time, `signal.aborted` is set to `true`, or if the\n     * `signal.onabort` method is called, or if it emits an `'abort'` event\n     * which you can listen to with `addEventListener`, then that means that\n     * the fetch should be abandoned. This may be passed along to async\n     * functions aware of AbortController/AbortSignal behavior.\n     *\n     * The `fetchMethod` should **only** return `undefined` or a Promise\n     * resolving to `undefined` if the AbortController signaled an `abort`\n     * event. In all other cases, it should return or resolve to a value\n     * suitable for adding to the cache.\n     *\n     * The `options` object is a union of the options that may be provided to\n     * `set()` and `get()`. If they are modified, then that will result in\n     * modifying the settings to `cache.set()` when the value is resolved, and\n     * in the case of\n     * {@link OptionsBase.noDeleteOnFetchRejection} and\n     * {@link OptionsBase.allowStaleOnFetchRejection}, the handling of\n     * `fetchMethod` failures.\n     *\n     * For example, a DNS cache may update the TTL based on the value returned\n     * from a remote DNS server by changing `options.ttl` in the `fetchMethod`.\n     */\n    fetchMethod?: Fetcher<K, V, FC>\n\n    /**\n     * Method that provides the implementation for {@link LRUCache#memo}\n     */\n    memoMethod?: Memoizer<K, V, FC>\n\n    /**\n     * Set to true to suppress the deletion of stale data when a\n     * {@link OptionsBase.fetchMethod} returns a rejected promise.\n     */\n    noDeleteOnFetchRejection?: boolean\n\n    /**\n     * Do not delete stale items when they are retrieved with\n     * {@link LRUCache#get}.\n     *\n     * Note that the `get` return value will still be `undefined`\n     * unless {@link OptionsBase.allowStale} is true.\n     *\n     * When using time-expiring entries with `ttl`, by default stale\n     * items will be removed from the cache when the key is accessed\n     * with `cache.get()`.\n     *\n     * Setting this option will cause stale items to remain in the cache, until\n     * they are explicitly deleted with `cache.delete(key)`, or retrieved with\n     * `noDeleteOnStaleGet` set to `false`.\n     *\n     * This may be overridden by passing an options object to `cache.get()`.\n     *\n     * Only relevant if a ttl is used.\n     */\n    noDeleteOnStaleGet?: boolean\n\n    /**\n     * Set to true to allow returning stale data when a\n     * {@link OptionsBase.fetchMethod} throws an error or returns a rejected\n     * promise.\n     *\n     * This differs from using {@link OptionsBase.allowStale} in that stale\n     * data will ONLY be returned in the case that the {@link LRUCache#fetch}\n     * fails, not any other times.\n     *\n     * If a `fetchMethod` fails, and there is no stale value available, the\n     * `fetch()` will resolve to `undefined`. Ie, all `fetchMethod` errors are\n     * suppressed.\n     *\n     * Implies `noDeleteOnFetchRejection`.\n     *\n     * This may be set in calls to `fetch()`, or defaulted on the constructor,\n     * or overridden by modifying the options object in the `fetchMethod`.\n     */\n    allowStaleOnFetchRejection?: boolean\n\n    /**\n     * Set to true to return a stale value from the cache when the\n     * `AbortSignal` passed to the {@link OptionsBase.fetchMethod} dispatches\n     * an `'abort'` event, whether user-triggered, or due to internal cache\n     * behavior.\n     *\n     * Unless {@link OptionsBase.ignoreFetchAbort} is also set, the underlying\n     * {@link OptionsBase.fetchMethod} will still be considered canceled, and\n     * any value it returns will be ignored and not cached.\n     *\n     * Caveat: since fetches are aborted when a new value is explicitly\n     * set in the cache, this can lead to fetch returning a stale value,\n     * since that was the fallback value _at the moment the `fetch()` was\n     * initiated_, even though the new updated value is now present in\n     * the cache.\n     *\n     * For example:\n     *\n     * ```ts\n     * const cache = new LRUCache<string, any>({\n     *   ttl: 100,\n     *   fetchMethod: async (url, oldValue, { signal }) =>  {\n     *     const res = await fetch(url, { signal })\n     *     return await res.json()\n     *   }\n     * })\n     * cache.set('https://example.com/', { some: 'data' })\n     * // 100ms go by...\n     * const result = cache.fetch('https://example.com/')\n     * cache.set('https://example.com/', { other: 'thing' })\n     * console.log(await result) // { some: 'data' }\n     * console.log(cache.get('https://example.com/')) // { other: 'thing' }\n     * ```\n     */\n    allowStaleOnFetchAbort?: boolean\n\n    /**\n     * Set to true to ignore the `abort` event emitted by the `AbortSignal`\n     * object passed to {@link OptionsBase.fetchMethod}, and still cache the\n     * resulting resolution value, as long as it is not `undefined`.\n     *\n     * When used on its own, this means aborted {@link LRUCache#fetch} calls\n     * are not immediately resolved or rejected when they are aborted, and\n     * instead take the full time to await.\n     *\n     * When used with {@link OptionsBase.allowStaleOnFetchAbort}, aborted\n     * {@link LRUCache#fetch} calls will resolve immediately to their stale\n     * cached value or `undefined`, and will continue to process and eventually\n     * update the cache when they resolve, as long as the resulting value is\n     * not `undefined`, thus supporting a \"return stale on timeout while\n     * refreshing\" mechanism by passing `AbortSignal.timeout(n)` as the signal.\n     *\n     * For example:\n     *\n     * ```ts\n     * const c = new LRUCache({\n     *   ttl: 100,\n     *   ignoreFetchAbort: true,\n     *   allowStaleOnFetchAbort: true,\n     *   fetchMethod: async (key, oldValue, { signal }) => {\n     *     // note: do NOT pass the signal to fetch()!\n     *     // let's say this fetch can take a long time.\n     *     const res = await fetch(`https://slow-backend-server/${key}`)\n     *     return await res.json()\n     *   },\n     * })\n     *\n     * // this will return the stale value after 100ms, while still\n     * // updating in the background for next time.\n     * const val = await c.fetch('key', { signal: AbortSignal.timeout(100) })\n     * ```\n     *\n     * **Note**: regardless of this setting, an `abort` event _is still\n     * emitted on the `AbortSignal` object_, so may result in invalid results\n     * when passed to other underlying APIs that use AbortSignals.\n     *\n     * This may be overridden in the {@link OptionsBase.fetchMethod} or the\n     * call to {@link LRUCache#fetch}.\n     */\n    ignoreFetchAbort?: boolean\n  }\n\n  export interface OptionsMaxLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    max: Count\n  }\n  export interface OptionsTTLLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    ttl: Milliseconds\n    ttlAutopurge: boolean\n  }\n  export interface OptionsSizeLimit<K, V, FC>\n    extends OptionsBase<K, V, FC> {\n    maxSize: Size\n  }\n\n  /**\n   * The valid safe options for the {@link LRUCache} constructor\n   */\n  export type Options<K, V, FC> =\n    | OptionsMaxLimit<K, V, FC>\n    | OptionsSizeLimit<K, V, FC>\n    | OptionsTTLLimit<K, V, FC>\n\n  /**\n   * Entry objects used by {@link LRUCache#load} and {@link LRUCache#dump},\n   * and returned by {@link LRUCache#info}.\n   */\n  export interface Entry<V> {\n    value: V\n    ttl?: Milliseconds\n    size?: Size\n    start?: Milliseconds\n  }\n}\n\n/**\n * Default export, the thing you're using this module to get.\n *\n * The `K` and `V` types define the key and value types, respectively. The\n * optional `FC` type defines the type of the `context` object passed to\n * `cache.fetch()` and `cache.memo()`.\n *\n * Keys and values **must not** be `null` or `undefined`.\n *\n * All properties from the options object (with the exception of `max`,\n * `maxSize`, `fetchMethod`, `memoMethod`, `dispose` and `disposeAfter`) are\n * added as normal public members. (The listed options are read-only getters.)\n *\n * Changing any of these will alter the defaults for subsequent method calls.\n */\nexport class LRUCache<K extends {}, V extends {}, FC = unknown>\n  implements Map<K, V>\n{\n  // options that cannot be changed without disaster\n  readonly #max: LRUCache.Count\n  readonly #maxSize: LRUCache.Size\n  readonly #dispose?: LRUCache.Disposer<K, V>\n  readonly #disposeAfter?: LRUCache.Disposer<K, V>\n  readonly #fetchMethod?: LRUCache.Fetcher<K, V, FC>\n  readonly #memoMethod?: LRUCache.Memoizer<K, V, FC>\n\n  /**\n   * {@link LRUCache.OptionsBase.ttl}\n   */\n  ttl: LRUCache.Milliseconds\n\n  /**\n   * {@link LRUCache.OptionsBase.ttlResolution}\n   */\n  ttlResolution: LRUCache.Milliseconds\n  /**\n   * {@link LRUCache.OptionsBase.ttlAutopurge}\n   */\n  ttlAutopurge: boolean\n  /**\n   * {@link LRUCache.OptionsBase.updateAgeOnGet}\n   */\n  updateAgeOnGet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.updateAgeOnHas}\n   */\n  updateAgeOnHas: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStale}\n   */\n  allowStale: boolean\n\n  /**\n   * {@link LRUCache.OptionsBase.noDisposeOnSet}\n   */\n  noDisposeOnSet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.noUpdateTTL}\n   */\n  noUpdateTTL: boolean\n  /**\n   * {@link LRUCache.OptionsBase.maxEntrySize}\n   */\n  maxEntrySize: LRUCache.Size\n  /**\n   * {@link LRUCache.OptionsBase.sizeCalculation}\n   */\n  sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  /**\n   * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n   */\n  noDeleteOnFetchRejection: boolean\n  /**\n   * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n   */\n  noDeleteOnStaleGet: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n   */\n  allowStaleOnFetchAbort: boolean\n  /**\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n   */\n  allowStaleOnFetchRejection: boolean\n  /**\n   * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n   */\n  ignoreFetchAbort: boolean\n\n  // computed properties\n  #size: LRUCache.Count\n  #calculatedSize: LRUCache.Size\n  #keyMap: Map<K, Index>\n  #keyList: (K | undefined)[]\n  #valList: (V | BackgroundFetch<V> | undefined)[]\n  #next: NumberArray\n  #prev: NumberArray\n  #head: Index\n  #tail: Index\n  #free: StackLike\n  #disposed?: DisposeTask<K, V>[]\n  #sizes?: ZeroArray\n  #starts?: ZeroArray\n  #ttls?: ZeroArray\n\n  #hasDispose: boolean\n  #hasFetchMethod: boolean\n  #hasDisposeAfter: boolean\n\n  /**\n   * Do not call this method unless you need to inspect the\n   * inner workings of the cache.  If anything returned by this\n   * object is modified in any way, strange breakage may occur.\n   *\n   * These fields are private for a reason!\n   *\n   * @internal\n   */\n  static unsafeExposeInternals<\n    K extends {},\n    V extends {},\n    FC extends unknown = unknown\n  >(c: LRUCache<K, V, FC>) {\n    return {\n      // properties\n      starts: c.#starts,\n      ttls: c.#ttls,\n      sizes: c.#sizes,\n      keyMap: c.#keyMap as Map<K, number>,\n      keyList: c.#keyList,\n      valList: c.#valList,\n      next: c.#next,\n      prev: c.#prev,\n      get head() {\n        return c.#head\n      },\n      get tail() {\n        return c.#tail\n      },\n      free: c.#free,\n      // methods\n      isBackgroundFetch: (p: any) => c.#isBackgroundFetch(p),\n      backgroundFetch: (\n        k: K,\n        index: number | undefined,\n        options: LRUCache.FetchOptions<K, V, FC>,\n        context: any\n      ): BackgroundFetch<V> =>\n        c.#backgroundFetch(\n          k,\n          index as Index | undefined,\n          options,\n          context\n        ),\n      moveToTail: (index: number): void =>\n        c.#moveToTail(index as Index),\n      indexes: (options?: { allowStale: boolean }) =>\n        c.#indexes(options),\n      rindexes: (options?: { allowStale: boolean }) =>\n        c.#rindexes(options),\n      isStale: (index: number | undefined) =>\n        c.#isStale(index as Index),\n    }\n  }\n\n  // Protected read-only members\n\n  /**\n   * {@link LRUCache.OptionsBase.max} (read-only)\n   */\n  get max(): LRUCache.Count {\n    return this.#max\n  }\n  /**\n   * {@link LRUCache.OptionsBase.maxSize} (read-only)\n   */\n  get maxSize(): LRUCache.Count {\n    return this.#maxSize\n  }\n  /**\n   * The total computed size of items in the cache (read-only)\n   */\n  get calculatedSize(): LRUCache.Size {\n    return this.#calculatedSize\n  }\n  /**\n   * The number of items stored in the cache (read-only)\n   */\n  get size(): LRUCache.Count {\n    return this.#size\n  }\n  /**\n   * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n   */\n  get fetchMethod(): LRUCache.Fetcher<K, V, FC> | undefined {\n    return this.#fetchMethod\n  }\n  get memoMethod(): LRUCache.Memoizer<K, V, FC> | undefined {\n    return this.#memoMethod\n  }\n  /**\n   * {@link LRUCache.OptionsBase.dispose} (read-only)\n   */\n  get dispose() {\n    return this.#dispose\n  }\n  /**\n   * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n   */\n  get disposeAfter() {\n    return this.#disposeAfter\n  }\n\n  constructor(\n    options: LRUCache.Options<K, V, FC> | LRUCache<K, V, FC>\n  ) {\n    const {\n      max = 0,\n      ttl,\n      ttlResolution = 1,\n      ttlAutopurge,\n      updateAgeOnGet,\n      updateAgeOnHas,\n      allowStale,\n      dispose,\n      disposeAfter,\n      noDisposeOnSet,\n      noUpdateTTL,\n      maxSize = 0,\n      maxEntrySize = 0,\n      sizeCalculation,\n      fetchMethod,\n      memoMethod,\n      noDeleteOnFetchRejection,\n      noDeleteOnStaleGet,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n    } = options\n\n    if (max !== 0 && !isPosInt(max)) {\n      throw new TypeError('max option must be a nonnegative integer')\n    }\n\n    const UintArray = max ? getUintArray(max) : Array\n    if (!UintArray) {\n      throw new Error('invalid max value: ' + max)\n    }\n\n    this.#max = max\n    this.#maxSize = maxSize\n    this.maxEntrySize = maxEntrySize || this.#maxSize\n    this.sizeCalculation = sizeCalculation\n    if (this.sizeCalculation) {\n      if (!this.#maxSize && !this.maxEntrySize) {\n        throw new TypeError(\n          'cannot set sizeCalculation without setting maxSize or maxEntrySize'\n        )\n      }\n      if (typeof this.sizeCalculation !== 'function') {\n        throw new TypeError('sizeCalculation set to non-function')\n      }\n    }\n\n    if (\n      memoMethod !== undefined &&\n      typeof memoMethod !== 'function'\n    ) {\n      throw new TypeError('memoMethod must be a function if defined')\n    }\n    this.#memoMethod = memoMethod\n\n    if (\n      fetchMethod !== undefined &&\n      typeof fetchMethod !== 'function'\n    ) {\n      throw new TypeError(\n        'fetchMethod must be a function if specified'\n      )\n    }\n    this.#fetchMethod = fetchMethod\n    this.#hasFetchMethod = !!fetchMethod\n\n    this.#keyMap = new Map()\n    this.#keyList = new Array(max).fill(undefined)\n    this.#valList = new Array(max).fill(undefined)\n    this.#next = new UintArray(max)\n    this.#prev = new UintArray(max)\n    this.#head = 0 as Index\n    this.#tail = 0 as Index\n    this.#free = Stack.create(max)\n    this.#size = 0\n    this.#calculatedSize = 0\n\n    if (typeof dispose === 'function') {\n      this.#dispose = dispose\n    }\n    if (typeof disposeAfter === 'function') {\n      this.#disposeAfter = disposeAfter\n      this.#disposed = []\n    } else {\n      this.#disposeAfter = undefined\n      this.#disposed = undefined\n    }\n    this.#hasDispose = !!this.#dispose\n    this.#hasDisposeAfter = !!this.#disposeAfter\n\n    this.noDisposeOnSet = !!noDisposeOnSet\n    this.noUpdateTTL = !!noUpdateTTL\n    this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection\n    this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection\n    this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort\n    this.ignoreFetchAbort = !!ignoreFetchAbort\n\n    // NB: maxEntrySize is set to maxSize if it's set\n    if (this.maxEntrySize !== 0) {\n      if (this.#maxSize !== 0) {\n        if (!isPosInt(this.#maxSize)) {\n          throw new TypeError(\n            'maxSize must be a positive integer if specified'\n          )\n        }\n      }\n      if (!isPosInt(this.maxEntrySize)) {\n        throw new TypeError(\n          'maxEntrySize must be a positive integer if specified'\n        )\n      }\n      this.#initializeSizeTracking()\n    }\n\n    this.allowStale = !!allowStale\n    this.noDeleteOnStaleGet = !!noDeleteOnStaleGet\n    this.updateAgeOnGet = !!updateAgeOnGet\n    this.updateAgeOnHas = !!updateAgeOnHas\n    this.ttlResolution =\n      isPosInt(ttlResolution) || ttlResolution === 0\n        ? ttlResolution\n        : 1\n    this.ttlAutopurge = !!ttlAutopurge\n    this.ttl = ttl || 0\n    if (this.ttl) {\n      if (!isPosInt(this.ttl)) {\n        throw new TypeError(\n          'ttl must be a positive integer if specified'\n        )\n      }\n      this.#initializeTTLTracking()\n    }\n\n    // do not allow completely unbounded caches\n    if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n      throw new TypeError(\n        'At least one of max, maxSize, or ttl is required'\n      )\n    }\n    if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n      const code = 'LRU_CACHE_UNBOUNDED'\n      if (shouldWarn(code)) {\n        warned.add(code)\n        const msg =\n          'TTL caching without ttlAutopurge, max, or maxSize can ' +\n          'result in unbounded memory consumption.'\n        emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache)\n      }\n    }\n  }\n\n  /**\n   * Return the number of ms left in the item's TTL. If item is not in cache,\n   * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.\n   */\n  getRemainingTTL(key: K) {\n    return this.#keyMap.has(key) ? Infinity : 0\n  }\n\n  #initializeTTLTracking() {\n    const ttls = new ZeroArray(this.#max)\n    const starts = new ZeroArray(this.#max)\n    this.#ttls = ttls\n    this.#starts = starts\n\n    this.#setItemTTL = (index, ttl, start = perf.now()) => {\n      starts[index] = ttl !== 0 ? start : 0\n      ttls[index] = ttl\n      if (ttl !== 0 && this.ttlAutopurge) {\n        const t = setTimeout(() => {\n          if (this.#isStale(index)) {\n            this.#delete(this.#keyList[index] as K, 'expire')\n          }\n        }, ttl + 1)\n        // unref() not supported on all platforms\n        /* c8 ignore start */\n        if (t.unref) {\n          t.unref()\n        }\n        /* c8 ignore stop */\n      }\n    }\n\n    this.#updateItemAge = index => {\n      starts[index] = ttls[index] !== 0 ? perf.now() : 0\n    }\n\n    this.#statusTTL = (status, index) => {\n      if (ttls[index]) {\n        const ttl = ttls[index]\n        const start = starts[index]\n        /* c8 ignore next */\n        if (!ttl || !start) return\n        status.ttl = ttl\n        status.start = start\n        status.now = cachedNow || getNow()\n        const age = status.now - start\n        status.remainingTTL = ttl - age\n      }\n    }\n\n    // debounce calls to perf.now() to 1s so we're not hitting\n    // that costly call repeatedly.\n    let cachedNow = 0\n    const getNow = () => {\n      const n = perf.now()\n      if (this.ttlResolution > 0) {\n        cachedNow = n\n        const t = setTimeout(\n          () => (cachedNow = 0),\n          this.ttlResolution\n        )\n        // not available on all platforms\n        /* c8 ignore start */\n        if (t.unref) {\n          t.unref()\n        }\n        /* c8 ignore stop */\n      }\n      return n\n    }\n\n    this.getRemainingTTL = key => {\n      const index = this.#keyMap.get(key)\n      if (index === undefined) {\n        return 0\n      }\n      const ttl = ttls[index]\n      const start = starts[index]\n      if (!ttl || !start) {\n        return Infinity\n      }\n      const age = (cachedNow || getNow()) - start\n      return ttl - age\n    }\n\n    this.#isStale = index => {\n      const s = starts[index]\n      const t = ttls[index]\n      return !!t && !!s && (cachedNow || getNow()) - s > t\n    }\n  }\n\n  // conditionally set private methods related to TTL\n  #updateItemAge: (index: Index) => void = () => {}\n  #statusTTL: (status: LRUCache.Status<V>, index: Index) => void =\n    () => {}\n  #setItemTTL: (\n    index: Index,\n    ttl: LRUCache.Milliseconds,\n    start?: LRUCache.Milliseconds\n    // ignore because we never call this if we're not already in TTL mode\n    /* c8 ignore start */\n  ) => void = () => {}\n  /* c8 ignore stop */\n\n  #isStale: (index: Index) => boolean = () => false\n\n  #initializeSizeTracking() {\n    const sizes = new ZeroArray(this.#max)\n    this.#calculatedSize = 0\n    this.#sizes = sizes\n    this.#removeItemSize = index => {\n      this.#calculatedSize -= sizes[index] as number\n      sizes[index] = 0\n    }\n    this.#requireSize = (k, v, size, sizeCalculation) => {\n      // provisionally accept background fetches.\n      // actual value size will be checked when they return.\n      if (this.#isBackgroundFetch(v)) {\n        return 0\n      }\n      if (!isPosInt(size)) {\n        if (sizeCalculation) {\n          if (typeof sizeCalculation !== 'function') {\n            throw new TypeError('sizeCalculation must be a function')\n          }\n          size = sizeCalculation(v, k)\n          if (!isPosInt(size)) {\n            throw new TypeError(\n              'sizeCalculation return invalid (expect positive integer)'\n            )\n          }\n        } else {\n          throw new TypeError(\n            'invalid size value (must be positive integer). ' +\n              'When maxSize or maxEntrySize is used, sizeCalculation ' +\n              'or size must be set.'\n          )\n        }\n      }\n      return size\n    }\n    this.#addItemSize = (\n      index: Index,\n      size: LRUCache.Size,\n      status?: LRUCache.Status<V>\n    ) => {\n      sizes[index] = size\n      if (this.#maxSize) {\n        const maxSize = this.#maxSize - (sizes[index] as number)\n        while (this.#calculatedSize > maxSize) {\n          this.#evict(true)\n        }\n      }\n      this.#calculatedSize += sizes[index] as number\n      if (status) {\n        status.entrySize = size\n        status.totalCalculatedSize = this.#calculatedSize\n      }\n    }\n  }\n\n  #removeItemSize: (index: Index) => void = _i => {}\n  #addItemSize: (\n    index: Index,\n    size: LRUCache.Size,\n    status?: LRUCache.Status<V>\n  ) => void = (_i, _s, _st) => {}\n  #requireSize: (\n    k: K,\n    v: V | BackgroundFetch<V>,\n    size?: LRUCache.Size,\n    sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  ) => LRUCache.Size = (\n    _k: K,\n    _v: V | BackgroundFetch<V>,\n    size?: LRUCache.Size,\n    sizeCalculation?: LRUCache.SizeCalculator<K, V>\n  ) => {\n    if (size || sizeCalculation) {\n      throw new TypeError(\n        'cannot set size without setting maxSize or maxEntrySize on cache'\n      )\n    }\n    return 0\n  };\n\n  *#indexes({ allowStale = this.allowStale } = {}) {\n    if (this.#size) {\n      for (let i = this.#tail; true; ) {\n        if (!this.#isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.#isStale(i)) {\n          yield i\n        }\n        if (i === this.#head) {\n          break\n        } else {\n          i = this.#prev[i] as Index\n        }\n      }\n    }\n  }\n\n  *#rindexes({ allowStale = this.allowStale } = {}) {\n    if (this.#size) {\n      for (let i = this.#head; true; ) {\n        if (!this.#isValidIndex(i)) {\n          break\n        }\n        if (allowStale || !this.#isStale(i)) {\n          yield i\n        }\n        if (i === this.#tail) {\n          break\n        } else {\n          i = this.#next[i] as Index\n        }\n      }\n    }\n  }\n\n  #isValidIndex(index: Index) {\n    return (\n      index !== undefined &&\n      this.#keyMap.get(this.#keyList[index] as K) === index\n    )\n  }\n\n  /**\n   * Return a generator yielding `[key, value]` pairs,\n   * in order from most recently used to least recently used.\n   */\n  *entries() {\n    for (const i of this.#indexes()) {\n      if (\n        this.#valList[i] !== undefined &&\n        this.#keyList[i] !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield [this.#keyList[i], this.#valList[i]] as [K, V]\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.entries}\n   *\n   * Return a generator yielding `[key, value]` pairs,\n   * in order from least recently used to most recently used.\n   */\n  *rentries() {\n    for (const i of this.#rindexes()) {\n      if (\n        this.#valList[i] !== undefined &&\n        this.#keyList[i] !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield [this.#keyList[i], this.#valList[i]]\n      }\n    }\n  }\n\n  /**\n   * Return a generator yielding the keys in the cache,\n   * in order from most recently used to least recently used.\n   */\n  *keys() {\n    for (const i of this.#indexes()) {\n      const k = this.#keyList[i]\n      if (\n        k !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield k\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.keys}\n   *\n   * Return a generator yielding the keys in the cache,\n   * in order from least recently used to most recently used.\n   */\n  *rkeys() {\n    for (const i of this.#rindexes()) {\n      const k = this.#keyList[i]\n      if (\n        k !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield k\n      }\n    }\n  }\n\n  /**\n   * Return a generator yielding the values in the cache,\n   * in order from most recently used to least recently used.\n   */\n  *values() {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      if (\n        v !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield this.#valList[i] as V\n      }\n    }\n  }\n\n  /**\n   * Inverse order version of {@link LRUCache.values}\n   *\n   * Return a generator yielding the values in the cache,\n   * in order from least recently used to most recently used.\n   */\n  *rvalues() {\n    for (const i of this.#rindexes()) {\n      const v = this.#valList[i]\n      if (\n        v !== undefined &&\n        !this.#isBackgroundFetch(this.#valList[i])\n      ) {\n        yield this.#valList[i]\n      }\n    }\n  }\n\n  /**\n   * Iterating over the cache itself yields the same results as\n   * {@link LRUCache.entries}\n   */\n  [Symbol.iterator]() {\n    return this.entries()\n  }\n\n  /**\n   * A String value that is used in the creation of the default string\n   * description of an object. Called by the built-in method\n   * `Object.prototype.toString`.\n   */\n  [Symbol.toStringTag] = 'LRUCache'\n\n  /**\n   * Find a value for which the supplied fn method returns a truthy value,\n   * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.\n   */\n  find(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => boolean,\n    getOptions: LRUCache.GetOptions<K, V, FC> = {}\n  ) {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      if (fn(value, this.#keyList[i] as K, this)) {\n        return this.get(this.#keyList[i] as K, getOptions)\n      }\n    }\n  }\n\n  /**\n   * Call the supplied function on each item in the cache, in order from most\n   * recently used to least recently used.\n   *\n   * `fn` is called as `fn(value, key, cache)`.\n   *\n   * If `thisp` is provided, function will be called in the `this`-context of\n   * the provided object, or the cache if no `thisp` object is provided.\n   *\n   * Does not update age or recenty of use, or iterate over stale values.\n   */\n  forEach(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => any,\n    thisp: any = this\n  ) {\n    for (const i of this.#indexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.#keyList[i] as K, this)\n    }\n  }\n\n  /**\n   * The same as {@link LRUCache.forEach} but items are iterated over in\n   * reverse order.  (ie, less recently used items are iterated over first.)\n   */\n  rforEach(\n    fn: (v: V, k: K, self: LRUCache<K, V, FC>) => any,\n    thisp: any = this\n  ) {\n    for (const i of this.#rindexes()) {\n      const v = this.#valList[i]\n      const value = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined) continue\n      fn.call(thisp, value, this.#keyList[i] as K, this)\n    }\n  }\n\n  /**\n   * Delete any stale entries. Returns true if anything was removed,\n   * false otherwise.\n   */\n  purgeStale() {\n    let deleted = false\n    for (const i of this.#rindexes({ allowStale: true })) {\n      if (this.#isStale(i)) {\n        this.#delete(this.#keyList[i] as K, 'expire')\n        deleted = true\n      }\n    }\n    return deleted\n  }\n\n  /**\n   * Get the extended info about a given entry, to get its value, size, and\n   * TTL info simultaneously. Returns `undefined` if the key is not present.\n   *\n   * Unlike {@link LRUCache#dump}, which is designed to be portable and survive\n   * serialization, the `start` value is always the current timestamp, and the\n   * `ttl` is a calculated remaining time to live (negative if expired).\n   *\n   * Always returns stale values, if their info is found in the cache, so be\n   * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})\n   * if relevant.\n   */\n  info(key: K): LRUCache.Entry<V> | undefined {\n    const i = this.#keyMap.get(key)\n    if (i === undefined) return undefined\n    const v = this.#valList[i]\n    const value: V | undefined = this.#isBackgroundFetch(v)\n      ? v.__staleWhileFetching\n      : v\n    if (value === undefined) return undefined\n    const entry: LRUCache.Entry<V> = { value }\n    if (this.#ttls && this.#starts) {\n      const ttl = this.#ttls[i]\n      const start = this.#starts[i]\n      if (ttl && start) {\n        const remain = ttl - (perf.now() - start)\n        entry.ttl = remain\n        entry.start = Date.now()\n      }\n    }\n    if (this.#sizes) {\n      entry.size = this.#sizes[i]\n    }\n    return entry\n  }\n\n  /**\n   * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n   * passed to {@link LRLUCache#load}.\n   *\n   * The `start` fields are calculated relative to a portable `Date.now()`\n   * timestamp, even if `performance.now()` is available.\n   *\n   * Stale entries are always included in the `dump`, even if\n   * {@link LRUCache.OptionsBase.allowStale} is false.\n   *\n   * Note: this returns an actual array, not a generator, so it can be more\n   * easily passed around.\n   */\n  dump() {\n    const arr: [K, LRUCache.Entry<V>][] = []\n    for (const i of this.#indexes({ allowStale: true })) {\n      const key = this.#keyList[i]\n      const v = this.#valList[i]\n      const value: V | undefined = this.#isBackgroundFetch(v)\n        ? v.__staleWhileFetching\n        : v\n      if (value === undefined || key === undefined) continue\n      const entry: LRUCache.Entry<V> = { value }\n      if (this.#ttls && this.#starts) {\n        entry.ttl = this.#ttls[i]\n        // always dump the start relative to a portable timestamp\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = perf.now() - (this.#starts[i] as number)\n        entry.start = Math.floor(Date.now() - age)\n      }\n      if (this.#sizes) {\n        entry.size = this.#sizes[i]\n      }\n      arr.unshift([key, entry])\n    }\n    return arr\n  }\n\n  /**\n   * Reset the cache and load in the items in entries in the order listed.\n   *\n   * The shape of the resulting cache may be different if the same options are\n   * not used in both caches.\n   *\n   * The `start` fields are assumed to be calculated relative to a portable\n   * `Date.now()` timestamp, even if `performance.now()` is available.\n   */\n  load(arr: [K, LRUCache.Entry<V>][]) {\n    this.clear()\n    for (const [key, entry] of arr) {\n      if (entry.start) {\n        // entry.start is a portable timestamp, but we may be using\n        // node's performance.now(), so calculate the offset, so that\n        // we get the intended remaining TTL, no matter how long it's\n        // been on ice.\n        //\n        // it's ok for this to be a bit slow, it's a rare operation.\n        const age = Date.now() - entry.start\n        entry.start = perf.now() - age\n      }\n      this.set(key, entry.value, entry)\n    }\n  }\n\n  /**\n   * Add a value to the cache.\n   *\n   * Note: if `undefined` is specified as a value, this is an alias for\n   * {@link LRUCache#delete}\n   *\n   * Fields on the {@link LRUCache.SetOptions} options param will override\n   * their corresponding values in the constructor options for the scope\n   * of this single `set()` operation.\n   *\n   * If `start` is provided, then that will set the effective start\n   * time for the TTL calculation. Note that this must be a previous\n   * value of `performance.now()` if supported, or a previous value of\n   * `Date.now()` if not.\n   *\n   * Options object may also include `size`, which will prevent\n   * calling the `sizeCalculation` function and just use the specified\n   * number if it is a positive integer, and `noDisposeOnSet` which\n   * will prevent calling a `dispose` function in the case of\n   * overwrites.\n   *\n   * If the `size` (or return value of `sizeCalculation`) for a given\n   * entry is greater than `maxEntrySize`, then the item will not be\n   * added to the cache.\n   *\n   * Will update the recency of the entry.\n   *\n   * If the value is `undefined`, then this is an alias for\n   * `cache.delete(key)`. `undefined` is never stored in the cache.\n   */\n  set(\n    k: K,\n    v: V | BackgroundFetch<V> | undefined,\n    setOptions: LRUCache.SetOptions<K, V, FC> = {}\n  ) {\n    if (v === undefined) {\n      this.delete(k)\n      return this\n    }\n    const {\n      ttl = this.ttl,\n      start,\n      noDisposeOnSet = this.noDisposeOnSet,\n      sizeCalculation = this.sizeCalculation,\n      status,\n    } = setOptions\n    let { noUpdateTTL = this.noUpdateTTL } = setOptions\n\n    const size = this.#requireSize(\n      k,\n      v,\n      setOptions.size || 0,\n      sizeCalculation\n    )\n    // if the item doesn't fit, don't do anything\n    // NB: maxEntrySize set to maxSize by default\n    if (this.maxEntrySize && size > this.maxEntrySize) {\n      if (status) {\n        status.set = 'miss'\n        status.maxEntrySizeExceeded = true\n      }\n      // have to delete, in case something is there already.\n      this.#delete(k, 'set')\n      return this\n    }\n    let index = this.#size === 0 ? undefined : this.#keyMap.get(k)\n    if (index === undefined) {\n      // addition\n      index = (\n        this.#size === 0\n          ? this.#tail\n          : this.#free.length !== 0\n          ? this.#free.pop()\n          : this.#size === this.#max\n          ? this.#evict(false)\n          : this.#size\n      ) as Index\n      this.#keyList[index] = k\n      this.#valList[index] = v\n      this.#keyMap.set(k, index)\n      this.#next[this.#tail] = index\n      this.#prev[index] = this.#tail\n      this.#tail = index\n      this.#size++\n      this.#addItemSize(index, size, status)\n      if (status) status.set = 'add'\n      noUpdateTTL = false\n    } else {\n      // update\n      this.#moveToTail(index)\n      const oldVal = this.#valList[index] as V | BackgroundFetch<V>\n      if (v !== oldVal) {\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n          oldVal.__abortController.abort(new Error('replaced'))\n          const { __staleWhileFetching: s } = oldVal\n          if (s !== undefined && !noDisposeOnSet) {\n            if (this.#hasDispose) {\n              this.#dispose?.(s as V, k, 'set')\n            }\n            if (this.#hasDisposeAfter) {\n              this.#disposed?.push([s as V, k, 'set'])\n            }\n          }\n        } else if (!noDisposeOnSet) {\n          if (this.#hasDispose) {\n            this.#dispose?.(oldVal as V, k, 'set')\n          }\n          if (this.#hasDisposeAfter) {\n            this.#disposed?.push([oldVal as V, k, 'set'])\n          }\n        }\n        this.#removeItemSize(index)\n        this.#addItemSize(index, size, status)\n        this.#valList[index] = v\n        if (status) {\n          status.set = 'replace'\n          const oldValue =\n            oldVal && this.#isBackgroundFetch(oldVal)\n              ? oldVal.__staleWhileFetching\n              : oldVal\n          if (oldValue !== undefined) status.oldValue = oldValue\n        }\n      } else if (status) {\n        status.set = 'update'\n      }\n    }\n    if (ttl !== 0 && !this.#ttls) {\n      this.#initializeTTLTracking()\n    }\n    if (this.#ttls) {\n      if (!noUpdateTTL) {\n        this.#setItemTTL(index, ttl, start)\n      }\n      if (status) this.#statusTTL(status, index)\n    }\n    if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n    return this\n  }\n\n  /**\n   * Evict the least recently used item, returning its value or\n   * `undefined` if cache is empty.\n   */\n  pop(): V | undefined {\n    try {\n      while (this.#size) {\n        const val = this.#valList[this.#head]\n        this.#evict(true)\n        if (this.#isBackgroundFetch(val)) {\n          if (val.__staleWhileFetching) {\n            return val.__staleWhileFetching\n          }\n        } else if (val !== undefined) {\n          return val\n        }\n      }\n    } finally {\n      if (this.#hasDisposeAfter && this.#disposed) {\n        const dt = this.#disposed\n        let task: DisposeTask<K, V> | undefined\n        while ((task = dt?.shift())) {\n          this.#disposeAfter?.(...task)\n        }\n      }\n    }\n  }\n\n  #evict(free: boolean) {\n    const head = this.#head\n    const k = this.#keyList[head] as K\n    const v = this.#valList[head] as V\n    if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n      v.__abortController.abort(new Error('evicted'))\n    } else if (this.#hasDispose || this.#hasDisposeAfter) {\n      if (this.#hasDispose) {\n        this.#dispose?.(v, k, 'evict')\n      }\n      if (this.#hasDisposeAfter) {\n        this.#disposed?.push([v, k, 'evict'])\n      }\n    }\n    this.#removeItemSize(head)\n    // if we aren't about to use the index, then null these out\n    if (free) {\n      this.#keyList[head] = undefined\n      this.#valList[head] = undefined\n      this.#free.push(head)\n    }\n    if (this.#size === 1) {\n      this.#head = this.#tail = 0 as Index\n      this.#free.length = 0\n    } else {\n      this.#head = this.#next[head] as Index\n    }\n    this.#keyMap.delete(k)\n    this.#size--\n    return head\n  }\n\n  /**\n   * Check if a key is in the cache, without updating the recency of use.\n   * Will return false if the item is stale, even though it is technically\n   * in the cache.\n   *\n   * Check if a key is in the cache, without updating the recency of\n   * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set\n   * to `true` in either the options or the constructor.\n   *\n   * Will return `false` if the item is stale, even though it is technically in\n   * the cache. The difference can be determined (if it matters) by using a\n   * `status` argument, and inspecting the `has` field.\n   *\n   * Will not update item age unless\n   * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n   */\n  has(k: K, hasOptions: LRUCache.HasOptions<K, V, FC> = {}) {\n    const { updateAgeOnHas = this.updateAgeOnHas, status } =\n      hasOptions\n    const index = this.#keyMap.get(k)\n    if (index !== undefined) {\n      const v = this.#valList[index]\n      if (\n        this.#isBackgroundFetch(v) &&\n        v.__staleWhileFetching === undefined\n      ) {\n        return false\n      }\n      if (!this.#isStale(index)) {\n        if (updateAgeOnHas) {\n          this.#updateItemAge(index)\n        }\n        if (status) {\n          status.has = 'hit'\n          this.#statusTTL(status, index)\n        }\n        return true\n      } else if (status) {\n        status.has = 'stale'\n        this.#statusTTL(status, index)\n      }\n    } else if (status) {\n      status.has = 'miss'\n    }\n    return false\n  }\n\n  /**\n   * Like {@link LRUCache#get} but doesn't update recency or delete stale\n   * items.\n   *\n   * Returns `undefined` if the item is stale, unless\n   * {@link LRUCache.OptionsBase.allowStale} is set.\n   */\n  peek(k: K, peekOptions: LRUCache.PeekOptions<K, V, FC> = {}) {\n    const { allowStale = this.allowStale } = peekOptions\n    const index = this.#keyMap.get(k)\n    if (\n      index === undefined ||\n      (!allowStale && this.#isStale(index))\n    ) {\n      return\n    }\n    const v = this.#valList[index]\n    // either stale and allowed, or forcing a refresh of non-stale value\n    return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v\n  }\n\n  #backgroundFetch(\n    k: K,\n    index: Index | undefined,\n    options: LRUCache.FetchOptions<K, V, FC>,\n    context: any\n  ): BackgroundFetch<V> {\n    const v = index === undefined ? undefined : this.#valList[index]\n    if (this.#isBackgroundFetch(v)) {\n      return v\n    }\n\n    const ac = new AC()\n    const { signal } = options\n    // when/if our AC signals, then stop listening to theirs.\n    signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n      signal: ac.signal,\n    })\n\n    const fetchOpts = {\n      signal: ac.signal,\n      options,\n      context,\n    }\n\n    const cb = (\n      v: V | undefined,\n      updateCache = false\n    ): V | undefined => {\n      const { aborted } = ac.signal\n      const ignoreAbort = options.ignoreFetchAbort && v !== undefined\n      if (options.status) {\n        if (aborted && !updateCache) {\n          options.status.fetchAborted = true\n          options.status.fetchError = ac.signal.reason\n          if (ignoreAbort) options.status.fetchAbortIgnored = true\n        } else {\n          options.status.fetchResolved = true\n        }\n      }\n      if (aborted && !ignoreAbort && !updateCache) {\n        return fetchFail(ac.signal.reason)\n      }\n      // either we didn't abort, and are still here, or we did, and ignored\n      const bf = p as BackgroundFetch<V>\n      if (this.#valList[index as Index] === p) {\n        if (v === undefined) {\n          if (bf.__staleWhileFetching) {\n            this.#valList[index as Index] = bf.__staleWhileFetching\n          } else {\n            this.#delete(k, 'fetch')\n          }\n        } else {\n          if (options.status) options.status.fetchUpdated = true\n          this.set(k, v, fetchOpts.options)\n        }\n      }\n      return v\n    }\n\n    const eb = (er: any) => {\n      if (options.status) {\n        options.status.fetchRejected = true\n        options.status.fetchError = er\n      }\n      return fetchFail(er)\n    }\n\n    const fetchFail = (er: any): V | undefined => {\n      const { aborted } = ac.signal\n      const allowStaleAborted =\n        aborted && options.allowStaleOnFetchAbort\n      const allowStale =\n        allowStaleAborted || options.allowStaleOnFetchRejection\n      const noDelete = allowStale || options.noDeleteOnFetchRejection\n      const bf = p as BackgroundFetch<V>\n      if (this.#valList[index as Index] === p) {\n        // if we allow stale on fetch rejections, then we need to ensure that\n        // the stale value is not removed from the cache when the fetch fails.\n        const del = !noDelete || bf.__staleWhileFetching === undefined\n        if (del) {\n          this.#delete(k, 'fetch')\n        } else if (!allowStaleAborted) {\n          // still replace the *promise* with the stale value,\n          // since we are done with the promise at this point.\n          // leave it untouched if we're still waiting for an\n          // aborted background fetch that hasn't yet returned.\n          this.#valList[index as Index] = bf.__staleWhileFetching\n        }\n      }\n      if (allowStale) {\n        if (options.status && bf.__staleWhileFetching !== undefined) {\n          options.status.returnedStale = true\n        }\n        return bf.__staleWhileFetching\n      } else if (bf.__returned === bf) {\n        throw er\n      }\n    }\n\n    const pcall = (\n      res: (v: V | undefined) => void,\n      rej: (e: any) => void\n    ) => {\n      const fmp = this.#fetchMethod?.(k, v, fetchOpts)\n      if (fmp && fmp instanceof Promise) {\n        fmp.then(v => res(v === undefined ? undefined : v), rej)\n      }\n      // ignored, we go until we finish, regardless.\n      // defer check until we are actually aborting,\n      // so fetchMethod can override.\n      ac.signal.addEventListener('abort', () => {\n        if (\n          !options.ignoreFetchAbort ||\n          options.allowStaleOnFetchAbort\n        ) {\n          res(undefined)\n          // when it eventually resolves, update the cache.\n          if (options.allowStaleOnFetchAbort) {\n            res = v => cb(v, true)\n          }\n        }\n      })\n    }\n\n    if (options.status) options.status.fetchDispatched = true\n    const p = new Promise(pcall).then(cb, eb)\n    const bf: BackgroundFetch<V> = Object.assign(p, {\n      __abortController: ac,\n      __staleWhileFetching: v,\n      __returned: undefined,\n    })\n\n    if (index === undefined) {\n      // internal, don't expose status.\n      this.set(k, bf, { ...fetchOpts.options, status: undefined })\n      index = this.#keyMap.get(k)\n    } else {\n      this.#valList[index] = bf\n    }\n    return bf\n  }\n\n  #isBackgroundFetch(p: any): p is BackgroundFetch<V> {\n    if (!this.#hasFetchMethod) return false\n    const b = p as BackgroundFetch<V>\n    return (\n      !!b &&\n      b instanceof Promise &&\n      b.hasOwnProperty('__staleWhileFetching') &&\n      b.__abortController instanceof AC\n    )\n  }\n\n  /**\n   * Make an asynchronous cached fetch using the\n   * {@link LRUCache.OptionsBase.fetchMethod} function.\n   *\n   * If the value is in the cache and not stale, then the returned\n   * Promise resolves to the value.\n   *\n   * If not in the cache, or beyond its TTL staleness, then\n   * `fetchMethod(key, staleValue, { options, signal, context })` is\n   * called, and the value returned will be added to the cache once\n   * resolved.\n   *\n   * If called with `allowStale`, and an asynchronous fetch is\n   * currently in progress to reload a stale value, then the former\n   * stale value will be returned.\n   *\n   * If called with `forceRefresh`, then the cached item will be\n   * re-fetched, even if it is not stale. However, if `allowStale` is also\n   * set, then the old value will still be returned. This is useful\n   * in cases where you want to force a reload of a cached value. If\n   * a background fetch is already in progress, then `forceRefresh`\n   * has no effect.\n   *\n   * If multiple fetches for the same key are issued, then they will all be\n   * coalesced into a single call to fetchMethod.\n   *\n   * Note that this means that handling options such as\n   * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort},\n   * {@link LRUCache.FetchOptions.signal},\n   * and {@link LRUCache.OptionsBase.allowStaleOnFetchRejection} will be\n   * determined by the FIRST fetch() call for a given key.\n   *\n   * This is a known (fixable) shortcoming which will be addresed on when\n   * someone complains about it, as the fix would involve added complexity and\n   * may not be worth the costs for this edge case.\n   *\n   * If {@link LRUCache.OptionsBase.fetchMethod} is not specified, then this is\n   * effectively an alias for `Promise.resolve(cache.get(key))`.\n   *\n   * When the fetch method resolves to a value, if the fetch has not\n   * been aborted due to deletion, eviction, or being overwritten,\n   * then it is added to the cache using the options provided.\n   *\n   * If the key is evicted or deleted before the `fetchMethod`\n   * resolves, then the AbortSignal passed to the `fetchMethod` will\n   * receive an `abort` event, and the promise returned by `fetch()`\n   * will reject with the reason for the abort.\n   *\n   * If a `signal` is passed to the `fetch()` call, then aborting the\n   * signal will abort the fetch and cause the `fetch()` promise to\n   * reject with the reason provided.\n   *\n   * **Setting `context`**\n   *\n   * If an `FC` type is set to a type other than `unknown`, `void`, or\n   * `undefined` in the {@link LRUCache} constructor, then all\n   * calls to `cache.fetch()` _must_ provide a `context` option. If\n   * set to `undefined` or `void`, then calls to fetch _must not_\n   * provide a `context` option.\n   *\n   * The `context` param allows you to provide arbitrary data that\n   * might be relevant in the course of fetching the data. It is only\n   * relevant for the course of a single `fetch()` operation, and\n   * discarded afterwards.\n   *\n   * **Note: `fetch()` calls are inflight-unique**\n   *\n   * If you call `fetch()` multiple times with the same key value,\n   * then every call after the first will resolve on the same\n   * promise<sup>1</sup>,\n   * _even if they have different settings that would otherwise change\n   * the behavior of the fetch_, such as `noDeleteOnFetchRejection`\n   * or `ignoreFetchAbort`.\n   *\n   * In most cases, this is not a problem (in fact, only fetching\n   * something once is what you probably want, if you're caching in\n   * the first place). If you are changing the fetch() options\n   * dramatically between runs, there's a good chance that you might\n   * be trying to fit divergent semantics into a single object, and\n   * would be better off with multiple cache instances.\n   *\n   * **1**: Ie, they're not the \"same Promise\", but they resolve at\n   * the same time, because they're both waiting on the same\n   * underlying fetchMethod response.\n   */\n\n  fetch(\n    k: K,\n    fetchOptions: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : LRUCache.FetchOptionsWithContext<K, V, FC>\n  ): Promise<undefined | V>\n\n  // this overload not allowed if context is required\n  fetch(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    fetchOptions?: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : never\n  ): Promise<undefined | V>\n\n  async fetch(\n    k: K,\n    fetchOptions: LRUCache.FetchOptions<K, V, FC> = {}\n  ): Promise<undefined | V> {\n    const {\n      // get options\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      // set options\n      ttl = this.ttl,\n      noDisposeOnSet = this.noDisposeOnSet,\n      size = 0,\n      sizeCalculation = this.sizeCalculation,\n      noUpdateTTL = this.noUpdateTTL,\n      // fetch exclusive options\n      noDeleteOnFetchRejection = this.noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection = this.allowStaleOnFetchRejection,\n      ignoreFetchAbort = this.ignoreFetchAbort,\n      allowStaleOnFetchAbort = this.allowStaleOnFetchAbort,\n      context,\n      forceRefresh = false,\n      status,\n      signal,\n    } = fetchOptions\n\n    if (!this.#hasFetchMethod) {\n      if (status) status.fetch = 'get'\n      return this.get(k, {\n        allowStale,\n        updateAgeOnGet,\n        noDeleteOnStaleGet,\n        status,\n      })\n    }\n\n    const options = {\n      allowStale,\n      updateAgeOnGet,\n      noDeleteOnStaleGet,\n      ttl,\n      noDisposeOnSet,\n      size,\n      sizeCalculation,\n      noUpdateTTL,\n      noDeleteOnFetchRejection,\n      allowStaleOnFetchRejection,\n      allowStaleOnFetchAbort,\n      ignoreFetchAbort,\n      status,\n      signal,\n    }\n\n    let index = this.#keyMap.get(k)\n    if (index === undefined) {\n      if (status) status.fetch = 'miss'\n      const p = this.#backgroundFetch(k, index, options, context)\n      return (p.__returned = p)\n    } else {\n      // in cache, maybe already fetching\n      const v = this.#valList[index]\n      if (this.#isBackgroundFetch(v)) {\n        const stale =\n          allowStale && v.__staleWhileFetching !== undefined\n        if (status) {\n          status.fetch = 'inflight'\n          if (stale) status.returnedStale = true\n        }\n        return stale ? v.__staleWhileFetching : (v.__returned = v)\n      }\n\n      // if we force a refresh, that means do NOT serve the cached value,\n      // unless we are already in the process of refreshing the cache.\n      const isStale = this.#isStale(index)\n      if (!forceRefresh && !isStale) {\n        if (status) status.fetch = 'hit'\n        this.#moveToTail(index)\n        if (updateAgeOnGet) {\n          this.#updateItemAge(index)\n        }\n        if (status) this.#statusTTL(status, index)\n        return v\n      }\n\n      // ok, it is stale or a forced refresh, and not already fetching.\n      // refresh the cache.\n      const p = this.#backgroundFetch(k, index, options, context)\n      const hasStale = p.__staleWhileFetching !== undefined\n      const staleVal = hasStale && allowStale\n      if (status) {\n        status.fetch = isStale ? 'stale' : 'refresh'\n        if (staleVal && isStale) status.returnedStale = true\n      }\n      return staleVal ? p.__staleWhileFetching : (p.__returned = p)\n    }\n  }\n\n  /**\n   * In some cases, `cache.fetch()` may resolve to `undefined`, either because\n   * a {@link LRUCache.OptionsBase#fetchMethod} was not provided (turning\n   * `cache.fetch(k)` into just an async wrapper around `cache.get(k)`) or\n   * because `ignoreFetchAbort` was specified (either to the constructor or\n   * in the {@link LRUCache.FetchOptions}). Also, the\n   * {@link OptionsBase.fetchMethod} may return `undefined` or `void`, making\n   * the test even more complicated.\n   *\n   * Because inferring the cases where `undefined` might be returned are so\n   * cumbersome, but testing for `undefined` can also be annoying, this method\n   * can be used, which will reject if `this.fetch()` resolves to undefined.\n   */\n  forceFetch(\n    k: K,\n    fetchOptions: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : LRUCache.FetchOptionsWithContext<K, V, FC>\n  ): Promise<V>\n  // this overload not allowed if context is required\n  forceFetch(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    fetchOptions?: unknown extends FC\n      ? LRUCache.FetchOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.FetchOptionsNoContext<K, V>\n      : never\n  ): Promise<V>\n  async forceFetch(\n    k: K,\n    fetchOptions: LRUCache.FetchOptions<K, V, FC> = {}\n  ): Promise<V> {\n    const v = await this.fetch(\n      k,\n      fetchOptions as unknown extends FC\n        ? LRUCache.FetchOptions<K, V, FC>\n        : FC extends undefined | void\n        ? LRUCache.FetchOptionsNoContext<K, V>\n        : LRUCache.FetchOptionsWithContext<K, V, FC>\n    )\n    if (v === undefined) throw new Error('fetch() returned undefined')\n    return v\n  }\n\n  /**\n   * If the key is found in the cache, then this is equivalent to\n   * {@link LRUCache#get}. If not, in the cache, then calculate the value using\n   * the {@link LRUCache.OptionsBase.memoMethod}, and add it to the cache.\n   *\n   * If an `FC` type is set to a type other than `unknown`, `void`, or\n   * `undefined` in the LRUCache constructor, then all calls to `cache.memo()`\n   * _must_ provide a `context` option. If set to `undefined` or `void`, then\n   * calls to memo _must not_ provide a `context` option.\n   *\n   * The `context` param allows you to provide arbitrary data that might be\n   * relevant in the course of fetching the data. It is only relevant for the\n   * course of a single `memo()` operation, and discarded afterwards.\n   */\n  memo(\n    k: K,\n    memoOptions: unknown extends FC\n      ? LRUCache.MemoOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.MemoOptionsNoContext<K, V>\n      : LRUCache.MemoOptionsWithContext<K, V, FC>\n  ): V\n  // this overload not allowed if context is required\n  memo(\n    k: unknown extends FC\n      ? K\n      : FC extends undefined | void\n      ? K\n      : never,\n    memoOptions?: unknown extends FC\n      ? LRUCache.MemoOptions<K, V, FC>\n      : FC extends undefined | void\n      ? LRUCache.MemoOptionsNoContext<K, V>\n      : never\n  ): V\n  memo(k: K, memoOptions: LRUCache.MemoOptions<K, V, FC> = {}) {\n    const memoMethod = this.#memoMethod\n    if (!memoMethod) {\n      throw new Error('no memoMethod provided to constructor')\n    }\n    const { context, forceRefresh, ...options } = memoOptions\n    const v = this.get(k, options)\n    if (!forceRefresh && v !== undefined) return v\n    const vv = memoMethod(k, v, {\n      options,\n      context,\n    } as LRUCache.MemoizerOptions<K, V, FC>)\n    this.set(k, vv, options)\n    return vv\n  }\n\n  /**\n   * Return a value from the cache. Will update the recency of the cache\n   * entry found.\n   *\n   * If the key is not found, get() will return `undefined`.\n   */\n  get(k: K, getOptions: LRUCache.GetOptions<K, V, FC> = {}) {\n    const {\n      allowStale = this.allowStale,\n      updateAgeOnGet = this.updateAgeOnGet,\n      noDeleteOnStaleGet = this.noDeleteOnStaleGet,\n      status,\n    } = getOptions\n    const index = this.#keyMap.get(k)\n    if (index !== undefined) {\n      const value = this.#valList[index]\n      const fetching = this.#isBackgroundFetch(value)\n      if (status) this.#statusTTL(status, index)\n      if (this.#isStale(index)) {\n        if (status) status.get = 'stale'\n        // delete only if not an in-flight background fetch\n        if (!fetching) {\n          if (!noDeleteOnStaleGet) {\n            this.#delete(k, 'expire')\n          }\n          if (status && allowStale) status.returnedStale = true\n          return allowStale ? value : undefined\n        } else {\n          if (\n            status &&\n            allowStale &&\n            value.__staleWhileFetching !== undefined\n          ) {\n            status.returnedStale = true\n          }\n          return allowStale ? value.__staleWhileFetching : undefined\n        }\n      } else {\n        if (status) status.get = 'hit'\n        // if we're currently fetching it, we don't actually have it yet\n        // it's not stale, which means this isn't a staleWhileRefetching.\n        // If it's not stale, and fetching, AND has a __staleWhileFetching\n        // value, then that means the user fetched with {forceRefresh:true},\n        // so it's safe to return that value.\n        if (fetching) {\n          return value.__staleWhileFetching\n        }\n        this.#moveToTail(index)\n        if (updateAgeOnGet) {\n          this.#updateItemAge(index)\n        }\n        return value\n      }\n    } else if (status) {\n      status.get = 'miss'\n    }\n  }\n\n  #connect(p: Index, n: Index) {\n    this.#prev[n] = p\n    this.#next[p] = n\n  }\n\n  #moveToTail(index: Index): void {\n    // if tail already, nothing to do\n    // if head, move head to next[index]\n    // else\n    //   move next[prev[index]] to next[index] (head has no prev)\n    //   move prev[next[index]] to prev[index]\n    // prev[index] = tail\n    // next[tail] = index\n    // tail = index\n    if (index !== this.#tail) {\n      if (index === this.#head) {\n        this.#head = this.#next[index] as Index\n      } else {\n        this.#connect(\n          this.#prev[index] as Index,\n          this.#next[index] as Index\n        )\n      }\n      this.#connect(this.#tail, index)\n      this.#tail = index\n    }\n  }\n\n  /**\n   * Deletes a key out of the cache.\n   *\n   * Returns true if the key was deleted, false otherwise.\n   */\n  delete(k: K) {\n    return this.#delete(k, 'delete')\n  }\n\n  #delete(k: K, reason: LRUCache.DisposeReason) {\n    let deleted = false\n    if (this.#size !== 0) {\n      const index = this.#keyMap.get(k)\n      if (index !== undefined) {\n        deleted = true\n        if (this.#size === 1) {\n          this.#clear(reason)\n        } else {\n          this.#removeItemSize(index)\n          const v = this.#valList[index]\n          if (this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('deleted'))\n          } else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n              this.#dispose?.(v as V, k, reason)\n            }\n            if (this.#hasDisposeAfter) {\n              this.#disposed?.push([v as V, k, reason])\n            }\n          }\n          this.#keyMap.delete(k)\n          this.#keyList[index] = undefined\n          this.#valList[index] = undefined\n          if (index === this.#tail) {\n            this.#tail = this.#prev[index] as Index\n          } else if (index === this.#head) {\n            this.#head = this.#next[index] as Index\n          } else {\n            const pi = this.#prev[index] as number\n            this.#next[pi] = this.#next[index] as number\n            const ni = this.#next[index] as number\n            this.#prev[ni] = this.#prev[index] as number\n          }\n          this.#size--\n          this.#free.push(index)\n        }\n      }\n    }\n    if (this.#hasDisposeAfter && this.#disposed?.length) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n    return deleted\n  }\n\n  /**\n   * Clear the cache entirely, throwing away all values.\n   */\n  clear() {\n    return this.#clear('delete')\n  }\n  #clear(reason: LRUCache.DisposeReason) {\n    for (const index of this.#rindexes({ allowStale: true })) {\n      const v = this.#valList[index]\n      if (this.#isBackgroundFetch(v)) {\n        v.__abortController.abort(new Error('deleted'))\n      } else {\n        const k = this.#keyList[index]\n        if (this.#hasDispose) {\n          this.#dispose?.(v as V, k as K, reason)\n        }\n        if (this.#hasDisposeAfter) {\n          this.#disposed?.push([v as V, k as K, reason])\n        }\n      }\n    }\n\n    this.#keyMap.clear()\n    this.#valList.fill(undefined)\n    this.#keyList.fill(undefined)\n    if (this.#ttls && this.#starts) {\n      this.#ttls.fill(0)\n      this.#starts.fill(0)\n    }\n    if (this.#sizes) {\n      this.#sizes.fill(0)\n    }\n    this.#head = 0 as Index\n    this.#tail = 0 as Index\n    this.#free.length = 0\n    this.#calculatedSize = 0\n    this.#size = 0\n    if (this.#hasDisposeAfter && this.#disposed) {\n      const dt = this.#disposed\n      let task: DisposeTask<K, V> | undefined\n      while ((task = dt?.shift())) {\n        this.#disposeAfter?.(...task)\n      }\n    }\n  }\n}\n"], "mappings": "+aAMA,IAAMA,EACJ,OAAO,aAAgB,UACvB,aACA,OAAO,YAAY,KAAQ,WACvB,YACA,KAEAC,EAAS,IAAI,IAMbC,EACJ,OAAO,SAAY,UAAc,QAAU,QAAU,CAAA,EAIjDC,EAAc,CAClBC,EACAC,EACAC,EACAC,IACE,CACF,OAAOL,EAAQ,aAAgB,WAC3BA,EAAQ,YAAYE,EAAKC,EAAMC,EAAMC,CAAE,EACvC,QAAQ,MAAM,IAAID,MAASD,MAASD,GAAK,CAC/C,EAEII,EAAK,WAAW,gBAChBC,EAAK,WAAW,YAGpB,GAAI,OAAOD,EAAO,IAAa,CAE7BC,EAAK,KAAiB,CACpB,QACA,SAAqC,CAAA,EACrC,OACA,QAAmB,GACnB,iBAAiBC,EAAWH,EAAwB,CAClD,KAAK,SAAS,KAAKA,CAAE,CACvB,GAGFC,EAAK,KAAqB,CACxB,aAAA,CACEG,EAAc,CAChB,CACA,OAAS,IAAIF,EACb,MAAMG,EAAW,CACf,GAAI,MAAK,OAAO,QAEhB,MAAK,OAAO,OAASA,EAErB,KAAK,OAAO,QAAU,GAEtB,QAAWL,KAAM,KAAK,OAAO,SAC3BA,EAAGK,CAAM,EAEX,KAAK,OAAO,UAAUA,CAAM,EAC9B,GAEF,IAAIC,EACFX,EAAQ,KAAK,8BAAgC,IACzCS,EAAiB,IAAK,CACrBE,IACLA,EAAyB,GACzBV,EACE,maAOA,sBACA,UACAQ,CAAc,EAElB,EAIF,IAAMG,EAAcR,GAAiB,CAACL,EAAO,IAAIK,CAAI,EAE/CS,EAAO,OAAO,MAAM,EAIpBC,EAAYC,GAChBA,GAAKA,IAAM,KAAK,MAAMA,CAAC,GAAKA,EAAI,GAAK,SAASA,CAAC,EAc3CC,EAAgBC,GACnBH,EAASG,CAAG,EAETA,GAAO,KAAK,IAAI,EAAG,CAAC,EACpB,WACAA,GAAO,KAAK,IAAI,EAAG,EAAE,EACrB,YACAA,GAAO,KAAK,IAAI,EAAG,EAAE,EACrB,YACAA,GAAO,OAAO,iBACdC,EACA,KATA,KAYAA,EAAN,cAAwB,KAAa,CACnC,YAAYC,EAAY,CACtB,MAAMA,CAAI,EACV,KAAK,KAAK,CAAC,CACb,KAMIC,EAAN,KAAW,CACT,KACA,OAGA,OAAO,OAAOH,EAAW,CACvB,IAAMI,EAAUL,EAAaC,CAAG,EAChC,GAAI,CAACI,EAAS,MAAO,CAAA,EACrBC,EAAAF,EAAMG,EAAgB,IACtB,IAAMC,EAAI,IAAIJ,EAAMH,EAAKI,CAAO,EAChC,OAAAC,EAAAF,EAAMG,EAAgB,IACfC,CACT,CACA,YACEP,EACAI,EAAyC,CAGzC,GAAI,CAACI,EAAAL,EAAMG,GACT,MAAM,IAAI,UAAU,yCAAyC,EAG/D,KAAK,KAAO,IAAIF,EAAQJ,CAAG,EAC3B,KAAK,OAAS,CAChB,CACA,KAAKF,EAAQ,CACX,KAAK,KAAK,KAAK,QAAQ,EAAIA,CAC7B,CACA,KAAG,CACD,OAAO,KAAK,KAAK,EAAE,KAAK,MAAM,CAChC,GA9BIW,EAANN,EAISG,EAAA,YAAPI,EAJID,EAIGH,EAAyB,IAi9BlC,IAAaK,EAAb,KAAqB,CAIVC,GACAC,GACAC,GACAC,GACAC,GACAC,GAKT,IAKA,cAIA,aAIA,eAIA,eAIA,WAKA,eAIA,YAIA,aAIA,gBAIA,yBAIA,mBAIA,uBAIA,2BAIA,iBAGAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAEAC,GACAC,GACAC,GAWA,OAAO,sBAILC,EAAqB,CACrB,MAAO,CAEL,OAAQA,EAAEL,GACV,KAAMK,EAAEJ,GACR,MAAOI,EAAEN,GACT,OAAQM,EAAEf,GACV,QAASe,EAAEd,GACX,QAASc,EAAEb,GACX,KAAMa,EAAEZ,GACR,KAAMY,EAAEX,GACR,IAAI,MAAI,CACN,OAAOW,EAAEV,EACX,EACA,IAAI,MAAI,CACN,OAAOU,EAAET,EACX,EACA,KAAMS,EAAER,GAER,kBAAoBS,GAAWD,EAAEE,GAAmBD,CAAC,EACrD,gBAAiB,CACfE,EACAC,EACAC,EACAC,IAEAN,EAAEO,GACAJ,EACAC,EACAC,EACAC,CAAO,EAEX,WAAaF,GACXJ,EAAEQ,GAAYJ,CAAc,EAC9B,QAAUC,GACRL,EAAES,GAASJ,CAAO,EACpB,SAAWA,GACTL,EAAEU,GAAUL,CAAO,EACrB,QAAUD,GACRJ,EAAEW,GAASP,CAAc,EAE/B,CAOA,IAAI,KAAG,CACL,OAAO,KAAK3B,EACd,CAIA,IAAI,SAAO,CACT,OAAO,KAAKC,EACd,CAIA,IAAI,gBAAc,CAChB,OAAO,KAAKM,EACd,CAIA,IAAI,MAAI,CACN,OAAO,KAAKD,EACd,CAIA,IAAI,aAAW,CACb,OAAO,KAAKF,EACd,CACA,IAAI,YAAU,CACZ,OAAO,KAAKC,EACd,CAIA,IAAI,SAAO,CACT,OAAO,KAAKH,EACd,CAIA,IAAI,cAAY,CACd,OAAO,KAAKC,EACd,CAEA,YACEyB,EAAwD,CAExD,GAAM,CACJ,IAAAxC,EAAM,EACN,IAAA+C,EACA,cAAAC,EAAgB,EAChB,aAAAC,EACA,eAAAC,EACA,eAAAC,EACA,WAAAC,EACA,QAAAC,EACA,aAAAC,EACA,eAAAC,EACA,YAAAC,EACA,QAAAC,EAAU,EACV,aAAAC,EAAe,EACf,gBAAAC,EACA,YAAAC,EACA,WAAAC,EACA,yBAAAC,EACA,mBAAAC,EACA,2BAAAC,EACA,uBAAAC,EACA,iBAAAC,CAAgB,EACd1B,EAEJ,GAAIxC,IAAQ,GAAK,CAACH,EAASG,CAAG,EAC5B,MAAM,IAAI,UAAU,0CAA0C,EAGhE,IAAMmE,EAAYnE,EAAMD,EAAaC,CAAG,EAAI,MAC5C,GAAI,CAACmE,EACH,MAAM,IAAI,MAAM,sBAAwBnE,CAAG,EAO7C,GAJA,KAAKY,GAAOZ,EACZ,KAAKa,GAAW4C,EAChB,KAAK,aAAeC,GAAgB,KAAK7C,GACzC,KAAK,gBAAkB8C,EACnB,KAAK,gBAAiB,CACxB,GAAI,CAAC,KAAK9C,IAAY,CAAC,KAAK,aAC1B,MAAM,IAAI,UACR,oEAAoE,EAGxE,GAAI,OAAO,KAAK,iBAAoB,WAClC,MAAM,IAAI,UAAU,qCAAqC,EAI7D,GACEgD,IAAe,QACf,OAAOA,GAAe,WAEtB,MAAM,IAAI,UAAU,0CAA0C,EAIhE,GAFA,KAAK5C,GAAc4C,EAGjBD,IAAgB,QAChB,OAAOA,GAAgB,WAEvB,MAAM,IAAI,UACR,6CAA6C,EAsCjD,GAnCA,KAAK5C,GAAe4C,EACpB,KAAK3B,GAAkB,CAAC,CAAC2B,EAEzB,KAAKxC,GAAU,IAAI,IACnB,KAAKC,GAAW,IAAI,MAAMrB,CAAG,EAAE,KAAK,MAAS,EAC7C,KAAKsB,GAAW,IAAI,MAAMtB,CAAG,EAAE,KAAK,MAAS,EAC7C,KAAKuB,GAAQ,IAAI4C,EAAUnE,CAAG,EAC9B,KAAKwB,GAAQ,IAAI2C,EAAUnE,CAAG,EAC9B,KAAKyB,GAAQ,EACb,KAAKC,GAAQ,EACb,KAAKC,GAAQlB,EAAM,OAAOT,CAAG,EAC7B,KAAKkB,GAAQ,EACb,KAAKC,GAAkB,EAEnB,OAAOkC,GAAY,aACrB,KAAKvC,GAAWuC,GAEd,OAAOC,GAAiB,YAC1B,KAAKvC,GAAgBuC,EACrB,KAAK1B,GAAY,CAAA,IAEjB,KAAKb,GAAgB,OACrB,KAAKa,GAAY,QAEnB,KAAKI,GAAc,CAAC,CAAC,KAAKlB,GAC1B,KAAKoB,GAAmB,CAAC,CAAC,KAAKnB,GAE/B,KAAK,eAAiB,CAAC,CAACwC,EACxB,KAAK,YAAc,CAAC,CAACC,EACrB,KAAK,yBAA2B,CAAC,CAACM,EAClC,KAAK,2BAA6B,CAAC,CAACE,EACpC,KAAK,uBAAyB,CAAC,CAACC,EAChC,KAAK,iBAAmB,CAAC,CAACC,EAGtB,KAAK,eAAiB,EAAG,CAC3B,GAAI,KAAKrD,KAAa,GAChB,CAAChB,EAAS,KAAKgB,EAAQ,EACzB,MAAM,IAAI,UACR,iDAAiD,EAIvD,GAAI,CAAChB,EAAS,KAAK,YAAY,EAC7B,MAAM,IAAI,UACR,sDAAsD,EAG1D,KAAKuE,GAAuB,EAa9B,GAVA,KAAK,WAAa,CAAC,CAAChB,EACpB,KAAK,mBAAqB,CAAC,CAACW,EAC5B,KAAK,eAAiB,CAAC,CAACb,EACxB,KAAK,eAAiB,CAAC,CAACC,EACxB,KAAK,cACHtD,EAASmD,CAAa,GAAKA,IAAkB,EACzCA,EACA,EACN,KAAK,aAAe,CAAC,CAACC,EACtB,KAAK,IAAMF,GAAO,EACd,KAAK,IAAK,CACZ,GAAI,CAAClD,EAAS,KAAK,GAAG,EACpB,MAAM,IAAI,UACR,6CAA6C,EAGjD,KAAKwE,GAAsB,EAI7B,GAAI,KAAKzD,KAAS,GAAK,KAAK,MAAQ,GAAK,KAAKC,KAAa,EACzD,MAAM,IAAI,UACR,kDAAkD,EAGtD,GAAI,CAAC,KAAK,cAAgB,CAAC,KAAKD,IAAQ,CAAC,KAAKC,GAAU,CACtD,IAAM1B,EAAO,sBACTQ,EAAWR,CAAI,IACjBL,EAAO,IAAIK,CAAI,EAIfH,EAFE,gGAEe,wBAAyBG,EAAMwB,CAAQ,GAG9D,CAMA,gBAAgB2D,EAAM,CACpB,OAAO,KAAKlD,GAAQ,IAAIkD,CAAG,EAAI,IAAW,CAC5C,CAEAD,IAAsB,CACpB,IAAME,EAAO,IAAItE,EAAU,KAAKW,EAAI,EAC9B4D,EAAS,IAAIvE,EAAU,KAAKW,EAAI,EACtC,KAAKmB,GAAQwC,EACb,KAAKzC,GAAU0C,EAEf,KAAKC,GAAc,CAAClC,EAAOQ,EAAK2B,EAAQ7F,EAAK,IAAG,IAAM,CAGpD,GAFA2F,EAAOjC,CAAK,EAAIQ,IAAQ,EAAI2B,EAAQ,EACpCH,EAAKhC,CAAK,EAAIQ,EACVA,IAAQ,GAAK,KAAK,aAAc,CAClC,IAAM4B,EAAI,WAAW,IAAK,CACpB,KAAK7B,GAASP,CAAK,GACrB,KAAKqC,GAAQ,KAAKvD,GAASkB,CAAK,EAAQ,QAAQ,CAEpD,EAAGQ,EAAM,CAAC,EAGN4B,EAAE,OACJA,EAAE,MAAK,EAIb,EAEA,KAAKE,GAAiBtC,GAAQ,CAC5BiC,EAAOjC,CAAK,EAAIgC,EAAKhC,CAAK,IAAM,EAAI1D,EAAK,IAAG,EAAK,CACnD,EAEA,KAAKiG,GAAa,CAACC,EAAQxC,IAAS,CAClC,GAAIgC,EAAKhC,CAAK,EAAG,CACf,IAAMQ,EAAMwB,EAAKhC,CAAK,EAChBmC,EAAQF,EAAOjC,CAAK,EAE1B,GAAI,CAACQ,GAAO,CAAC2B,EAAO,OACpBK,EAAO,IAAMhC,EACbgC,EAAO,MAAQL,EACfK,EAAO,IAAMC,GAAaC,EAAM,EAChC,IAAMC,EAAMH,EAAO,IAAML,EACzBK,EAAO,aAAehC,EAAMmC,EAEhC,EAIA,IAAIF,EAAY,EACVC,EAAS,IAAK,CAClB,IAAM,EAAIpG,EAAK,IAAG,EAClB,GAAI,KAAK,cAAgB,EAAG,CAC1BmG,EAAY,EACZ,IAAML,EAAI,WACR,IAAOK,EAAY,EACnB,KAAK,aAAa,EAIhBL,EAAE,OACJA,EAAE,MAAK,EAIX,OAAO,CACT,EAEA,KAAK,gBAAkBL,GAAM,CAC3B,IAAM/B,EAAQ,KAAKnB,GAAQ,IAAIkD,CAAG,EAClC,GAAI/B,IAAU,OACZ,MAAO,GAET,IAAMQ,EAAMwB,EAAKhC,CAAK,EAChBmC,EAAQF,EAAOjC,CAAK,EAC1B,GAAI,CAACQ,GAAO,CAAC2B,EACX,MAAO,KAET,IAAMQ,GAAOF,GAAaC,EAAM,GAAMP,EACtC,OAAO3B,EAAMmC,CACf,EAEA,KAAKpC,GAAWP,GAAQ,CACtB,IAAMhC,EAAIiE,EAAOjC,CAAK,EAChBoC,EAAIJ,EAAKhC,CAAK,EACpB,MAAO,CAAC,CAACoC,GAAK,CAAC,CAACpE,IAAMyE,GAAaC,EAAM,GAAM1E,EAAIoE,CACrD,CACF,CAGAE,GAAyC,IAAK,CAAE,EAChDC,GACE,IAAK,CAAE,EACTL,GAMY,IAAK,CAAE,EAGnB3B,GAAsC,IAAM,GAE5CsB,IAAuB,CACrB,IAAMe,EAAQ,IAAIlF,EAAU,KAAKW,EAAI,EACrC,KAAKO,GAAkB,EACvB,KAAKU,GAASsD,EACd,KAAKC,GAAkB7C,GAAQ,CAC7B,KAAKpB,IAAmBgE,EAAM5C,CAAK,EACnC4C,EAAM5C,CAAK,EAAI,CACjB,EACA,KAAK8C,GAAe,CAAC/C,EAAGgD,EAAGpF,EAAMyD,IAAmB,CAGlD,GAAI,KAAKtB,GAAmBiD,CAAC,EAC3B,MAAO,GAET,GAAI,CAACzF,EAASK,CAAI,EAChB,GAAIyD,EAAiB,CACnB,GAAI,OAAOA,GAAoB,WAC7B,MAAM,IAAI,UAAU,oCAAoC,EAG1D,GADAzD,EAAOyD,EAAgB2B,EAAGhD,CAAC,EACvB,CAACzC,EAASK,CAAI,EAChB,MAAM,IAAI,UACR,0DAA0D,MAI9D,OAAM,IAAI,UACR,2HAEwB,EAI9B,OAAOA,CACT,EACA,KAAKqF,GAAe,CAClBhD,EACArC,EACA6E,IACE,CAEF,GADAI,EAAM5C,CAAK,EAAIrC,EACX,KAAKW,GAAU,CACjB,IAAM4C,EAAU,KAAK5C,GAAYsE,EAAM5C,CAAK,EAC5C,KAAO,KAAKpB,GAAkBsC,GAC5B,KAAK+B,GAAO,EAAI,EAGpB,KAAKrE,IAAmBgE,EAAM5C,CAAK,EAC/BwC,IACFA,EAAO,UAAY7E,EACnB6E,EAAO,oBAAsB,KAAK5D,GAEtC,CACF,CAEAiE,GAA0CK,GAAK,CAAE,EACjDF,GAIY,CAACE,EAAIC,EAAIC,IAAO,CAAE,EAC9BN,GAKqB,CACnBO,EACAC,EACA3F,EACAyD,IACE,CACF,GAAIzD,GAAQyD,EACV,MAAM,IAAI,UACR,kEAAkE,EAGtE,MAAO,EACT,EAEA,CAACf,GAAS,CAAE,WAAAQ,EAAa,KAAK,UAAU,EAAK,CAAA,EAAE,CAC7C,GAAI,KAAKlC,GACP,QAAS4E,EAAI,KAAKpE,GACZ,GAAC,KAAKqE,GAAcD,CAAC,KAGrB1C,GAAc,CAAC,KAAKN,GAASgD,CAAC,KAChC,MAAMA,GAEJA,IAAM,KAAKrE,MAGbqE,EAAI,KAAKtE,GAAMsE,CAAC,CAIxB,CAEA,CAACjD,GAAU,CAAE,WAAAO,EAAa,KAAK,UAAU,EAAK,CAAA,EAAE,CAC9C,GAAI,KAAKlC,GACP,QAAS4E,EAAI,KAAKrE,GACZ,GAAC,KAAKsE,GAAcD,CAAC,KAGrB1C,GAAc,CAAC,KAAKN,GAASgD,CAAC,KAChC,MAAMA,GAEJA,IAAM,KAAKpE,MAGboE,EAAI,KAAKvE,GAAMuE,CAAC,CAIxB,CAEAC,GAAcxD,EAAY,CACxB,OACEA,IAAU,QACV,KAAKnB,GAAQ,IAAI,KAAKC,GAASkB,CAAK,CAAM,IAAMA,CAEpD,CAMA,CAAC,SAAO,CACN,QAAWuD,KAAK,KAAKlD,GAAQ,EAEzB,KAAKtB,GAASwE,CAAC,IAAM,QACrB,KAAKzE,GAASyE,CAAC,IAAM,QACrB,CAAC,KAAKzD,GAAmB,KAAKf,GAASwE,CAAC,CAAC,IAEzC,KAAM,CAAC,KAAKzE,GAASyE,CAAC,EAAG,KAAKxE,GAASwE,CAAC,CAAC,EAG/C,CAQA,CAAC,UAAQ,CACP,QAAWA,KAAK,KAAKjD,GAAS,EAE1B,KAAKvB,GAASwE,CAAC,IAAM,QACrB,KAAKzE,GAASyE,CAAC,IAAM,QACrB,CAAC,KAAKzD,GAAmB,KAAKf,GAASwE,CAAC,CAAC,IAEzC,KAAM,CAAC,KAAKzE,GAASyE,CAAC,EAAG,KAAKxE,GAASwE,CAAC,CAAC,EAG/C,CAMA,CAAC,MAAI,CACH,QAAWA,KAAK,KAAKlD,GAAQ,EAAI,CAC/B,IAAMN,EAAI,KAAKjB,GAASyE,CAAC,EAEvBxD,IAAM,QACN,CAAC,KAAKD,GAAmB,KAAKf,GAASwE,CAAC,CAAC,IAEzC,MAAMxD,GAGZ,CAQA,CAAC,OAAK,CACJ,QAAWwD,KAAK,KAAKjD,GAAS,EAAI,CAChC,IAAMP,EAAI,KAAKjB,GAASyE,CAAC,EAEvBxD,IAAM,QACN,CAAC,KAAKD,GAAmB,KAAKf,GAASwE,CAAC,CAAC,IAEzC,MAAMxD,GAGZ,CAMA,CAAC,QAAM,CACL,QAAWwD,KAAK,KAAKlD,GAAQ,EACjB,KAAKtB,GAASwE,CAAC,IAEjB,QACN,CAAC,KAAKzD,GAAmB,KAAKf,GAASwE,CAAC,CAAC,IAEzC,MAAM,KAAKxE,GAASwE,CAAC,EAG3B,CAQA,CAAC,SAAO,CACN,QAAWA,KAAK,KAAKjD,GAAS,EAClB,KAAKvB,GAASwE,CAAC,IAEjB,QACN,CAAC,KAAKzD,GAAmB,KAAKf,GAASwE,CAAC,CAAC,IAEzC,MAAM,KAAKxE,GAASwE,CAAC,EAG3B,CAMA,CAAC,OAAO,QAAQ,GAAC,CACf,OAAO,KAAK,QAAO,CACrB,CAOA,CAAC,OAAO,WAAW,EAAI,WAMvB,KACE1G,EACA4G,EAA4C,CAAA,EAAE,CAE9C,QAAW,KAAK,KAAKpD,GAAQ,EAAI,CAC/B,IAAM0C,EAAI,KAAKhE,GAAS,CAAC,EACnB2E,EAAQ,KAAK5D,GAAmBiD,CAAC,EACnCA,EAAE,qBACFA,EACJ,GAAIW,IAAU,QACV7G,EAAG6G,EAAO,KAAK5E,GAAS,CAAC,EAAQ,IAAI,EACvC,OAAO,KAAK,IAAI,KAAKA,GAAS,CAAC,EAAQ2E,CAAU,EAGvD,CAaA,QACE5G,EACA8G,EAAa,KAAI,CAEjB,QAAW,KAAK,KAAKtD,GAAQ,EAAI,CAC/B,IAAM0C,EAAI,KAAKhE,GAAS,CAAC,EACnB2E,EAAQ,KAAK5D,GAAmBiD,CAAC,EACnCA,EAAE,qBACFA,EACAW,IAAU,QACd7G,EAAG,KAAK8G,EAAOD,EAAO,KAAK5E,GAAS,CAAC,EAAQ,IAAI,EAErD,CAMA,SACEjC,EACA8G,EAAa,KAAI,CAEjB,QAAW,KAAK,KAAKrD,GAAS,EAAI,CAChC,IAAMyC,EAAI,KAAKhE,GAAS,CAAC,EACnB2E,EAAQ,KAAK5D,GAAmBiD,CAAC,EACnCA,EAAE,qBACFA,EACAW,IAAU,QACd7G,EAAG,KAAK8G,EAAOD,EAAO,KAAK5E,GAAS,CAAC,EAAQ,IAAI,EAErD,CAMA,YAAU,CACR,IAAI8E,EAAU,GACd,QAAWL,KAAK,KAAKjD,GAAU,CAAE,WAAY,EAAI,CAAE,EAC7C,KAAKC,GAASgD,CAAC,IACjB,KAAKlB,GAAQ,KAAKvD,GAASyE,CAAC,EAAQ,QAAQ,EAC5CK,EAAU,IAGd,OAAOA,CACT,CAcA,KAAK7B,EAAM,CACT,IAAMwB,EAAI,KAAK1E,GAAQ,IAAIkD,CAAG,EAC9B,GAAIwB,IAAM,OAAW,OACrB,IAAMR,EAAI,KAAKhE,GAASwE,CAAC,EACnBG,EAAuB,KAAK5D,GAAmBiD,CAAC,EAClDA,EAAE,qBACFA,EACJ,GAAIW,IAAU,OAAW,OACzB,IAAMG,EAA2B,CAAE,MAAAH,CAAK,EACxC,GAAI,KAAKlE,IAAS,KAAKD,GAAS,CAC9B,IAAMiB,EAAM,KAAKhB,GAAM+D,CAAC,EAClBpB,EAAQ,KAAK5C,GAAQgE,CAAC,EAC5B,GAAI/C,GAAO2B,EAAO,CAChB,IAAM2B,EAAStD,GAAOlE,EAAK,IAAG,EAAK6F,GACnC0B,EAAM,IAAMC,EACZD,EAAM,MAAQ,KAAK,IAAG,GAG1B,OAAI,KAAKvE,KACPuE,EAAM,KAAO,KAAKvE,GAAOiE,CAAC,GAErBM,CACT,CAeA,MAAI,CACF,IAAME,EAAgC,CAAA,EACtC,QAAWR,KAAK,KAAKlD,GAAS,CAAE,WAAY,EAAI,CAAE,EAAG,CACnD,IAAM0B,EAAM,KAAKjD,GAASyE,CAAC,EACrBR,EAAI,KAAKhE,GAASwE,CAAC,EACnBG,EAAuB,KAAK5D,GAAmBiD,CAAC,EAClDA,EAAE,qBACFA,EACJ,GAAIW,IAAU,QAAa3B,IAAQ,OAAW,SAC9C,IAAM8B,EAA2B,CAAE,MAAAH,CAAK,EACxC,GAAI,KAAKlE,IAAS,KAAKD,GAAS,CAC9BsE,EAAM,IAAM,KAAKrE,GAAM+D,CAAC,EAGxB,IAAMZ,EAAMrG,EAAK,IAAG,EAAM,KAAKiD,GAAQgE,CAAC,EACxCM,EAAM,MAAQ,KAAK,MAAM,KAAK,IAAG,EAAKlB,CAAG,EAEvC,KAAKrD,KACPuE,EAAM,KAAO,KAAKvE,GAAOiE,CAAC,GAE5BQ,EAAI,QAAQ,CAAChC,EAAK8B,CAAK,CAAC,EAE1B,OAAOE,CACT,CAWA,KAAKA,EAA6B,CAChC,KAAK,MAAK,EACV,OAAW,CAAChC,EAAK8B,CAAK,IAAKE,EAAK,CAC9B,GAAIF,EAAM,MAAO,CAOf,IAAMlB,EAAM,KAAK,IAAG,EAAKkB,EAAM,MAC/BA,EAAM,MAAQvH,EAAK,IAAG,EAAKqG,EAE7B,KAAK,IAAIZ,EAAK8B,EAAM,MAAOA,CAAK,EAEpC,CAgCA,IACE9D,EACAgD,EACAiB,EAA4C,CAAA,EAAE,CAE9C,GAAIjB,IAAM,OACR,YAAK,OAAOhD,CAAC,EACN,KAET,GAAM,CACJ,IAAAS,EAAM,KAAK,IACX,MAAA2B,EACA,eAAAnB,EAAiB,KAAK,eACtB,gBAAAI,EAAkB,KAAK,gBACvB,OAAAoB,CAAM,EACJwB,EACA,CAAE,YAAA/C,EAAc,KAAK,WAAW,EAAK+C,EAEnCrG,EAAO,KAAKmF,GAChB/C,EACAgD,EACAiB,EAAW,MAAQ,EACnB5C,CAAe,EAIjB,GAAI,KAAK,cAAgBzD,EAAO,KAAK,aACnC,OAAI6E,IACFA,EAAO,IAAM,OACbA,EAAO,qBAAuB,IAGhC,KAAKH,GAAQtC,EAAG,KAAK,EACd,KAET,IAAIC,EAAQ,KAAKrB,KAAU,EAAI,OAAY,KAAKE,GAAQ,IAAIkB,CAAC,EAC7D,GAAIC,IAAU,OAEZA,EACE,KAAKrB,KAAU,EACX,KAAKQ,GACL,KAAKC,GAAM,SAAW,EACtB,KAAKA,GAAM,IAAG,EACd,KAAKT,KAAU,KAAKN,GACpB,KAAK4E,GAAO,EAAK,EACjB,KAAKtE,GAEX,KAAKG,GAASkB,CAAK,EAAID,EACvB,KAAKhB,GAASiB,CAAK,EAAI+C,EACvB,KAAKlE,GAAQ,IAAIkB,EAAGC,CAAK,EACzB,KAAKhB,GAAM,KAAKG,EAAK,EAAIa,EACzB,KAAKf,GAAMe,CAAK,EAAI,KAAKb,GACzB,KAAKA,GAAQa,EACb,KAAKrB,KACL,KAAKqE,GAAahD,EAAOrC,EAAM6E,CAAM,EACjCA,IAAQA,EAAO,IAAM,OACzBvB,EAAc,OACT,CAEL,KAAKb,GAAYJ,CAAK,EACtB,IAAMiE,EAAS,KAAKlF,GAASiB,CAAK,EAClC,GAAI+C,IAAMkB,EAAQ,CAChB,GAAI,KAAKvE,IAAmB,KAAKI,GAAmBmE,CAAM,EAAG,CAC3DA,EAAO,kBAAkB,MAAM,IAAI,MAAM,UAAU,CAAC,EACpD,GAAM,CAAE,qBAAsBjG,CAAC,EAAKiG,EAChCjG,IAAM,QAAa,CAACgD,IAClB,KAAKvB,IACP,KAAKlB,KAAWP,EAAQ+B,EAAG,KAAK,EAE9B,KAAKJ,IACP,KAAKN,IAAW,KAAK,CAACrB,EAAQ+B,EAAG,KAAK,CAAC,QAGjCiB,IACN,KAAKvB,IACP,KAAKlB,KAAW0F,EAAalE,EAAG,KAAK,EAEnC,KAAKJ,IACP,KAAKN,IAAW,KAAK,CAAC4E,EAAalE,EAAG,KAAK,CAAC,GAMhD,GAHA,KAAK8C,GAAgB7C,CAAK,EAC1B,KAAKgD,GAAahD,EAAOrC,EAAM6E,CAAM,EACrC,KAAKzD,GAASiB,CAAK,EAAI+C,EACnBP,EAAQ,CACVA,EAAO,IAAM,UACb,IAAM0B,EACJD,GAAU,KAAKnE,GAAmBmE,CAAM,EACpCA,EAAO,qBACPA,EACFC,IAAa,SAAW1B,EAAO,SAAW0B,SAEvC1B,IACTA,EAAO,IAAM,UAYjB,GATIhC,IAAQ,GAAK,CAAC,KAAKhB,IACrB,KAAKsC,GAAsB,EAEzB,KAAKtC,KACFyB,GACH,KAAKiB,GAAYlC,EAAOQ,EAAK2B,CAAK,EAEhCK,GAAQ,KAAKD,GAAWC,EAAQxC,CAAK,GAEvC,CAACgB,GAAkB,KAAKrB,IAAoB,KAAKN,GAAW,CAC9D,IAAM8E,EAAK,KAAK9E,GACZ+E,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK3F,KAAgB,GAAG4F,CAAI,EAGhC,OAAO,IACT,CAMA,KAAG,CACD,GAAI,CACF,KAAO,KAAKzF,IAAO,CACjB,IAAM0F,EAAM,KAAKtF,GAAS,KAAKG,EAAK,EAEpC,GADA,KAAK+D,GAAO,EAAI,EACZ,KAAKnD,GAAmBuE,CAAG,GAC7B,GAAIA,EAAI,qBACN,OAAOA,EAAI,6BAEJA,IAAQ,OACjB,OAAOA,WAIX,GAAI,KAAK1E,IAAoB,KAAKN,GAAW,CAC3C,IAAM8E,EAAK,KAAK9E,GACZ+E,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK3F,KAAgB,GAAG4F,CAAI,GAIpC,CAEAnB,GAAOqB,EAAa,CAClB,IAAMC,EAAO,KAAKrF,GACZa,EAAI,KAAKjB,GAASyF,CAAI,EACtBxB,EAAI,KAAKhE,GAASwF,CAAI,EAC5B,OAAI,KAAK7E,IAAmB,KAAKI,GAAmBiD,CAAC,EACnDA,EAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC,GACrC,KAAKtD,IAAe,KAAKE,MAC9B,KAAKF,IACP,KAAKlB,KAAWwE,EAAGhD,EAAG,OAAO,EAE3B,KAAKJ,IACP,KAAKN,IAAW,KAAK,CAAC0D,EAAGhD,EAAG,OAAO,CAAC,GAGxC,KAAK8C,GAAgB0B,CAAI,EAErBD,IACF,KAAKxF,GAASyF,CAAI,EAAI,OACtB,KAAKxF,GAASwF,CAAI,EAAI,OACtB,KAAKnF,GAAM,KAAKmF,CAAI,GAElB,KAAK5F,KAAU,GACjB,KAAKO,GAAQ,KAAKC,GAAQ,EAC1B,KAAKC,GAAM,OAAS,GAEpB,KAAKF,GAAQ,KAAKF,GAAMuF,CAAI,EAE9B,KAAK1F,GAAQ,OAAOkB,CAAC,EACrB,KAAKpB,KACE4F,CACT,CAkBA,IAAIxE,EAAMyE,EAA4C,CAAA,EAAE,CACtD,GAAM,CAAE,eAAA5D,EAAiB,KAAK,eAAgB,OAAA4B,CAAM,EAClDgC,EACIxE,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GAAIC,IAAU,OAAW,CACvB,IAAM+C,EAAI,KAAKhE,GAASiB,CAAK,EAC7B,GACE,KAAKF,GAAmBiD,CAAC,GACzBA,EAAE,uBAAyB,OAE3B,MAAO,GAET,GAAK,KAAKxC,GAASP,CAAK,EASbwC,IACTA,EAAO,IAAM,QACb,KAAKD,GAAWC,EAAQxC,CAAK,OAV7B,QAAIY,GACF,KAAK0B,GAAetC,CAAK,EAEvBwC,IACFA,EAAO,IAAM,MACb,KAAKD,GAAWC,EAAQxC,CAAK,GAExB,QAKAwC,IACTA,EAAO,IAAM,QAEf,MAAO,EACT,CASA,KAAKzC,EAAM0E,EAA8C,CAAA,EAAE,CACzD,GAAM,CAAE,WAAA5D,EAAa,KAAK,UAAU,EAAK4D,EACnCzE,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GACEC,IAAU,QACT,CAACa,GAAc,KAAKN,GAASP,CAAK,EAEnC,OAEF,IAAM+C,EAAI,KAAKhE,GAASiB,CAAK,EAE7B,OAAO,KAAKF,GAAmBiD,CAAC,EAAIA,EAAE,qBAAuBA,CAC/D,CAEA5C,GACEJ,EACAC,EACAC,EACAC,EAAY,CAEZ,IAAM6C,EAAI/C,IAAU,OAAY,OAAY,KAAKjB,GAASiB,CAAK,EAC/D,GAAI,KAAKF,GAAmBiD,CAAC,EAC3B,OAAOA,EAGT,IAAM2B,EAAK,IAAI5H,EACT,CAAE,OAAA6H,CAAM,EAAK1E,EAEnB0E,GAAQ,iBAAiB,QAAS,IAAMD,EAAG,MAAMC,EAAO,MAAM,EAAG,CAC/D,OAAQD,EAAG,OACZ,EAED,IAAME,EAAY,CAChB,OAAQF,EAAG,OACX,QAAAzE,EACA,QAAAC,GAGI2E,EAAK,CACT9B,EACA+B,EAAc,KACG,CACjB,GAAM,CAAE,QAAAC,CAAO,EAAKL,EAAG,OACjBM,EAAc/E,EAAQ,kBAAoB8C,IAAM,OAUtD,GATI9C,EAAQ,SACN8E,GAAW,CAACD,GACd7E,EAAQ,OAAO,aAAe,GAC9BA,EAAQ,OAAO,WAAayE,EAAG,OAAO,OAClCM,IAAa/E,EAAQ,OAAO,kBAAoB,KAEpDA,EAAQ,OAAO,cAAgB,IAG/B8E,GAAW,CAACC,GAAe,CAACF,EAC9B,OAAOG,EAAUP,EAAG,OAAO,MAAM,EAGnC,IAAMQ,EAAKrF,EACX,OAAI,KAAKd,GAASiB,CAAc,IAAMH,IAChCkD,IAAM,OACJmC,EAAG,qBACL,KAAKnG,GAASiB,CAAc,EAAIkF,EAAG,qBAEnC,KAAK7C,GAAQtC,EAAG,OAAO,GAGrBE,EAAQ,SAAQA,EAAQ,OAAO,aAAe,IAClD,KAAK,IAAIF,EAAGgD,EAAG6B,EAAU,OAAO,IAG7B7B,CACT,EAEMoC,EAAMC,IACNnF,EAAQ,SACVA,EAAQ,OAAO,cAAgB,GAC/BA,EAAQ,OAAO,WAAamF,GAEvBH,EAAUG,CAAE,GAGfH,EAAaG,GAA0B,CAC3C,GAAM,CAAE,QAAAL,CAAO,EAAKL,EAAG,OACjBW,EACJN,GAAW9E,EAAQ,uBACfY,EACJwE,GAAqBpF,EAAQ,2BACzBqF,EAAWzE,GAAcZ,EAAQ,yBACjCiF,EAAKrF,EAeX,GAdI,KAAKd,GAASiB,CAAc,IAAMH,IAGxB,CAACyF,GAAYJ,EAAG,uBAAyB,OAEnD,KAAK7C,GAAQtC,EAAG,OAAO,EACbsF,IAKV,KAAKtG,GAASiB,CAAc,EAAIkF,EAAG,uBAGnCrE,EACF,OAAIZ,EAAQ,QAAUiF,EAAG,uBAAyB,SAChDjF,EAAQ,OAAO,cAAgB,IAE1BiF,EAAG,qBACL,GAAIA,EAAG,aAAeA,EAC3B,MAAME,CAEV,EAEMG,EAAQ,CACZC,EACAC,IACE,CACF,IAAMC,EAAM,KAAKjH,KAAesB,EAAGgD,EAAG6B,CAAS,EAC3Cc,GAAOA,aAAe,SACxBA,EAAI,KAAK3C,GAAKyC,EAAIzC,IAAM,OAAY,OAAYA,CAAC,EAAG0C,CAAG,EAKzDf,EAAG,OAAO,iBAAiB,QAAS,IAAK,EAErC,CAACzE,EAAQ,kBACTA,EAAQ,0BAERuF,EAAI,MAAS,EAETvF,EAAQ,yBACVuF,EAAMzC,GAAK8B,EAAG9B,EAAG,EAAI,GAG3B,CAAC,CACH,EAEI9C,EAAQ,SAAQA,EAAQ,OAAO,gBAAkB,IACrD,IAAMJ,EAAI,IAAI,QAAQ0F,CAAK,EAAE,KAAKV,EAAIM,CAAE,EAClCD,EAAyB,OAAO,OAAOrF,EAAG,CAC9C,kBAAmB6E,EACnB,qBAAsB3B,EACtB,WAAY,OACb,EAED,OAAI/C,IAAU,QAEZ,KAAK,IAAID,EAAGmF,EAAI,CAAE,GAAGN,EAAU,QAAS,OAAQ,MAAS,CAAE,EAC3D5E,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,GAE1B,KAAKhB,GAASiB,CAAK,EAAIkF,EAElBA,CACT,CAEApF,GAAmBD,EAAM,CACvB,GAAI,CAAC,KAAKH,GAAiB,MAAO,GAClC,IAAMiG,EAAI9F,EACV,MACE,CAAC,CAAC8F,GACFA,aAAa,SACbA,EAAE,eAAe,sBAAsB,GACvCA,EAAE,6BAA6B7I,CAEnC,CA+GA,MAAM,MACJiD,EACA6F,EAAgD,CAAA,EAAE,CAElD,GAAM,CAEJ,WAAA/E,EAAa,KAAK,WAClB,eAAAF,EAAiB,KAAK,eACtB,mBAAAa,EAAqB,KAAK,mBAE1B,IAAAhB,EAAM,KAAK,IACX,eAAAQ,EAAiB,KAAK,eACtB,KAAArD,EAAO,EACP,gBAAAyD,EAAkB,KAAK,gBACvB,YAAAH,EAAc,KAAK,YAEnB,yBAAAM,EAA2B,KAAK,yBAChC,2BAAAE,EAA6B,KAAK,2BAClC,iBAAAE,EAAmB,KAAK,iBACxB,uBAAAD,EAAyB,KAAK,uBAC9B,QAAAxB,EACA,aAAA2F,EAAe,GACf,OAAArD,EACA,OAAAmC,CAAM,EACJiB,EAEJ,GAAI,CAAC,KAAKlG,GACR,OAAI8C,IAAQA,EAAO,MAAQ,OACpB,KAAK,IAAIzC,EAAG,CACjB,WAAAc,EACA,eAAAF,EACA,mBAAAa,EACA,OAAAgB,EACD,EAGH,IAAMvC,EAAU,CACd,WAAAY,EACA,eAAAF,EACA,mBAAAa,EACA,IAAAhB,EACA,eAAAQ,EACA,KAAArD,EACA,gBAAAyD,EACA,YAAAH,EACA,yBAAAM,EACA,2BAAAE,EACA,uBAAAC,EACA,iBAAAC,EACA,OAAAa,EACA,OAAAmC,GAGE3E,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAC9B,GAAIC,IAAU,OAAW,CACnBwC,IAAQA,EAAO,MAAQ,QAC3B,IAAM3C,EAAI,KAAKM,GAAiBJ,EAAGC,EAAOC,EAASC,CAAO,EAC1D,OAAQL,EAAE,WAAaA,MAClB,CAEL,IAAMkD,EAAI,KAAKhE,GAASiB,CAAK,EAC7B,GAAI,KAAKF,GAAmBiD,CAAC,EAAG,CAC9B,IAAM+C,EACJjF,GAAckC,EAAE,uBAAyB,OAC3C,OAAIP,IACFA,EAAO,MAAQ,WACXsD,IAAOtD,EAAO,cAAgB,KAE7BsD,EAAQ/C,EAAE,qBAAwBA,EAAE,WAAaA,EAK1D,IAAMgD,EAAU,KAAKxF,GAASP,CAAK,EACnC,GAAI,CAAC6F,GAAgB,CAACE,EACpB,OAAIvD,IAAQA,EAAO,MAAQ,OAC3B,KAAKpC,GAAYJ,CAAK,EAClBW,GACF,KAAK2B,GAAetC,CAAK,EAEvBwC,GAAQ,KAAKD,GAAWC,EAAQxC,CAAK,EAClC+C,EAKT,IAAMlD,EAAI,KAAKM,GAAiBJ,EAAGC,EAAOC,EAASC,CAAO,EAEpD8F,EADWnG,EAAE,uBAAyB,QACfgB,EAC7B,OAAI2B,IACFA,EAAO,MAAQuD,EAAU,QAAU,UAC/BC,GAAYD,IAASvD,EAAO,cAAgB,KAE3CwD,EAAWnG,EAAE,qBAAwBA,EAAE,WAAaA,EAE/D,CAoCA,MAAM,WACJE,EACA6F,EAAgD,CAAA,EAAE,CAElD,IAAM7C,EAAI,MAAM,KAAK,MACnBhD,EACA6F,CAI8C,EAEhD,GAAI7C,IAAM,OAAW,MAAM,IAAI,MAAM,4BAA4B,EACjE,OAAOA,CACT,CAqCA,KAAKhD,EAAMkG,EAA8C,CAAA,EAAE,CACzD,IAAM3E,EAAa,KAAK5C,GACxB,GAAI,CAAC4C,EACH,MAAM,IAAI,MAAM,uCAAuC,EAEzD,GAAM,CAAE,QAAApB,EAAS,aAAA2F,EAAc,GAAG5F,CAAO,EAAKgG,EACxClD,EAAI,KAAK,IAAIhD,EAAGE,CAAO,EAC7B,GAAI,CAAC4F,GAAgB9C,IAAM,OAAW,OAAOA,EAC7C,IAAMmD,EAAK5E,EAAWvB,EAAGgD,EAAG,CAC1B,QAAA9C,EACA,QAAAC,EACqC,EACvC,YAAK,IAAIH,EAAGmG,EAAIjG,CAAO,EAChBiG,CACT,CAQA,IAAInG,EAAM0D,EAA4C,CAAA,EAAE,CACtD,GAAM,CACJ,WAAA5C,EAAa,KAAK,WAClB,eAAAF,EAAiB,KAAK,eACtB,mBAAAa,EAAqB,KAAK,mBAC1B,OAAAgB,CAAM,EACJiB,EACEzD,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GAAIC,IAAU,OAAW,CACvB,IAAM0D,EAAQ,KAAK3E,GAASiB,CAAK,EAC3BmG,EAAW,KAAKrG,GAAmB4D,CAAK,EAE9C,OADIlB,GAAQ,KAAKD,GAAWC,EAAQxC,CAAK,EACrC,KAAKO,GAASP,CAAK,GACjBwC,IAAQA,EAAO,IAAM,SAEpB2D,GAQD3D,GACA3B,GACA6C,EAAM,uBAAyB,SAE/BlB,EAAO,cAAgB,IAElB3B,EAAa6C,EAAM,qBAAuB,SAb5ClC,GACH,KAAKa,GAAQtC,EAAG,QAAQ,EAEtByC,GAAU3B,IAAY2B,EAAO,cAAgB,IAC1C3B,EAAa6C,EAAQ,UAY1BlB,IAAQA,EAAO,IAAM,OAMrB2D,EACKzC,EAAM,sBAEf,KAAKtD,GAAYJ,CAAK,EAClBW,GACF,KAAK2B,GAAetC,CAAK,EAEpB0D,SAEAlB,IACTA,EAAO,IAAM,OAEjB,CAEA4D,GAASvG,EAAUtC,EAAQ,CACzB,KAAK0B,GAAM1B,CAAC,EAAIsC,EAChB,KAAKb,GAAMa,CAAC,EAAItC,CAClB,CAEA6C,GAAYJ,EAAY,CASlBA,IAAU,KAAKb,KACba,IAAU,KAAKd,GACjB,KAAKA,GAAQ,KAAKF,GAAMgB,CAAK,EAE7B,KAAKoG,GACH,KAAKnH,GAAMe,CAAK,EAChB,KAAKhB,GAAMgB,CAAK,CAAU,EAG9B,KAAKoG,GAAS,KAAKjH,GAAOa,CAAK,EAC/B,KAAKb,GAAQa,EAEjB,CAOA,OAAOD,EAAI,CACT,OAAO,KAAKsC,GAAQtC,EAAG,QAAQ,CACjC,CAEAsC,GAAQtC,EAAM7C,EAA8B,CAC1C,IAAI0G,EAAU,GACd,GAAI,KAAKjF,KAAU,EAAG,CACpB,IAAMqB,EAAQ,KAAKnB,GAAQ,IAAIkB,CAAC,EAChC,GAAIC,IAAU,OAEZ,GADA4D,EAAU,GACN,KAAKjF,KAAU,EACjB,KAAK0H,GAAOnJ,CAAM,MACb,CACL,KAAK2F,GAAgB7C,CAAK,EAC1B,IAAM+C,EAAI,KAAKhE,GAASiB,CAAK,EAc7B,GAbI,KAAKF,GAAmBiD,CAAC,EAC3BA,EAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC,GACrC,KAAKtD,IAAe,KAAKE,MAC9B,KAAKF,IACP,KAAKlB,KAAWwE,EAAQhD,EAAG7C,CAAM,EAE/B,KAAKyC,IACP,KAAKN,IAAW,KAAK,CAAC0D,EAAQhD,EAAG7C,CAAM,CAAC,GAG5C,KAAK2B,GAAQ,OAAOkB,CAAC,EACrB,KAAKjB,GAASkB,CAAK,EAAI,OACvB,KAAKjB,GAASiB,CAAK,EAAI,OACnBA,IAAU,KAAKb,GACjB,KAAKA,GAAQ,KAAKF,GAAMe,CAAK,UACpBA,IAAU,KAAKd,GACxB,KAAKA,GAAQ,KAAKF,GAAMgB,CAAK,MACxB,CACL,IAAMsG,EAAK,KAAKrH,GAAMe,CAAK,EAC3B,KAAKhB,GAAMsH,CAAE,EAAI,KAAKtH,GAAMgB,CAAK,EACjC,IAAMuG,EAAK,KAAKvH,GAAMgB,CAAK,EAC3B,KAAKf,GAAMsH,CAAE,EAAI,KAAKtH,GAAMe,CAAK,EAEnC,KAAKrB,KACL,KAAKS,GAAM,KAAKY,CAAK,GAI3B,GAAI,KAAKL,IAAoB,KAAKN,IAAW,OAAQ,CACnD,IAAM8E,EAAK,KAAK9E,GACZ+E,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK3F,KAAgB,GAAG4F,CAAI,EAGhC,OAAOR,CACT,CAKA,OAAK,CACH,OAAO,KAAKyC,GAAO,QAAQ,CAC7B,CACAA,GAAOnJ,EAA8B,CACnC,QAAW8C,KAAS,KAAKM,GAAU,CAAE,WAAY,EAAI,CAAE,EAAG,CACxD,IAAMyC,EAAI,KAAKhE,GAASiB,CAAK,EAC7B,GAAI,KAAKF,GAAmBiD,CAAC,EAC3BA,EAAE,kBAAkB,MAAM,IAAI,MAAM,SAAS,CAAC,MACzC,CACL,IAAMhD,EAAI,KAAKjB,GAASkB,CAAK,EACzB,KAAKP,IACP,KAAKlB,KAAWwE,EAAQhD,EAAQ7C,CAAM,EAEpC,KAAKyC,IACP,KAAKN,IAAW,KAAK,CAAC0D,EAAQhD,EAAQ7C,CAAM,CAAC,GAoBnD,GAfA,KAAK2B,GAAQ,MAAK,EAClB,KAAKE,GAAS,KAAK,MAAS,EAC5B,KAAKD,GAAS,KAAK,MAAS,EACxB,KAAKU,IAAS,KAAKD,KACrB,KAAKC,GAAM,KAAK,CAAC,EACjB,KAAKD,GAAQ,KAAK,CAAC,GAEjB,KAAKD,IACP,KAAKA,GAAO,KAAK,CAAC,EAEpB,KAAKJ,GAAQ,EACb,KAAKC,GAAQ,EACb,KAAKC,GAAM,OAAS,EACpB,KAAKR,GAAkB,EACvB,KAAKD,GAAQ,EACT,KAAKgB,IAAoB,KAAKN,GAAW,CAC3C,IAAM8E,EAAK,KAAK9E,GACZ+E,EACJ,KAAQA,EAAOD,GAAI,MAAK,GACtB,KAAK3F,KAAgB,GAAG4F,CAAI,EAGlC,GAvwDF,QAAA,SAAAhG", "names": ["perf", "warned", "PROCESS", "emitWarning", "msg", "type", "code", "fn", "AC", "AS", "_", "warnACPolyfill", "reason", "printACPolyfillWarning", "<PERSON><PERSON><PERSON><PERSON>", "TYPE", "isPosInt", "n", "getUintArray", "max", "ZeroArray", "size", "_<PERSON>ack", "HeapCls", "__privateSet", "_constructing", "s", "__privateGet", "<PERSON><PERSON>", "__privateAdd", "L<PERSON><PERSON><PERSON>", "#max", "#maxSize", "#dispose", "#disposeAfter", "#fetchMethod", "#memoMethod", "#size", "#calculatedSize", "#keyMap", "#keyList", "#valList", "#next", "#prev", "#head", "#tail", "#free", "#disposed", "#sizes", "#starts", "#ttls", "#hasDispose", "#hasFetchMethod", "#hasDisposeAfter", "c", "p", "#isBackgroundFetch", "k", "index", "options", "context", "#backgroundFetch", "#moveToTail", "#indexes", "#rindexes", "#isStale", "ttl", "ttlResolution", "ttlAutopurge", "updateAgeOnGet", "updateAgeOnHas", "allowStale", "dispose", "disposeAfter", "noDisposeOnSet", "noUpdateTTL", "maxSize", "maxEntrySize", "sizeCalculation", "fetch<PERSON><PERSON><PERSON>", "memoMethod", "noDeleteOnFetchRejection", "noDeleteOnStaleGet", "allowStaleOnFetchRejection", "allowStaleOnFetchAbort", "ignoreFetchAbort", "UintArray", "#initializeSizeTracking", "#initializeTTLTracking", "key", "ttls", "starts", "#setItemTTL", "start", "t", "#delete", "#updateItemAge", "#statusTTL", "status", "cachedNow", "getNow", "age", "sizes", "#removeItemSize", "#requireSize", "v", "#addItemSize", "#evict", "_i", "_s", "_st", "_k", "_v", "i", "#isValidIndex", "getOptions", "value", "thisp", "deleted", "entry", "remain", "arr", "setOptions", "oldVal", "oldValue", "dt", "task", "val", "free", "head", "hasOptions", "peekOptions", "ac", "signal", "fetchOpts", "cb", "updateCache", "aborted", "ignoreAbort", "fetchFail", "bf", "eb", "er", "allowStaleAborted", "noDelete", "pcall", "res", "rej", "fmp", "b", "fetchOptions", "forceRefresh", "stale", "isStale", "staleVal", "memoOptions", "vv", "fetching", "#connect", "#clear", "pi", "ni"]}