!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/menu-surface",[],t):"object"==typeof exports?exports["menu-surface"]=t():(e.mdc=e.mdc||{},e.mdc["menu-surface"]=t())}(this,function(){return n={},i.m=r={0:function(e,t,r){"use strict";(function(e){}).call(this,r(20))},1:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapResourceUrl=t.isResourceUrl=t.createResourceUrl=t.TrustedResourceUrl=void 0,r(0);var i=r(4),o=r(9),a=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(e,t){this.privateDoNotAccessOrElseWrappedResourceUrl=e}var s=window.TrustedScriptURL;t.TrustedResourceUrl=null!=s?s:a,t.createResourceUrl=function(e){var t,r=e,n=null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScriptURL(r);return null!=n?n:new a(r,i.secretToken)},t.isResourceUrl=function(e){return e instanceof t.TrustedResourceUrl},t.unwrapResourceUrl=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isScriptURL(e))return e;if(e instanceof a)return e.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},10:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyle=t.isStyle=t.createStyle=t.SafeStyle=void 0,r(0);function o(){}var a=r(4);t.SafeStyle=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},u);function u(e,t){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=e,r}t.createStyle=function(e){return new c(e,a.secretToken)},t.isStyle=function(e){return e instanceof c},t.unwrapStyle=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},11:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AttributePolicyAction=t.SanitizerTable=void 0;var n,i,o=(a.prototype.isAllowedElement=function(e){return"form"!==e.toLowerCase()&&(this.allowedElements.has(e)||this.elementPolicies.has(e))},a.prototype.getAttributePolicy=function(e,t){var r=this.elementPolicies.get(t);return(null==r?void 0:r.has(e))?r.get(e):this.allowedGlobalAttributes.has(e)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(e)||{policyAction:n.DROP}},a);function a(e,t,r,n){this.allowedElements=e,this.elementPolicies=t,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}t.SanitizerTable=o,(i=n=t.AttributePolicyAction||(t.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},12:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.isStyleSheet=t.createStyleSheet=t.SafeStyleSheet=void 0,r(0);function o(){}var a=r(4);t.SafeStyleSheet=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},u);function u(e,t){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=e,r}t.createStyleSheet=function(e){return new c(e,a.secretToken)},t.isStyleSheet=function(e){return e instanceof c},t.unwrapStyleSheet=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},13:function(e,t,r){"use strict";var i=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__spreadArray||function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCComponent=void 0;var s=r(17),c=r(18),n=r(7);var u,l,f=(p.attachTo=function(e){return new p(e,new n.MDCFoundation({}))},p.prototype.initialize=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},p.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},p.prototype.initialSyncWithDOM=function(){},p.prototype.destroy=function(){this.foundation.destroy()},p.prototype.listen=function(e,t,r){this.root.addEventListener(e,t,r)},p.prototype.unlisten=function(e,t,r){this.root.removeEventListener(e,t,r)},p.prototype.emit=function(e,t,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(e,{bubbles:r,detail:t}):(n=document.createEvent("CustomEvent")).initCustomEvent(e,r,!1,t),this.root.dispatchEvent(n)},p.prototype.safeSetAttribute=function(e,t,r){if("tabindex"===t.toLowerCase())e.tabIndex=Number(r);else if(0===t.indexOf("data-")){var n=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})}(t.replace(/^data-/,""));e.dataset[n]=r}else c.safeElement.setPrefixedAttribute([s.safeAttrPrefix(u=u||i(["aria-"],["aria-"])),s.safeAttrPrefix(l=l||i(["role"],["role"]))],e,t,r)},p);function p(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=e,this.initialize.apply(this,a([],o(r))),this.foundation=void 0===t?this.getDefaultFoundation():t,this.foundation.init(),this.initialSyncWithDOM()}t.MDCComponent=f,t.default=f},14:function(e,t,r){"use strict";var d=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},f=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.HtmlSanitizerImpl=void 0,r(0);var n=r(2),i=r(4),h=r(3),c=r(23),y=r(24),o=r(16),m=r(11),a=(s.prototype.sanitizeAssertUnchanged=function(e){this.changes=[];var t=this.sanitize(e);if(0===this.changes.length)return t;throw new Error("")},s.prototype.sanitize=function(e){var t=document.createElement("span");t.appendChild(this.sanitizeToFragment(e));var r=(new XMLSerializer).serializeToString(t);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},s.prototype.sanitizeToFragment=function(e){for(var t=this,r=(0,c.createInertFragment)(e),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(e){return t.nodeFilter(e)},!1),i=n.nextNode(),o=document.createDocumentFragment(),a=o;null!==i;){var s=void 0;if((0,y.isText)(i))s=this.sanitizeTextNode(i);else{if(!(0,y.isElement)(i))throw new Error("Node is not of type text or element");s=this.sanitizeElementNode(i)}if(a.appendChild(s),i=n.firstChild())a=s;else for(;!(i=n.nextSibling())&&(i=n.parentNode());)a=a.parentNode}return o},s.prototype.sanitizeTextNode=function(e){return document.createTextNode(e.data)},s.prototype.sanitizeElementNode=function(e){var t,r,n=(0,y.getNodeName)(e),i=document.createElement(n),o=e.attributes;try{for(var a=d(o),s=a.next();!s.done;s=a.next()){var c=s.value,u=c.name,l=c.value,f=this.sanitizerTable.getAttributePolicy(u,n);if(this.satisfiesAllConditions(f.conditions,o))switch(f.policyAction){case m.AttributePolicyAction.KEEP:i.setAttribute(u,l);break;case m.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var p=(0,h.restrictivelySanitizeUrl)(l);p!==l&&this.recordChange("Url in attribute ".concat(u,' was modified during sanitization. Original url:"').concat(l,'" was sanitized to: "').concat(p,'"')),i.setAttribute(u,p);break;case m.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(u,l.toLowerCase());break;case m.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(u,l);break;case m.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(u," was dropped"));break;default:v(f.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(u,"."))}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return i},s.prototype.nodeFilter=function(e){if((0,y.isText)(e))return NodeFilter.FILTER_ACCEPT;if(!(0,y.isElement)(e))return NodeFilter.FILTER_REJECT;var t=(0,y.getNodeName)(e);return null===t?(this.recordChange("Node name was null for node: ".concat(e)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(t)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(t," was dropped")),NodeFilter.FILTER_REJECT)},s.prototype.recordChange=function(e){0===this.changes.length&&this.changes.push("")},s.prototype.satisfiesAllConditions=function(e,t){var r,n,i;if(!e)return!0;try{for(var o=d(e),a=o.next();!a.done;a=o.next()){var s=f(a.value,2),c=s[0],u=s[1],l=null===(i=t.getNamedItem(c))||void 0===i?void 0:i.value;if(l&&!u.has(l))return!1}}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0},s);function s(e,t){this.sanitizerTable=e,this.changes=[],(0,i.ensureTokenIsValid)(t)}t.HtmlSanitizerImpl=a;var u=function(){return new a(o.defaultSanitizerTable,i.secretToken)}();function v(e,t){throw void 0===t&&(t="unexpected value ".concat(e,"!")),new Error(t)}t.sanitizeHtml=function(e){return u.sanitize(e)},t.sanitizeHtmlAssertUnchanged=function(e){return u.sanitizeAssertUnchanged(e)},t.sanitizeHtmlToFragment=function(e){return u.sanitizeToFragment(e)}},15:function(e,t,r){"use strict";var i=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.setPrefixedAttribute=t.buildPrefixedAttributeSetter=t.insertAdjacentHtml=t.setCssText=t.setOuterHtml=t.setInnerHtml=void 0;var a=r(8),s=r(2),n=r(10);function c(e,t,r,n){if(0===e.length)throw new Error("No prefixes are provided");var i=e.map(function(e){return(0,a.unwrapAttributePrefix)(e)}),o=r.toLowerCase();if(i.every(function(e){return 0!==o.indexOf(e)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));t.setAttribute(r,n)}function u(e){if("script"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}t.setInnerHtml=function(e,t){!function(e){return void 0!==e.tagName}(e)||u(e),e.innerHTML=(0,s.unwrapHtml)(t)},t.setOuterHtml=function(e,t){var r=e.parentElement;null!==r&&u(r),e.outerHTML=(0,s.unwrapHtml)(t)},t.setCssText=function(e,t){e.style.cssText=(0,n.unwrapStyle)(t)},t.insertAdjacentHtml=function(e,t,r){var n="beforebegin"===t||"afterend"===t?e.parentElement:e;null!==n&&u(n),e.insertAdjacentHTML(t,(0,s.unwrapHtml)(r))},t.buildPrefixedAttributeSetter=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=o([e],i(t),!1);return function(e,t,r){c(n,e,t,r)}},t.setPrefixedAttribute=c},16:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSanitizerTable=void 0;var n=r(11);t.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},17:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.SafeStyleSheet=t.isStyleSheet=t.unwrapStyle=t.SafeStyle=t.isStyle=t.unwrapScript=t.SafeScript=t.isScript=t.EMPTY_SCRIPT=t.unwrapResourceUrl=t.TrustedResourceUrl=t.isResourceUrl=t.unwrapHtml=t.SafeHtml=t.isHtml=t.EMPTY_HTML=t.unwrapAttributePrefix=t.SafeAttributePrefix=t.safeStyleSheet=t.concatStyleSheets=t.safeStyle=t.concatStyles=t.scriptFromJson=t.safeScriptWithArgs=t.safeScript=t.concatScripts=t.trustedResourceUrl=t.replaceFragment=t.blobUrlFromScript=t.appendParams=t.HtmlSanitizerBuilder=t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.htmlEscape=t.createScriptSrc=t.createScript=t.concatHtmls=t.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(t,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var i=r(22);Object.defineProperty(t,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(t,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(t,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(t,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var o=r(14);Object.defineProperty(t,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(t,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(t,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(t,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var s=r(26);Object.defineProperty(t,"appendParams",{enumerable:!0,get:function(){return s.appendParams}}),Object.defineProperty(t,"blobUrlFromScript",{enumerable:!0,get:function(){return s.blobUrlFromScript}}),Object.defineProperty(t,"replaceFragment",{enumerable:!0,get:function(){return s.replaceFragment}}),Object.defineProperty(t,"trustedResourceUrl",{enumerable:!0,get:function(){return s.trustedResourceUrl}});var c=r(27);Object.defineProperty(t,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(t,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(t,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(t,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var u=r(28);Object.defineProperty(t,"concatStyles",{enumerable:!0,get:function(){return u.concatStyles}}),Object.defineProperty(t,"safeStyle",{enumerable:!0,get:function(){return u.safeStyle}});var l=r(29);Object.defineProperty(t,"concatStyleSheets",{enumerable:!0,get:function(){return l.concatStyleSheets}}),Object.defineProperty(t,"safeStyleSheet",{enumerable:!0,get:function(){return l.safeStyleSheet}});var f=r(8);Object.defineProperty(t,"SafeAttributePrefix",{enumerable:!0,get:function(){return f.SafeAttributePrefix}}),Object.defineProperty(t,"unwrapAttributePrefix",{enumerable:!0,get:function(){return f.unwrapAttributePrefix}});var p=r(2);Object.defineProperty(t,"EMPTY_HTML",{enumerable:!0,get:function(){return p.EMPTY_HTML}}),Object.defineProperty(t,"isHtml",{enumerable:!0,get:function(){return p.isHtml}}),Object.defineProperty(t,"SafeHtml",{enumerable:!0,get:function(){return p.SafeHtml}}),Object.defineProperty(t,"unwrapHtml",{enumerable:!0,get:function(){return p.unwrapHtml}});var d=r(1);Object.defineProperty(t,"isResourceUrl",{enumerable:!0,get:function(){return d.isResourceUrl}}),Object.defineProperty(t,"TrustedResourceUrl",{enumerable:!0,get:function(){return d.TrustedResourceUrl}}),Object.defineProperty(t,"unwrapResourceUrl",{enumerable:!0,get:function(){return d.unwrapResourceUrl}});var h=r(5);Object.defineProperty(t,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return h.EMPTY_SCRIPT}}),Object.defineProperty(t,"isScript",{enumerable:!0,get:function(){return h.isScript}}),Object.defineProperty(t,"SafeScript",{enumerable:!0,get:function(){return h.SafeScript}}),Object.defineProperty(t,"unwrapScript",{enumerable:!0,get:function(){return h.unwrapScript}});var y=r(10);Object.defineProperty(t,"isStyle",{enumerable:!0,get:function(){return y.isStyle}}),Object.defineProperty(t,"SafeStyle",{enumerable:!0,get:function(){return y.SafeStyle}}),Object.defineProperty(t,"unwrapStyle",{enumerable:!0,get:function(){return y.unwrapStyle}});var m=r(12);Object.defineProperty(t,"isStyleSheet",{enumerable:!0,get:function(){return m.isStyleSheet}}),Object.defineProperty(t,"SafeStyleSheet",{enumerable:!0,get:function(){return m.SafeStyleSheet}}),Object.defineProperty(t,"unwrapStyleSheet",{enumerable:!0,get:function(){return m.unwrapStyleSheet}})},18:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&("get"in i?t.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.safeWorker=t.safeWindow=t.safeServiceWorkerContainer=t.safeRange=t.safeLocation=t.safeGlobal=t.safeDomParser=t.safeDocument=t.safeStyleEl=t.safeScriptEl=t.safeObjectEl=t.safeLinkEl=t.safeInputEl=t.safeIframeEl=t.safeFormEl=t.safeEmbedEl=t.safeElement=t.safeButtonEl=t.safeAreaEl=t.safeAnchorEl=void 0,t.safeAnchorEl=o(r(30)),t.safeAreaEl=o(r(31)),t.safeButtonEl=o(r(32)),t.safeElement=o(r(15)),t.safeEmbedEl=o(r(33)),t.safeFormEl=o(r(34)),t.safeIframeEl=o(r(35)),t.safeInputEl=o(r(36)),t.safeLinkEl=o(r(37)),t.safeObjectEl=o(r(38)),t.safeScriptEl=o(r(39)),t.safeStyleEl=o(r(40)),t.safeDocument=o(r(41)),t.safeDomParser=o(r(42)),t.safeGlobal=o(r(43)),t.safeLocation=o(r(44)),t.safeRange=o(r(45)),t.safeServiceWorkerContainer=o(r(46)),t.safeWindow=o(r(47)),t.safeWorker=o(r(48))},19:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);t.safeAttrPrefix=function(e){var t=e[0].toLowerCase();return(0,n.createAttributePrefix)(t)}},2:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapHtml=t.isHtml=t.EMPTY_HTML=t.createHtml=t.SafeHtml=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedHtml=e}function s(e,t){return null!=t?t:new o(e,n.secretToken)}var c=window.TrustedHTML;t.SafeHtml=null!=c?c:o,t.createHtml=function(e){var t,r=e;return s(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createHTML(r))},t.EMPTY_HTML=function(){var e;return s("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyHTML)}(),t.isHtml=function(e){return e instanceof t.SafeHtml},t.unwrapHtml=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isHTML(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},20:function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var c,u=[],l=!1,f=-1;function p(){l&&c&&(l=!1,c.length?u=c.concat(u):f=-1,u.length&&d())}function d(){if(!l){var e=s(p);l=!0;for(var t=u.length;t;){for(c=u,u=[];++f<t;)c&&c[f].run();f=-1,t=u.length}c=null,l=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function y(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];u.push(new h(e,t)),1!==u.length||l||s(d)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=y,i.addListener=y,i.once=y,i.off=y,i.removeListener=y,i.removeAllListeners=y,i.emit=y,i.prependListener=y,i.prependOnceListener=y,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},21:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SECURITY_SENSITIVE_ATTRIBUTES=void 0,t.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},22:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatHtmls=t.createScriptSrc=t.createScript=t.htmlEscape=void 0;var o=r(2),a=r(1),i=r(5);function s(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}t.htmlEscape=function(e,t){void 0===t&&(t={});var r=s(e);return t.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),t.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),t.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(r)},t.createScript=function(e,t){void 0===t&&(t={});var r=(0,i.unwrapScript)(e).toString(),n="<script";return t.id&&(n+=' id="'.concat(s(t.id),'"')),t.nonce&&(n+=' nonce="'.concat(s(t.nonce),'"')),t.type&&(n+=' type="'.concat(s(t.type),'"')),n+=">".concat(r,"<\/script>"),(0,o.createHtml)(n)},t.createScriptSrc=function(e,t,r){var n=(0,a.unwrapResourceUrl)(e).toString(),i='<script src="'.concat(s(n),'"');return t&&(i+=" async"),r&&(i+=' nonce="'.concat(s(r),'"')),i+="><\/script>",(0,o.createHtml)(i)},t.concatHtmls=function(e){return(0,o.createHtml)(e.map(o.unwrapHtml).join(""))}},23:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInertFragment=void 0;var n=r(15),i=r(2);t.createInertFragment=function(e){var t=document.createElement("template"),r=(0,i.createHtml)(e);return(0,n.setInnerHtml)(t,r),t.content}},24:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElement=t.isText=t.getNodeName=void 0,t.getNodeName=function(e){var t=e.nodeName;return"string"==typeof t?t:"FORM"},t.isText=function(e){return e.nodeType===Node.TEXT_NODE},t.isElement=function(e){var t=e.nodeType;return t===Node.ELEMENT_NODE||"number"!=typeof t}},25:function(e,t,r){"use strict";var O=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},w=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlSanitizerBuilder=void 0;var n=r(4),i=r(14),o=r(16),_=r(11),a=(s.prototype.onlyAllowElements=function(e){var t,r,n=new Set,i=new Map;try{for(var o=O(e),a=o.next();!a.done;a=o.next()){var s=a.value;if(s=s.toUpperCase(),!this.sanitizerTable.isAllowedElement(s))throw new Error("Element: ".concat(s,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(s);void 0!==c?i.set(s,c):n.add(s)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return this.sanitizerTable=new _.SanitizerTable(n,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},s.prototype.onlyAllowAttributes=function(e){var t,r,n,i,o,a,s=new Set,c=new Map,u=new Map;try{for(var l=O(e),f=l.next();!f.done;f=l.next()){var p=f.value;this.sanitizerTable.allowedGlobalAttributes.has(p)&&s.add(p),this.sanitizerTable.globalAttributePolicies.has(p)&&c.set(p,this.sanitizerTable.globalAttributePolicies.get(p))}}catch(e){t={error:e}}finally{try{f&&!f.done&&(r=l.return)&&r.call(l)}finally{if(t)throw t.error}}try{for(var d=O(this.sanitizerTable.elementPolicies.entries()),h=d.next();!h.done;h=d.next()){var y=w(h.value,2),m=y[0],v=y[1],b=new Map;try{for(var S=(o=void 0,O(v.entries())),g=S.next();!g.done;g=S.next()){var E=w(g.value,2),T=(p=E[0],E[1]);e.has(p)&&b.set(p,T)}}catch(e){o={error:e}}finally{try{g&&!g.done&&(a=S.return)&&a.call(S)}finally{if(o)throw o.error}}u.set(m,b)}}catch(e){n={error:e}}finally{try{h&&!h.done&&(i=d.return)&&i.call(d)}finally{if(n)throw n.error}}return this.sanitizerTable=new _.SanitizerTable(this.sanitizerTable.allowedElements,u,s,c),this},s.prototype.allowDataAttributes=function(e){var t,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=O(e),o=i.next();!o.done;o=i.next()){var a=o.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));n.add(a)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return this.sanitizerTable=new _.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},s.prototype.allowStyleAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("style",{policyAction:_.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new _.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},s.prototype.allowClassAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("class",{policyAction:_.AttributePolicyAction.KEEP}),this.sanitizerTable=new _.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},s.prototype.allowIdAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("id",{policyAction:_.AttributePolicyAction.KEEP}),this.sanitizerTable=new _.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},s.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},s);function s(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}t.HtmlSanitizerBuilder=a},250:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(251),t),i(r(84),t),i(r(58),t),i(r(67),t),i(r(252),t)},251:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},252:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},26:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.blobUrlFromScript=t.replaceFragment=t.appendParams=t.trustedResourceUrl=void 0,r(0);var s=r(1),n=r(5);r(6);t.trustedResourceUrl=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(0===t.length)return(0,s.createResourceUrl)(e[0]);e[0].toLowerCase();for(var n=[e[0]],i=0;i<t.length;i++)n.push(encodeURIComponent(t[i])),n.push(e[i+1]);return(0,s.createResourceUrl)(n.join(""))},t.appendParams=function(e,t){var o=(0,s.unwrapResourceUrl)(e).toString();if(/#/.test(o)){throw new Error("")}var a=/\?/.test(o)?"&":"?";return t.forEach(function(e,t){for(var r=e instanceof Array?e:[e],n=0;n<r.length;n++){var i=r[n];null!=i&&(o+=a+encodeURIComponent(t)+"="+encodeURIComponent(String(i)),a="&")}}),(0,s.createResourceUrl)(o)};var i=/[^#]*/;t.replaceFragment=function(e,t){var r=(0,s.unwrapResourceUrl)(e).toString();return(0,s.createResourceUrl)(i.exec(r)[0]+"#"+t)},t.blobUrlFromScript=function(e){var t=(0,n.unwrapScript)(e).toString(),r=new Blob([t],{type:"text/javascript"});return(0,s.createResourceUrl)(URL.createObjectURL(r))}},27:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeScriptWithArgs=t.scriptFromJson=t.concatScripts=t.safeScript=void 0,r(0);var i=r(5);r(6);function o(e){return(0,i.createScript)(JSON.stringify(e).replace(/</g,"\\x3c"))}t.safeScript=function(e){return(0,i.createScript)(e[0])},t.concatScripts=function(e){return(0,i.createScript)(e.map(i.unwrapScript).join(""))},t.scriptFromJson=o,t.safeScriptWithArgs=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.map(function(e){return o(e).toString()});return(0,i.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},28:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyles=t.safeStyle=void 0,r(0);r(6);var n=r(10);t.safeStyle=function(e){var t=e[0];return(0,n.createStyle)(t)},t.concatStyles=function(e){return(0,n.createStyle)(e.map(n.unwrapStyle).join(""))}},29:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyleSheets=t.safeStyleSheet=void 0,r(0);r(6);var n=r(12);t.safeStyleSheet=function(e){var t=e[0];return(0,n.createStyleSheet)(t)},t.concatStyleSheets=function(e){return(0,n.createStyleSheet)(e.map(n.unwrapStyleSheet).join(""))}},3:function(e,t,r){"use strict";function n(e){var t;try{t=new URL(e)}catch(e){return"https:"}return t.protocol}Object.defineProperty(t,"__esModule",{value:!0}),t.restrictivelySanitizeUrl=t.unwrapUrlOrSanitize=t.sanitizeJavascriptUrl=void 0,r(0);var i=["data:","http:","https:","mailto:","ftp:"];function o(e){if("javascript:"!==n(e))return e}t.sanitizeJavascriptUrl=o,t.unwrapUrlOrSanitize=function(e){return o(e)},t.restrictivelySanitizeUrl=function(e){var t=n(e);return void 0!==t&&-1!==i.indexOf(t.toLowerCase())?e:"about:invalid#zClosurez"}},30:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},31:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},32:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},33:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=void 0;var n=r(1);t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t)}},34:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setAction=void 0;var n=r(3);t.setAction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.action=r)}},35:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrcdoc=t.setSrc=void 0;var n=r(2),i=r(1);t.setSrc=function(e,t){e.src=(0,i.unwrapResourceUrl)(t).toString()},t.setSrcdoc=function(e,t){e.srcdoc=(0,n.unwrapHtml)(t)}},36:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},37:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHrefAndRel=void 0;var i=r(3),o=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];t.setHrefAndRel=function(e,t,r){if(t instanceof o.TrustedResourceUrl)e.href=(0,o.unwrapResourceUrl)(t).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,i.unwrapUrlOrSanitize)(t);if(void 0===n)return;e.href=n}e.rel=r}},38:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setData=void 0;var n=r(1);t.setData=function(e,t){e.data=(0,n.unwrapResourceUrl)(t)}},39:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=t.setTextContent=void 0;var n=r(1),i=r(5);function o(e){var t=function(e){var t,r=e.document,n=null===(t=r.querySelector)||void 0===t?void 0:t.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(e.ownerDocument&&e.ownerDocument.defaultView||window);t&&e.setAttribute("nonce",t)}t.setTextContent=function(e,t){e.textContent=(0,i.unwrapScript)(t),o(e)},t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t),o(e)}},4:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureTokenIsValid=t.secretToken=void 0,t.secretToken={},t.ensureTokenIsValid=function(e){if(e!==t.secretToken)throw new Error("Bad secret")}},40:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setTextContent=void 0;var n=r(12);t.setTextContent=function(e,t){e.textContent=(0,n.unwrapStyleSheet)(t)}},41:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execCommandInsertHtml=t.execCommand=t.write=void 0;var o=r(2);t.write=function(e,t){e.write((0,o.unwrapHtml)(t))},t.execCommand=function(e,t,r){var n=String(t),i=r;return"inserthtml"===n.toLowerCase()&&(i=(0,o.unwrapHtml)(r)),e.execCommand(n,!1,i)},t.execCommandInsertHtml=function(e,t){return e.execCommand("insertHTML",!1,(0,o.unwrapHtml)(t))}},42:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseFromString=t.parseHtml=void 0;var n=r(2);function i(e,t,r){return e.parseFromString((0,n.unwrapHtml)(t),r)}t.parseHtml=function(e,t){return i(e,t,"text/html")},t.parseFromString=i},43:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.globalEval=void 0;var i=r(5);t.globalEval=function(e,t){var r=(0,i.unwrapScript)(t),n=e.eval(r);return n===r&&(n=e.eval(r.toString())),n}},44:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assign=t.replace=t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)},t.replace=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.replace(r)},t.assign=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.assign(r)}},45:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createContextualFragment=void 0;var n=r(2);t.createContextualFragment=function(e,t){return e.createContextualFragment((0,n.unwrapHtml)(t))}},46:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.register=void 0;var n=r(1);t.register=function(e,t,r){return e.register((0,n.unwrapResourceUrl)(t),r)}},47:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.open=void 0;var o=r(3);t.open=function(e,t,r,n){var i=(0,o.unwrapUrlOrSanitize)(t);return void 0!==i?e.open(i,r,n):null}},48:function(e,t,r){"use strict";var n=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.importScripts=t.createShared=t.create=void 0;var o=r(1);t.create=function(e,t){return new Worker((0,o.unwrapResourceUrl)(e),t)},t.createShared=function(e,t){return new SharedWorker((0,o.unwrapResourceUrl)(e),t)},t.importScripts=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];e.importScripts.apply(e,i([],n(t.map(function(e){return(0,o.unwrapResourceUrl)(e)})),!1))}},5:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapScript=t.isScript=t.EMPTY_SCRIPT=t.createScript=t.SafeScript=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedScript=e}function s(e,t){return null!=t?t:new o(e,n.secretToken)}var c=window.TrustedScript;t.SafeScript=null!=c?c:o,t.createScript=function(e){var t,r=e;return s(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScript(r))},t.EMPTY_SCRIPT=function(){var e;return s("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyScript)}(),t.isScript=function(e){return e instanceof t.SafeScript},t.unwrapScript=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isScript(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},57:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getCorrectEventName=t.getCorrectPropertyName=void 0;var a={animation:{prefixed:"-webkit-animation",standard:"animation"},transform:{prefixed:"-webkit-transform",standard:"transform"},transition:{prefixed:"-webkit-transition",standard:"transition"}},s={animationend:{cssProperty:"animation",prefixed:"webkitAnimationEnd",standard:"animationend"},animationiteration:{cssProperty:"animation",prefixed:"webkitAnimationIteration",standard:"animationiteration"},animationstart:{cssProperty:"animation",prefixed:"webkitAnimationStart",standard:"animationstart"},transitionend:{cssProperty:"transition",prefixed:"webkitTransitionEnd",standard:"transitionend"}};function c(e){return Boolean(e.document)&&"function"==typeof e.document.createElement}t.getCorrectPropertyName=function(e,t){if(c(e)&&t in a){var r=e.document.createElement("div"),n=a[t],i=n.standard,o=n.prefixed;return i in r.style?i:o}return t},t.getCorrectEventName=function(e,t){if(c(e)&&t in s){var r=e.document.createElement("div"),n=s[t],i=n.standard,o=n.prefixed;return n.cssProperty in r.style?i:o}return t}},58:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Corner=t.CornerBit=t.numbers=t.strings=t.cssClasses=void 0;t.cssClasses={ANCHOR:"mdc-menu-surface--anchor",ANIMATING_CLOSED:"mdc-menu-surface--animating-closed",ANIMATING_OPEN:"mdc-menu-surface--animating-open",FIXED:"mdc-menu-surface--fixed",IS_OPEN_BELOW:"mdc-menu-surface--is-open-below",OPEN:"mdc-menu-surface--open",ROOT:"mdc-menu-surface"};var n={CLOSED_EVENT:"MDCMenuSurface:closed",CLOSING_EVENT:"MDCMenuSurface:closing",OPENED_EVENT:"MDCMenuSurface:opened",OPENING_EVENT:"MDCMenuSurface:opening",FOCUSABLE_ELEMENTS:["button:not(:disabled)",'[href]:not([aria-disabled="true"])',"input:not(:disabled)","select:not(:disabled)","textarea:not(:disabled)",'[tabindex]:not([tabindex="-1"]):not([aria-disabled="true"])'].join(", ")};t.strings=n;var i,o,a,s;t.numbers={TRANSITION_OPEN_DURATION:120,TRANSITION_CLOSE_DURATION:75,MARGIN_TO_EDGE:32,ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO:.67,TOUCH_EVENT_WAIT_MS:30},(o=i=i||{})[o.BOTTOM=1]="BOTTOM",o[o.CENTER=2]="CENTER",o[o.RIGHT=4]="RIGHT",o[o.FLIP_RTL=8]="FLIP_RTL",t.CornerBit=i,(s=a=a||{})[s.TOP_LEFT=0]="TOP_LEFT",s[s.TOP_RIGHT=4]="TOP_RIGHT",s[s.BOTTOM_LEFT=1]="BOTTOM_LEFT",s[s.BOTTOM_RIGHT=5]="BOTTOM_RIGHT",s[s.TOP_START=8]="TOP_START",s[s.TOP_END=12]="TOP_END",s[s.BOTTOM_START=9]="BOTTOM_START",s[s.BOTTOM_END=13]="BOTTOM_END",t.Corner=a},6:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertIsTemplateObject=void 0,t.assertIsTemplateObject=function(e,t,r){if(!Array.isArray(e)||!Array.isArray(e.raw)||!t&&1!==e.length)throw new TypeError(r)}},67:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},d=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCMenuSurfaceFoundation=void 0;var a,s=r(7),v=r(58),c=(a=s.MDCFoundation,i(b,a),Object.defineProperty(b,"cssClasses",{get:function(){return v.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(b,"strings",{get:function(){return v.strings},enumerable:!1,configurable:!0}),Object.defineProperty(b,"numbers",{get:function(){return v.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(b,"Corner",{get:function(){return v.Corner},enumerable:!1,configurable:!0}),Object.defineProperty(b,"defaultAdapter",{get:function(){return{addClass:function(){},removeClass:function(){},hasClass:function(){return!1},hasAnchor:function(){return!1},isElementInContainer:function(){return!1},isFocused:function(){return!1},isRtl:function(){return!1},getInnerDimensions:function(){return{height:0,width:0}},getAnchorDimensions:function(){return null},getViewportDimensions:function(){return{height:0,width:0}},getBodyDimensions:function(){return{height:0,width:0}},getWindowScroll:function(){return{x:0,y:0}},setPosition:function(){},setMaxHeight:function(){},setTransformOrigin:function(){},saveFocus:function(){},restoreFocus:function(){},notifyClose:function(){},notifyClosing:function(){},notifyOpen:function(){},notifyOpening:function(){},registerWindowEventHandler:function(){},deregisterWindowEventHandler:function(){}}},enumerable:!1,configurable:!0}),b.prototype.init=function(){var e=b.cssClasses,t=e.ROOT,r=e.OPEN;if(!this.adapter.hasClass(t))throw new Error(t+" class required in root element.");this.adapter.hasClass(r)&&(this.isSurfaceOpen=!0),this.resizeListener=this.handleResize.bind(this),this.adapter.registerWindowEventHandler("resize",this.resizeListener)},b.prototype.destroy=function(){clearTimeout(this.openAnimationEndTimerId),clearTimeout(this.closeAnimationEndTimerId),cancelAnimationFrame(this.animationRequestId),this.adapter.deregisterWindowEventHandler("resize",this.resizeListener)},b.prototype.setAnchorCorner=function(e){this.anchorCorner=e},b.prototype.flipCornerHorizontally=function(){this.originCorner=this.originCorner^v.CornerBit.RIGHT},b.prototype.setAnchorMargin=function(e){this.anchorMargin.top=e.top||0,this.anchorMargin.right=e.right||0,this.anchorMargin.bottom=e.bottom||0,this.anchorMargin.left=e.left||0},b.prototype.setIsHoisted=function(e){this.isHoistedElement=e},b.prototype.setFixedPosition=function(e){this.isFixedPosition=e},b.prototype.isFixed=function(){return this.isFixedPosition},b.prototype.setAbsolutePosition=function(e,t){this.position.x=this.isFinite(e)?e:0,this.position.y=this.isFinite(t)?t:0},b.prototype.setIsHorizontallyCenteredOnViewport=function(e){this.isHorizontallyCenteredOnViewport=e},b.prototype.setQuickOpen=function(e){this.isQuickOpen=e},b.prototype.setMaxHeight=function(e){this.maxHeight=e},b.prototype.setOpenBottomBias=function(e){this.openBottomBias=e},b.prototype.isOpen=function(){return this.isSurfaceOpen},b.prototype.open=function(){var e=this;this.isSurfaceOpen||(this.adapter.notifyOpening(),this.adapter.saveFocus(),this.isQuickOpen?(this.isSurfaceOpen=!0,this.adapter.addClass(b.cssClasses.OPEN),this.dimensions=this.adapter.getInnerDimensions(),this.autoposition(),this.adapter.notifyOpen()):(this.adapter.addClass(b.cssClasses.ANIMATING_OPEN),this.animationRequestId=requestAnimationFrame(function(){e.dimensions=e.adapter.getInnerDimensions(),e.autoposition(),e.adapter.addClass(b.cssClasses.OPEN),e.openAnimationEndTimerId=setTimeout(function(){e.openAnimationEndTimerId=0,e.adapter.removeClass(b.cssClasses.ANIMATING_OPEN),e.adapter.notifyOpen()},v.numbers.TRANSITION_OPEN_DURATION)}),this.isSurfaceOpen=!0),this.adapter.registerWindowEventHandler("resize",this.resizeListener))},b.prototype.close=function(e){var t=this;if(void 0===e&&(e=!1),this.isSurfaceOpen){if(this.adapter.notifyClosing(),this.adapter.deregisterWindowEventHandler("resize",this.resizeListener),this.isQuickOpen)return this.isSurfaceOpen=!1,e||this.maybeRestoreFocus(),this.adapter.removeClass(b.cssClasses.OPEN),this.adapter.removeClass(b.cssClasses.IS_OPEN_BELOW),void this.adapter.notifyClose();this.adapter.addClass(b.cssClasses.ANIMATING_CLOSED),requestAnimationFrame(function(){t.adapter.removeClass(b.cssClasses.OPEN),t.adapter.removeClass(b.cssClasses.IS_OPEN_BELOW),t.closeAnimationEndTimerId=setTimeout(function(){t.closeAnimationEndTimerId=0,t.adapter.removeClass(b.cssClasses.ANIMATING_CLOSED),t.adapter.notifyClose()},v.numbers.TRANSITION_CLOSE_DURATION)}),this.isSurfaceOpen=!1,e||this.maybeRestoreFocus()}},b.prototype.handleBodyClick=function(e){var t=e.target;this.adapter.isElementInContainer(t)||this.close()},b.prototype.handleKeydown=function(e){var t=e.keyCode;"Escape"!==e.key&&27!==t||this.close()},b.prototype.handleResize=function(){this.dimensions=this.adapter.getInnerDimensions(),this.autoposition()},b.prototype.autoposition=function(){var e;this.measurements=this.getAutoLayoutmeasurements();var t=this.getoriginCorner(),r=this.getMenuSurfaceMaxHeight(t),n=this.hasBit(t,v.CornerBit.BOTTOM)?"bottom":"top",i=this.hasBit(t,v.CornerBit.RIGHT)?"right":"left",o=this.getHorizontalOriginOffset(t),a=this.getVerticalOriginOffset(t),s=this.measurements,c=s.anchorSize,u=s.surfaceSize,l=((e={})[i]=o,e[n]=a,e);c.width/u.width>v.numbers.ANCHOR_TO_MENU_SURFACE_WIDTH_RATIO&&(i="center"),(this.isHoistedElement||this.isFixedPosition)&&this.adjustPositionForHoistedElement(l),this.adapter.setTransformOrigin(i+" "+n),this.adapter.setPosition(l),this.adapter.setMaxHeight(r?r+"px":""),this.hasBit(t,v.CornerBit.BOTTOM)||this.adapter.addClass(b.cssClasses.IS_OPEN_BELOW)},b.prototype.getAutoLayoutmeasurements=function(){var e=this.adapter.getAnchorDimensions(),t=this.adapter.getBodyDimensions(),r=this.adapter.getViewportDimensions(),n=this.adapter.getWindowScroll();return{anchorSize:e=e||{top:this.position.y,right:this.position.x,bottom:this.position.y,left:this.position.x,width:0,height:0},bodySize:t,surfaceSize:this.dimensions,viewportDistance:{top:e.top,right:r.width-e.right,bottom:r.height-e.bottom,left:e.left},viewportSize:r,windowScroll:n}},b.prototype.getoriginCorner=function(){var e,t,r=this.originCorner,n=this.measurements,i=n.viewportDistance,o=n.anchorSize,a=n.surfaceSize,s=b.numbers.MARGIN_TO_EDGE;!(0<(t=this.hasBit(this.anchorCorner,v.CornerBit.BOTTOM)?(e=i.top-s+this.anchorMargin.bottom,i.bottom-s-this.anchorMargin.bottom):(e=i.top-s+this.anchorMargin.top,i.bottom-s+o.height-this.anchorMargin.top))-a.height)&&e>t+this.openBottomBias&&(r=this.setBit(r,v.CornerBit.BOTTOM));var c,u,l=this.adapter.isRtl(),f=this.hasBit(this.anchorCorner,v.CornerBit.FLIP_RTL),p=this.hasBit(this.anchorCorner,v.CornerBit.RIGHT)||this.hasBit(r,v.CornerBit.RIGHT),d=!1;u=(d=l&&f?!p:p)?(c=i.left+o.width+this.anchorMargin.left,i.right-this.anchorMargin.left):(c=i.left+this.anchorMargin.left,i.right+o.width-this.anchorMargin.left);var h=0<c-a.width,y=0<u-a.width,m=this.hasBit(r,v.CornerBit.FLIP_RTL)&&this.hasBit(r,v.CornerBit.RIGHT);return y&&m&&l||!h&&m?r=this.unsetBit(r,v.CornerBit.RIGHT):(h&&d&&l||h&&!d&&p||!y&&u<=c)&&(r=this.setBit(r,v.CornerBit.RIGHT)),r},b.prototype.getMenuSurfaceMaxHeight=function(e){if(0<this.maxHeight)return this.maxHeight;var t=this.measurements.viewportDistance,r=0,n=this.hasBit(e,v.CornerBit.BOTTOM),i=this.hasBit(this.anchorCorner,v.CornerBit.BOTTOM),o=b.numbers.MARGIN_TO_EDGE;return n?(r=t.top+this.anchorMargin.top-o,i||(r+=this.measurements.anchorSize.height)):(r=t.bottom-this.anchorMargin.bottom+this.measurements.anchorSize.height-o,i&&(r-=this.measurements.anchorSize.height)),r},b.prototype.getHorizontalOriginOffset=function(e){var t=this.measurements.anchorSize,r=this.hasBit(e,v.CornerBit.RIGHT),n=this.hasBit(this.anchorCorner,v.CornerBit.RIGHT);if(r){var i=n?t.width-this.anchorMargin.left:this.anchorMargin.right;return this.isHoistedElement||this.isFixedPosition?i-(this.measurements.viewportSize.width-this.measurements.bodySize.width):i}return n?t.width-this.anchorMargin.right:this.anchorMargin.left},b.prototype.getVerticalOriginOffset=function(e){var t=this.measurements.anchorSize,r=this.hasBit(e,v.CornerBit.BOTTOM),n=this.hasBit(this.anchorCorner,v.CornerBit.BOTTOM);return r?n?t.height-this.anchorMargin.top:-this.anchorMargin.bottom:n?t.height+this.anchorMargin.bottom:this.anchorMargin.top},b.prototype.adjustPositionForHoistedElement=function(e){var t,r,n=this.measurements,i=n.windowScroll,o=n.viewportDistance,a=n.surfaceSize,s=n.viewportSize,c=Object.keys(e);try{for(var u=d(c),l=u.next();!l.done;l=u.next()){var f=l.value,p=e[f]||0;!this.isHorizontallyCenteredOnViewport||"left"!==f&&"right"!==f?(p+=o[f],this.isFixedPosition||("top"===f?p+=i.y:"bottom"===f?p-=i.y:"left"===f?p+=i.x:p-=i.x),e[f]=p):e[f]=(s.width-a.width)/2}}catch(e){t={error:e}}finally{try{l&&!l.done&&(r=u.return)&&r.call(u)}finally{if(t)throw t.error}}},b.prototype.maybeRestoreFocus=function(){var e=this,t=this.adapter.isFocused(),r=this.adapter.getOwnerDocument?this.adapter.getOwnerDocument():document,n=r.activeElement&&this.adapter.isElementInContainer(r.activeElement);(t||n)&&setTimeout(function(){e.adapter.restoreFocus()},v.numbers.TOUCH_EVENT_WAIT_MS)},b.prototype.hasBit=function(e,t){return Boolean(e&t)},b.prototype.setBit=function(e,t){return e|t},b.prototype.unsetBit=function(e,t){return e^t},b.prototype.isFinite=function(e){return"number"==typeof e&&isFinite(e)},b);function b(e){var t=a.call(this,o(o({},b.defaultAdapter),e))||this;return t.isSurfaceOpen=!1,t.isQuickOpen=!1,t.isHoistedElement=!1,t.isFixedPosition=!1,t.isHorizontallyCenteredOnViewport=!1,t.maxHeight=0,t.openBottomBias=0,t.openAnimationEndTimerId=0,t.closeAnimationEndTimerId=0,t.animationRequestId=0,t.anchorCorner=v.Corner.TOP_START,t.originCorner=v.Corner.TOP_START,t.anchorMargin={top:0,right:0,bottom:0,left:0},t.position={x:0,y:0},t}t.MDCMenuSurfaceFoundation=c,t.default=c},7:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCFoundation=void 0;var n=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(e){void 0===e&&(e={}),this.adapter=e}t.MDCFoundation=n,t.default=n},8:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapAttributePrefix=t.createAttributePrefix=t.SafeAttributePrefix=void 0,r(0);function o(){}var a=r(4);t.SafeAttributePrefix=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},u);function u(e,t){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=e,r}t.createAttributePrefix=function(e){return new c(e,a.secretToken)},t.unwrapAttributePrefix=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},84:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.MDCMenuSurface=void 0;var o,a=r(57),s=r(13),c=r(58),u=r(67),l=(o=s.MDCComponent,i(f,o),f.attachTo=function(e){return new f(e)},f.prototype.initialSyncWithDOM=function(){var t=this,e=this.root.parentElement;this.anchorElement=e&&e.classList.contains(c.cssClasses.ANCHOR)?e:null,this.root.classList.contains(c.cssClasses.FIXED)&&this.setFixedPosition(!0),this.handleKeydown=function(e){t.foundation.handleKeydown(e)},this.handleBodyClick=function(e){t.foundation.handleBodyClick(e)},this.registerBodyClickListener=function(){document.body.addEventListener("click",t.handleBodyClick,{capture:!0})},this.deregisterBodyClickListener=function(){document.body.removeEventListener("click",t.handleBodyClick,{capture:!0})},this.listen("keydown",this.handleKeydown),this.listen(c.strings.OPENED_EVENT,this.registerBodyClickListener),this.listen(c.strings.CLOSED_EVENT,this.deregisterBodyClickListener)},f.prototype.destroy=function(){this.unlisten("keydown",this.handleKeydown),this.unlisten(c.strings.OPENED_EVENT,this.registerBodyClickListener),this.unlisten(c.strings.CLOSED_EVENT,this.deregisterBodyClickListener),o.prototype.destroy.call(this)},f.prototype.isOpen=function(){return this.foundation.isOpen()},f.prototype.open=function(){this.foundation.open()},f.prototype.close=function(e){void 0===e&&(e=!1),this.foundation.close(e)},Object.defineProperty(f.prototype,"quickOpen",{set:function(e){this.foundation.setQuickOpen(e)},enumerable:!1,configurable:!0}),f.prototype.setIsHoisted=function(e){this.foundation.setIsHoisted(e)},f.prototype.setMenuSurfaceAnchorElement=function(e){this.anchorElement=e},f.prototype.setFixedPosition=function(e){e?this.root.classList.add(c.cssClasses.FIXED):this.root.classList.remove(c.cssClasses.FIXED),this.foundation.setFixedPosition(e)},f.prototype.setAbsolutePosition=function(e,t){this.foundation.setAbsolutePosition(e,t),this.setIsHoisted(!0)},f.prototype.setAnchorCorner=function(e){this.foundation.setAnchorCorner(e)},f.prototype.setAnchorMargin=function(e){this.foundation.setAnchorMargin(e)},f.prototype.getDefaultFoundation=function(){var r=this,e={addClass:function(e){r.root.classList.add(e)},removeClass:function(e){r.root.classList.remove(e)},hasClass:function(e){return r.root.classList.contains(e)},hasAnchor:function(){return!!r.anchorElement},notifyClose:function(){r.emit(u.MDCMenuSurfaceFoundation.strings.CLOSED_EVENT,{})},notifyClosing:function(){r.emit(u.MDCMenuSurfaceFoundation.strings.CLOSING_EVENT,{})},notifyOpen:function(){r.emit(u.MDCMenuSurfaceFoundation.strings.OPENED_EVENT,{})},notifyOpening:function(){r.emit(u.MDCMenuSurfaceFoundation.strings.OPENING_EVENT,{})},isElementInContainer:function(e){return r.root.contains(e)},isRtl:function(){return"rtl"===getComputedStyle(r.root).getPropertyValue("direction")},setTransformOrigin:function(e){var t=a.getCorrectPropertyName(window,"transform")+"-origin";r.root.style.setProperty(t,e)},isFocused:function(){return document.activeElement===r.root},saveFocus:function(){r.previousFocus=document.activeElement},restoreFocus:function(){r.root.contains(document.activeElement)&&r.previousFocus&&r.previousFocus.focus&&r.previousFocus.focus()},getInnerDimensions:function(){return{width:r.root.offsetWidth,height:r.root.offsetHeight}},getAnchorDimensions:function(){return r.anchorElement?r.anchorElement.getBoundingClientRect():null},getViewportDimensions:function(){return{width:window.innerWidth,height:window.innerHeight}},getBodyDimensions:function(){return{width:document.body.clientWidth,height:document.body.clientHeight}},getWindowScroll:function(){return{x:window.pageXOffset,y:window.pageYOffset}},setPosition:function(e){var t=r.root;t.style.left="left"in e?e.left+"px":"",t.style.right="right"in e?e.right+"px":"",t.style.top="top"in e?e.top+"px":"",t.style.bottom="bottom"in e?e.bottom+"px":""},setMaxHeight:function(e){r.root.style.maxHeight=e},registerWindowEventHandler:function(e,t){window.addEventListener(e,t)},deregisterWindowEventHandler:function(e,t){window.removeEventListener(e,t)}};return new u.MDCMenuSurfaceFoundation(e)},f);function f(){return null!==o&&o.apply(this,arguments)||this}t.MDCMenuSurface=l},9:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEST_ONLY=t.getTrustedTypesPolicy=t.getTrustedTypes=void 0;var n,i="google#safe";function o(){var e;return""!==i&&null!==(e=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==e?e:null}t.getTrustedTypes=o,t.getTrustedTypesPolicy=function(){var e,t;if(void 0===n)try{n=null!==(t=null===(e=o())||void 0===e?void 0:e.createPolicy(i,{createHTML:function(e){return e},createScript:function(e){return e},createScriptURL:function(e){return e}}))&&void 0!==t?t:null}catch(e){n=null}return n},t.TEST_ONLY={resetDefaults:function(){n=void 0,i="google#safe"},setTrustedTypesPolicyName:function(e){i=e}}}},i.c=n,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=250);function i(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}var r,n});