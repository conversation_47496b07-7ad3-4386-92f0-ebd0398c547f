{"version": 3, "file": "foundation.js", "sourceRoot": "", "sources": ["foundation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,2BAA2B,CAAC;AAIxD,OAAO,EAAC,UAAU,EAAC,MAAM,aAAa,CAAC;AAEvC,oCAAoC;AACpC;IACI,8CAAsC;IAyBxC,oCAAY,OAA0C;QAAtD,YACE,wCAAU,0BAA0B,CAAC,cAAc,GAAK,OAAO,EAAE,SAKlE;QAHC,KAAI,CAAC,wBAAwB,GAAG;YAC9B,KAAI,CAAC,uBAAuB,EAAE,CAAC;QACjC,CAAC,CAAC;;IACJ,CAAC;IA9BD,sBAAoB,wCAAU;aAA9B;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAMD,sBAAoB,4CAAc;QAJlC;;;WAGG;aACH;YACE,wGAAwG;YACxG,OAAO;gBACL,QAAQ,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACzB,WAAW,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC5B,QAAQ,EAAE,cAAM,OAAA,KAAK,EAAL,CAAK;gBACrB,QAAQ,EAAE,cAAM,OAAA,CAAC,EAAD,CAAC;gBACjB,0BAA0B,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC3C,4BAA4B,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;aAC9C,CAAC;YACF,yCAAyC;QAC3C,CAAC;;;OAAA;IAaQ,yCAAI,GAAb;QACE,IAAI,CAAC,OAAO,CAAC,0BAA0B,CACnC,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACrD,CAAC;IAEQ,4CAAO,GAAhB;QACE,IAAI,CAAC,OAAO,CAAC,4BAA4B,CACrC,cAAc,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,6CAAQ,GAAR;QACE,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACH,0CAAK,GAAL,UAAM,WAAoB;QACjB,IAAA,WAAW,GAAI,0BAA0B,CAAC,UAAU,YAAzC,CAA0C;QAC5D,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SACpC;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SACvC;IACH,CAAC;IAED;;;;OAIG;IACH,0CAAK,GAAL,UAAM,WAAoB;QAClB,IAAA,KACF,0BAA0B,CAAC,UAAU,EADlC,iBAAiB,uBAAA,EAAE,WAAW,iBACI,CAAC;QAC1C,IAAI,WAAW,EAAE;YACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;SAC1C;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;YAC5C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SACvC;IACH,CAAC;IAED;;;;OAIG;IACH,gDAAW,GAAX,UAAY,UAAmB;QACtB,IAAA,cAAc,GAAI,0BAA0B,CAAC,UAAU,eAAzC,CAA0C;QAC/D,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;SACvC;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;SAC1C;IACH,CAAC;IAED,0DAAqB,GAArB,UAAsB,kBAA2B;QACxC,IAAA,0BAA0B,GAAI,0BAA0B,CAAC,UAAU,2BAAzC,CAA0C;QAC3E,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;SACnD;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC;SACtD;IACH,CAAC;IAED,0DAAqB,GAArB;QACS,IAAA,0BAA0B,GAAI,0BAA0B,CAAC,UAAU,2BAAzC,CAA0C;QAC3E,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;IAC3D,CAAC;IAEO,4DAAuB,GAA/B;QACS,IAAA,WAAW,GAAI,0BAA0B,CAAC,UAAU,YAAzC,CAA0C;QAC5D,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;IACxC,CAAC;IACH,iCAAC;AAAD,CAAC,AAjHD,CACI,aAAa,GAgHhB;;AAED,iHAAiH;AACjH,eAAe,0BAA0B,CAAC"}