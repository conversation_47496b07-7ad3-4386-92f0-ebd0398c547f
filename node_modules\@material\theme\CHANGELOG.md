# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [15.0.0-canary.7f224ddd4.0](https://github.com/material-components/material-components-web/compare/v14.0.0...v15.0.0-canary.7f224ddd4.0) (2023-12-28)


### Bug Fixes

* **theme:** account for null being passed into .theme-styles ([2a9697d](https://github.com/material-components/material-components-web/commit/2a9697dc53e9c7b5b89cc479df9f97a46c095a43))


### Features

* **datepicker:** datepicker textfield theming api ([a86d36f](https://github.com/material-components/material-components-web/commit/a86d36fd2ac987f5a7ca160470964ff58b198300))
* **map-ext:** add a `map-get-or-err` helper function. ([d9f8210](https://github.com/material-components/material-components-web/commit/d9f821042d192caddabfa88d0d95c54454cd9a58))
* **theme:** allow to specify varname prefix in custom-properties configuration ([f0a0bbc](https://github.com/material-components/material-components-web/commit/f0a0bbc754058398bdef427d3646a0794a07047a))
* add simpler theming validation functions ([558c2be](https://github.com/material-components/material-components-web/commit/558c2be6282cfe6db7f166d21350f866011955f8))
