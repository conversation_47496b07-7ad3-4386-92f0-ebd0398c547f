import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatGridListModule } from '@angular/material/grid-list';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule } from '@angular/router';

import { ApiService } from '../../core/services/api.service';
import { StockSummary } from '../../core/models/product.model';
import { SalesStats } from '../../core/models/sale.model';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    MatGridListModule,
    MatProgressSpinnerModule,
    RouterModule
  ],
  template: `
    <div class="dashboard-container">
      <h1>Dashboard</h1>
      
      <div class="stats-grid" *ngIf="!loading">
        <!-- Sales Stats -->
        <mat-card class="stat-card sales">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>attach_money</mat-icon>
              Today's Sales
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-value">{{ salesStats?.totalRevenue | currency }}</div>
            <div class="stat-label">{{ salesStats?.totalSales }} transactions</div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/sales/history">View Details</button>
          </mat-card-actions>
        </mat-card>

        <!-- Inventory Stats -->
        <mat-card class="stat-card inventory">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>inventory</mat-icon>
              Inventory
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-value">{{ stockSummary?.totalProducts }}</div>
            <div class="stat-label">Total Products</div>
            <div class="stat-detail" *ngIf="stockSummary?.lowStockCount">
              <span class="warning">{{ stockSummary.lowStockCount }} low stock</span>
            </div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/inventory/stock">Manage Stock</button>
          </mat-card-actions>
        </mat-card>

        <!-- Low Stock Alert -->
        <mat-card class="stat-card alert" *ngIf="stockSummary?.lowStockCount">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>warning</mat-icon>
              Low Stock Alert
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="stat-value warning">{{ stockSummary.lowStockCount }}</div>
            <div class="stat-label">Products need restocking</div>
          </mat-card-content>
          <mat-card-actions>
            <button mat-button routerLink="/inventory/stock" [queryParams]="{lowStock: true}">
              View Low Stock
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Quick Actions -->
        <mat-card class="stat-card actions">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>flash_on</mat-icon>
              Quick Actions
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="quick-actions">
              <button mat-raised-button color="primary" routerLink="/sales/pos">
                <mat-icon>point_of_sale</mat-icon>
                New Sale
              </button>
              <button mat-raised-button routerLink="/inventory/products/new">
                <mat-icon>add</mat-icon>
                Add Product
              </button>
              <button mat-raised-button routerLink="/customers/new">
                <mat-icon>person_add</mat-icon>
                Add Customer
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Recent Activity -->
      <div class="recent-activity" *ngIf="!loading">
        <mat-card>
          <mat-card-header>
            <mat-card-title>Recent Activity</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-list">
              <div class="activity-item" *ngFor="let activity of recentActivity">
                <mat-icon [class]="activity.type">{{ activity.icon }}</mat-icon>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.timestamp | date:'short' }}</div>
                </div>
              </div>
              <div *ngIf="!recentActivity?.length" class="no-activity">
                No recent activity
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Loading State -->
      <div class="loading-container" *ngIf="loading">
        <mat-spinner></mat-spinner>
        <p>Loading dashboard...</p>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .stat-card {
      min-height: 200px;
    }

    .stat-card.sales {
      border-left: 4px solid #4caf50;
    }

    .stat-card.inventory {
      border-left: 4px solid #2196f3;
    }

    .stat-card.alert {
      border-left: 4px solid #ff9800;
    }

    .stat-card.actions {
      border-left: 4px solid #9c27b0;
    }

    .stat-value {
      font-size: 2.5em;
      font-weight: bold;
      margin: 10px 0;
    }

    .stat-value.warning {
      color: #ff9800;
    }

    .stat-label {
      color: #666;
      font-size: 0.9em;
    }

    .stat-detail {
      margin-top: 10px;
    }

    .warning {
      color: #ff9800;
      font-weight: bold;
    }

    .quick-actions {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .quick-actions button {
      justify-content: flex-start;
    }

    .quick-actions mat-icon {
      margin-right: 10px;
    }

    .recent-activity {
      margin-top: 30px;
    }

    .activity-list {
      max-height: 300px;
      overflow-y: auto;
    }

    .activity-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }

    .activity-item:last-child {
      border-bottom: none;
    }

    .activity-item mat-icon {
      margin-right: 15px;
      color: #666;
    }

    .activity-content {
      flex: 1;
    }

    .activity-title {
      font-weight: 500;
    }

    .activity-time {
      font-size: 0.8em;
      color: #999;
    }

    .no-activity {
      text-align: center;
      color: #999;
      padding: 20px;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 300px;
    }

    .loading-container p {
      margin-top: 20px;
      color: #666;
    }
  `]
})
export class DashboardComponent implements OnInit {
  private apiService = inject(ApiService);

  loading = true;
  stockSummary: StockSummary | null = null;
  salesStats: SalesStats | null = null;
  recentActivity: any[] = [];

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    this.loading = true;

    // Load stock summary
    this.apiService.get<StockSummary>('/inventory/stock/summary').subscribe({
      next: (response) => {
        this.stockSummary = response.data;
      },
      error: (error) => {
        console.error('Failed to load stock summary:', error);
      }
    });

    // Load sales stats for today
    const today = new Date().toISOString().split('T')[0];
    this.apiService.get<SalesStats>('/sales/stats', { 
      dateFrom: today, 
      dateTo: today 
    }).subscribe({
      next: (response) => {
        this.salesStats = response.data;
      },
      error: (error) => {
        console.error('Failed to load sales stats:', error);
      }
    });

    // Simulate loading completion
    setTimeout(() => {
      this.loading = false;
    }, 1000);
  }
}
