!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/dialog",[],t):"object"==typeof exports?exports.dialog=t():(e.mdc=e.mdc||{},e.mdc.dialog=t())}(this,function(){return n={},i.m=r=[function(e,t,r){"use strict";(function(e){}).call(this,r(20))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapResourceUrl=t.isResourceUrl=t.createResourceUrl=t.TrustedResourceUrl=void 0,r(0);var i=r(4),o=r(9),a=(n.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},n);function n(e,t){this.privateDoNotAccessOrElseWrappedResourceUrl=e}var s=window.TrustedScriptURL;t.TrustedResourceUrl=null!=s?s:a,t.createResourceUrl=function(e){var t,r=e,n=null===(t=(0,o.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScriptURL(r);return null!=n?n:new a(r,i.secretToken)},t.isResourceUrl=function(e){return e instanceof t.TrustedResourceUrl},t.unwrapResourceUrl=function(e){var t;if(null===(t=(0,o.getTrustedTypes)())||void 0===t?void 0:t.isScriptURL(e))return e;if(e instanceof a)return e.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapHtml=t.isHtml=t.EMPTY_HTML=t.createHtml=t.SafeHtml=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedHtml=e}function s(e,t){return null!=t?t:new o(e,n.secretToken)}var c=window.TrustedHTML;t.SafeHtml=null!=c?c:o,t.createHtml=function(e){var t,r=e;return s(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createHTML(r))},t.EMPTY_HTML=function(){var e;return s("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyHTML)}(),t.isHtml=function(e){return e instanceof t.SafeHtml},t.unwrapHtml=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isHTML(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},function(e,t,r){"use strict";function n(e){var t;try{t=new URL(e)}catch(e){return"https:"}return t.protocol}Object.defineProperty(t,"__esModule",{value:!0}),t.restrictivelySanitizeUrl=t.unwrapUrlOrSanitize=t.sanitizeJavascriptUrl=void 0,r(0);var i=["data:","http:","https:","mailto:","ftp:"];function o(e){if("javascript:"!==n(e))return e}t.sanitizeJavascriptUrl=o,t.unwrapUrlOrSanitize=function(e){return o(e)},t.restrictivelySanitizeUrl=function(e){var t=n(e);return void 0!==t&&-1!==i.indexOf(t.toLowerCase())?e:"about:invalid#zClosurez"}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureTokenIsValid=t.secretToken=void 0,t.secretToken={},t.ensureTokenIsValid=function(e){if(e!==t.secretToken)throw new Error("Bad secret")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapScript=t.isScript=t.EMPTY_SCRIPT=t.createScript=t.SafeScript=void 0,r(0);var n=r(4),i=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(e,t){this.privateDoNotAccessOrElseWrappedScript=e}function s(e,t){return null!=t?t:new o(e,n.secretToken)}var c=window.TrustedScript;t.SafeScript=null!=c?c:o,t.createScript=function(e){var t,r=e;return s(r,null===(t=(0,i.getTrustedTypesPolicy)())||void 0===t?void 0:t.createScript(r))},t.EMPTY_SCRIPT=function(){var e;return s("",null===(e=(0,i.getTrustedTypes)())||void 0===e?void 0:e.emptyScript)}(),t.isScript=function(e){return e instanceof t.SafeScript},t.unwrapScript=function(e){var t;if(null===(t=(0,i.getTrustedTypes)())||void 0===t?void 0:t.isScript(e))return e;if(e instanceof o)return e.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assertIsTemplateObject=void 0,t.assertIsTemplateObject=function(e,t,r){if(!Array.isArray(e)||!Array.isArray(e.raw)||!t&&1!==e.length)throw new TypeError(r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.MDCFoundation=void 0;var n=(Object.defineProperty(i,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(i,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),i.prototype.init=function(){},i.prototype.destroy=function(){},i);function i(e){void 0===e&&(e={}),this.adapter=e}t.MDCFoundation=n,t.default=n},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapAttributePrefix=t.createAttributePrefix=t.SafeAttributePrefix=void 0,r(0);function o(){}var a=r(4);t.SafeAttributePrefix=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},u);function u(e,t){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=e,r}t.createAttributePrefix=function(e){return new c(e,a.secretToken)},t.unwrapAttributePrefix=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.TEST_ONLY=t.getTrustedTypesPolicy=t.getTrustedTypes=void 0;var n,i="google#safe";function o(){var e;return""!==i&&null!==(e=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==e?e:null}t.getTrustedTypes=o,t.getTrustedTypesPolicy=function(){var e,t;if(void 0===n)try{n=null!==(t=null===(e=o())||void 0===e?void 0:e.createPolicy(i,{createHTML:function(e){return e},createScript:function(e){return e},createScriptURL:function(e){return e}}))&&void 0!==t?t:null}catch(e){n=null}return n},t.TEST_ONLY={resetDefaults:function(){n=void 0,i="google#safe"},setTrustedTypesPolicyName:function(e){i=e}}},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyle=t.isStyle=t.createStyle=t.SafeStyle=void 0,r(0);function o(){}var a=r(4);t.SafeStyle=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},u);function u(e,t){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=e,r}t.createStyle=function(e){return new c(e,a.secretToken)},t.isStyle=function(e){return e instanceof c},t.unwrapStyle=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AttributePolicyAction=t.SanitizerTable=void 0;var n,i,o=(a.prototype.isAllowedElement=function(e){return"form"!==e.toLowerCase()&&(this.allowedElements.has(e)||this.elementPolicies.has(e))},a.prototype.getAttributePolicy=function(e,t){var r=this.elementPolicies.get(t);return(null==r?void 0:r.has(e))?r.get(e):this.allowedGlobalAttributes.has(e)?{policyAction:n.KEEP}:this.globalAttributePolicies.get(e)||{policyAction:n.DROP}},a);function a(e,t,r,n){this.allowedElements=e,this.elementPolicies=t,this.allowedGlobalAttributes=r,this.globalAttributePolicies=n}t.SanitizerTable=o,(i=n=t.AttributePolicyAction||(t.AttributePolicyAction={}))[i.DROP=0]="DROP",i[i.KEEP=1]="KEEP",i[i.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",i[i.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",i[i.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.isStyleSheet=t.createStyleSheet=t.SafeStyleSheet=void 0,r(0);function o(){}var a=r(4);t.SafeStyleSheet=o;var s,c=(i(u,s=o),u.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},u);function u(e,t){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=e,r}t.createStyleSheet=function(e){return new c(e,a.secretToken)},t.isStyleSheet=function(e){return e instanceof c},t.unwrapStyleSheet=function(e){if(e instanceof c)return e.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},function(e,t,r){"use strict";var i=this&&this.__makeTemplateObject||function(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e},o=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},a=this&&this.__spreadArray||function(e,t){for(var r=0,n=t.length,i=e.length;r<n;r++,i++)e[i]=t[r];return e};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCComponent=void 0;var s=r(17),c=r(18),n=r(7);var u,l,d=(f.attachTo=function(e){return new f(e,new n.MDCFoundation({}))},f.prototype.initialize=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},f.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},f.prototype.initialSyncWithDOM=function(){},f.prototype.destroy=function(){this.foundation.destroy()},f.prototype.listen=function(e,t,r){this.root.addEventListener(e,t,r)},f.prototype.unlisten=function(e,t,r){this.root.removeEventListener(e,t,r)},f.prototype.emit=function(e,t,r){var n;void 0===r&&(r=!1),"function"==typeof CustomEvent?n=new CustomEvent(e,{bubbles:r,detail:t}):(n=document.createEvent("CustomEvent")).initCustomEvent(e,r,!1,t),this.root.dispatchEvent(n)},f.prototype.safeSetAttribute=function(e,t,r){if("tabindex"===t.toLowerCase())e.tabIndex=Number(r);else if(0===t.indexOf("data-")){var n=function(e){return String(e).replace(/\-([a-z])/g,function(e,t){return t.toUpperCase()})}(t.replace(/^data-/,""));e.dataset[n]=r}else c.safeElement.setPrefixedAttribute([s.safeAttrPrefix(u=u||i(["aria-"],["aria-"])),s.safeAttrPrefix(l=l||i(["role"],["role"]))],e,t,r)},f);function f(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.root=e,this.initialize.apply(this,a([],o(r))),this.foundation=void 0===t?this.getDefaultFoundation():t,this.foundation.init(),this.initialSyncWithDOM()}t.MDCComponent=d,t.default=d},function(e,t,r){"use strict";var p=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.HtmlSanitizerImpl=void 0,r(0);var n=r(2),i=r(4),h=r(3),c=r(23),v=r(24),o=r(16),y=r(11),a=(s.prototype.sanitizeAssertUnchanged=function(e){this.changes=[];var t=this.sanitize(e);if(0===this.changes.length)return t;throw new Error("")},s.prototype.sanitize=function(e){var t=document.createElement("span");t.appendChild(this.sanitizeToFragment(e));var r=(new XMLSerializer).serializeToString(t);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,n.createHtml)(r)},s.prototype.sanitizeToFragment=function(e){for(var t=this,r=(0,c.createInertFragment)(e),n=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(e){return t.nodeFilter(e)},!1),i=n.nextNode(),o=document.createDocumentFragment(),a=o;null!==i;){var s=void 0;if((0,v.isText)(i))s=this.sanitizeTextNode(i);else{if(!(0,v.isElement)(i))throw new Error("Node is not of type text or element");s=this.sanitizeElementNode(i)}if(a.appendChild(s),i=n.firstChild())a=s;else for(;!(i=n.nextSibling())&&(i=n.parentNode());)a=a.parentNode}return o},s.prototype.sanitizeTextNode=function(e){return document.createTextNode(e.data)},s.prototype.sanitizeElementNode=function(e){var t,r,n=(0,v.getNodeName)(e),i=document.createElement(n),o=e.attributes;try{for(var a=p(o),s=a.next();!s.done;s=a.next()){var c=s.value,u=c.name,l=c.value,d=this.sanitizerTable.getAttributePolicy(u,n);if(this.satisfiesAllConditions(d.conditions,o))switch(d.policyAction){case y.AttributePolicyAction.KEEP:i.setAttribute(u,l);break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var f=(0,h.restrictivelySanitizeUrl)(l);f!==l&&this.recordChange("Url in attribute ".concat(u,' was modified during sanitization. Original url:"').concat(l,'" was sanitized to: "').concat(f,'"')),i.setAttribute(u,f);break;case y.AttributePolicyAction.KEEP_AND_NORMALIZE:i.setAttribute(u,l.toLowerCase());break;case y.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:i.setAttribute(u,l);break;case y.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(u," was dropped"));break;default:m(d.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(u,"."))}}catch(e){t={error:e}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(t)throw t.error}}return i},s.prototype.nodeFilter=function(e){if((0,v.isText)(e))return NodeFilter.FILTER_ACCEPT;if(!(0,v.isElement)(e))return NodeFilter.FILTER_REJECT;var t=(0,v.getNodeName)(e);return null===t?(this.recordChange("Node name was null for node: ".concat(e)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(t)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(t," was dropped")),NodeFilter.FILTER_REJECT)},s.prototype.recordChange=function(e){0===this.changes.length&&this.changes.push("")},s.prototype.satisfiesAllConditions=function(e,t){var r,n,i;if(!e)return!0;try{for(var o=p(e),a=o.next();!a.done;a=o.next()){var s=d(a.value,2),c=s[0],u=s[1],l=null===(i=t.getNamedItem(c))||void 0===i?void 0:i.value;if(l&&!u.has(l))return!1}}catch(e){r={error:e}}finally{try{a&&!a.done&&(n=o.return)&&n.call(o)}finally{if(r)throw r.error}}return!0},s);function s(e,t){this.sanitizerTable=e,this.changes=[],(0,i.ensureTokenIsValid)(t)}t.HtmlSanitizerImpl=a;var u=function(){return new a(o.defaultSanitizerTable,i.secretToken)}();function m(e,t){throw void 0===t&&(t="unexpected value ".concat(e,"!")),new Error(t)}t.sanitizeHtml=function(e){return u.sanitize(e)},t.sanitizeHtmlAssertUnchanged=function(e){return u.sanitizeAssertUnchanged(e)},t.sanitizeHtmlToFragment=function(e){return u.sanitizeToFragment(e)}},function(e,t,r){"use strict";var i=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},o=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.setPrefixedAttribute=t.buildPrefixedAttributeSetter=t.insertAdjacentHtml=t.setCssText=t.setOuterHtml=t.setInnerHtml=void 0;var a=r(8),s=r(2),n=r(10);function c(e,t,r,n){if(0===e.length)throw new Error("No prefixes are provided");var i=e.map(function(e){return(0,a.unwrapAttributePrefix)(e)}),o=r.toLowerCase();if(i.every(function(e){return 0!==o.indexOf(e)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));t.setAttribute(r,n)}function u(e){if("script"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===e.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}t.setInnerHtml=function(e,t){!function(e){return void 0!==e.tagName}(e)||u(e),e.innerHTML=(0,s.unwrapHtml)(t)},t.setOuterHtml=function(e,t){var r=e.parentElement;null!==r&&u(r),e.outerHTML=(0,s.unwrapHtml)(t)},t.setCssText=function(e,t){e.style.cssText=(0,n.unwrapStyle)(t)},t.insertAdjacentHtml=function(e,t,r){var n="beforebegin"===t||"afterend"===t?e.parentElement:e;null!==n&&u(n),e.insertAdjacentHTML(t,(0,s.unwrapHtml)(r))},t.buildPrefixedAttributeSetter=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=o([e],i(t),!1);return function(e,t,r){c(n,e,t,r)}},t.setPrefixedAttribute=c},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultSanitizerTable=void 0;var n=r(11);t.defaultSanitizerTable=new n.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:n.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:n.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unwrapStyleSheet=t.SafeStyleSheet=t.isStyleSheet=t.unwrapStyle=t.SafeStyle=t.isStyle=t.unwrapScript=t.SafeScript=t.isScript=t.EMPTY_SCRIPT=t.unwrapResourceUrl=t.TrustedResourceUrl=t.isResourceUrl=t.unwrapHtml=t.SafeHtml=t.isHtml=t.EMPTY_HTML=t.unwrapAttributePrefix=t.SafeAttributePrefix=t.safeStyleSheet=t.concatStyleSheets=t.safeStyle=t.concatStyles=t.scriptFromJson=t.safeScriptWithArgs=t.safeScript=t.concatScripts=t.trustedResourceUrl=t.replaceFragment=t.blobUrlFromScript=t.appendParams=t.HtmlSanitizerBuilder=t.sanitizeHtmlToFragment=t.sanitizeHtmlAssertUnchanged=t.sanitizeHtml=t.htmlEscape=t.createScriptSrc=t.createScript=t.concatHtmls=t.safeAttrPrefix=void 0;var n=r(19);Object.defineProperty(t,"safeAttrPrefix",{enumerable:!0,get:function(){return n.safeAttrPrefix}});var i=r(22);Object.defineProperty(t,"concatHtmls",{enumerable:!0,get:function(){return i.concatHtmls}}),Object.defineProperty(t,"createScript",{enumerable:!0,get:function(){return i.createScript}}),Object.defineProperty(t,"createScriptSrc",{enumerable:!0,get:function(){return i.createScriptSrc}}),Object.defineProperty(t,"htmlEscape",{enumerable:!0,get:function(){return i.htmlEscape}});var o=r(14);Object.defineProperty(t,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(t,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(t,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(t,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var s=r(26);Object.defineProperty(t,"appendParams",{enumerable:!0,get:function(){return s.appendParams}}),Object.defineProperty(t,"blobUrlFromScript",{enumerable:!0,get:function(){return s.blobUrlFromScript}}),Object.defineProperty(t,"replaceFragment",{enumerable:!0,get:function(){return s.replaceFragment}}),Object.defineProperty(t,"trustedResourceUrl",{enumerable:!0,get:function(){return s.trustedResourceUrl}});var c=r(27);Object.defineProperty(t,"concatScripts",{enumerable:!0,get:function(){return c.concatScripts}}),Object.defineProperty(t,"safeScript",{enumerable:!0,get:function(){return c.safeScript}}),Object.defineProperty(t,"safeScriptWithArgs",{enumerable:!0,get:function(){return c.safeScriptWithArgs}}),Object.defineProperty(t,"scriptFromJson",{enumerable:!0,get:function(){return c.scriptFromJson}});var u=r(28);Object.defineProperty(t,"concatStyles",{enumerable:!0,get:function(){return u.concatStyles}}),Object.defineProperty(t,"safeStyle",{enumerable:!0,get:function(){return u.safeStyle}});var l=r(29);Object.defineProperty(t,"concatStyleSheets",{enumerable:!0,get:function(){return l.concatStyleSheets}}),Object.defineProperty(t,"safeStyleSheet",{enumerable:!0,get:function(){return l.safeStyleSheet}});var d=r(8);Object.defineProperty(t,"SafeAttributePrefix",{enumerable:!0,get:function(){return d.SafeAttributePrefix}}),Object.defineProperty(t,"unwrapAttributePrefix",{enumerable:!0,get:function(){return d.unwrapAttributePrefix}});var f=r(2);Object.defineProperty(t,"EMPTY_HTML",{enumerable:!0,get:function(){return f.EMPTY_HTML}}),Object.defineProperty(t,"isHtml",{enumerable:!0,get:function(){return f.isHtml}}),Object.defineProperty(t,"SafeHtml",{enumerable:!0,get:function(){return f.SafeHtml}}),Object.defineProperty(t,"unwrapHtml",{enumerable:!0,get:function(){return f.unwrapHtml}});var p=r(1);Object.defineProperty(t,"isResourceUrl",{enumerable:!0,get:function(){return p.isResourceUrl}}),Object.defineProperty(t,"TrustedResourceUrl",{enumerable:!0,get:function(){return p.TrustedResourceUrl}}),Object.defineProperty(t,"unwrapResourceUrl",{enumerable:!0,get:function(){return p.unwrapResourceUrl}});var h=r(5);Object.defineProperty(t,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return h.EMPTY_SCRIPT}}),Object.defineProperty(t,"isScript",{enumerable:!0,get:function(){return h.isScript}}),Object.defineProperty(t,"SafeScript",{enumerable:!0,get:function(){return h.SafeScript}}),Object.defineProperty(t,"unwrapScript",{enumerable:!0,get:function(){return h.unwrapScript}});var v=r(10);Object.defineProperty(t,"isStyle",{enumerable:!0,get:function(){return v.isStyle}}),Object.defineProperty(t,"SafeStyle",{enumerable:!0,get:function(){return v.SafeStyle}}),Object.defineProperty(t,"unwrapStyle",{enumerable:!0,get:function(){return v.unwrapStyle}});var y=r(12);Object.defineProperty(t,"isStyleSheet",{enumerable:!0,get:function(){return y.isStyleSheet}}),Object.defineProperty(t,"SafeStyleSheet",{enumerable:!0,get:function(){return y.SafeStyleSheet}}),Object.defineProperty(t,"unwrapStyleSheet",{enumerable:!0,get:function(){return y.unwrapStyleSheet}})},function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);i&&("get"in i?t.__esModule:!i.writable&&!i.configurable)||(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.safeWorker=t.safeWindow=t.safeServiceWorkerContainer=t.safeRange=t.safeLocation=t.safeGlobal=t.safeDomParser=t.safeDocument=t.safeStyleEl=t.safeScriptEl=t.safeObjectEl=t.safeLinkEl=t.safeInputEl=t.safeIframeEl=t.safeFormEl=t.safeEmbedEl=t.safeElement=t.safeButtonEl=t.safeAreaEl=t.safeAnchorEl=void 0,t.safeAnchorEl=o(r(30)),t.safeAreaEl=o(r(31)),t.safeButtonEl=o(r(32)),t.safeElement=o(r(15)),t.safeEmbedEl=o(r(33)),t.safeFormEl=o(r(34)),t.safeIframeEl=o(r(35)),t.safeInputEl=o(r(36)),t.safeLinkEl=o(r(37)),t.safeObjectEl=o(r(38)),t.safeScriptEl=o(r(39)),t.safeStyleEl=o(r(40)),t.safeDocument=o(r(41)),t.safeDomParser=o(r(42)),t.safeGlobal=o(r(43)),t.safeLocation=o(r(44)),t.safeRange=o(r(45)),t.safeServiceWorkerContainer=o(r(46)),t.safeWindow=o(r(47)),t.safeWorker=o(r(48))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeAttrPrefix=void 0,r(0);var n=r(8);r(6),r(21);t.safeAttrPrefix=function(e){var t=e[0].toLowerCase();return(0,n.createAttributePrefix)(t)}},function(e,t){var r,n,i=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(t){if(r===setTimeout)return setTimeout(t,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(e){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var c,u=[],l=!1,d=-1;function f(){l&&c&&(l=!1,c.length?u=c.concat(u):d=-1,u.length&&p())}function p(){if(!l){var e=s(f);l=!0;for(var t=u.length;t;){for(c=u,u=[];++d<t;)c&&c[d].run();d=-1,t=u.length}c=null,l=!1,function(t){if(n===clearTimeout)return clearTimeout(t);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(t);try{n(t)}catch(e){try{return n.call(null,t)}catch(e){return n.call(this,t)}}}(e)}}function h(e,t){this.fun=e,this.array=t}function v(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];u.push(new h(e,t)),1!==u.length||l||s(p)},h.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=v,i.addListener=v,i.once=v,i.off=v,i.removeListener=v,i.removeAllListeners=v,i.emit=v,i.prependListener=v,i.prependOnceListener=v,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SECURITY_SENSITIVE_ATTRIBUTES=void 0,t.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatHtmls=t.createScriptSrc=t.createScript=t.htmlEscape=void 0;var o=r(2),a=r(1),i=r(5);function s(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}t.htmlEscape=function(e,t){void 0===t&&(t={});var r=s(e);return t.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),t.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),t.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(r)},t.createScript=function(e,t){void 0===t&&(t={});var r=(0,i.unwrapScript)(e).toString(),n="<script";return t.id&&(n+=' id="'.concat(s(t.id),'"')),t.nonce&&(n+=' nonce="'.concat(s(t.nonce),'"')),t.type&&(n+=' type="'.concat(s(t.type),'"')),n+=">".concat(r,"<\/script>"),(0,o.createHtml)(n)},t.createScriptSrc=function(e,t,r){var n=(0,a.unwrapResourceUrl)(e).toString(),i='<script src="'.concat(s(n),'"');return t&&(i+=" async"),r&&(i+=' nonce="'.concat(s(r),'"')),i+="><\/script>",(0,o.createHtml)(i)},t.concatHtmls=function(e){return(0,o.createHtml)(e.map(o.unwrapHtml).join(""))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createInertFragment=void 0;var n=r(15),i=r(2);t.createInertFragment=function(e){var t=document.createElement("template"),r=(0,i.createHtml)(e);return(0,n.setInnerHtml)(t,r),t.content}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isElement=t.isText=t.getNodeName=void 0,t.getNodeName=function(e){var t=e.nodeName;return"string"==typeof t?t:"FORM"},t.isText=function(e){return e.nodeType===Node.TEXT_NODE},t.isElement=function(e){var t=e.nodeType;return t===Node.ELEMENT_NODE||"number"!=typeof t}},function(e,t,r){"use strict";var _=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},T=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a};Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlSanitizerBuilder=void 0;var n=r(4),i=r(14),o=r(16),O=r(11),a=(s.prototype.onlyAllowElements=function(e){var t,r,n=new Set,i=new Map;try{for(var o=_(e),a=o.next();!a.done;a=o.next()){var s=a.value;if(s=s.toUpperCase(),!this.sanitizerTable.isAllowedElement(s))throw new Error("Element: ".concat(s,", is not allowed by html5_contract.textpb"));var c=this.sanitizerTable.elementPolicies.get(s);void 0!==c?i.set(s,c):n.add(s)}}catch(e){t={error:e}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return this.sanitizerTable=new O.SanitizerTable(n,i,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},s.prototype.onlyAllowAttributes=function(e){var t,r,n,i,o,a,s=new Set,c=new Map,u=new Map;try{for(var l=_(e),d=l.next();!d.done;d=l.next()){var f=d.value;this.sanitizerTable.allowedGlobalAttributes.has(f)&&s.add(f),this.sanitizerTable.globalAttributePolicies.has(f)&&c.set(f,this.sanitizerTable.globalAttributePolicies.get(f))}}catch(e){t={error:e}}finally{try{d&&!d.done&&(r=l.return)&&r.call(l)}finally{if(t)throw t.error}}try{for(var p=_(this.sanitizerTable.elementPolicies.entries()),h=p.next();!h.done;h=p.next()){var v=T(h.value,2),y=v[0],m=v[1],S=new Map;try{for(var b=(o=void 0,_(m.entries())),g=b.next();!g.done;g=b.next()){var E=T(g.value,2),A=(f=E[0],E[1]);e.has(f)&&S.set(f,A)}}catch(e){o={error:e}}finally{try{g&&!g.done&&(a=b.return)&&a.call(b)}finally{if(o)throw o.error}}u.set(y,S)}}catch(e){n={error:e}}finally{try{h&&!h.done&&(i=p.return)&&i.call(p)}finally{if(n)throw n.error}}return this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,u,s,c),this},s.prototype.allowDataAttributes=function(e){var t,r,n=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var i=_(e),o=i.next();!o.done;o=i.next()){var a=o.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));n.add(a)}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}return this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,n,this.sanitizerTable.globalAttributePolicies),this},s.prototype.allowStyleAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("style",{policyAction:O.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},s.prototype.allowClassAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("class",{policyAction:O.AttributePolicyAction.KEEP}),this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},s.prototype.allowIdAttributes=function(){var e=new Map(this.sanitizerTable.globalAttributePolicies);return e.set("id",{policyAction:O.AttributePolicyAction.KEEP}),this.sanitizerTable=new O.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,e),this},s.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new i.HtmlSanitizerImpl(this.sanitizerTable,n.secretToken)},s);function s(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}t.HtmlSanitizerBuilder=a},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.blobUrlFromScript=t.replaceFragment=t.appendParams=t.trustedResourceUrl=void 0,r(0);var s=r(1),n=r(5);r(6);t.trustedResourceUrl=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(0===t.length)return(0,s.createResourceUrl)(e[0]);e[0].toLowerCase();for(var n=[e[0]],i=0;i<t.length;i++)n.push(encodeURIComponent(t[i])),n.push(e[i+1]);return(0,s.createResourceUrl)(n.join(""))},t.appendParams=function(e,t){var o=(0,s.unwrapResourceUrl)(e).toString();if(/#/.test(o)){throw new Error("")}var a=/\?/.test(o)?"&":"?";return t.forEach(function(e,t){for(var r=e instanceof Array?e:[e],n=0;n<r.length;n++){var i=r[n];null!=i&&(o+=a+encodeURIComponent(t)+"="+encodeURIComponent(String(i)),a="&")}}),(0,s.createResourceUrl)(o)};var i=/[^#]*/;t.replaceFragment=function(e,t){var r=(0,s.unwrapResourceUrl)(e).toString();return(0,s.createResourceUrl)(i.exec(r)[0]+"#"+t)},t.blobUrlFromScript=function(e){var t=(0,n.unwrapScript)(e).toString(),r=new Blob([t],{type:"text/javascript"});return(0,s.createResourceUrl)(URL.createObjectURL(r))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.safeScriptWithArgs=t.scriptFromJson=t.concatScripts=t.safeScript=void 0,r(0);var i=r(5);r(6);function o(e){return(0,i.createScript)(JSON.stringify(e).replace(/</g,"\\x3c"))}t.safeScript=function(e){return(0,i.createScript)(e[0])},t.concatScripts=function(e){return(0,i.createScript)(e.map(i.unwrapScript).join(""))},t.scriptFromJson=o,t.safeScriptWithArgs=function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=e.map(function(e){return o(e).toString()});return(0,i.createScript)("(".concat(n.join(""),")(").concat(r.join(","),")"))}}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyles=t.safeStyle=void 0,r(0);r(6);var n=r(10);t.safeStyle=function(e){var t=e[0];return(0,n.createStyle)(t)},t.concatStyles=function(e){return(0,n.createStyle)(e.map(n.unwrapStyle).join(""))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.concatStyleSheets=t.safeStyleSheet=void 0,r(0);r(6);var n=r(12);t.safeStyleSheet=function(e){var t=e[0];return(0,n.createStyleSheet)(t)},t.concatStyleSheets=function(e){return(0,n.createStyleSheet)(e.map(n.unwrapStyleSheet).join(""))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=void 0;var n=r(1);t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setAction=void 0;var n=r(3);t.setAction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.action=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrcdoc=t.setSrc=void 0;var n=r(2),i=r(1);t.setSrc=function(e,t){e.src=(0,i.unwrapResourceUrl)(t).toString()},t.setSrcdoc=function(e,t){e.srcdoc=(0,n.unwrapHtml)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setFormaction=void 0;var n=r(3);t.setFormaction=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.formAction=r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setHrefAndRel=void 0;var i=r(3),o=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];t.setHrefAndRel=function(e,t,r){if(t instanceof o.TrustedResourceUrl)e.href=(0,o.unwrapResourceUrl)(t).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var n=(0,i.unwrapUrlOrSanitize)(t);if(void 0===n)return;e.href=n}e.rel=r}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setData=void 0;var n=r(1);t.setData=function(e,t){e.data=(0,n.unwrapResourceUrl)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setSrc=t.setTextContent=void 0;var n=r(1),i=r(5);function o(e){var t=function(e){var t,r=e.document,n=null===(t=r.querySelector)||void 0===t?void 0:t.call(r,"script[nonce]");return n&&(n.nonce||n.getAttribute("nonce"))||""}(e.ownerDocument&&e.ownerDocument.defaultView||window);t&&e.setAttribute("nonce",t)}t.setTextContent=function(e,t){e.textContent=(0,i.unwrapScript)(t),o(e)},t.setSrc=function(e,t){e.src=(0,n.unwrapResourceUrl)(t),o(e)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.setTextContent=void 0;var n=r(12);t.setTextContent=function(e,t){e.textContent=(0,n.unwrapStyleSheet)(t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execCommandInsertHtml=t.execCommand=t.write=void 0;var o=r(2);t.write=function(e,t){e.write((0,o.unwrapHtml)(t))},t.execCommand=function(e,t,r){var n=String(t),i=r;return"inserthtml"===n.toLowerCase()&&(i=(0,o.unwrapHtml)(r)),e.execCommand(n,!1,i)},t.execCommandInsertHtml=function(e,t){return e.execCommand("insertHTML",!1,(0,o.unwrapHtml)(t))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseFromString=t.parseHtml=void 0;var n=r(2);function i(e,t,r){return e.parseFromString((0,n.unwrapHtml)(t),r)}t.parseHtml=function(e,t){return i(e,t,"text/html")},t.parseFromString=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.globalEval=void 0;var i=r(5);t.globalEval=function(e,t){var r=(0,i.unwrapScript)(t),n=e.eval(r);return n===r&&(n=e.eval(r.toString())),n}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.assign=t.replace=t.setHref=void 0;var n=r(3);t.setHref=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&(e.href=r)},t.replace=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.replace(r)},t.assign=function(e,t){var r=(0,n.unwrapUrlOrSanitize)(t);void 0!==r&&e.assign(r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createContextualFragment=void 0;var n=r(2);t.createContextualFragment=function(e,t){return e.createContextualFragment((0,n.unwrapHtml)(t))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.register=void 0;var n=r(1);t.register=function(e,t,r){return e.register((0,n.unwrapResourceUrl)(t),r)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.open=void 0;var o=r(3);t.open=function(e,t,r,n){var i=(0,o.unwrapUrlOrSanitize)(t);return void 0!==i?e.open(i,r,n):null}},function(e,t,r){"use strict";var n=this&&this.__read||function(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||0<t--)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a},i=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||((n=n||Array.prototype.slice.call(t,0,i))[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0}),t.importScripts=t.createShared=t.create=void 0;var o=r(1);t.create=function(e,t){return new Worker((0,o.unwrapResourceUrl)(e),t)},t.createShared=function(e,t){return new SharedWorker((0,o.unwrapResourceUrl)(e),t)},t.importScripts=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];e.importScripts.apply(e,i([],n(t.map(function(e){return(0,o.unwrapResourceUrl)(e)})),!1))}},function(e,t,r){"use strict";function n(e,t){return(e.matches||e.webkitMatchesSelector||e.msMatchesSelector).call(e,t)}Object.defineProperty(t,"__esModule",{value:!0}),t.estimateScrollWidth=t.matches=t.closest=void 0,t.closest=function(e,t){if(e.closest)return e.closest(t);for(var r=e;r;){if(n(r,t))return r;r=r.parentElement}return null},t.matches=n,t.estimateScrollWidth=function(e){var t=e;if(null!==t.offsetParent)return t.scrollWidth;var r=t.cloneNode(!0);r.style.setProperty("position","absolute"),r.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(r);var n=r.scrollWidth;return document.documentElement.removeChild(r),n}},function(e,t,r){"use strict";var a;Object.defineProperty(t,"__esModule",{value:!0}),t.getNormalizedEventCoords=t.supportsCssVariables=void 0,t.supportsCssVariables=function(e,t){void 0===t&&(t=!1);var r,n=e.CSS;if("boolean"==typeof a&&!t)return a;if(!(n&&"function"==typeof n.supports))return!1;var i=n.supports("--css-vars","yes"),o=n.supports("(--css-vars: yes)")&&n.supports("color","#00000000");return r=i||o,t||(a=r),r},t.getNormalizedEventCoords=function(e,t,r){if(!e)return{x:0,y:0};var n,i,o=t.x,a=t.y,s=o+r.left,c=a+r.top;if("touchstart"===e.type){var u=e;n=u.changedTouches[0].pageX-s,i=u.changedTouches[0].pageY-c}else{var l=e;n=l.pageX-s,i=l.pageY-c}return{x:n,y:i}}},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},a=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCRippleFoundation=void 0;var s,c=r(7),u=r(54),l=r(50),d=["touchstart","pointerdown","mousedown","keydown"],f=["touchend","pointerup","mouseup","contextmenu"],p=[],h=(s=c.MDCFoundation,i(v,s),Object.defineProperty(v,"cssClasses",{get:function(){return u.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(v,"strings",{get:function(){return u.strings},enumerable:!1,configurable:!0}),Object.defineProperty(v,"numbers",{get:function(){return u.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(v,"defaultAdapter",{get:function(){return{addClass:function(){},browserSupportsCssVars:function(){return!0},computeBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},containsEventTarget:function(){return!0},deregisterDocumentInteractionHandler:function(){},deregisterInteractionHandler:function(){},deregisterResizeHandler:function(){},getWindowPageOffset:function(){return{x:0,y:0}},isSurfaceActive:function(){return!0},isSurfaceDisabled:function(){return!0},isUnbounded:function(){return!0},registerDocumentInteractionHandler:function(){},registerInteractionHandler:function(){},registerResizeHandler:function(){},removeClass:function(){},updateCssVariable:function(){}}},enumerable:!1,configurable:!0}),v.prototype.init=function(){var e=this,t=this.supportsPressRipple();if(this.registerRootHandlers(t),t){var r=v.cssClasses,n=r.ROOT,i=r.UNBOUNDED;requestAnimationFrame(function(){e.adapter.addClass(n),e.adapter.isUnbounded()&&(e.adapter.addClass(i),e.layoutInternal())})}},v.prototype.destroy=function(){var e=this;if(this.supportsPressRipple()){this.activationTimer&&(clearTimeout(this.activationTimer),this.activationTimer=0,this.adapter.removeClass(v.cssClasses.FG_ACTIVATION)),this.fgDeactivationRemovalTimer&&(clearTimeout(this.fgDeactivationRemovalTimer),this.fgDeactivationRemovalTimer=0,this.adapter.removeClass(v.cssClasses.FG_DEACTIVATION));var t=v.cssClasses,r=t.ROOT,n=t.UNBOUNDED;requestAnimationFrame(function(){e.adapter.removeClass(r),e.adapter.removeClass(n),e.removeCssVars()})}this.deregisterRootHandlers(),this.deregisterDeactivationHandlers()},v.prototype.activate=function(e){this.activateImpl(e)},v.prototype.deactivate=function(){this.deactivateImpl()},v.prototype.layout=function(){var e=this;this.layoutFrame&&cancelAnimationFrame(this.layoutFrame),this.layoutFrame=requestAnimationFrame(function(){e.layoutInternal(),e.layoutFrame=0})},v.prototype.setUnbounded=function(e){var t=v.cssClasses.UNBOUNDED;e?this.adapter.addClass(t):this.adapter.removeClass(t)},v.prototype.handleFocus=function(){var e=this;requestAnimationFrame(function(){e.adapter.addClass(v.cssClasses.BG_FOCUSED)})},v.prototype.handleBlur=function(){var e=this;requestAnimationFrame(function(){e.adapter.removeClass(v.cssClasses.BG_FOCUSED)})},v.prototype.supportsPressRipple=function(){return this.adapter.browserSupportsCssVars()},v.prototype.defaultActivationState=function(){return{activationEvent:void 0,hasDeactivationUXRun:!1,isActivated:!1,isProgrammatic:!1,wasActivatedByPointer:!1,wasElementMadeActive:!1}},v.prototype.registerRootHandlers=function(e){var t,r;if(e){try{for(var n=a(d),i=n.next();!i.done;i=n.next()){var o=i.value;this.adapter.registerInteractionHandler(o,this.activateHandler)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}this.adapter.isUnbounded()&&this.adapter.registerResizeHandler(this.resizeHandler)}this.adapter.registerInteractionHandler("focus",this.focusHandler),this.adapter.registerInteractionHandler("blur",this.blurHandler)},v.prototype.registerDeactivationHandlers=function(e){var t,r;if("keydown"===e.type)this.adapter.registerInteractionHandler("keyup",this.deactivateHandler);else try{for(var n=a(f),i=n.next();!i.done;i=n.next()){var o=i.value;this.adapter.registerDocumentInteractionHandler(o,this.deactivateHandler)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(r=n.return)&&r.call(n)}finally{if(t)throw t.error}}},v.prototype.deregisterRootHandlers=function(){var t,e;try{for(var r=a(d),n=r.next();!n.done;n=r.next()){var i=n.value;this.adapter.deregisterInteractionHandler(i,this.activateHandler)}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}this.adapter.deregisterInteractionHandler("focus",this.focusHandler),this.adapter.deregisterInteractionHandler("blur",this.blurHandler),this.adapter.isUnbounded()&&this.adapter.deregisterResizeHandler(this.resizeHandler)},v.prototype.deregisterDeactivationHandlers=function(){var t,e;this.adapter.deregisterInteractionHandler("keyup",this.deactivateHandler);try{for(var r=a(f),n=r.next();!n.done;n=r.next()){var i=n.value;this.adapter.deregisterDocumentInteractionHandler(i,this.deactivateHandler)}}catch(e){t={error:e}}finally{try{n&&!n.done&&(e=r.return)&&e.call(r)}finally{if(t)throw t.error}}},v.prototype.removeCssVars=function(){var t=this,r=v.strings;Object.keys(r).forEach(function(e){0===e.indexOf("VAR_")&&t.adapter.updateCssVariable(r[e],null)})},v.prototype.activateImpl=function(e){var t=this;if(!this.adapter.isSurfaceDisabled()){var r=this.activationState;if(!r.isActivated){var n=this.previousActivationEvent;n&&void 0!==e&&n.type!==e.type||(r.isActivated=!0,r.isProgrammatic=void 0===e,r.activationEvent=e,r.wasActivatedByPointer=!r.isProgrammatic&&void 0!==e&&("mousedown"===e.type||"touchstart"===e.type||"pointerdown"===e.type),void 0!==e&&0<p.length&&p.some(function(e){return t.adapter.containsEventTarget(e)})?this.resetActivationState():(void 0!==e&&(p.push(e.target),this.registerDeactivationHandlers(e)),r.wasElementMadeActive=this.checkElementMadeActive(e),r.wasElementMadeActive&&this.animateActivation(),requestAnimationFrame(function(){p=[],r.wasElementMadeActive||void 0===e||" "!==e.key&&32!==e.keyCode||(r.wasElementMadeActive=t.checkElementMadeActive(e),r.wasElementMadeActive&&t.animateActivation()),r.wasElementMadeActive||(t.activationState=t.defaultActivationState())})))}}},v.prototype.checkElementMadeActive=function(e){return void 0===e||"keydown"!==e.type||this.adapter.isSurfaceActive()},v.prototype.animateActivation=function(){var e=this,t=v.strings,r=t.VAR_FG_TRANSLATE_START,n=t.VAR_FG_TRANSLATE_END,i=v.cssClasses,o=i.FG_DEACTIVATION,a=i.FG_ACTIVATION,s=v.numbers.DEACTIVATION_TIMEOUT_MS;this.layoutInternal();var c="",u="";if(!this.adapter.isUnbounded()){var l=this.getFgTranslationCoordinates(),d=l.startPoint,f=l.endPoint;c=d.x+"px, "+d.y+"px",u=f.x+"px, "+f.y+"px"}this.adapter.updateCssVariable(r,c),this.adapter.updateCssVariable(n,u),clearTimeout(this.activationTimer),clearTimeout(this.fgDeactivationRemovalTimer),this.rmBoundedActivationClasses(),this.adapter.removeClass(o),this.adapter.computeBoundingRect(),this.adapter.addClass(a),this.activationTimer=setTimeout(function(){e.activationTimerCallback()},s)},v.prototype.getFgTranslationCoordinates=function(){var e,t=this.activationState,r=t.activationEvent;return{startPoint:e={x:(e=t.wasActivatedByPointer?l.getNormalizedEventCoords(r,this.adapter.getWindowPageOffset(),this.adapter.computeBoundingRect()):{x:this.frame.width/2,y:this.frame.height/2}).x-this.initialSize/2,y:e.y-this.initialSize/2},endPoint:{x:this.frame.width/2-this.initialSize/2,y:this.frame.height/2-this.initialSize/2}}},v.prototype.runDeactivationUXLogicIfReady=function(){var e=this,t=v.cssClasses.FG_DEACTIVATION,r=this.activationState,n=r.hasDeactivationUXRun,i=r.isActivated;!n&&i||!this.activationAnimationHasEnded||(this.rmBoundedActivationClasses(),this.adapter.addClass(t),this.fgDeactivationRemovalTimer=setTimeout(function(){e.adapter.removeClass(t)},u.numbers.FG_DEACTIVATION_MS))},v.prototype.rmBoundedActivationClasses=function(){var e=v.cssClasses.FG_ACTIVATION;this.adapter.removeClass(e),this.activationAnimationHasEnded=!1,this.adapter.computeBoundingRect()},v.prototype.resetActivationState=function(){var e=this;this.previousActivationEvent=this.activationState.activationEvent,this.activationState=this.defaultActivationState(),setTimeout(function(){return e.previousActivationEvent=void 0},v.numbers.TAP_DELAY_MS)},v.prototype.deactivateImpl=function(){var e=this,t=this.activationState;if(t.isActivated){var r=o({},t);t.isProgrammatic?(requestAnimationFrame(function(){e.animateDeactivation(r)}),this.resetActivationState()):(this.deregisterDeactivationHandlers(),requestAnimationFrame(function(){e.activationState.hasDeactivationUXRun=!0,e.animateDeactivation(r),e.resetActivationState()}))}},v.prototype.animateDeactivation=function(e){var t=e.wasActivatedByPointer,r=e.wasElementMadeActive;(t||r)&&this.runDeactivationUXLogicIfReady()},v.prototype.layoutInternal=function(){var e=this;this.frame=this.adapter.computeBoundingRect();var t=Math.max(this.frame.height,this.frame.width);this.maxRadius=this.adapter.isUnbounded()?t:Math.sqrt(Math.pow(e.frame.width,2)+Math.pow(e.frame.height,2))+v.numbers.PADDING;var r=Math.floor(t*v.numbers.INITIAL_ORIGIN_SCALE);this.adapter.isUnbounded()&&r%2!=0?this.initialSize=r-1:this.initialSize=r,this.fgScale=""+this.maxRadius/this.initialSize,this.updateLayoutCssVars()},v.prototype.updateLayoutCssVars=function(){var e=v.strings,t=e.VAR_FG_SIZE,r=e.VAR_LEFT,n=e.VAR_TOP,i=e.VAR_FG_SCALE;this.adapter.updateCssVariable(t,this.initialSize+"px"),this.adapter.updateCssVariable(i,this.fgScale),this.adapter.isUnbounded()&&(this.unboundedCoords={left:Math.round(this.frame.width/2-this.initialSize/2),top:Math.round(this.frame.height/2-this.initialSize/2)},this.adapter.updateCssVariable(r,this.unboundedCoords.left+"px"),this.adapter.updateCssVariable(n,this.unboundedCoords.top+"px"))},v);function v(e){var t=s.call(this,o(o({},v.defaultAdapter),e))||this;return t.activationAnimationHasEnded=!1,t.activationTimer=0,t.fgDeactivationRemovalTimer=0,t.fgScale="0",t.frame={width:0,height:0},t.initialSize=0,t.layoutFrame=0,t.maxRadius=0,t.unboundedCoords={left:0,top:0},t.activationState=t.defaultActivationState(),t.activationTimerCallback=function(){t.activationAnimationHasEnded=!0,t.runDeactivationUXLogicIfReady()},t.activateHandler=function(e){t.activateImpl(e)},t.deactivateHandler=function(){t.deactivateImpl()},t.focusHandler=function(){t.handleFocus()},t.blurHandler=function(){t.handleBlur()},t.resizeHandler=function(){t.layout()},t}t.MDCRippleFoundation=h,t.default=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.applyPassive=void 0,t.applyPassive=function(e){return void 0===e&&(e=window),!!function(e){void 0===e&&(e=window);var t=!1;try{var r={get passive(){return!(t=!0)}},n=function(){};e.document.addEventListener("test",n,r),e.document.removeEventListener("test",n,r)}catch(e){t=!1}return t}(e)&&{passive:!0}}},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&o(t,e,r);return a(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCRipple=void 0;var c,u=r(13),l=r(52),d=r(49),f=r(51),p=s(r(50)),h=(c=u.MDCComponent,i(v,c),v.attachTo=function(e,t){void 0===t&&(t={isUnbounded:void 0});var r=new v(e);return void 0!==t.isUnbounded&&(r.unbounded=t.isUnbounded),r},v.createAdapter=function(r){return{addClass:function(e){r.root.classList.add(e)},browserSupportsCssVars:function(){return p.supportsCssVariables(window)},computeBoundingRect:function(){return r.root.getBoundingClientRect()},containsEventTarget:function(e){return r.root.contains(e)},deregisterDocumentInteractionHandler:function(e,t){document.documentElement.removeEventListener(e,t,l.applyPassive())},deregisterInteractionHandler:function(e,t){r.root.removeEventListener(e,t,l.applyPassive())},deregisterResizeHandler:function(e){window.removeEventListener("resize",e)},getWindowPageOffset:function(){return{x:window.pageXOffset,y:window.pageYOffset}},isSurfaceActive:function(){return d.matches(r.root,":active")},isSurfaceDisabled:function(){return Boolean(r.disabled)},isUnbounded:function(){return Boolean(r.unbounded)},registerDocumentInteractionHandler:function(e,t){document.documentElement.addEventListener(e,t,l.applyPassive())},registerInteractionHandler:function(e,t){r.root.addEventListener(e,t,l.applyPassive())},registerResizeHandler:function(e){window.addEventListener("resize",e)},removeClass:function(e){r.root.classList.remove(e)},updateCssVariable:function(e,t){r.root.style.setProperty(e,t)}}},Object.defineProperty(v.prototype,"unbounded",{get:function(){return Boolean(this.isUnbounded)},set:function(e){this.isUnbounded=Boolean(e),this.setUnbounded()},enumerable:!1,configurable:!0}),v.prototype.activate=function(){this.foundation.activate()},v.prototype.deactivate=function(){this.foundation.deactivate()},v.prototype.layout=function(){this.foundation.layout()},v.prototype.getDefaultFoundation=function(){return new f.MDCRippleFoundation(v.createAdapter(this))},v.prototype.initialSyncWithDOM=function(){var e=this.root;this.isUnbounded="mdcRippleIsUnbounded"in e.dataset},v.prototype.setUnbounded=function(){this.foundation.setUnbounded(Boolean(this.isUnbounded))},v);function v(){var e=null!==c&&c.apply(this,arguments)||this;return e.disabled=!1,e}t.MDCRipple=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.numbers=t.strings=t.cssClasses=void 0,t.cssClasses={BG_FOCUSED:"mdc-ripple-upgraded--background-focused",FG_ACTIVATION:"mdc-ripple-upgraded--foreground-activation",FG_DEACTIVATION:"mdc-ripple-upgraded--foreground-deactivation",ROOT:"mdc-ripple-upgraded",UNBOUNDED:"mdc-ripple-upgraded--unbounded"},t.strings={VAR_FG_SCALE:"--mdc-ripple-fg-scale",VAR_FG_SIZE:"--mdc-ripple-fg-size",VAR_FG_TRANSLATE_END:"--mdc-ripple-fg-translate-end",VAR_FG_TRANSLATE_START:"--mdc-ripple-fg-translate-start",VAR_LEFT:"--mdc-ripple-left",VAR_TOP:"--mdc-ripple-top"},t.numbers={DEACTIVATION_TIMEOUT_MS:225,FG_DEACTIVATION_MS:150,INITIAL_ORIGIN_SCALE:.6,PADDING:10,TAP_DELAY_MS:300}},,,,,,,,,function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AnimationFrame=void 0;var n=(i.prototype.request=function(t,r){var n=this;this.cancel(t);var e=requestAnimationFrame(function(e){n.rafIDs.delete(t),r(e)});this.rafIDs.set(t,e)},i.prototype.cancel=function(e){var t=this.rafIDs.get(e);t&&(cancelAnimationFrame(t),this.rafIDs.delete(e))},i.prototype.cancelAll=function(){var r=this;this.rafIDs.forEach(function(e,t){r.cancel(t)})},i.prototype.getQueue=function(){var r=[];return this.rafIDs.forEach(function(e,t){r.push(t)}),r},i);function i(){this.rafIDs=new Map}t.AnimationFrame=n},,,,,function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FocusTrap=void 0;var o="mdc-dom-focus-sentinel",n=(i.prototype.trapFocus=function(){var e=this.getFocusableElements(this.root);if(0===e.length)throw new Error("FocusTrap: Element must have at least one focusable child.");this.elFocusedBeforeTrapFocus=document.activeElement instanceof HTMLElement?document.activeElement:null,this.wrapTabFocus(this.root),this.options.skipInitialFocus||this.focusInitialElement(e,this.options.initialFocusEl)},i.prototype.releaseFocus=function(){Array.from(this.root.querySelectorAll("."+o)).forEach(function(e){e.parentElement.removeChild(e)}),!this.options.skipRestoreFocus&&this.elFocusedBeforeTrapFocus&&this.elFocusedBeforeTrapFocus.focus()},i.prototype.wrapTabFocus=function(t){var r=this,e=this.createSentinel(),n=this.createSentinel();e.addEventListener("focus",function(){var e=r.getFocusableElements(t);0<e.length&&e[e.length-1].focus()}),n.addEventListener("focus",function(){var e=r.getFocusableElements(t);0<e.length&&e[0].focus()}),t.insertBefore(e,t.children[0]),t.appendChild(n)},i.prototype.focusInitialElement=function(e,t){var r=0;t&&(r=Math.max(e.indexOf(t),0)),e[r].focus()},i.prototype.getFocusableElements=function(e){return Array.from(e.querySelectorAll("[autofocus], [tabindex], a, input, textarea, select, button")).filter(function(e){var t="true"===e.getAttribute("aria-disabled")||null!=e.getAttribute("disabled")||null!=e.getAttribute("hidden")||"true"===e.getAttribute("aria-hidden"),r=0<=e.tabIndex&&0<e.getBoundingClientRect().width&&!e.classList.contains(o)&&!t,n=!1;if(r){var i=getComputedStyle(e);n="none"===i.display||"hidden"===i.visibility}return r&&!n})},i.prototype.createSentinel=function(){var e=document.createElement("div");return e.setAttribute("tabindex","0"),e.setAttribute("aria-hidden","true"),e.classList.add(o),e},i);function i(e,t){void 0===t&&(t={}),this.root=e,this.options=t,this.elFocusedBeforeTrapFocus=null}t.FocusTrap=n},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.areTopsMisaligned=t.isScrollAtBottom=t.isScrollAtTop=t.isScrollable=t.createFocusTrapInstance=void 0,t.createFocusTrapInstance=function(e,t,r){return t(e,{initialFocusEl:r})},t.isScrollable=function(e){return!!e&&e.scrollHeight>e.offsetHeight},t.isScrollAtTop=function(e){return!!e&&0===e.scrollTop},t.isScrollAtBottom=function(e){return!!e&&Math.ceil(e.scrollHeight-e.scrollTop)===e.clientHeight},t.areTopsMisaligned=function(e){var t=new Set;return[].forEach.call(e,function(e){return t.add(e.offsetTop)}),1<t.size}},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCDialogFoundation=void 0;var a,s,c=r(63),u=r(7),l=r(141);(s=a=a||{}).POLL_SCROLL_POS="poll_scroll_position",s.POLL_LAYOUT_CHANGE="poll_layout_change";var d,f=(d=u.MDCFoundation,i(p,d),Object.defineProperty(p,"cssClasses",{get:function(){return l.cssClasses},enumerable:!1,configurable:!0}),Object.defineProperty(p,"strings",{get:function(){return l.strings},enumerable:!1,configurable:!0}),Object.defineProperty(p,"numbers",{get:function(){return l.numbers},enumerable:!1,configurable:!0}),Object.defineProperty(p,"defaultAdapter",{get:function(){return{addBodyClass:function(){},addClass:function(){},areButtonsStacked:function(){return!1},clickDefaultButton:function(){},eventTargetMatches:function(){return!1},getActionFromEvent:function(){return""},getInitialFocusEl:function(){return null},hasClass:function(){return!1},isContentScrollable:function(){return!1},notifyClosed:function(){},notifyClosing:function(){},notifyOpened:function(){},notifyOpening:function(){},releaseFocus:function(){},removeBodyClass:function(){},removeClass:function(){},reverseButtons:function(){},trapFocus:function(){},registerContentEventHandler:function(){},deregisterContentEventHandler:function(){},isScrollableContentAtTop:function(){return!1},isScrollableContentAtBottom:function(){return!1},registerWindowEventHandler:function(){},deregisterWindowEventHandler:function(){}}},enumerable:!1,configurable:!0}),p.prototype.init=function(){this.adapter.hasClass(l.cssClasses.STACKED)&&this.setAutoStackButtons(!1),this.isFullscreen=this.adapter.hasClass(l.cssClasses.FULLSCREEN)},p.prototype.destroy=function(){this.animationTimer&&(clearTimeout(this.animationTimer),this.handleAnimationTimerEnd()),this.isFullscreen&&this.adapter.deregisterContentEventHandler("scroll",this.contentScrollHandler),this.animFrame.cancelAll(),this.adapter.deregisterWindowEventHandler("resize",this.windowResizeHandler),this.adapter.deregisterWindowEventHandler("orientationchange",this.windowOrientationChangeHandler)},p.prototype.open=function(e){var t=this;this.dialogOpen=!0,this.adapter.notifyOpening(),this.adapter.addClass(l.cssClasses.OPENING),this.isFullscreen&&this.adapter.registerContentEventHandler("scroll",this.contentScrollHandler),e&&e.isAboveFullscreenDialog&&this.adapter.addClass(l.cssClasses.SCRIM_HIDDEN),this.adapter.registerWindowEventHandler("resize",this.windowResizeHandler),this.adapter.registerWindowEventHandler("orientationchange",this.windowOrientationChangeHandler),this.runNextAnimationFrame(function(){t.adapter.addClass(l.cssClasses.OPEN),e&&e.isScrimless||t.adapter.addBodyClass(l.cssClasses.SCROLL_LOCK),t.layout(),t.animationTimer=setTimeout(function(){t.handleAnimationTimerEnd(),t.adapter.trapFocus(t.adapter.getInitialFocusEl()),t.adapter.notifyOpened()},l.numbers.DIALOG_ANIMATION_OPEN_TIME_MS)})},p.prototype.close=function(e){var t=this;void 0===e&&(e=""),this.dialogOpen&&(this.dialogOpen=!1,this.adapter.notifyClosing(e),this.adapter.addClass(l.cssClasses.CLOSING),this.adapter.removeClass(l.cssClasses.OPEN),this.adapter.removeBodyClass(l.cssClasses.SCROLL_LOCK),this.isFullscreen&&this.adapter.deregisterContentEventHandler("scroll",this.contentScrollHandler),this.adapter.deregisterWindowEventHandler("resize",this.windowResizeHandler),this.adapter.deregisterWindowEventHandler("orientationchange",this.windowOrientationChangeHandler),cancelAnimationFrame(this.animationFrame),this.animationFrame=0,clearTimeout(this.animationTimer),this.animationTimer=setTimeout(function(){t.adapter.releaseFocus(),t.handleAnimationTimerEnd(),t.adapter.notifyClosed(e)},l.numbers.DIALOG_ANIMATION_CLOSE_TIME_MS))},p.prototype.showSurfaceScrim=function(){var e=this;this.adapter.addClass(l.cssClasses.SURFACE_SCRIM_SHOWING),this.runNextAnimationFrame(function(){e.adapter.addClass(l.cssClasses.SURFACE_SCRIM_SHOWN)})},p.prototype.hideSurfaceScrim=function(){this.adapter.removeClass(l.cssClasses.SURFACE_SCRIM_SHOWN),this.adapter.addClass(l.cssClasses.SURFACE_SCRIM_HIDING)},p.prototype.handleSurfaceScrimTransitionEnd=function(){this.adapter.removeClass(l.cssClasses.SURFACE_SCRIM_HIDING),this.adapter.removeClass(l.cssClasses.SURFACE_SCRIM_SHOWING)},p.prototype.isOpen=function(){return this.dialogOpen},p.prototype.getEscapeKeyAction=function(){return this.escapeKeyAction},p.prototype.setEscapeKeyAction=function(e){this.escapeKeyAction=e},p.prototype.getScrimClickAction=function(){return this.scrimClickAction},p.prototype.setScrimClickAction=function(e){this.scrimClickAction=e},p.prototype.getAutoStackButtons=function(){return this.autoStackButtons},p.prototype.setAutoStackButtons=function(e){this.autoStackButtons=e},p.prototype.getSuppressDefaultPressSelector=function(){return this.suppressDefaultPressSelector},p.prototype.setSuppressDefaultPressSelector=function(e){this.suppressDefaultPressSelector=e},p.prototype.layout=function(){var e=this;this.animFrame.request(a.POLL_LAYOUT_CHANGE,function(){e.layoutInternal()})},p.prototype.handleClick=function(e){if(this.adapter.eventTargetMatches(e.target,l.strings.SCRIM_SELECTOR)&&""!==this.scrimClickAction)this.close(this.scrimClickAction);else{var t=this.adapter.getActionFromEvent(e);t&&this.close(t)}},p.prototype.handleKeydown=function(e){var t="Enter"===e.key||13===e.keyCode;if(t&&!this.adapter.getActionFromEvent(e)){var r=e.composedPath?e.composedPath()[0]:e.target,n=!this.suppressDefaultPressSelector||!this.adapter.eventTargetMatches(r,this.suppressDefaultPressSelector);t&&n&&this.adapter.clickDefaultButton()}},p.prototype.handleDocumentKeydown=function(e){"Escape"!==e.key&&27!==e.keyCode||""===this.escapeKeyAction||this.close(this.escapeKeyAction)},p.prototype.handleScrollEvent=function(){var e=this;this.animFrame.request(a.POLL_SCROLL_POS,function(){e.toggleScrollDividerHeader(),e.toggleScrollDividerFooter()})},p.prototype.layoutInternal=function(){this.autoStackButtons&&this.detectStackedButtons(),this.toggleScrollableClasses()},p.prototype.handleAnimationTimerEnd=function(){this.animationTimer=0,this.adapter.removeClass(l.cssClasses.OPENING),this.adapter.removeClass(l.cssClasses.CLOSING)},p.prototype.runNextAnimationFrame=function(e){var t=this;cancelAnimationFrame(this.animationFrame),this.animationFrame=requestAnimationFrame(function(){t.animationFrame=0,clearTimeout(t.animationTimer),t.animationTimer=setTimeout(e,0)})},p.prototype.detectStackedButtons=function(){this.adapter.removeClass(l.cssClasses.STACKED);var e=this.adapter.areButtonsStacked();e&&this.adapter.addClass(l.cssClasses.STACKED),e!==this.areButtonsStacked&&(this.adapter.reverseButtons(),this.areButtonsStacked=e)},p.prototype.toggleScrollableClasses=function(){this.adapter.removeClass(l.cssClasses.SCROLLABLE),this.adapter.isContentScrollable()&&(this.adapter.addClass(l.cssClasses.SCROLLABLE),this.isFullscreen&&(this.toggleScrollDividerHeader(),this.toggleScrollDividerFooter()))},p.prototype.toggleScrollDividerHeader=function(){this.adapter.isScrollableContentAtTop()?this.adapter.hasClass(l.cssClasses.SCROLL_DIVIDER_HEADER)&&this.adapter.removeClass(l.cssClasses.SCROLL_DIVIDER_HEADER):this.adapter.addClass(l.cssClasses.SCROLL_DIVIDER_HEADER)},p.prototype.toggleScrollDividerFooter=function(){this.adapter.isScrollableContentAtBottom()?this.adapter.hasClass(l.cssClasses.SCROLL_DIVIDER_FOOTER)&&this.adapter.removeClass(l.cssClasses.SCROLL_DIVIDER_FOOTER):this.adapter.addClass(l.cssClasses.SCROLL_DIVIDER_FOOTER)},p);function p(e){var t=d.call(this,o(o({},p.defaultAdapter),e))||this;return t.dialogOpen=!1,t.isFullscreen=!1,t.animationFrame=0,t.animationTimer=0,t.escapeKeyAction=l.strings.CLOSE_ACTION,t.scrimClickAction=l.strings.CLOSE_ACTION,t.autoStackButtons=!0,t.areButtonsStacked=!1,t.suppressDefaultPressSelector=l.strings.SUPPRESS_DEFAULT_PRESS_SELECTOR,t.animFrame=new c.AnimationFrame,t.contentScrollHandler=function(){t.handleScrollEvent()},t.windowResizeHandler=function(){t.layout()},t.windowOrientationChangeHandler=function(){t.layout()},t}t.MDCDialogFoundation=f,t.default=f},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.numbers=t.strings=t.cssClasses=void 0,t.cssClasses={CLOSING:"mdc-dialog--closing",OPEN:"mdc-dialog--open",OPENING:"mdc-dialog--opening",SCROLLABLE:"mdc-dialog--scrollable",SCROLL_LOCK:"mdc-dialog-scroll-lock",STACKED:"mdc-dialog--stacked",FULLSCREEN:"mdc-dialog--fullscreen",SCROLL_DIVIDER_HEADER:"mdc-dialog-scroll-divider-header",SCROLL_DIVIDER_FOOTER:"mdc-dialog-scroll-divider-footer",SURFACE_SCRIM_SHOWN:"mdc-dialog__surface-scrim--shown",SURFACE_SCRIM_SHOWING:"mdc-dialog__surface-scrim--showing",SURFACE_SCRIM_HIDING:"mdc-dialog__surface-scrim--hiding",SCRIM_HIDDEN:"mdc-dialog__scrim--hidden"},t.strings={ACTION_ATTRIBUTE:"data-mdc-dialog-action",BUTTON_DEFAULT_ATTRIBUTE:"data-mdc-dialog-button-default",BUTTON_SELECTOR:".mdc-dialog__button",CLOSED_EVENT:"MDCDialog:closed",CLOSE_ACTION:"close",CLOSING_EVENT:"MDCDialog:closing",CONTAINER_SELECTOR:".mdc-dialog__container",CONTENT_SELECTOR:".mdc-dialog__content",DESTROY_ACTION:"destroy",INITIAL_FOCUS_ATTRIBUTE:"data-mdc-dialog-initial-focus",OPENED_EVENT:"MDCDialog:opened",OPENING_EVENT:"MDCDialog:opening",SCRIM_SELECTOR:".mdc-dialog__scrim",SUPPRESS_DEFAULT_PRESS_SELECTOR:["textarea",".mdc-menu .mdc-list-item",".mdc-menu .mdc-deprecated-list-item"].join(", "),SURFACE_SELECTOR:".mdc-dialog__surface"},t.numbers={DIALOG_ANIMATION_CLOSE_TIME_MS:75,DIALOG_ANIMATION_OPEN_TIME_MS:150}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return i(t,e),t},a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.util=void 0;var s=o(r(139));t.util=s,a(r(223),t),a(r(224),t),a(r(141),t),a(r(140),t),a(r(225),t)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})},function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&o(t,e,r);return a(t,e),t},c=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(t,"__esModule",{value:!0}),t.MDCDialog=void 0;var u,l=r(13),d=r(68),f=r(49),p=r(53),h=r(140),v=s(r(139)),y=h.MDCDialogFoundation.strings,m=(u=l.MDCComponent,i(S,u),Object.defineProperty(S.prototype,"isOpen",{get:function(){return this.foundation.isOpen()},enumerable:!1,configurable:!0}),Object.defineProperty(S.prototype,"escapeKeyAction",{get:function(){return this.foundation.getEscapeKeyAction()},set:function(e){this.foundation.setEscapeKeyAction(e)},enumerable:!1,configurable:!0}),Object.defineProperty(S.prototype,"scrimClickAction",{get:function(){return this.foundation.getScrimClickAction()},set:function(e){this.foundation.setScrimClickAction(e)},enumerable:!1,configurable:!0}),Object.defineProperty(S.prototype,"autoStackButtons",{get:function(){return this.foundation.getAutoStackButtons()},set:function(e){this.foundation.setAutoStackButtons(e)},enumerable:!1,configurable:!0}),S.attachTo=function(e){return new S(e)},S.prototype.initialize=function(e){var t,r;void 0===e&&(e=function(e,t){return new d.FocusTrap(e,t)});var n=this.root.querySelector(y.CONTAINER_SELECTOR);if(!n)throw new Error("Dialog component requires a "+y.CONTAINER_SELECTOR+" container element");this.container=n,this.content=this.root.querySelector(y.CONTENT_SELECTOR),this.buttons=Array.from(this.root.querySelectorAll(y.BUTTON_SELECTOR)),this.defaultButton=this.root.querySelector("["+y.BUTTON_DEFAULT_ATTRIBUTE+"]"),this.focusTrapFactory=e,this.buttonRipples=[];try{for(var i=c(this.buttons),o=i.next();!o.done;o=i.next()){var a=o.value;this.buttonRipples.push(new p.MDCRipple(a))}}catch(e){t={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(t)throw t.error}}},S.prototype.initialSyncWithDOM=function(){var e=this;this.focusTrap=v.createFocusTrapInstance(this.container,this.focusTrapFactory,this.getInitialFocusEl()||void 0),this.handleClick=this.foundation.handleClick.bind(this.foundation),this.handleKeydown=this.foundation.handleKeydown.bind(this.foundation),this.handleDocumentKeydown=this.foundation.handleDocumentKeydown.bind(this.foundation),this.handleOpening=function(){document.addEventListener("keydown",e.handleDocumentKeydown)},this.handleClosing=function(){document.removeEventListener("keydown",e.handleDocumentKeydown)},this.listen("click",this.handleClick),this.listen("keydown",this.handleKeydown),this.listen(y.OPENING_EVENT,this.handleOpening),this.listen(y.CLOSING_EVENT,this.handleClosing)},S.prototype.destroy=function(){this.unlisten("click",this.handleClick),this.unlisten("keydown",this.handleKeydown),this.unlisten(y.OPENING_EVENT,this.handleOpening),this.unlisten(y.CLOSING_EVENT,this.handleClosing),this.handleClosing(),this.buttonRipples.forEach(function(e){e.destroy()}),u.prototype.destroy.call(this)},S.prototype.layout=function(){this.foundation.layout()},S.prototype.open=function(){this.foundation.open()},S.prototype.close=function(e){void 0===e&&(e=""),this.foundation.close(e)},S.prototype.getDefaultFoundation=function(){var r=this,e={addBodyClass:function(e){document.body.classList.add(e)},addClass:function(e){r.root.classList.add(e)},areButtonsStacked:function(){return v.areTopsMisaligned(r.buttons)},clickDefaultButton:function(){r.defaultButton&&!r.defaultButton.disabled&&r.defaultButton.click()},eventTargetMatches:function(e,t){return!!e&&f.matches(e,t)},getActionFromEvent:function(e){if(!e.target)return"";var t=f.closest(e.target,"["+y.ACTION_ATTRIBUTE+"]");return t&&t.getAttribute(y.ACTION_ATTRIBUTE)},getInitialFocusEl:function(){return r.getInitialFocusEl()},hasClass:function(e){return r.root.classList.contains(e)},isContentScrollable:function(){return v.isScrollable(r.content)},notifyClosed:function(e){r.emit(y.CLOSED_EVENT,e?{action:e}:{})},notifyClosing:function(e){r.emit(y.CLOSING_EVENT,e?{action:e}:{})},notifyOpened:function(){r.emit(y.OPENED_EVENT,{})},notifyOpening:function(){r.emit(y.OPENING_EVENT,{})},releaseFocus:function(){r.focusTrap.releaseFocus()},removeBodyClass:function(e){document.body.classList.remove(e)},removeClass:function(e){r.root.classList.remove(e)},reverseButtons:function(){r.buttons.reverse(),r.buttons.forEach(function(e){e.parentElement.appendChild(e)})},trapFocus:function(){r.focusTrap.trapFocus()},registerContentEventHandler:function(e,t){r.content instanceof HTMLElement&&r.content.addEventListener(e,t)},deregisterContentEventHandler:function(e,t){r.content instanceof HTMLElement&&r.content.removeEventListener(e,t)},isScrollableContentAtTop:function(){return v.isScrollAtTop(r.content)},isScrollableContentAtBottom:function(){return v.isScrollAtBottom(r.content)},registerWindowEventHandler:function(e,t){window.addEventListener(e,t)},deregisterWindowEventHandler:function(e,t){window.removeEventListener(e,t)}};return new h.MDCDialogFoundation(e)},S.prototype.getInitialFocusEl=function(){return this.root.querySelector("["+y.INITIAL_FOCUS_ATTRIBUTE+"]")},S);function S(){return null!==u&&u.apply(this,arguments)||this}t.MDCDialog=m},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0})}],i.c=n,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=222);function i(e){if(n[e])return n[e].exports;var t=n[e]={i:e,l:!1,exports:{}};return r[e].call(t.exports,t,t.exports,i),t.l=!0,t.exports}var r,n});