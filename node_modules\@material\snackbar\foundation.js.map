{"version": 3, "file": "foundation.js", "sourceRoot": "", "sources": ["foundation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;;AAEH,OAAO,EAAC,aAAa,EAAC,MAAM,2BAA2B,CAAC;AAGxD,OAAO,EAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAC,MAAM,aAAa,CAAC;AAElD,IAAA,OAAO,GAAmB,UAAU,QAA7B,EAAE,IAAI,GAAa,UAAU,KAAvB,EAAE,OAAO,GAAI,UAAU,QAAd,CAAe;AACrC,IAAA,aAAa,GAA6C,OAAO,cAApD,EAAE,cAAc,GAA6B,OAAO,eAApC,EAAE,uBAAuB,GAAI,OAAO,wBAAX,CAAY;AAEzE,8BAA8B;AAC9B;IAA2C,yCAAiC;IAgC1E,+BAAY,OAAqC;QAAjD,YACE,wCAAU,qBAAqB,CAAC,cAAc,GAAK,OAAO,EAAE,SAC7D;QATO,YAAM,GAAG,KAAK,CAAC;QACf,oBAAc,GAAG,CAAC,CAAC;QACnB,oBAAc,GAAG,CAAC,CAAC;QACnB,sBAAgB,GAAG,CAAC,CAAC;QACrB,0BAAoB,GAAG,OAAO,CAAC,+BAA+B,CAAC;QAC/D,mBAAa,GAAG,IAAI,CAAC;;IAI7B,CAAC;IAjCD,sBAAoB,mCAAU;aAA9B;YACE,OAAO,UAAU,CAAC;QACpB,CAAC;;;OAAA;IAED,sBAAoB,gCAAO;aAA3B;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAoB,gCAAO;aAA3B;YACE,OAAO,OAAO,CAAC;QACjB,CAAC;;;OAAA;IAED,sBAAoB,uCAAc;aAAlC;YACE,OAAO;gBACL,QAAQ,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACzB,QAAQ,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBACzB,YAAY,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC7B,aAAa,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC9B,YAAY,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC7B,aAAa,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;gBAC9B,WAAW,EAAE,cAAM,OAAA,SAAS,EAAT,CAAS;aAC7B,CAAC;QACJ,CAAC;;;OAAA;IAaQ,uCAAO,GAAhB;QACE,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED,oCAAI,GAAJ;QAAA,iBAwBC;QAvBC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QAExB,wEAAwE;QACxE,YAAY;QACZ,IAAI,CAAC,qBAAqB,CAAC;YACzB,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAE5B,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC;gBAC/B,IAAM,SAAS,GAAG,KAAI,CAAC,YAAY,EAAE,CAAC;gBACtC,KAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,KAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC5B,IAAI,SAAS,KAAK,OAAO,CAAC,aAAa,EAAE;oBACvC,KAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC;wBACjC,KAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;oBAC7B,CAAC,EAAE,SAAS,CAAC,CAAC;iBACf;YACH,CAAC,EAAE,OAAO,CAAC,+BAA+B,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,qCAAK,GAAL,UAAM,MAAW;QAAjB,iBAsBC;QAtBK,uBAAA,EAAA,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,0EAA0E;YAC1E,mCAAmC;YACnC,OAAO;SACR;QAED,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAE7B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE7C,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC;YAC/B,KAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,KAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,EAAE,OAAO,CAAC,gCAAgC,CAAC,CAAC;IAC/C,CAAC;IAED,sCAAM,GAAN;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,4CAAY,GAAZ;QACE,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED,4CAAY,GAAZ,UAAa,SAAiB;QAC5B,4DAA4D;QAC5D,IAAM,QAAQ,GAAG,OAAO,CAAC,2BAA2B,CAAC;QACrD,IAAM,QAAQ,GAAG,OAAO,CAAC,2BAA2B,CAAC;QACrD,IAAM,kBAAkB,GAAG,OAAO,CAAC,aAAa,CAAC;QAEjD,IAAI,SAAS,KAAK,OAAO,CAAC,aAAa;YACnC,CAAC,SAAS,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,CAAC,EAAE;YACpD,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;SACvC;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,yDAC8B,QAAQ,cAAI,QAAQ,sBAC1D,kBAAkB,+BAA0B,SAAS,MAAG,CAAC,CAAC;SACnE;IACH,CAAC;IAED,gDAAgB,GAAhB;QACE,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,gDAAgB,GAAhB,UAAiB,aAAsB;QACrC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,6CAAa,GAAb,UAAc,KAAoB;QAChC,IAAM,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,KAAK,EAAE,CAAC;QACnE,IAAI,WAAW,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;YAC1C,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;SAC5B;IACH,CAAC;IAED,uDAAuB,GAAvB,UAAwB,MAAkB;QACxC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IAC5B,CAAC;IAED,qDAAqB,GAArB,UAAsB,MAAkB;QACtC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;IAC7B,CAAC;IAED,gEAAgC,GAAhC,UAAiC,MAAkB;QACjD,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;IACtC,CAAC;IAEO,qDAAqB,GAA7B;QACE,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACpC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;IAC5B,CAAC;IAEO,uDAAuB,GAA/B;QACE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAC/C,CAAC;IAED;;;OAGG;IACK,qDAAqB,GAA7B,UAA8B,QAAoB;QAAlD,iBAOC;QANC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC1C,IAAI,CAAC,cAAc,GAAG,qBAAqB,CAAC;YAC1C,KAAI,CAAC,cAAc,GAAG,CAAC,CAAC;YACxB,YAAY,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC;YAClC,KAAI,CAAC,cAAc,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IACH,4BAAC;AAAD,CAAC,AAjLD,CAA2C,aAAa,GAiLvD;;AAED,iHAAiH;AACjH,eAAe,qBAAqB,CAAC"}