{"version": 3, "file": "async.mjs", "sources": ["../../../../../../../packages/platform-browser/animations/async/src/async_animation_renderer.ts", "../../../../../../../packages/platform-browser/animations/async/src/providers.ts", "../../../../../../../packages/platform-browser/animations/async/src/async-animations.ts", "../../../../../../../packages/platform-browser/animations/async/public_api.ts", "../../../../../../../packages/platform-browser/animations/async/index.ts", "../../../../../../../packages/platform-browser/animations/async/async.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵAnimationEngine as AnimationEngine, ɵAnimationRenderer as AnimationRenderer, ɵAnimationRendererFactory as AnimationRendererFactory} from '@angular/animations/browser';\nimport {inject, Injectable, NgZone, OnDestroy, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2, ɵAnimationRendererType as AnimationRendererType, ɵChangeDetectionScheduler as ChangeDetectionScheduler, ɵRuntimeError as RuntimeError} from '@angular/core';\nimport {ɵRuntimeErrorCode as RuntimeErrorCode} from '@angular/platform-browser';\n\nconst ANIMATION_PREFIX = '@';\n\n@Injectable()\nexport class AsyncAnimationRendererFactory implements OnDestroy, RendererFactory2 {\n  private _rendererFactoryPromise: Promise<AnimationRendererFactory>|null = null;\n  private readonly scheduler = inject(ChangeDetectionScheduler, {optional: true});\n  private _engine?: AnimationEngine;\n\n  /**\n   *\n   * @param moduleImpl allows to provide a mock implmentation (or will load the animation module)\n   */\n  constructor(\n      private doc: Document, private delegate: RendererFactory2, private zone: NgZone,\n      private animationType: 'animations'|'noop', private moduleImpl?: Promise<{\n        ɵcreateEngine:\n            (type: 'animations'|'noop', doc: Document,\n             scheduler: ChangeDetectionScheduler|null) => AnimationEngine,\n        ɵAnimationRendererFactory: typeof AnimationRendererFactory\n      }>) {}\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    // When the root view is removed, the renderer defers the actual work to the\n    // `TransitionAnimationEngine` to do this, and the `TransitionAnimationEngine` doesn't actually\n    // remove the DOM node, but just calls `markElementAsRemoved()`. The actual DOM node is not\n    // removed until `TransitionAnimationEngine` \"flushes\".\n    // Note: we already flush on destroy within the `InjectableAnimationEngine`. The injectable\n    // engine is not provided when async animations are used.\n    this._engine?.flush();\n  }\n\n  /**\n   * @internal\n   */\n  private loadImpl(): Promise<AnimationRendererFactory> {\n    const moduleImpl = this.moduleImpl ?? import('@angular/animations/browser');\n\n    return moduleImpl\n        .catch((e) => {\n          throw new RuntimeError(\n              RuntimeErrorCode.ANIMATION_RENDERER_ASYNC_LOADING_FAILURE,\n              (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                  'Async loading for animations package was ' +\n                      'enabled, but loading failed. Angular falls back to using regular rendering. ' +\n                      'No animations will be displayed and their styles won\\'t be applied.');\n        })\n        .then(({ɵcreateEngine, ɵAnimationRendererFactory}) => {\n          // We can't create the renderer yet because we might need the hostElement and the type\n          // Both are provided in createRenderer().\n          this._engine = ɵcreateEngine(this.animationType, this.doc, this.scheduler);\n          const rendererFactory =\n              new ɵAnimationRendererFactory(this.delegate, this._engine, this.zone);\n          this.delegate = rendererFactory;\n          return rendererFactory;\n        });\n  }\n\n  /**\n   * This method is delegating the renderer creation to the factories.\n   * It uses default factory while the animation factory isn't loaded\n   * and will rely on the animation factory once it is loaded.\n   *\n   * Calling this method will trigger as side effect the loading of the animation module\n   * if the renderered component uses animations.\n   */\n  createRenderer(hostElement: any, rendererType: RendererType2): Renderer2 {\n    const renderer = this.delegate.createRenderer(hostElement, rendererType);\n\n    if ((renderer as AnimationRenderer).ɵtype === AnimationRendererType.Regular) {\n      // The factory is already loaded, this is an animation renderer\n      return renderer;\n    }\n\n    // We need to prevent the DomRenderer to throw an error because of synthetic properties\n    if (typeof (renderer as any).throwOnSyntheticProps === 'boolean') {\n      (renderer as any).throwOnSyntheticProps = false;\n    }\n\n    // Using a dynamic renderer to switch the renderer implementation once the module is loaded.\n    const dynamicRenderer = new DynamicDelegationRenderer(renderer);\n\n    // Kick off the module loading if the component uses animations but the module hasn't been\n    // loaded yet.\n    if (rendererType?.data?.['animation'] && !this._rendererFactoryPromise) {\n      this._rendererFactoryPromise = this.loadImpl();\n    }\n\n    this._rendererFactoryPromise\n        ?.then((animationRendererFactory) => {\n          const animationRenderer =\n              animationRendererFactory.createRenderer(hostElement, rendererType);\n          dynamicRenderer.use(animationRenderer);\n        })\n        .catch(e => {\n          // Permanently use regular renderer when loading fails.\n          dynamicRenderer.use(renderer);\n        });\n\n    return dynamicRenderer;\n  }\n\n  begin(): void {\n    this.delegate.begin?.();\n  }\n\n  end(): void {\n    this.delegate.end?.();\n  }\n\n  whenRenderingDone?(): Promise<any> {\n    return this.delegate.whenRenderingDone?.() ?? Promise.resolve();\n  }\n}\n\n/**\n * The class allows to dynamicly switch between different renderer implementations\n * by changing the delegate renderer.\n */\nexport class DynamicDelegationRenderer implements Renderer2 {\n  // List of callbacks that need to be replayed on the animation renderer once its loaded\n  private replay: ((renderer: Renderer2) => void)[]|null = [];\n  readonly ɵtype = AnimationRendererType.Delegated;\n\n  constructor(private delegate: Renderer2) {}\n\n  use(impl: Renderer2) {\n    this.delegate = impl;\n\n    if (this.replay !== null) {\n      // Replay queued actions using the animation renderer to apply\n      // all events and properties collected while loading was in progress.\n      for (const fn of this.replay) {\n        fn(impl);\n      }\n      // Set to `null` to indicate that the queue was processed\n      // and we no longer need to collect events and properties.\n      this.replay = null;\n    }\n  }\n\n  get data(): {[key: string]: any} {\n    return this.delegate.data;\n  }\n\n  destroy(): void {\n    this.replay = null;\n    this.delegate.destroy();\n  }\n\n  createElement(name: string, namespace?: string|null) {\n    return this.delegate.createElement(name, namespace);\n  }\n\n  createComment(value: string): void {\n    return this.delegate.createComment(value);\n  }\n\n  createText(value: string): any {\n    return this.delegate.createText(value);\n  }\n\n  get destroyNode(): ((node: any) => void)|null {\n    return this.delegate.destroyNode;\n  }\n\n  appendChild(parent: any, newChild: any): void {\n    this.delegate.appendChild(parent, newChild);\n  }\n\n  insertBefore(parent: any, newChild: any, refChild: any, isMove?: boolean|undefined): void {\n    this.delegate.insertBefore(parent, newChild, refChild, isMove);\n  }\n\n  removeChild(parent: any, oldChild: any, isHostElement?: boolean|undefined): void {\n    this.delegate.removeChild(parent, oldChild, isHostElement);\n  }\n\n  selectRootElement(selectorOrNode: any, preserveContent?: boolean|undefined): any {\n    return this.delegate.selectRootElement(selectorOrNode, preserveContent);\n  }\n\n  parentNode(node: any): any {\n    return this.delegate.parentNode(node);\n  }\n\n  nextSibling(node: any): any {\n    return this.delegate.nextSibling(node);\n  }\n\n  setAttribute(el: any, name: string, value: string, namespace?: string|null|undefined): void {\n    this.delegate.setAttribute(el, name, value, namespace);\n  }\n\n  removeAttribute(el: any, name: string, namespace?: string|null|undefined): void {\n    this.delegate.removeAttribute(el, name, namespace);\n  }\n\n  addClass(el: any, name: string): void {\n    this.delegate.addClass(el, name);\n  }\n\n  removeClass(el: any, name: string): void {\n    this.delegate.removeClass(el, name);\n  }\n\n  setStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.setStyle(el, style, value, flags);\n  }\n\n  removeStyle(el: any, style: string, flags?: RendererStyleFlags2|undefined): void {\n    this.delegate.removeStyle(el, style, flags);\n  }\n\n  setProperty(el: any, name: string, value: any): void {\n    // We need to keep track of animation properties set on default renderer\n    // So we can also set them also on the animation renderer\n    if (this.shouldReplay(name)) {\n      this.replay!.push((renderer: Renderer2) => renderer.setProperty(el, name, value));\n    }\n    this.delegate.setProperty(el, name, value);\n  }\n\n  setValue(node: any, value: string): void {\n    this.delegate.setValue(node, value);\n  }\n\n  listen(target: any, eventName: string, callback: (event: any) => boolean | void): () => void {\n    // We need to keep track of animation events registred by the default renderer\n    // So we can also register them against the animation renderer\n    if (this.shouldReplay(eventName)) {\n      this.replay!.push((renderer: Renderer2) => renderer.listen(target, eventName, callback));\n    }\n    return this.delegate.listen(target, eventName, callback);\n  }\n\n  private shouldReplay(propOrEventName: string): boolean {\n    //`null` indicates that we no longer need to collect events and properties\n    return this.replay !== null && propOrEventName.startsWith(ANIMATION_PREFIX);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {ANIMATION_MODULE_TYPE, EnvironmentProviders, makeEnvironmentProviders, NgZone, RendererFactory2, ɵperformanceMarkFeature as performanceMarkFeature} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '@angular/platform-browser';\n\nimport {AsyncAnimationRendererFactory} from './async_animation_renderer';\n\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * When you use this function instead of the eager `provideAnimations()`, animations won't be\n * renderered until the renderer is loaded.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimationsAsync()\n *   ]\n * });\n * ```\n *\n * @param type pass `'noop'` as argument to disable animations.\n *\n * @publicApi\n * @developerPreview\n */\nexport function provideAnimationsAsync(type: 'animations'|'noop' = 'animations'):\n    EnvironmentProviders {\n  performanceMarkFeature('NgAsyncAnimations');\n  return makeEnvironmentProviders([\n    {\n      provide: RendererFactory2,\n      useFactory: (doc: Document, renderer: DomRendererFactory2, zone: NgZone) => {\n        return new AsyncAnimationRendererFactory(doc, renderer, zone, type);\n      },\n      deps: [DOCUMENT, DomRendererFactory2, NgZone],\n    },\n    {\n      provide: ANIMATION_MODULE_TYPE,\n      useValue: type === 'noop' ? 'NoopAnimations' : 'BrowserAnimations',\n    },\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {provideAnimationsAsync} from './providers';\nexport * from './private_export';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/async-animations';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["ChangeDetectionScheduler", "RuntimeError", "performanceMarkFeature", "DomRendererFactory2"], "mappings": ";;;;;;;;;;;AAYA,MAAM,gBAAgB,GAAG,GAAG,CAAC;MAGhB,6BAA6B,CAAA;AAKxC;;;AAGG;IACH,WACY,CAAA,GAAa,EAAU,QAA0B,EAAU,IAAY,EACvE,aAAkC,EAAU,UAKlD,EAAA;QANM,IAAG,CAAA,GAAA,GAAH,GAAG,CAAU;QAAU,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAkB;QAAU,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACvE,IAAa,CAAA,aAAA,GAAb,aAAa,CAAqB;QAAU,IAAU,CAAA,UAAA,GAAV,UAAU,CAK5D;QAfE,IAAuB,CAAA,uBAAA,GAA2C,IAAI,CAAC;QAC9D,IAAS,CAAA,SAAA,GAAG,MAAM,CAACA,yBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;KActE;;IAGV,WAAW,GAAA;;;;;;;AAOT,QAAA,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;KACvB;AAED;;AAEG;IACK,QAAQ,GAAA;QACd,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,OAAO,6BAA6B,CAAC,CAAC;AAE5E,QAAA,OAAO,UAAU;AACZ,aAAA,KAAK,CAAC,CAAC,CAAC,KAAI;YACX,MAAM,IAAIC,aAAY,CAAA,IAAA,kEAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC1C,2CAA2C;oBACvC,8EAA8E;AAC9E,oBAAA,qEAAqE,CAAC,CAAC;AACrF,SAAC,CAAC;aACD,IAAI,CAAC,CAAC,EAAC,aAAa,EAAE,yBAAyB,EAAC,KAAI;;;AAGnD,YAAA,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3E,YAAA,MAAM,eAAe,GACjB,IAAI,yBAAyB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1E,YAAA,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;AAChC,YAAA,OAAO,eAAe,CAAC;AACzB,SAAC,CAAC,CAAC;KACR;AAED;;;;;;;AAOG;IACH,cAAc,CAAC,WAAgB,EAAE,YAA2B,EAAA;AAC1D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAEzE,QAAA,IAAK,QAA8B,CAAC,KAAK,KAAA,CAAA,sCAAoC;;AAE3E,YAAA,OAAO,QAAQ,CAAC;SACjB;;AAGD,QAAA,IAAI,OAAQ,QAAgB,CAAC,qBAAqB,KAAK,SAAS,EAAE;AAC/D,YAAA,QAAgB,CAAC,qBAAqB,GAAG,KAAK,CAAC;SACjD;;AAGD,QAAA,MAAM,eAAe,GAAG,IAAI,yBAAyB,CAAC,QAAQ,CAAC,CAAC;;;AAIhE,QAAA,IAAI,YAAY,EAAE,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;AACtE,YAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;SAChD;AAED,QAAA,IAAI,CAAC,uBAAuB;AACxB,cAAE,IAAI,CAAC,CAAC,wBAAwB,KAAI;YAClC,MAAM,iBAAiB,GACnB,wBAAwB,CAAC,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACvE,YAAA,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACzC,SAAC,CAAC;aACD,KAAK,CAAC,CAAC,IAAG;;AAET,YAAA,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAC,CAAC,CAAC;AAEP,QAAA,OAAO,eAAe,CAAC;KACxB;IAED,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC;KACzB;IAED,GAAG,GAAA;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;KACvB;IAED,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;KACjE;yHA7GU,6BAA6B,EAAA,IAAA,EAAA,SAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAA7B,6BAA6B,EAAA,CAAA,CAAA,EAAA;;sGAA7B,6BAA6B,EAAA,UAAA,EAAA,CAAA;kBADzC,UAAU;;AAiHX;;;AAGG;MACU,yBAAyB,CAAA;AAKpC,IAAA,WAAA,CAAoB,QAAmB,EAAA;QAAnB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAW;;QAH/B,IAAM,CAAA,MAAA,GAA2C,EAAE,CAAC;AACnD,QAAA,IAAA,CAAA,KAAK,GAAmC,CAAA,uCAAA;KAEN;AAE3C,IAAA,GAAG,CAAC,IAAe,EAAA;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAErB,QAAA,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;;;AAGxB,YAAA,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC5B,EAAE,CAAC,IAAI,CAAC,CAAC;aACV;;;AAGD,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;KACF;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;KAC3B;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;KACzB;IAED,aAAa,CAAC,IAAY,EAAE,SAAuB,EAAA;QACjD,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KACrD;AAED,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KAC3C;AAED,IAAA,UAAU,CAAC,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;KACxC;AAED,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;KAClC;IAED,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;QACpC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;KAC7C;AAED,IAAA,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAE,MAA0B,EAAA;AAChF,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;KAChE;AAED,IAAA,WAAW,CAAC,MAAW,EAAE,QAAa,EAAE,aAAiC,EAAA;QACvE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;KAC5D;IAED,iBAAiB,CAAC,cAAmB,EAAE,eAAmC,EAAA;QACxE,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;KACzE;AAED,IAAA,UAAU,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;KACvC;AAED,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;KACxC;AAED,IAAA,YAAY,CAAC,EAAO,EAAE,IAAY,EAAE,KAAa,EAAE,SAAiC,EAAA;AAClF,QAAA,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;KACxD;AAED,IAAA,eAAe,CAAC,EAAO,EAAE,IAAY,EAAE,SAAiC,EAAA;QACtE,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;KACpD;IAED,QAAQ,CAAC,EAAO,EAAE,IAAY,EAAA;QAC5B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;KAClC;IAED,WAAW,CAAC,EAAO,EAAE,IAAY,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;KACrC;AAED,IAAA,QAAQ,CAAC,EAAO,EAAE,KAAa,EAAE,KAAU,EAAE,KAAqC,EAAA;AAChF,QAAA,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KACjD;AAED,IAAA,WAAW,CAAC,EAAO,EAAE,KAAa,EAAE,KAAqC,EAAA;QACvE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KAC7C;AAED,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;;;AAG3C,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,CAAC,QAAmB,KAAK,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;SACnF;QACD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;KAC5C;IAED,QAAQ,CAAC,IAAS,EAAE,KAAa,EAAA;QAC/B,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KACrC;AAED,IAAA,MAAM,CAAC,MAAW,EAAE,SAAiB,EAAE,QAAwC,EAAA;;;AAG7E,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;YAChC,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,CAAC,QAAmB,KAAK,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;SAC1F;AACD,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC1D;AAEO,IAAA,YAAY,CAAC,eAAuB,EAAA;;AAE1C,QAAA,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;KAC7E;AACF;;AC9OD;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACa,SAAA,sBAAsB,CAAC,IAAA,GAA4B,YAAY,EAAA;IAE7EC,uBAAsB,CAAC,mBAAmB,CAAC,CAAC;AAC5C,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA;AACE,YAAA,OAAO,EAAE,gBAAgB;YACzB,UAAU,EAAE,CAAC,GAAa,EAAE,QAA6B,EAAE,IAAY,KAAI;gBACzE,OAAO,IAAI,6BAA6B,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;aACrE;AACD,YAAA,IAAI,EAAE,CAAC,QAAQ,EAAEC,oBAAmB,EAAE,MAAM,CAAC;AAC9C,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,qBAAqB;YAC9B,QAAQ,EAAE,IAAI,KAAK,MAAM,GAAG,gBAAgB,GAAG,mBAAmB;AACnE,SAAA;AACF,KAAA,CAAC,CAAC;AACL;;AClDA;;;;AAIG;;ACJH;;;;AAIG;;ACJH;;ACRA;;AAEG;;;;"}