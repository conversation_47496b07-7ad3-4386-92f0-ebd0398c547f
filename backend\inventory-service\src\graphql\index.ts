import { gql } from 'apollo-server-express';
import { PrismaClient } from '@prisma/client';

// GraphQL Type Definitions
export const typeDefs = gql`
  type Category {
    id: ID!
    name: String!
    description: String
    isActive: Boolean!
    createdAt: String!
    updatedAt: String!
    products: [Product!]!
    productCount: Int!
  }

  type Product {
    id: ID!
    name: String!
    description: String
    sku: String!
    barcode: String
    price: Float!
    cost: Float
    categoryId: String!
    isActive: Boolean!
    createdAt: String!
    updatedAt: String!
    category: Category!
    stock: Stock
  }

  type Stock {
    id: ID!
    productId: String!
    quantity: Int!
    minThreshold: Int!
    maxThreshold: Int
    lastRestocked: String
    createdAt: String!
    updatedAt: String!
    product: Product!
    status: StockStatus!
    isLowStock: Boolean!
    isOutOfStock: Boolean!
  }

  enum StockStatus {
    NORMAL
    LOW
    OUT
  }

  type ProductWithStock {
    id: ID!
    name: String!
    description: String
    sku: String!
    barcode: String
    price: Float!
    cost: Float
    categoryId: String!
    isActive: Boolean!
    category: Category!
    stock: Stock
    stockStatus: StockStatus!
  }

  type PaginationInfo {
    page: Int!
    limit: Int!
    total: Int!
    totalPages: Int!
    hasNext: Boolean!
    hasPrev: Boolean!
  }

  type ProductConnection {
    products: [ProductWithStock!]!
    pagination: PaginationInfo!
  }

  type CategoryConnection {
    categories: [Category!]!
    pagination: PaginationInfo!
  }

  type StockSummary {
    totalProducts: Int!
    lowStockCount: Int!
    outOfStockCount: Int!
    totalQuantity: Int!
    totalCostValue: Float!
    totalRetailValue: Float!
    healthyStockCount: Int!
    lowStockPercentage: Float!
  }

  input ProductFilter {
    search: String
    categoryId: String
    lowStock: Boolean
    outOfStock: Boolean
    priceMin: Float
    priceMax: Float
  }

  input CategoryFilter {
    search: String
  }

  input PaginationInput {
    page: Int = 1
    limit: Int = 10
  }

  type Query {
    # Products
    products(filter: ProductFilter, pagination: PaginationInput): ProductConnection!
    product(id: ID!): ProductWithStock
    productByBarcode(barcode: String!): ProductWithStock
    productBySku(sku: String!): ProductWithStock

    # Categories
    categories(filter: CategoryFilter, pagination: PaginationInput): CategoryConnection!
    category(id: ID!): Category

    # Stock
    lowStockProducts: [ProductWithStock!]!
    outOfStockProducts: [ProductWithStock!]!
    stockSummary: StockSummary!

    # Search
    searchProducts(query: String!, limit: Int = 10): [ProductWithStock!]!
  }

  type Mutation {
    # Products
    createProduct(input: CreateProductInput!): ProductWithStock!
    updateProduct(id: ID!, input: UpdateProductInput!): ProductWithStock!
    deleteProduct(id: ID!): Boolean!

    # Categories
    createCategory(input: CreateCategoryInput!): Category!
    updateCategory(id: ID!, input: UpdateCategoryInput!): Category!
    deleteCategory(id: ID!): Boolean!

    # Stock
    adjustStock(productId: ID!, adjustment: Int!, reason: String!, notes: String): Stock!
    setStock(productId: ID!, quantity: Int!, minThreshold: Int, maxThreshold: Int): Stock!
  }

  input CreateProductInput {
    name: String!
    description: String
    sku: String!
    barcode: String
    price: Float!
    cost: Float
    categoryId: String!
    initialStock: Int = 0
    minThreshold: Int = 10
    maxThreshold: Int
  }

  input UpdateProductInput {
    name: String
    description: String
    sku: String
    barcode: String
    price: Float
    cost: Float
    categoryId: String
    isActive: Boolean
  }

  input CreateCategoryInput {
    name: String!
    description: String
  }

  input UpdateCategoryInput {
    name: String
    description: String
    isActive: Boolean
  }
`;

// Types for GraphQL context and arguments
interface GraphQLContext {
  user?: {
    userId: string;
    roles: string[];
    permissions: string[];
  };
  prisma: PrismaClient;
  redis: any;
}

interface ProductFilter {
  search?: string;
  categoryId?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  priceMin?: number;
  priceMax?: number;
}

interface CategoryFilter {
  search?: string;
}

interface PaginationInput {
  page?: number;
  limit?: number;
}

// Helper function to determine stock status
function getStockStatus(stock: any): string {
  if (!stock) return 'NORMAL';
  if (stock.quantity === 0) return 'OUT';
  if (stock.quantity <= stock.minThreshold) return 'LOW';
  return 'NORMAL';
}

// GraphQL Resolvers
export const resolvers = {
  Query: {
    // Products
    products: async (_: any, { filter = {}, pagination = {} }: { filter?: ProductFilter; pagination?: PaginationInput }, { prisma }: GraphQLContext) => {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const where: any = { isActive: true };

      // Apply filters
      if (filter.search) {
        where.OR = [
          { name: { contains: filter.search, mode: 'insensitive' } },
          { description: { contains: filter.search, mode: 'insensitive' } },
          { sku: { contains: filter.search, mode: 'insensitive' } },
          { barcode: { contains: filter.search, mode: 'insensitive' } },
        ];
      }

      if (filter.categoryId) {
        where.categoryId = filter.categoryId;
      }

      if (filter.priceMin || filter.priceMax) {
        where.price = {};
        if (filter.priceMin) where.price.gte = filter.priceMin;
        if (filter.priceMax) where.price.lte = filter.priceMax;
      }

      if (filter.lowStock) {
        where.stock = {
          quantity: {
            lte: (prisma as any).raw('stock.min_threshold')
          }
        };
      }

      if (filter.outOfStock) {
        where.stock = {
          quantity: 0
        };
      }

      const [products, total] = await Promise.all([
        prisma.product.findMany({
          where,
          skip,
          take: limit,
          include: {
            category: true,
            stock: true,
          },
          orderBy: { name: 'asc' }
        }),
        prisma.product.count({ where })
      ]);

      return {
        products: products.map(product => ({
          ...product,
          stockStatus: getStockStatus(product.stock),
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: skip + limit < total,
          hasPrev: page > 1,
        }
      };
    },

    product: async (_: any, { id }: { id: string }, { prisma }: GraphQLContext) => {
      const product = await prisma.product.findUnique({
        where: { id },
        include: {
          category: true,
          stock: true,
        }
      });

      if (!product) return null;

      return {
        ...product,
        stockStatus: getStockStatus(product.stock),
      };
    },

    productByBarcode: async (_: any, { barcode }: { barcode: string }, { prisma }: GraphQLContext) => {
      const product = await prisma.product.findUnique({
        where: { barcode },
        include: {
          category: true,
          stock: true,
        }
      });

      if (!product) return null;

      return {
        ...product,
        stockStatus: getStockStatus(product.stock),
      };
    },

    productBySku: async (_: any, { sku }: { sku: string }, { prisma }: GraphQLContext) => {
      const product = await prisma.product.findUnique({
        where: { sku },
        include: {
          category: true,
          stock: true,
        }
      });

      if (!product) return null;

      return {
        ...product,
        stockStatus: getStockStatus(product.stock),
      };
    },

    // Categories
    categories: async (_: any, { filter = {}, pagination = {} }: { filter?: CategoryFilter; pagination?: PaginationInput }, { prisma }: GraphQLContext) => {
      const { page = 1, limit = 10 } = pagination;
      const skip = (page - 1) * limit;

      const where: any = { isActive: true };

      if (filter.search) {
        where.OR = [
          { name: { contains: filter.search, mode: 'insensitive' } },
          { description: { contains: filter.search, mode: 'insensitive' } },
        ];
      }

      const [categories, total] = await Promise.all([
        prisma.category.findMany({
          where,
          skip,
          take: limit,
          include: {
            products: {
              where: { isActive: true }
            },
            _count: {
              select: { products: true }
            }
          },
          orderBy: { name: 'asc' }
        }),
        prisma.category.count({ where })
      ]);

      return {
        categories: categories.map(category => ({
          ...category,
          productCount: category._count.products,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: skip + limit < total,
          hasPrev: page > 1,
        }
      };
    },

    category: async (_: any, { id }: { id: string }, { prisma }: GraphQLContext) => {
      const category = await prisma.category.findUnique({
        where: { id },
        include: {
          products: {
            where: { isActive: true },
            include: { stock: true }
          },
          _count: {
            select: { products: true }
          }
        }
      });

      if (!category) return null;

      return {
        ...category,
        productCount: category._count.products,
      };
    },

    // Stock queries
    lowStockProducts: async (_: any, __: any, { prisma }: GraphQLContext) => {
      const products = await prisma.product.findMany({
        where: {
          isActive: true,
          stock: {
            quantity: {
              lte: (prisma as any).raw('stock.min_threshold')
            }
          }
        },
        include: {
          category: true,
          stock: true,
        },
        orderBy: {
          stock: {
            quantity: 'asc'
          }
        }
      });

      return products.map(product => ({
        ...product,
        stockStatus: getStockStatus(product.stock),
      }));
    },

    outOfStockProducts: async (_: any, __: any, { prisma }: GraphQLContext) => {
      const products = await prisma.product.findMany({
        where: {
          isActive: true,
          stock: {
            quantity: 0
          }
        },
        include: {
          category: true,
          stock: true,
        },
        orderBy: { name: 'asc' }
      });

      return products.map(product => ({
        ...product,
        stockStatus: 'OUT',
      }));
    },

    stockSummary: async (_: any, __: any, { prisma }: GraphQLContext) => {
      const [
        totalProducts,
        lowStockCount,
        outOfStockCount,
        stockItems
      ] = await Promise.all([
        prisma.stock.count({
          where: { product: { isActive: true } }
        }),
        prisma.stock.count({
          where: {
            quantity: { lte: (prisma as any).raw('stock.min_threshold') },
            product: { isActive: true }
          }
        }),
        prisma.stock.count({
          where: {
            quantity: 0,
            product: { isActive: true }
          }
        }),
        prisma.stock.findMany({
          where: { product: { isActive: true } },
          include: {
            product: {
              select: { cost: true, price: true }
            }
          }
        })
      ]);

      const totalQuantity = stockItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalCostValue = stockItems.reduce((sum, item) => sum + (item.quantity * (item.product.cost || 0)), 0);
      const totalRetailValue = stockItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0);

      return {
        totalProducts,
        lowStockCount,
        outOfStockCount,
        totalQuantity,
        totalCostValue,
        totalRetailValue,
        healthyStockCount: totalProducts - lowStockCount,
        lowStockPercentage: totalProducts > 0 ? (lowStockCount / totalProducts) * 100 : 0,
      };
    },

    searchProducts: async (_: any, { query, limit }: { query: string; limit: number }, { prisma }: GraphQLContext) => {
      const products = await prisma.product.findMany({
        where: {
          isActive: true,
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { sku: { contains: query, mode: 'insensitive' } },
            { barcode: { contains: query, mode: 'insensitive' } },
          ]
        },
        take: limit,
        include: {
          category: true,
          stock: true,
        },
        orderBy: { name: 'asc' }
      });

      return products.map(product => ({
        ...product,
        stockStatus: getStockStatus(product.stock),
      }));
    },
  },

  // Nested resolvers
  ProductWithStock: {
    stockStatus: (product: any) => getStockStatus(product.stock),
  },

  Stock: {
    status: (stock: any) => getStockStatus(stock),
    isLowStock: (stock: any) => stock.quantity <= stock.minThreshold,
    isOutOfStock: (stock: any) => stock.quantity === 0,
  },
};
