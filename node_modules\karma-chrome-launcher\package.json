{"name": "karma-chrome-launcher", "version": "3.2.0", "description": "A Karma plugin. Launcher for Chrome and Chrome Canary.", "main": "index.js", "scripts": {"lint": "standard", "integration-test": "npx karma start examples/simple/karma.conf.js --single-run --browsers ChromeHeadless", "unit-test": "npx mocha test/*", "test": "npm run unit-test && npm run integration-test", "release": "semantic-release", "commitlint": "commitlint"}, "repository": {"type": "git", "url": "git://github.com/karma-runner/karma-chrome-launcher.git"}, "keywords": ["karma-plugin", "karma-launcher", "chrome"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"which": "^1.2.1"}, "license": "MIT", "devDependencies": {"@commitlint/cli": "^12.1.4", "@commitlint/config-angular": "^12.1.4", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.1", "chai": "^4.2.0", "karma": "^6.4.1", "karma-mocha": "1.x || ^0.2.0", "mocha": "^5.2.0", "semantic-release": "^17.0.1", "sinon": "^7.1.1", "standard": "^12.0.0"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "dignifiedquire <<EMAIL>>", "<PERSON> <<EMAIL>>", "rogeriopvl <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "greenkeeperio-bot <<EMAIL>>", "johnj<PERSON>ton <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>yer <<EMAIL>>", "dependabot[bot] <49699333+dependabot[bot]@users.noreply.github.com>", "<PERSON><PERSON> <<EMAIL>>", "Aymeric <PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <david<PERSON>@wix.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON>ian-R <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <mark<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "Parashuram N <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <is<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "aSemy <<EMAIL>>", "brutalcrozt <<EMAIL>>", "cexbrayat <<EMAIL>>", "daniel rod<PERSON> <<EMAIL>>", "gkostov <<EMAIL>>", "semantic-release-bot <<EMAIL>>"]}