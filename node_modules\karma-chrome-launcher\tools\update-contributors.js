// From https://github.com/karma-runner/karma/blob/master/tools/update-contributors.js
// When modifying this file also modify upstream.
const { execSync } = require('child_process')
const { readFileSync, writeFileSync } = require('fs')
const { resolve } = require('path')

const prepare = async (pluginConfig, { logger }) => {
  // Example output:
  //   1042  V<PERSON><PERSON><PERSON> <<EMAIL>>
  //    412  <PERSON><PERSON><PERSON> <<EMAIL>>
  //    206  dignifiedquire <<EMAIL>>
  //    139  johnjbarton <<EMAIL>>
  const stdout = execSync('git log --pretty=short | git shortlog -nse', { encoding: 'utf8' })

  const pkgPath = resolve(__dirname, '..', 'package.json')
  const pkg = JSON.parse(readFileSync(pkgPath, 'utf8'))

  // First line is already included as author field. Last line is dropped as it is an empty line.
  pkg.contributors = stdout.split('\n').slice(1, -1).map((line) => line.replace(/^[\W\d]+/, ''))
  writeFileSync(pkgPath, JSON.stringify(pkg, undefined, '  ') + '\n', 'utf8')

  logger.info('Updated contributors list.')
}

module.exports = { prepare }
