!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("@material/tooltip",[],e):"object"==typeof exports?exports.tooltip=e():(t.mdc=t.mdc||{},t.mdc.tooltip=e())}(this,function(){return i={},n.m=r={0:function(t,e,r){"use strict";(function(t){}).call(this,r(20))},1:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapResourceUrl=e.isResourceUrl=e.createResourceUrl=e.TrustedResourceUrl=void 0,r(0);var n=r(4),o=r(9),a=(i.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedResourceUrl.toString()},i);function i(t,e){this.privateDoNotAccessOrElseWrappedResourceUrl=t}var s=window.TrustedScriptURL;e.TrustedResourceUrl=null!=s?s:a,e.createResourceUrl=function(t){var e,r=t,i=null===(e=(0,o.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScriptURL(r);return null!=i?i:new a(r,n.secretToken)},e.isResourceUrl=function(t){return t instanceof e.TrustedResourceUrl},e.unwrapResourceUrl=function(t){var e;if(null===(e=(0,o.getTrustedTypes)())||void 0===e?void 0:e.isScriptURL(t))return t;if(t instanceof a)return t.privateDoNotAccessOrElseWrappedResourceUrl;throw new Error("")}},10:function(t,e,r){"use strict";var i,n=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyle=e.isStyle=e.createStyle=e.SafeStyle=void 0,r(0);function o(){}var a=r(4);e.SafeStyle=o;var s,l=(n(c,s=o),c.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyle},c);function c(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyle=t,r}e.createStyle=function(t){return new l(t,a.secretToken)},e.isStyle=function(t){return t instanceof l},e.unwrapStyle=function(t){if(t instanceof l)return t.privateDoNotAccessOrElseWrappedStyle;throw new Error("")}},11:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AttributePolicyAction=e.SanitizerTable=void 0;var i,n,o=(a.prototype.isAllowedElement=function(t){return"form"!==t.toLowerCase()&&(this.allowedElements.has(t)||this.elementPolicies.has(t))},a.prototype.getAttributePolicy=function(t,e){var r=this.elementPolicies.get(e);return(null==r?void 0:r.has(t))?r.get(t):this.allowedGlobalAttributes.has(t)?{policyAction:i.KEEP}:this.globalAttributePolicies.get(t)||{policyAction:i.DROP}},a);function a(t,e,r,i){this.allowedElements=t,this.elementPolicies=e,this.allowedGlobalAttributes=r,this.globalAttributePolicies=i}e.SanitizerTable=o,(n=i=e.AttributePolicyAction||(e.AttributePolicyAction={}))[n.DROP=0]="DROP",n[n.KEEP=1]="KEEP",n[n.KEEP_AND_SANITIZE_URL=2]="KEEP_AND_SANITIZE_URL",n[n.KEEP_AND_NORMALIZE=3]="KEEP_AND_NORMALIZE",n[n.KEEP_AND_SANITIZE_STYLE=4]="KEEP_AND_SANITIZE_STYLE"},12:function(t,e,r){"use strict";var i,n=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.isStyleSheet=e.createStyleSheet=e.SafeStyleSheet=void 0,r(0);function o(){}var a=r(4);e.SafeStyleSheet=o;var s,l=(n(c,s=o),c.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedStyleSheet},c);function c(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedStyleSheet=t,r}e.createStyleSheet=function(t){return new l(t,a.secretToken)},e.isStyleSheet=function(t){return t instanceof l},e.unwrapStyleSheet=function(t){if(t instanceof l)return t.privateDoNotAccessOrElseWrappedStyleSheet;throw new Error("")}},120:function(t,e,r){"use strict";var i,n;Object.defineProperty(e,"__esModule",{value:!0}),e.XPositionWithCaret=e.YPositionWithCaret=e.PositionWithCaret=e.strings=e.YPosition=e.AnchorBoundaryType=e.XPosition=e.events=e.attributes=e.numbers=e.CssClasses=void 0,(n=i=i||{}).RICH="mdc-tooltip--rich",n.SHOWN="mdc-tooltip--shown",n.SHOWING="mdc-tooltip--showing",n.SHOWING_TRANSITION="mdc-tooltip--showing-transition",n.HIDE="mdc-tooltip--hide",n.HIDE_TRANSITION="mdc-tooltip--hide-transition",n.MULTILINE_TOOLTIP="mdc-tooltip--multiline",n.SURFACE="mdc-tooltip__surface",n.SURFACE_ANIMATION="mdc-tooltip__surface-animation",n.TOOLTIP_CARET_TOP="mdc-tooltip__caret-surface-top",n.TOOLTIP_CARET_BOTTOM="mdc-tooltip__caret-surface-bottom",e.CssClasses=i;e.numbers={BOUNDED_ANCHOR_GAP:4,UNBOUNDED_ANCHOR_GAP:8,MIN_VIEWPORT_TOOLTIP_THRESHOLD:8,HIDE_DELAY_MS:600,SHOW_DELAY_MS:500,MIN_WIDTH:40,MIN_HEIGHT:24,MAX_WIDTH:200,CARET_INDENTATION:24,ANIMATION_SCALE:.8,RICH_MAX_WIDTH:320};e.attributes={ARIA_EXPANDED:"aria-expanded",ARIA_HASPOPUP:"aria-haspopup",PERSISTENT:"data-mdc-tooltip-persistent",SCROLLABLE_ANCESTOR:"tooltip-scrollable-ancestor",HAS_CARET:"data-mdc-tooltip-has-caret"};var o,a,s,l,c,u;e.events={HIDDEN:"MDCTooltip:hidden",SHOWN:"MDCTooltip:shown"},(a=o=o||{})[a.DETECTED=0]="DETECTED",a[a.START=1]="START",a[a.CENTER=2]="CENTER",a[a.END=3]="END",a[a.SIDE_START=4]="SIDE_START",a[a.SIDE_END=5]="SIDE_END",e.XPosition=o,(l=s=s||{})[l.DETECTED=0]="DETECTED",l[l.ABOVE=1]="ABOVE",l[l.BELOW=2]="BELOW",l[l.SIDE=3]="SIDE",e.YPosition=s,(u=c=c||{})[u.BOUNDED=0]="BOUNDED",u[u.UNBOUNDED=1]="UNBOUNDED",e.AnchorBoundaryType=c;var d,p,h,f,T,E;e.strings={LEFT:"left",RIGHT:"right",CENTER:"center",TOP:"top",BOTTOM:"bottom"},(p=d=d||{})[p.DETECTED=0]="DETECTED",p[p.ABOVE_START=1]="ABOVE_START",p[p.ABOVE_CENTER=2]="ABOVE_CENTER",p[p.ABOVE_END=3]="ABOVE_END",p[p.TOP_SIDE_START=4]="TOP_SIDE_START",p[p.CENTER_SIDE_START=5]="CENTER_SIDE_START",p[p.BOTTOM_SIDE_START=6]="BOTTOM_SIDE_START",p[p.TOP_SIDE_END=7]="TOP_SIDE_END",p[p.CENTER_SIDE_END=8]="CENTER_SIDE_END",p[p.BOTTOM_SIDE_END=9]="BOTTOM_SIDE_END",p[p.BELOW_START=10]="BELOW_START",p[p.BELOW_CENTER=11]="BELOW_CENTER",p[p.BELOW_END=12]="BELOW_END",e.PositionWithCaret=d,(f=h=h||{})[f.ABOVE=1]="ABOVE",f[f.BELOW=2]="BELOW",f[f.SIDE_TOP=3]="SIDE_TOP",f[f.SIDE_CENTER=4]="SIDE_CENTER",f[f.SIDE_BOTTOM=5]="SIDE_BOTTOM",e.YPositionWithCaret=h,(E=T=T||{})[E.START=1]="START",E[E.CENTER=2]="CENTER",E[E.END=3]="END",E[E.SIDE_START=4]="SIDE_START",E[E.SIDE_END=5]="SIDE_END",e.XPositionWithCaret=T},13:function(t,e,r){"use strict";var n=this&&this.__makeTemplateObject||function(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t},o=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(i=o.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return a},a=this&&this.__spreadArray||function(t,e){for(var r=0,i=e.length,n=t.length;r<i;r++,n++)t[n]=e[r];return t};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCComponent=void 0;var s=r(17),l=r(18),i=r(7);var c,u,d=(p.attachTo=function(t){return new p(t,new i.MDCFoundation({}))},p.prototype.initialize=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e]},p.prototype.getDefaultFoundation=function(){throw new Error("Subclasses must override getDefaultFoundation to return a properly configured foundation class")},p.prototype.initialSyncWithDOM=function(){},p.prototype.destroy=function(){this.foundation.destroy()},p.prototype.listen=function(t,e,r){this.root.addEventListener(t,e,r)},p.prototype.unlisten=function(t,e,r){this.root.removeEventListener(t,e,r)},p.prototype.emit=function(t,e,r){var i;void 0===r&&(r=!1),"function"==typeof CustomEvent?i=new CustomEvent(t,{bubbles:r,detail:e}):(i=document.createEvent("CustomEvent")).initCustomEvent(t,r,!1,e),this.root.dispatchEvent(i)},p.prototype.safeSetAttribute=function(t,e,r){if("tabindex"===e.toLowerCase())t.tabIndex=Number(r);else if(0===e.indexOf("data-")){var i=function(t){return String(t).replace(/\-([a-z])/g,function(t,e){return e.toUpperCase()})}(e.replace(/^data-/,""));t.dataset[i]=r}else l.safeElement.setPrefixedAttribute([s.safeAttrPrefix(c=c||n(["aria-"],["aria-"])),s.safeAttrPrefix(u=u||n(["role"],["role"]))],t,e,r)},p);function p(t,e){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];this.root=t,this.initialize.apply(this,a([],o(r))),this.foundation=void 0===e?this.getDefaultFoundation():e,this.foundation.init(),this.initialSyncWithDOM()}e.MDCComponent=d,e.default=d},14:function(t,e,r){"use strict";var h=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(i=o.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.HtmlSanitizerImpl=void 0,r(0);var i=r(2),n=r(4),f=r(3),l=r(23),T=r(24),o=r(16),E=r(11),a=(s.prototype.sanitizeAssertUnchanged=function(t){this.changes=[];var e=this.sanitize(t);if(0===this.changes.length)return e;throw new Error("")},s.prototype.sanitize=function(t){var e=document.createElement("span");e.appendChild(this.sanitizeToFragment(t));var r=(new XMLSerializer).serializeToString(e);return r=r.slice(r.indexOf(">")+1,r.lastIndexOf("</")),(0,i.createHtml)(r)},s.prototype.sanitizeToFragment=function(t){for(var e=this,r=(0,l.createInertFragment)(t),i=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,function(t){return e.nodeFilter(t)},!1),n=i.nextNode(),o=document.createDocumentFragment(),a=o;null!==n;){var s=void 0;if((0,T.isText)(n))s=this.sanitizeTextNode(n);else{if(!(0,T.isElement)(n))throw new Error("Node is not of type text or element");s=this.sanitizeElementNode(n)}if(a.appendChild(s),n=i.firstChild())a=s;else for(;!(n=i.nextSibling())&&(n=i.parentNode());)a=a.parentNode}return o},s.prototype.sanitizeTextNode=function(t){return document.createTextNode(t.data)},s.prototype.sanitizeElementNode=function(t){var e,r,i=(0,T.getNodeName)(t),n=document.createElement(i),o=t.attributes;try{for(var a=h(o),s=a.next();!s.done;s=a.next()){var l=s.value,c=l.name,u=l.value,d=this.sanitizerTable.getAttributePolicy(c,i);if(this.satisfiesAllConditions(d.conditions,o))switch(d.policyAction){case E.AttributePolicyAction.KEEP:n.setAttribute(c,u);break;case E.AttributePolicyAction.KEEP_AND_SANITIZE_URL:var p=(0,f.restrictivelySanitizeUrl)(u);p!==u&&this.recordChange("Url in attribute ".concat(c,' was modified during sanitization. Original url:"').concat(u,'" was sanitized to: "').concat(p,'"')),n.setAttribute(c,p);break;case E.AttributePolicyAction.KEEP_AND_NORMALIZE:n.setAttribute(c,u.toLowerCase());break;case E.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:n.setAttribute(c,u);break;case E.AttributePolicyAction.DROP:this.recordChange("Attribute: ".concat(c," was dropped"));break;default:y(d.policyAction,"Unhandled AttributePolicyAction case")}else this.recordChange("Not all conditions satisfied for attribute: ".concat(c,"."))}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=a.return)&&r.call(a)}finally{if(e)throw e.error}}return n},s.prototype.nodeFilter=function(t){if((0,T.isText)(t))return NodeFilter.FILTER_ACCEPT;if(!(0,T.isElement)(t))return NodeFilter.FILTER_REJECT;var e=(0,T.getNodeName)(t);return null===e?(this.recordChange("Node name was null for node: ".concat(t)),NodeFilter.FILTER_REJECT):this.sanitizerTable.isAllowedElement(e)?NodeFilter.FILTER_ACCEPT:(this.recordChange("Element: ".concat(e," was dropped")),NodeFilter.FILTER_REJECT)},s.prototype.recordChange=function(t){0===this.changes.length&&this.changes.push("")},s.prototype.satisfiesAllConditions=function(t,e){var r,i,n;if(!t)return!0;try{for(var o=h(t),a=o.next();!a.done;a=o.next()){var s=d(a.value,2),l=s[0],c=s[1],u=null===(n=e.getNamedItem(l))||void 0===n?void 0:n.value;if(u&&!c.has(u))return!1}}catch(t){r={error:t}}finally{try{a&&!a.done&&(i=o.return)&&i.call(o)}finally{if(r)throw r.error}}return!0},s);function s(t,e){this.sanitizerTable=t,this.changes=[],(0,n.ensureTokenIsValid)(e)}e.HtmlSanitizerImpl=a;var c=function(){return new a(o.defaultSanitizerTable,n.secretToken)}();function y(t,e){throw void 0===e&&(e="unexpected value ".concat(t,"!")),new Error(e)}e.sanitizeHtml=function(t){return c.sanitize(t)},e.sanitizeHtmlAssertUnchanged=function(t){return c.sanitizeAssertUnchanged(t)},e.sanitizeHtmlToFragment=function(t){return c.sanitizeToFragment(t)}},15:function(t,e,r){"use strict";var n=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(i=o.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return a},o=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var i,n=0,o=e.length;n<o;n++)!i&&n in e||((i=i||Array.prototype.slice.call(e,0,n))[n]=e[n]);return t.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.setPrefixedAttribute=e.buildPrefixedAttributeSetter=e.insertAdjacentHtml=e.setCssText=e.setOuterHtml=e.setInnerHtml=void 0;var a=r(8),s=r(2),i=r(10);function l(t,e,r,i){if(0===t.length)throw new Error("No prefixes are provided");var n=t.map(function(t){return(0,a.unwrapAttributePrefix)(t)}),o=r.toLowerCase();if(n.every(function(t){return 0!==o.indexOf(t)}))throw new Error('Attribute "'.concat(r,'" does not match any of the allowed prefixes.'));e.setAttribute(r,i)}function c(t){if("script"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeScript.");if("style"===t.tagName.toLowerCase())throw new Error("Use setTextContent with a SafeStyleSheet.")}e.setInnerHtml=function(t,e){!function(t){return void 0!==t.tagName}(t)||c(t),t.innerHTML=(0,s.unwrapHtml)(e)},e.setOuterHtml=function(t,e){var r=t.parentElement;null!==r&&c(r),t.outerHTML=(0,s.unwrapHtml)(e)},e.setCssText=function(t,e){t.style.cssText=(0,i.unwrapStyle)(e)},e.insertAdjacentHtml=function(t,e,r){var i="beforebegin"===e||"afterend"===e?t.parentElement:t;null!==i&&c(i),t.insertAdjacentHTML(e,(0,s.unwrapHtml)(r))},e.buildPrefixedAttributeSetter=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var i=o([t],n(e),!1);return function(t,e,r){l(i,t,e,r)}},e.setPrefixedAttribute=l},16:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.defaultSanitizerTable=void 0;var i=r(11);e.defaultSanitizerTable=new i.SanitizerTable(new Set(["ARTICLE","SECTION","NAV","ASIDE","H1","H2","H3","H4","H5","H6","HEADER","FOOTER","ADDRESS","P","HR","PRE","BLOCKQUOTE","OL","UL","LH","LI","DL","DT","DD","FIGURE","FIGCAPTION","MAIN","DIV","EM","STRONG","SMALL","S","CITE","Q","DFN","ABBR","RUBY","RB","RT","RTC","RP","DATA","TIME","CODE","VAR","SAMP","KBD","SUB","SUP","I","B","U","MARK","BDI","BDO","SPAN","BR","WBR","INS","DEL","PICTURE","PARAM","TRACK","MAP","TABLE","CAPTION","COLGROUP","COL","TBODY","THEAD","TFOOT","TR","TD","TH","SELECT","DATALIST","OPTGROUP","OPTION","OUTPUT","PROGRESS","METER","FIELDSET","LEGEND","DETAILS","SUMMARY","MENU","DIALOG","SLOT","CANVAS","FONT","CENTER"]),new Map([["A",new Map([["href",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AREA",new Map([["href",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["LINK",new Map([["href",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL,conditions:new Map([["rel",new Set(["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"])]])}]])],["SOURCE",new Map([["src",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["IMG",new Map([["src",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["VIDEO",new Map([["src",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])],["AUDIO",new Map([["src",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}]])]]),new Set(["title","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-current","aria-disabled","aria-dropeffect","aria-expanded","aria-haspopup","aria-hidden","aria-invalid","aria-label","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","alt","align","autocapitalize","autocomplete","autocorrect","autofocus","autoplay","bgcolor","border","cellpadding","cellspacing","checked","color","cols","colspan","controls","datetime","disabled","download","draggable","enctype","face","formenctype","frameborder","height","hreflang","hidden","ismap","label","lang","loop","max","maxlength","media","minlength","min","multiple","muted","nonce","open","placeholder","preload","rel","required","reversed","role","rows","rowspan","selected","shape","size","sizes","slot","span","spellcheck","start","step","summary","translate","type","valign","value","width","wrap","itemscope","itemtype","itemid","itemprop","itemref"]),new Map([["dir",{policyAction:i.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["dir",new Set(["auto","ltr","rtl"])]])}],["async",{policyAction:i.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["async",new Set(["async"])]])}],["cite",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["loading",{policyAction:i.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["loading",new Set(["eager","lazy"])]])}],["poster",{policyAction:i.AttributePolicyAction.KEEP_AND_SANITIZE_URL}],["target",{policyAction:i.AttributePolicyAction.KEEP_AND_NORMALIZE,conditions:new Map([["target",new Set(["_self","_blank"])]])}]]))},17:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapStyleSheet=e.SafeStyleSheet=e.isStyleSheet=e.unwrapStyle=e.SafeStyle=e.isStyle=e.unwrapScript=e.SafeScript=e.isScript=e.EMPTY_SCRIPT=e.unwrapResourceUrl=e.TrustedResourceUrl=e.isResourceUrl=e.unwrapHtml=e.SafeHtml=e.isHtml=e.EMPTY_HTML=e.unwrapAttributePrefix=e.SafeAttributePrefix=e.safeStyleSheet=e.concatStyleSheets=e.safeStyle=e.concatStyles=e.scriptFromJson=e.safeScriptWithArgs=e.safeScript=e.concatScripts=e.trustedResourceUrl=e.replaceFragment=e.blobUrlFromScript=e.appendParams=e.HtmlSanitizerBuilder=e.sanitizeHtmlToFragment=e.sanitizeHtmlAssertUnchanged=e.sanitizeHtml=e.htmlEscape=e.createScriptSrc=e.createScript=e.concatHtmls=e.safeAttrPrefix=void 0;var i=r(19);Object.defineProperty(e,"safeAttrPrefix",{enumerable:!0,get:function(){return i.safeAttrPrefix}});var n=r(22);Object.defineProperty(e,"concatHtmls",{enumerable:!0,get:function(){return n.concatHtmls}}),Object.defineProperty(e,"createScript",{enumerable:!0,get:function(){return n.createScript}}),Object.defineProperty(e,"createScriptSrc",{enumerable:!0,get:function(){return n.createScriptSrc}}),Object.defineProperty(e,"htmlEscape",{enumerable:!0,get:function(){return n.htmlEscape}});var o=r(14);Object.defineProperty(e,"sanitizeHtml",{enumerable:!0,get:function(){return o.sanitizeHtml}}),Object.defineProperty(e,"sanitizeHtmlAssertUnchanged",{enumerable:!0,get:function(){return o.sanitizeHtmlAssertUnchanged}}),Object.defineProperty(e,"sanitizeHtmlToFragment",{enumerable:!0,get:function(){return o.sanitizeHtmlToFragment}});var a=r(25);Object.defineProperty(e,"HtmlSanitizerBuilder",{enumerable:!0,get:function(){return a.HtmlSanitizerBuilder}});var s=r(26);Object.defineProperty(e,"appendParams",{enumerable:!0,get:function(){return s.appendParams}}),Object.defineProperty(e,"blobUrlFromScript",{enumerable:!0,get:function(){return s.blobUrlFromScript}}),Object.defineProperty(e,"replaceFragment",{enumerable:!0,get:function(){return s.replaceFragment}}),Object.defineProperty(e,"trustedResourceUrl",{enumerable:!0,get:function(){return s.trustedResourceUrl}});var l=r(27);Object.defineProperty(e,"concatScripts",{enumerable:!0,get:function(){return l.concatScripts}}),Object.defineProperty(e,"safeScript",{enumerable:!0,get:function(){return l.safeScript}}),Object.defineProperty(e,"safeScriptWithArgs",{enumerable:!0,get:function(){return l.safeScriptWithArgs}}),Object.defineProperty(e,"scriptFromJson",{enumerable:!0,get:function(){return l.scriptFromJson}});var c=r(28);Object.defineProperty(e,"concatStyles",{enumerable:!0,get:function(){return c.concatStyles}}),Object.defineProperty(e,"safeStyle",{enumerable:!0,get:function(){return c.safeStyle}});var u=r(29);Object.defineProperty(e,"concatStyleSheets",{enumerable:!0,get:function(){return u.concatStyleSheets}}),Object.defineProperty(e,"safeStyleSheet",{enumerable:!0,get:function(){return u.safeStyleSheet}});var d=r(8);Object.defineProperty(e,"SafeAttributePrefix",{enumerable:!0,get:function(){return d.SafeAttributePrefix}}),Object.defineProperty(e,"unwrapAttributePrefix",{enumerable:!0,get:function(){return d.unwrapAttributePrefix}});var p=r(2);Object.defineProperty(e,"EMPTY_HTML",{enumerable:!0,get:function(){return p.EMPTY_HTML}}),Object.defineProperty(e,"isHtml",{enumerable:!0,get:function(){return p.isHtml}}),Object.defineProperty(e,"SafeHtml",{enumerable:!0,get:function(){return p.SafeHtml}}),Object.defineProperty(e,"unwrapHtml",{enumerable:!0,get:function(){return p.unwrapHtml}});var h=r(1);Object.defineProperty(e,"isResourceUrl",{enumerable:!0,get:function(){return h.isResourceUrl}}),Object.defineProperty(e,"TrustedResourceUrl",{enumerable:!0,get:function(){return h.TrustedResourceUrl}}),Object.defineProperty(e,"unwrapResourceUrl",{enumerable:!0,get:function(){return h.unwrapResourceUrl}});var f=r(5);Object.defineProperty(e,"EMPTY_SCRIPT",{enumerable:!0,get:function(){return f.EMPTY_SCRIPT}}),Object.defineProperty(e,"isScript",{enumerable:!0,get:function(){return f.isScript}}),Object.defineProperty(e,"SafeScript",{enumerable:!0,get:function(){return f.SafeScript}}),Object.defineProperty(e,"unwrapScript",{enumerable:!0,get:function(){return f.unwrapScript}});var T=r(10);Object.defineProperty(e,"isStyle",{enumerable:!0,get:function(){return T.isStyle}}),Object.defineProperty(e,"SafeStyle",{enumerable:!0,get:function(){return T.SafeStyle}}),Object.defineProperty(e,"unwrapStyle",{enumerable:!0,get:function(){return T.unwrapStyle}});var E=r(12);Object.defineProperty(e,"isStyleSheet",{enumerable:!0,get:function(){return E.isStyleSheet}}),Object.defineProperty(e,"SafeStyleSheet",{enumerable:!0,get:function(){return E.SafeStyleSheet}}),Object.defineProperty(e,"unwrapStyleSheet",{enumerable:!0,get:function(){return E.unwrapStyleSheet}})},179:function(t,e,r){"use strict";var i,n=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,r=1,i=arguments.length;r<i;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},g=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},T=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(i=o.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return a},E=this&&this.__spreadArray||function(t,e){for(var r=0,i=e.length,n=t.length;r<i;r++,n++)t[n]=e[r];return t};Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTooltipFoundation=void 0;var a,s=r(63),y=r(57),l=r(7),c=r(55),P=r(120),u=P.CssClasses.RICH,d=P.CssClasses.SHOWN,p=P.CssClasses.SHOWING,h=P.CssClasses.SHOWING_TRANSITION,f=P.CssClasses.HIDE,m=P.CssClasses.HIDE_TRANSITION,v=P.CssClasses.MULTILINE_TOOLTIP;(a=a||{}).POLL_ANCHOR="poll_anchor";var S,A="undefined"!=typeof window,O=(S=l.MDCFoundation,n(b,S),Object.defineProperty(b,"defaultAdapter",{get:function(){return{getAttribute:function(){return null},setAttribute:function(){},removeAttribute:function(){},addClass:function(){},hasClass:function(){return!1},removeClass:function(){},getComputedStyleProperty:function(){return""},setStyleProperty:function(){},setSurfaceAnimationStyleProperty:function(){},getViewportWidth:function(){return 0},getViewportHeight:function(){return 0},getTooltipSize:function(){return{width:0,height:0}},getAnchorBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},getParentBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},getAnchorAttribute:function(){return null},setAnchorAttribute:function(){return null},isRTL:function(){return!1},anchorContainsElement:function(){return!1},tooltipContainsElement:function(){return!1},focusAnchorElement:function(){},registerEventHandler:function(){},deregisterEventHandler:function(){},registerAnchorEventHandler:function(){},deregisterAnchorEventHandler:function(){},registerDocumentEventHandler:function(){},deregisterDocumentEventHandler:function(){},registerWindowEventHandler:function(){},deregisterWindowEventHandler:function(){},notifyHidden:function(){},notifyShown:function(){},getTooltipCaretBoundingRect:function(){return{top:0,right:0,bottom:0,left:0,width:0,height:0}},setTooltipCaretStyle:function(){},clearTooltipCaretStyles:function(){},getActiveElement:function(){return null},isInstanceOfElement:function(){return!1}}},enumerable:!1,configurable:!0}),b.prototype.init=function(){this.richTooltip=this.adapter.hasClass(u),this.persistentTooltip="true"===this.adapter.getAttribute(P.attributes.PERSISTENT),this.interactiveTooltip=!!this.adapter.getAnchorAttribute(P.attributes.ARIA_EXPANDED)&&"dialog"===this.adapter.getAnchorAttribute(P.attributes.ARIA_HASPOPUP),this.hasCaret=this.richTooltip&&"true"===this.adapter.getAttribute(P.attributes.HAS_CARET)},b.prototype.isShown=function(){return this.tooltipShown},b.prototype.isRich=function(){return this.richTooltip},b.prototype.isPersistent=function(){return this.persistentTooltip},b.prototype.handleAnchorMouseEnter=function(){var t=this;this.tooltipShown?this.show():(this.clearHideTimeout(),this.showTimeout=setTimeout(function(){t.show()},this.showDelayMs))},b.prototype.handleAnchorTouchstart=function(){var t=this;this.showTimeout=setTimeout(function(){t.show()},this.showDelayMs),this.adapter.registerWindowEventHandler("contextmenu",this.preventContextMenuOnLongTouch)},b.prototype.preventContextMenuOnLongTouch=function(t){t.preventDefault()},b.prototype.tooltipContainsRelatedTargetElement=function(t){return this.adapter.isInstanceOfElement(t)&&this.adapter.tooltipContainsElement(t)},b.prototype.anchorOrTooltipContainsTargetElement=function(t){return this.adapter.isInstanceOfElement(t)&&(this.adapter.tooltipContainsElement(t)||this.adapter.anchorContainsElement(t))},b.prototype.handleAnchorTouchend=function(){this.clearShowTimeout(),this.isShown()||this.adapter.deregisterWindowEventHandler("contextmenu",this.preventContextMenuOnLongTouch)},b.prototype.handleAnchorFocus=function(t){var e=this;this.tooltipContainsRelatedTargetElement(t.relatedTarget)||(this.showTimeout=setTimeout(function(){e.show()},this.showDelayMs))},b.prototype.handleAnchorMouseLeave=function(){var t=this;this.clearShowTimeout(),this.hideTimeout=setTimeout(function(){t.hide()},this.hideDelayMs)},b.prototype.handleAnchorClick=function(){this.tooltipShown?this.hide():this.show()},b.prototype.handleDocumentClick=function(t){this.richTooltip&&this.persistentTooltip&&this.anchorOrTooltipContainsTargetElement(t.target)||this.hide()},b.prototype.handleKeydown=function(t){if(c.normalizeKey(t)!==c.KEY.ESCAPE)return!0;var e=this.adapter.getActiveElement(),r=!1;return this.adapter.isInstanceOfElement(e)&&(r=this.adapter.tooltipContainsElement(e)),r&&this.adapter.focusAnchorElement(),this.hide(),t.stopPropagation(),!1},b.prototype.handleAnchorBlur=function(t){this.richTooltip&&(null===t.relatedTarget||this.tooltipContainsRelatedTargetElement(t.relatedTarget))||this.hide()},b.prototype.handleTooltipMouseEnter=function(){this.show()},b.prototype.handleTooltipMouseLeave=function(){var t=this;this.clearShowTimeout(),this.hideTimeout=setTimeout(function(){t.hide()},this.hideDelayMs)},b.prototype.handleRichTooltipFocusOut=function(t){this.anchorOrTooltipContainsTargetElement(t.relatedTarget)||null===t.relatedTarget&&this.interactiveTooltip||this.hide()},b.prototype.handleWindowScrollEvent=function(){this.persistentTooltip||this.hide()},b.prototype.handleWindowChangeEvent=function(){var t=this;this.animFrame.request(a.POLL_ANCHOR,function(){t.repositionTooltipOnAnchorMove()})},b.prototype.show=function(){var e,t,r=this;if(this.clearHideTimeout(),this.clearShowTimeout(),!this.tooltipShown){this.tooltipShown=!0,this.adapter.removeAttribute("aria-hidden"),this.richTooltip&&(this.interactiveTooltip&&this.adapter.setAnchorAttribute("aria-expanded","true"),this.adapter.registerEventHandler("focusout",this.richTooltipFocusOutHandler)),this.persistentTooltip||(this.adapter.registerEventHandler("mouseenter",this.tooltipMouseEnterHandler),this.adapter.registerEventHandler("mouseleave",this.tooltipMouseLeaveHandler)),this.adapter.removeClass(f),this.adapter.addClass(p),this.isTooltipMultiline()&&!this.richTooltip&&this.adapter.addClass(v),this.anchorRect=this.adapter.getAnchorBoundingRect(),this.parentRect=this.adapter.getParentBoundingRect(),this.richTooltip?this.positionRichTooltip():this.positionPlainTooltip(),this.adapter.registerAnchorEventHandler("blur",this.anchorBlurHandler),this.adapter.registerDocumentEventHandler("click",this.documentClickHandler),this.adapter.registerDocumentEventHandler("keydown",this.documentKeydownHandler),this.adapter.registerWindowEventHandler("scroll",this.windowScrollHandler),this.adapter.registerWindowEventHandler("resize",this.windowResizeHandler);try{for(var i=g(this.addAncestorScrollEventListeners),n=i.next();!n.done;n=i.next())(0,n.value)()}catch(t){e={error:t}}finally{try{n&&!n.done&&(t=i.return)&&t.call(i)}finally{if(e)throw e.error}}this.frameId=requestAnimationFrame(function(){r.clearAllAnimationClasses(),r.adapter.addClass(d),r.adapter.addClass(h)})}},b.prototype.hide=function(){var e,t;if(this.clearHideTimeout(),this.clearShowTimeout(),this.tooltipShown){this.frameId&&cancelAnimationFrame(this.frameId),this.tooltipShown=!1,this.adapter.setAttribute("aria-hidden","true"),this.adapter.deregisterEventHandler("focusout",this.richTooltipFocusOutHandler),this.richTooltip&&this.interactiveTooltip&&this.adapter.setAnchorAttribute("aria-expanded","false"),this.persistentTooltip||(this.adapter.deregisterEventHandler("mouseenter",this.tooltipMouseEnterHandler),this.adapter.deregisterEventHandler("mouseleave",this.tooltipMouseLeaveHandler)),this.clearAllAnimationClasses(),this.adapter.addClass(f),this.adapter.addClass(m),this.adapter.removeClass(d),this.adapter.deregisterAnchorEventHandler("blur",this.anchorBlurHandler),this.adapter.deregisterDocumentEventHandler("click",this.documentClickHandler),this.adapter.deregisterDocumentEventHandler("keydown",this.documentKeydownHandler),this.adapter.deregisterWindowEventHandler("scroll",this.windowScrollHandler),this.adapter.deregisterWindowEventHandler("resize",this.windowResizeHandler),this.adapter.deregisterWindowEventHandler("contextmenu",this.preventContextMenuOnLongTouch);try{for(var r=g(this.removeAncestorScrollEventListeners),i=r.next();!i.done;i=r.next())(0,i.value)()}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}}},b.prototype.handleTransitionEnd=function(){var t=this.adapter.hasClass(f);this.adapter.removeClass(p),this.adapter.removeClass(h),this.adapter.removeClass(f),this.adapter.removeClass(m),t&&null===this.showTimeout?this.adapter.notifyHidden():t||this.adapter.notifyShown()},b.prototype.clearAllAnimationClasses=function(){this.adapter.removeClass(h),this.adapter.removeClass(m)},b.prototype.setTooltipPosition=function(t){var e=t.xPos,r=t.yPos,i=t.withCaretPos;this.hasCaret&&i?this.tooltipPositionWithCaret=i:(e&&(this.xTooltipPos=e),r&&(this.yTooltipPos=r))},b.prototype.setAnchorBoundaryType=function(t){t===P.AnchorBoundaryType.UNBOUNDED?this.anchorGap=P.numbers.UNBOUNDED_ANCHOR_GAP:this.anchorGap=P.numbers.BOUNDED_ANCHOR_GAP},b.prototype.setShowDelay=function(t){this.showDelayMs=t},b.prototype.setHideDelay=function(t){this.hideDelayMs=t},b.prototype.isTooltipMultiline=function(){var t=this.adapter.getTooltipSize();return t.height>P.numbers.MIN_HEIGHT&&t.width>=P.numbers.MAX_WIDTH},b.prototype.positionPlainTooltip=function(){var t=this.calculateTooltipStyles(this.anchorRect),e=t.top,r=t.yTransformOrigin,i=t.left,n=t.xTransformOrigin,o=A?y.getCorrectPropertyName(window,"transform"):"transform";this.adapter.setSurfaceAnimationStyleProperty(o+"-origin",n+" "+r),this.adapter.setStyleProperty("top",e+"px"),this.adapter.setStyleProperty("left",i+"px")},b.prototype.positionRichTooltip=function(){var t,e,r,i;this.adapter.setStyleProperty("width",""),this.adapter.setStyleProperty("left","-"+P.numbers.RICH_MAX_WIDTH+"px");var n=Number(this.adapter.getComputedStyleProperty("width").slice(0,-2)),o=isFinite(n)?n:P.numbers.RICH_MAX_WIDTH,a=Math.max(this.adapter.getViewportWidth()-2*P.numbers.MIN_VIEWPORT_TOOLTIP_THRESHOLD,P.numbers.MIN_WIDTH),s=Math.min(a,o);this.adapter.setStyleProperty("width",s+"px");var l=this.hasCaret?this.calculateTooltipWithCaretStyles(this.anchorRect):this.calculateTooltipStyles(this.anchorRect),c=l.top,u=l.yTransformOrigin,d=l.left,p=l.xTransformOrigin,h=A?y.getCorrectPropertyName(window,"transform"):"transform";this.adapter.setSurfaceAnimationStyleProperty(h+"-origin",p+" "+u);var f=d-(null!==(e=null===(t=this.parentRect)||void 0===t?void 0:t.left)&&void 0!==e?e:0),T=c-(null!==(i=null===(r=this.parentRect)||void 0===r?void 0:r.top)&&void 0!==i?i:0);this.adapter.setStyleProperty("top",T+"px"),this.adapter.setStyleProperty("left",f+"px")},b.prototype.calculateTooltipStyles=function(t){if(!t)return{top:0,left:0};var e=this.adapter.getTooltipSize(),r=this.calculateYTooltipDistance(t,e.height),i=this.calculateXTooltipDistance(t,e.width);return{top:r.distance,yTransformOrigin:r.yTransformOrigin,left:i.distance,xTransformOrigin:i.xTransformOrigin}},b.prototype.calculateXTooltipDistance=function(t,e){var r,i,n,o,a,s,l,c=!this.adapter.isRTL();if(this.richTooltip)r=c?t.left-e:t.right,i=c?t.right:t.left-e,s=c?P.strings.RIGHT:P.strings.LEFT,l=c?P.strings.LEFT:P.strings.RIGHT;else{r=c?t.left:t.right-e,i=c?t.right-e:t.left,n=t.left+(t.width-e)/2;var u=t.left-(e+this.anchorGap),d=t.right+this.anchorGap;o=c?u:d,a=c?d:u,s=c?P.strings.LEFT:P.strings.RIGHT,l=c?P.strings.RIGHT:P.strings.LEFT}var p=[r,n,i];this.xTooltipPos===P.XPosition.SIDE_START?p.push(o):this.xTooltipPos===P.XPosition.SIDE_END&&p.push(a);var h=this.richTooltip?this.determineValidPositionOptions(r,i):this.determineValidPositionOptions.apply(this,E([],T(p)));if(this.xTooltipPos===P.XPosition.START&&h.has(r))return{distance:r,xTransformOrigin:s};if(this.xTooltipPos===P.XPosition.END&&h.has(i))return{distance:i,xTransformOrigin:l};if(this.xTooltipPos===P.XPosition.CENTER&&h.has(n))return{distance:n,xTransformOrigin:P.strings.CENTER};if(this.xTooltipPos===P.XPosition.SIDE_START&&h.has(o))return{distance:o,xTransformOrigin:l};if(this.xTooltipPos===P.XPosition.SIDE_END&&h.has(a))return{distance:a,xTransformOrigin:s};var f=(this.richTooltip?[{distance:i,xTransformOrigin:l},{distance:r,xTransformOrigin:s}]:[{distance:n,xTransformOrigin:P.strings.CENTER},{distance:r,xTransformOrigin:s},{distance:i,xTransformOrigin:l}]).find(function(t){var e=t.distance;return h.has(e)});return f||(t.left<0?{distance:this.minViewportTooltipThreshold,xTransformOrigin:P.strings.LEFT}:{distance:this.adapter.getViewportWidth()-(e+this.minViewportTooltipThreshold),xTransformOrigin:P.strings.RIGHT})},b.prototype.determineValidPositionOptions=function(){for(var e,t,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var n=new Set,o=new Set;try{for(var a=g(r),s=a.next();!s.done;s=a.next()){var l=s.value;this.positionHonorsViewportThreshold(l)?n.add(l):this.positionDoesntCollideWithViewport(l)&&o.add(l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n.size?n:o},b.prototype.positionHonorsViewportThreshold=function(t){var e=this.adapter.getViewportWidth();return t+this.adapter.getTooltipSize().width<=e-this.minViewportTooltipThreshold&&t>=this.minViewportTooltipThreshold},b.prototype.positionDoesntCollideWithViewport=function(t){var e=this.adapter.getViewportWidth();return t+this.adapter.getTooltipSize().width<=e&&0<=t},b.prototype.calculateYTooltipDistance=function(t,e){var r=t.bottom+this.anchorGap,i=t.top-(this.anchorGap+e),n=t.top+t.height/2-e/2,o=[i,r];this.yTooltipPos===P.YPosition.SIDE&&o.push(n);var a=this.determineValidYPositionOptions.apply(this,E([],T(o)));return this.yTooltipPos===P.YPosition.ABOVE&&a.has(i)?{distance:i,yTransformOrigin:P.strings.BOTTOM}:this.yTooltipPos===P.YPosition.BELOW&&a.has(r)?{distance:r,yTransformOrigin:P.strings.TOP}:this.yTooltipPos===P.YPosition.SIDE&&a.has(n)?{distance:n,yTransformOrigin:P.strings.CENTER}:a.has(r)?{distance:r,yTransformOrigin:P.strings.TOP}:a.has(i)?{distance:i,yTransformOrigin:P.strings.BOTTOM}:{distance:r,yTransformOrigin:P.strings.TOP}},b.prototype.determineValidYPositionOptions=function(){for(var e,t,r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];var n=new Set,o=new Set;try{for(var a=g(r),s=a.next();!s.done;s=a.next()){var l=s.value;this.yPositionHonorsViewportThreshold(l)?n.add(l):this.yPositionDoesntCollideWithViewport(l)&&o.add(l)}}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}return n.size?n:o},b.prototype.yPositionHonorsViewportThreshold=function(t){var e=this.adapter.getViewportHeight();return t+this.adapter.getTooltipSize().height+this.minViewportTooltipThreshold<=e&&t>=this.minViewportTooltipThreshold},b.prototype.yPositionDoesntCollideWithViewport=function(t){var e=this.adapter.getViewportHeight();return t+this.adapter.getTooltipSize().height<=e&&0<=t},b.prototype.calculateTooltipWithCaretStyles=function(t){this.adapter.clearTooltipCaretStyles();var e=this.adapter.getTooltipCaretBoundingRect();if(!t||!e)return{position:P.PositionWithCaret.DETECTED,top:0,left:0};var r=e.width/P.numbers.ANIMATION_SCALE,i=e.height/P.numbers.ANIMATION_SCALE/2,n=this.adapter.getTooltipSize(),o=this.calculateYWithCaretDistanceOptions(t,n.height,{caretWidth:r,caretHeight:i}),a=this.calculateXWithCaretDistanceOptions(t,n.width,{caretWidth:r,caretHeight:i}),s=this.validateTooltipWithCaretDistances(o,a);s.size<1&&(s=this.generateBackupPositionOption(t,n,{caretWidth:r,caretHeight:i}));var l=this.determineTooltipWithCaretDistance(s),c=l.position,u=l.xDistance,d=l.yDistance,p=this.setCaretPositionStyles(c,{caretWidth:r,caretHeight:i});return{yTransformOrigin:p.yTransformOrigin,xTransformOrigin:p.xTransformOrigin,top:d,left:u}},b.prototype.calculateXWithCaretDistanceOptions=function(t,e,r){var i=r.caretWidth,n=r.caretHeight,o=!this.adapter.isRTL(),a=t.left+t.width/2,s=t.left-(e+this.anchorGap+n),l=t.right+this.anchorGap+n,c=o?s:l,u=o?l:s,d=a-(P.numbers.CARET_INDENTATION+i/2),p=a-(e-P.numbers.CARET_INDENTATION-i/2),h=o?d:p,f=o?p:d,T=a-e/2;return new Map([[P.XPositionWithCaret.START,h],[P.XPositionWithCaret.CENTER,T],[P.XPositionWithCaret.END,f],[P.XPositionWithCaret.SIDE_END,u],[P.XPositionWithCaret.SIDE_START,c]])},b.prototype.calculateYWithCaretDistanceOptions=function(t,e,r){var i=r.caretWidth,n=r.caretHeight,o=t.top+t.height/2,a=t.bottom+this.anchorGap+n,s=t.top-(this.anchorGap+e+n),l=o-(P.numbers.CARET_INDENTATION+i/2),c=o-e/2,u=o-(e-P.numbers.CARET_INDENTATION-i/2);return new Map([[P.YPositionWithCaret.ABOVE,s],[P.YPositionWithCaret.BELOW,a],[P.YPositionWithCaret.SIDE_TOP,l],[P.YPositionWithCaret.SIDE_CENTER,c],[P.YPositionWithCaret.SIDE_BOTTOM,u]])},b.prototype.repositionTooltipOnAnchorMove=function(){var t=this.adapter.getAnchorBoundingRect();if(t&&this.anchorRect){var e=this.adapter.getViewportHeight();t.top+t.height<0||t.bottom-t.height>=e||t.top===this.anchorRect.top&&t.left===this.anchorRect.left&&t.height===this.anchorRect.height&&t.width===this.anchorRect.width||(this.anchorRect=t,this.parentRect=this.adapter.getParentBoundingRect(),this.richTooltip?this.positionRichTooltip():this.positionPlainTooltip())}},b.prototype.validateTooltipWithCaretDistances=function(t,e){var r,i,n,o,a,s,l=new Map,c=new Map,u=new Map([[P.YPositionWithCaret.ABOVE,[P.XPositionWithCaret.START,P.XPositionWithCaret.CENTER,P.XPositionWithCaret.END]],[P.YPositionWithCaret.BELOW,[P.XPositionWithCaret.START,P.XPositionWithCaret.CENTER,P.XPositionWithCaret.END]],[P.YPositionWithCaret.SIDE_TOP,[P.XPositionWithCaret.SIDE_START,P.XPositionWithCaret.SIDE_END]],[P.YPositionWithCaret.SIDE_CENTER,[P.XPositionWithCaret.SIDE_START,P.XPositionWithCaret.SIDE_END]],[P.YPositionWithCaret.SIDE_BOTTOM,[P.XPositionWithCaret.SIDE_START,P.XPositionWithCaret.SIDE_END]]]);try{for(var d=g(u.keys()),p=d.next();!p.done;p=d.next()){var h=p.value,f=t.get(h);if(this.yPositionHonorsViewportThreshold(f))try{for(var T=(n=void 0,g(u.get(h))),E=T.next();!E.done;E=T.next()){var y=E.value,m=e.get(y);if(this.positionHonorsViewportThreshold(m)){var v=this.caretPositionOptionsMapping(y,h);l.set(v,{xDistance:m,yDistance:f})}}}catch(t){n={error:t}}finally{try{E&&!E.done&&(o=T.return)&&o.call(T)}finally{if(n)throw n.error}}if(this.yPositionDoesntCollideWithViewport(f))try{for(var S=(a=void 0,g(u.get(h))),A=S.next();!A.done;A=S.next())y=A.value,m=e.get(y),this.positionDoesntCollideWithViewport(m)&&(v=this.caretPositionOptionsMapping(y,h),c.set(v,{xDistance:m,yDistance:f}))}catch(t){a={error:t}}finally{try{A&&!A.done&&(s=S.return)&&s.call(S)}finally{if(a)throw a.error}}}}catch(t){r={error:t}}finally{try{p&&!p.done&&(i=d.return)&&i.call(d)}finally{if(r)throw r.error}}return l.size?l:c},b.prototype.generateBackupPositionOption=function(t,e,r){var i,n,o,a,s=!this.adapter.isRTL();n=t.left<0?(i=this.minViewportTooltipThreshold+r.caretHeight,s?P.XPositionWithCaret.END:P.XPositionWithCaret.START):(i=this.adapter.getViewportWidth()-(e.width+this.minViewportTooltipThreshold+r.caretHeight),s?P.XPositionWithCaret.START:P.XPositionWithCaret.END),a=t.top<0?(o=this.minViewportTooltipThreshold+r.caretHeight,P.YPositionWithCaret.BELOW):(o=this.adapter.getViewportHeight()-(e.height+this.minViewportTooltipThreshold+r.caretHeight),P.YPositionWithCaret.ABOVE);var l=this.caretPositionOptionsMapping(n,a);return new Map([[l,{xDistance:i,yDistance:o}]])},b.prototype.determineTooltipWithCaretDistance=function(e){if(e.has(this.tooltipPositionWithCaret)){var t=e.get(this.tooltipPositionWithCaret);return{position:this.tooltipPositionWithCaret,xDistance:t.xDistance,yDistance:t.yDistance}}var r=[P.PositionWithCaret.ABOVE_START,P.PositionWithCaret.ABOVE_CENTER,P.PositionWithCaret.ABOVE_END,P.PositionWithCaret.TOP_SIDE_START,P.PositionWithCaret.CENTER_SIDE_START,P.PositionWithCaret.BOTTOM_SIDE_START,P.PositionWithCaret.TOP_SIDE_END,P.PositionWithCaret.CENTER_SIDE_END,P.PositionWithCaret.BOTTOM_SIDE_END,P.PositionWithCaret.BELOW_START,P.PositionWithCaret.BELOW_CENTER,P.PositionWithCaret.BELOW_END].find(function(t){return e.has(t)}),i=e.get(r);return{position:r,xDistance:i.xDistance,yDistance:i.yDistance}},b.prototype.caretPositionOptionsMapping=function(t,e){switch(e){case P.YPositionWithCaret.ABOVE:if(t===P.XPositionWithCaret.START)return P.PositionWithCaret.ABOVE_START;if(t===P.XPositionWithCaret.CENTER)return P.PositionWithCaret.ABOVE_CENTER;if(t===P.XPositionWithCaret.END)return P.PositionWithCaret.ABOVE_END;break;case P.YPositionWithCaret.BELOW:if(t===P.XPositionWithCaret.START)return P.PositionWithCaret.BELOW_START;if(t===P.XPositionWithCaret.CENTER)return P.PositionWithCaret.BELOW_CENTER;if(t===P.XPositionWithCaret.END)return P.PositionWithCaret.BELOW_END;break;case P.YPositionWithCaret.SIDE_TOP:if(t===P.XPositionWithCaret.SIDE_START)return P.PositionWithCaret.TOP_SIDE_START;if(t===P.XPositionWithCaret.SIDE_END)return P.PositionWithCaret.TOP_SIDE_END;break;case P.YPositionWithCaret.SIDE_CENTER:if(t===P.XPositionWithCaret.SIDE_START)return P.PositionWithCaret.CENTER_SIDE_START;if(t===P.XPositionWithCaret.SIDE_END)return P.PositionWithCaret.CENTER_SIDE_END;break;case P.YPositionWithCaret.SIDE_BOTTOM:if(t===P.XPositionWithCaret.SIDE_START)return P.PositionWithCaret.BOTTOM_SIDE_START;if(t===P.XPositionWithCaret.SIDE_END)return P.PositionWithCaret.BOTTOM_SIDE_END}throw new Error("MDCTooltipFoundation: Invalid caret position of "+t+", "+e)},b.prototype.setCaretPositionStyles=function(t,e){var r,i,n=this.calculateCaretPositionOnTooltip(t,e);if(!n)return{yTransformOrigin:0,xTransformOrigin:0};this.adapter.clearTooltipCaretStyles(),this.adapter.setTooltipCaretStyle(n.yAlignment,n.yAxisPx),this.adapter.setTooltipCaretStyle(n.xAlignment,n.xAxisPx);var o=n.skew*(Math.PI/180),a=Math.cos(o);this.adapter.setTooltipCaretStyle("transform","rotate("+n.rotation+"deg) skewY("+n.skew+"deg) scaleX("+a+")"),this.adapter.setTooltipCaretStyle("transform-origin",n.xAlignment+" "+n.yAlignment);try{for(var s=g(n.caretCorners),l=s.next();!l.done;l=s.next()){var c=l.value;this.adapter.setTooltipCaretStyle(c,"0")}}catch(t){r={error:t}}finally{try{l&&!l.done&&(i=s.return)&&i.call(s)}finally{if(r)throw r.error}}return{yTransformOrigin:n.yTransformOrigin,xTransformOrigin:n.xTransformOrigin}},b.prototype.calculateCaretPositionOnTooltip=function(t,e){var r=!this.adapter.isRTL(),i=this.adapter.getComputedStyleProperty("width"),n=this.adapter.getComputedStyleProperty("height");if(i&&n&&e){var o="calc(("+i+" - "+e.caretWidth+"px) / 2)",a="calc(("+n+" - "+e.caretWidth+"px) / 2)",s="0",l=P.numbers.CARET_INDENTATION+"px",c="calc("+i+" - "+l+")",u="calc("+n+" - "+l+")",d=Math.abs(55),p=["border-bottom-right-radius","border-top-left-radius"],h=["border-bottom-left-radius","border-top-right-radius"];switch(t){case P.PositionWithCaret.BELOW_CENTER:return{yAlignment:P.strings.TOP,xAlignment:P.strings.LEFT,yAxisPx:s,xAxisPx:o,rotation:-35,skew:-20,xTransformOrigin:o,yTransformOrigin:s,caretCorners:p};case P.PositionWithCaret.BELOW_END:return{yAlignment:P.strings.TOP,xAlignment:r?P.strings.RIGHT:P.strings.LEFT,yAxisPx:s,xAxisPx:l,rotation:r?35:-35,skew:r?20:-20,xTransformOrigin:r?c:l,yTransformOrigin:s,caretCorners:r?h:p};case P.PositionWithCaret.BELOW_START:return{yAlignment:P.strings.TOP,xAlignment:r?P.strings.LEFT:P.strings.RIGHT,yAxisPx:s,xAxisPx:l,rotation:r?-35:35,skew:r?-20:20,xTransformOrigin:r?l:c,yTransformOrigin:s,caretCorners:r?p:h};case P.PositionWithCaret.TOP_SIDE_END:return{yAlignment:P.strings.TOP,xAlignment:r?P.strings.LEFT:P.strings.RIGHT,yAxisPx:l,xAxisPx:s,rotation:r?d:-1*d,skew:r?-20:20,xTransformOrigin:r?s:i,yTransformOrigin:l,caretCorners:r?p:h};case P.PositionWithCaret.CENTER_SIDE_END:return{yAlignment:P.strings.TOP,xAlignment:r?P.strings.LEFT:P.strings.RIGHT,yAxisPx:a,xAxisPx:s,rotation:r?d:-1*d,skew:r?-20:20,xTransformOrigin:r?s:i,yTransformOrigin:a,caretCorners:r?p:h};case P.PositionWithCaret.BOTTOM_SIDE_END:return{yAlignment:P.strings.BOTTOM,xAlignment:r?P.strings.LEFT:P.strings.RIGHT,yAxisPx:l,xAxisPx:s,rotation:r?-1*d:d,skew:r?20:-20,xTransformOrigin:r?s:i,yTransformOrigin:u,caretCorners:r?h:p};case P.PositionWithCaret.TOP_SIDE_START:return{yAlignment:P.strings.TOP,xAlignment:r?P.strings.RIGHT:P.strings.LEFT,yAxisPx:l,xAxisPx:s,rotation:r?-1*d:d,skew:r?20:-20,xTransformOrigin:r?i:s,yTransformOrigin:l,caretCorners:r?h:p};case P.PositionWithCaret.CENTER_SIDE_START:return{yAlignment:P.strings.TOP,xAlignment:r?P.strings.RIGHT:P.strings.LEFT,yAxisPx:a,xAxisPx:s,rotation:r?-1*d:d,skew:r?20:-20,xTransformOrigin:r?i:s,yTransformOrigin:a,caretCorners:r?h:p};case P.PositionWithCaret.BOTTOM_SIDE_START:return{yAlignment:P.strings.BOTTOM,xAlignment:r?P.strings.RIGHT:P.strings.LEFT,yAxisPx:l,xAxisPx:s,rotation:r?d:-1*d,skew:r?-20:20,xTransformOrigin:r?i:s,yTransformOrigin:u,caretCorners:r?p:h};case P.PositionWithCaret.ABOVE_CENTER:return{yAlignment:P.strings.BOTTOM,xAlignment:P.strings.LEFT,yAxisPx:s,xAxisPx:o,rotation:35,skew:20,xTransformOrigin:o,yTransformOrigin:n,caretCorners:h};case P.PositionWithCaret.ABOVE_END:return{yAlignment:P.strings.BOTTOM,xAlignment:r?P.strings.RIGHT:P.strings.LEFT,yAxisPx:s,xAxisPx:l,rotation:r?-35:35,skew:r?-20:20,xTransformOrigin:r?c:l,yTransformOrigin:n,caretCorners:r?p:h};default:case P.PositionWithCaret.ABOVE_START:return{yAlignment:P.strings.BOTTOM,xAlignment:r?P.strings.LEFT:P.strings.RIGHT,yAxisPx:s,xAxisPx:l,rotation:r?35:-35,skew:r?20:-20,xTransformOrigin:r?l:c,yTransformOrigin:n,caretCorners:r?h:p}}}},b.prototype.clearShowTimeout=function(){this.showTimeout&&(clearTimeout(this.showTimeout),this.showTimeout=null)},b.prototype.clearHideTimeout=function(){this.hideTimeout&&(clearTimeout(this.hideTimeout),this.hideTimeout=null)},b.prototype.attachScrollHandler=function(t){var e=this;this.addAncestorScrollEventListeners.push(function(){t("scroll",e.windowScrollHandler)})},b.prototype.removeScrollHandler=function(t){var e=this;this.removeAncestorScrollEventListeners.push(function(){t("scroll",e.windowScrollHandler)})},b.prototype.destroy=function(){var e,t;this.frameId&&(cancelAnimationFrame(this.frameId),this.frameId=null),this.clearHideTimeout(),this.clearShowTimeout(),this.adapter.removeClass(d),this.adapter.removeClass(h),this.adapter.removeClass(p),this.adapter.removeClass(f),this.adapter.removeClass(m),this.richTooltip&&this.adapter.deregisterEventHandler("focusout",this.richTooltipFocusOutHandler),this.persistentTooltip||(this.adapter.deregisterEventHandler("mouseenter",this.tooltipMouseEnterHandler),this.adapter.deregisterEventHandler("mouseleave",this.tooltipMouseLeaveHandler)),this.adapter.deregisterAnchorEventHandler("blur",this.anchorBlurHandler),this.adapter.deregisterDocumentEventHandler("click",this.documentClickHandler),this.adapter.deregisterDocumentEventHandler("keydown",this.documentKeydownHandler),this.adapter.deregisterWindowEventHandler("scroll",this.windowScrollHandler),this.adapter.deregisterWindowEventHandler("resize",this.windowResizeHandler);try{for(var r=g(this.removeAncestorScrollEventListeners),i=r.next();!i.done;i=r.next())(0,i.value)()}catch(t){e={error:t}}finally{try{i&&!i.done&&(t=r.return)&&t.call(r)}finally{if(e)throw e.error}}this.animFrame.cancelAll()},b);function b(t){var e=S.call(this,o(o({},b.defaultAdapter),t))||this;return e.tooltipShown=!1,e.anchorGap=P.numbers.BOUNDED_ANCHOR_GAP,e.xTooltipPos=P.XPosition.DETECTED,e.yTooltipPos=P.YPosition.DETECTED,e.tooltipPositionWithCaret=P.PositionWithCaret.DETECTED,e.minViewportTooltipThreshold=P.numbers.MIN_VIEWPORT_TOOLTIP_THRESHOLD,e.hideDelayMs=P.numbers.HIDE_DELAY_MS,e.showDelayMs=P.numbers.SHOW_DELAY_MS,e.anchorRect=null,e.parentRect=null,e.frameId=null,e.hideTimeout=null,e.showTimeout=null,e.addAncestorScrollEventListeners=new Array,e.removeAncestorScrollEventListeners=new Array,e.animFrame=new s.AnimationFrame,e.anchorBlurHandler=function(t){e.handleAnchorBlur(t)},e.documentClickHandler=function(t){e.handleDocumentClick(t)},e.documentKeydownHandler=function(t){e.handleKeydown(t)},e.tooltipMouseEnterHandler=function(){e.handleTooltipMouseEnter()},e.tooltipMouseLeaveHandler=function(){e.handleTooltipMouseLeave()},e.richTooltipFocusOutHandler=function(t){e.handleRichTooltipFocusOut(t)},e.windowScrollHandler=function(){e.handleWindowScrollEvent()},e.windowResizeHandler=function(){e.handleWindowChangeEvent()},e}e.MDCTooltipFoundation=O,e.default=O},18:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r);var n=Object.getOwnPropertyDescriptor(e,r);n&&("get"in n?e.__esModule:!n.writable&&!n.configurable)||(n={enumerable:!0,get:function(){return e[r]}}),Object.defineProperty(t,i,n)}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&i(e,t,r);return n(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.safeWorker=e.safeWindow=e.safeServiceWorkerContainer=e.safeRange=e.safeLocation=e.safeGlobal=e.safeDomParser=e.safeDocument=e.safeStyleEl=e.safeScriptEl=e.safeObjectEl=e.safeLinkEl=e.safeInputEl=e.safeIframeEl=e.safeFormEl=e.safeEmbedEl=e.safeElement=e.safeButtonEl=e.safeAreaEl=e.safeAnchorEl=void 0,e.safeAnchorEl=o(r(30)),e.safeAreaEl=o(r(31)),e.safeButtonEl=o(r(32)),e.safeElement=o(r(15)),e.safeEmbedEl=o(r(33)),e.safeFormEl=o(r(34)),e.safeIframeEl=o(r(35)),e.safeInputEl=o(r(36)),e.safeLinkEl=o(r(37)),e.safeObjectEl=o(r(38)),e.safeScriptEl=o(r(39)),e.safeStyleEl=o(r(40)),e.safeDocument=o(r(41)),e.safeDomParser=o(r(42)),e.safeGlobal=o(r(43)),e.safeLocation=o(r(44)),e.safeRange=o(r(45)),e.safeServiceWorkerContainer=o(r(46)),e.safeWindow=o(r(47)),e.safeWorker=o(r(48))},19:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeAttrPrefix=void 0,r(0);var i=r(8);r(6),r(21);e.safeAttrPrefix=function(t){var e=t[0].toLowerCase();return(0,i.createAttributePrefix)(e)}},2:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapHtml=e.isHtml=e.EMPTY_HTML=e.createHtml=e.SafeHtml=void 0,r(0);var i=r(4),n=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedHtml.toString()},a);function a(t,e){this.privateDoNotAccessOrElseWrappedHtml=t}function s(t,e){return null!=e?e:new o(t,i.secretToken)}var l=window.TrustedHTML;e.SafeHtml=null!=l?l:o,e.createHtml=function(t){var e,r=t;return s(r,null===(e=(0,n.getTrustedTypesPolicy)())||void 0===e?void 0:e.createHTML(r))},e.EMPTY_HTML=function(){var t;return s("",null===(t=(0,n.getTrustedTypes)())||void 0===t?void 0:t.emptyHTML)}(),e.isHtml=function(t){return t instanceof e.SafeHtml},e.unwrapHtml=function(t){var e;if(null===(e=(0,n.getTrustedTypes)())||void 0===e?void 0:e.isHTML(t))return t;if(t instanceof o)return t.privateDoNotAccessOrElseWrappedHtml;throw new Error("")}},20:function(t,e){var r,i,n=t.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function s(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(t){r=o}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(t){i=a}}();var l,c=[],u=!1,d=-1;function p(){u&&l&&(u=!1,l.length?c=l.concat(c):d=-1,c.length&&h())}function h(){if(!u){var t=s(p);u=!0;for(var e=c.length;e;){for(l=c,c=[];++d<e;)l&&l[d].run();d=-1,e=c.length}l=null,u=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(t)}}function f(t,e){this.fun=t,this.array=e}function T(){}n.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];c.push(new f(t,e)),1!==c.length||u||s(h)},f.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=T,n.addListener=T,n.once=T,n.off=T,n.removeListener=T,n.removeAllListeners=T,n.emit=T,n.prependListener=T,n.prependOnceListener=T,n.listeners=function(t){return[]},n.binding=function(t){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(t){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},21:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SECURITY_SENSITIVE_ATTRIBUTES=void 0,e.SECURITY_SENSITIVE_ATTRIBUTES=["href","rel","src","srcdoc","action","formaction","sandbox","cite","poster","icon"]},22:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatHtmls=e.createScriptSrc=e.createScript=e.htmlEscape=void 0;var o=r(2),a=r(1),n=r(5);function s(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}e.htmlEscape=function(t,e){void 0===e&&(e={});var r=s(t);return e.preserveSpaces&&(r=r.replace(/(^|[\r\n\t ]) /g,"$1&#160;")),e.preserveNewlines&&(r=r.replace(/(\r\n|\n|\r)/g,"<br>")),e.preserveTabs&&(r=r.replace(/(\t+)/g,'<span style="white-space:pre">$1</span>')),(0,o.createHtml)(r)},e.createScript=function(t,e){void 0===e&&(e={});var r=(0,n.unwrapScript)(t).toString(),i="<script";return e.id&&(i+=' id="'.concat(s(e.id),'"')),e.nonce&&(i+=' nonce="'.concat(s(e.nonce),'"')),e.type&&(i+=' type="'.concat(s(e.type),'"')),i+=">".concat(r,"<\/script>"),(0,o.createHtml)(i)},e.createScriptSrc=function(t,e,r){var i=(0,a.unwrapResourceUrl)(t).toString(),n='<script src="'.concat(s(i),'"');return e&&(n+=" async"),r&&(n+=' nonce="'.concat(s(r),'"')),n+="><\/script>",(0,o.createHtml)(n)},e.concatHtmls=function(t){return(0,o.createHtml)(t.map(o.unwrapHtml).join(""))}},23:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createInertFragment=void 0;var i=r(15),n=r(2);e.createInertFragment=function(t){var e=document.createElement("template"),r=(0,n.createHtml)(t);return(0,i.setInnerHtml)(e,r),e.content}},24:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isElement=e.isText=e.getNodeName=void 0,e.getNodeName=function(t){var e=t.nodeName;return"string"==typeof e?e:"FORM"},e.isText=function(t){return t.nodeType===Node.TEXT_NODE},e.isElement=function(t){var e=t.nodeType;return e===Node.ELEMENT_NODE||"number"!=typeof e}},25:function(t,e,r){"use strict";var P=this&&this.__values||function(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},O=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(i=o.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return a};Object.defineProperty(e,"__esModule",{value:!0}),e.HtmlSanitizerBuilder=void 0;var i=r(4),n=r(14),o=r(16),b=r(11),a=(s.prototype.onlyAllowElements=function(t){var e,r,i=new Set,n=new Map;try{for(var o=P(t),a=o.next();!a.done;a=o.next()){var s=a.value;if(s=s.toUpperCase(),!this.sanitizerTable.isAllowedElement(s))throw new Error("Element: ".concat(s,", is not allowed by html5_contract.textpb"));var l=this.sanitizerTable.elementPolicies.get(s);void 0!==l?n.set(s,l):i.add(s)}}catch(t){e={error:t}}finally{try{a&&!a.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return this.sanitizerTable=new b.SanitizerTable(i,n,this.sanitizerTable.allowedGlobalAttributes,this.sanitizerTable.globalAttributePolicies),this},s.prototype.onlyAllowAttributes=function(t){var e,r,i,n,o,a,s=new Set,l=new Map,c=new Map;try{for(var u=P(t),d=u.next();!d.done;d=u.next()){var p=d.value;this.sanitizerTable.allowedGlobalAttributes.has(p)&&s.add(p),this.sanitizerTable.globalAttributePolicies.has(p)&&l.set(p,this.sanitizerTable.globalAttributePolicies.get(p))}}catch(t){e={error:t}}finally{try{d&&!d.done&&(r=u.return)&&r.call(u)}finally{if(e)throw e.error}}try{for(var h=P(this.sanitizerTable.elementPolicies.entries()),f=h.next();!f.done;f=h.next()){var T=O(f.value,2),E=T[0],y=T[1],m=new Map;try{for(var v=(o=void 0,P(y.entries())),S=v.next();!S.done;S=v.next()){var A=O(S.value,2),g=(p=A[0],A[1]);t.has(p)&&m.set(p,g)}}catch(t){o={error:t}}finally{try{S&&!S.done&&(a=v.return)&&a.call(v)}finally{if(o)throw o.error}}c.set(E,m)}}catch(t){i={error:t}}finally{try{f&&!f.done&&(n=h.return)&&n.call(h)}finally{if(i)throw i.error}}return this.sanitizerTable=new b.SanitizerTable(this.sanitizerTable.allowedElements,c,s,l),this},s.prototype.allowDataAttributes=function(t){var e,r,i=new Set(this.sanitizerTable.allowedGlobalAttributes);try{for(var n=P(t),o=n.next();!o.done;o=n.next()){var a=o.value;if(0!==a.indexOf("data-"))throw new Error("data attribute: ".concat(a,' does not begin with the prefix "data-"'));i.add(a)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=n.return)&&r.call(n)}finally{if(e)throw e.error}}return this.sanitizerTable=new b.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,i,this.sanitizerTable.globalAttributePolicies),this},s.prototype.allowStyleAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("style",{policyAction:b.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE}),this.sanitizerTable=new b.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowClassAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("class",{policyAction:b.AttributePolicyAction.KEEP}),this.sanitizerTable=new b.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.allowIdAttributes=function(){var t=new Map(this.sanitizerTable.globalAttributePolicies);return t.set("id",{policyAction:b.AttributePolicyAction.KEEP}),this.sanitizerTable=new b.SanitizerTable(this.sanitizerTable.allowedElements,this.sanitizerTable.elementPolicies,this.sanitizerTable.allowedGlobalAttributes,t),this},s.prototype.build=function(){if(this.calledBuild)throw new Error("this sanitizer has already called build");return this.calledBuild=!0,new n.HtmlSanitizerImpl(this.sanitizerTable,i.secretToken)},s);function s(){this.calledBuild=!1,this.sanitizerTable=o.defaultSanitizerTable}e.HtmlSanitizerBuilder=a},26:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.blobUrlFromScript=e.replaceFragment=e.appendParams=e.trustedResourceUrl=void 0,r(0);var s=r(1),i=r(5);r(6);e.trustedResourceUrl=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];if(0===e.length)return(0,s.createResourceUrl)(t[0]);t[0].toLowerCase();for(var i=[t[0]],n=0;n<e.length;n++)i.push(encodeURIComponent(e[n])),i.push(t[n+1]);return(0,s.createResourceUrl)(i.join(""))},e.appendParams=function(t,e){var o=(0,s.unwrapResourceUrl)(t).toString();if(/#/.test(o)){throw new Error("")}var a=/\?/.test(o)?"&":"?";return e.forEach(function(t,e){for(var r=t instanceof Array?t:[t],i=0;i<r.length;i++){var n=r[i];null!=n&&(o+=a+encodeURIComponent(e)+"="+encodeURIComponent(String(n)),a="&")}}),(0,s.createResourceUrl)(o)};var n=/[^#]*/;e.replaceFragment=function(t,e){var r=(0,s.unwrapResourceUrl)(t).toString();return(0,s.createResourceUrl)(n.exec(r)[0]+"#"+e)},e.blobUrlFromScript=function(t){var e=(0,i.unwrapScript)(t).toString(),r=new Blob([e],{type:"text/javascript"});return(0,s.createResourceUrl)(URL.createObjectURL(r))}},27:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.safeScriptWithArgs=e.scriptFromJson=e.concatScripts=e.safeScript=void 0,r(0);var n=r(5);r(6);function o(t){return(0,n.createScript)(JSON.stringify(t).replace(/</g,"\\x3c"))}e.safeScript=function(t){return(0,n.createScript)(t[0])},e.concatScripts=function(t){return(0,n.createScript)(t.map(n.unwrapScript).join(""))},e.scriptFromJson=o,e.safeScriptWithArgs=function(i){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var r=t.map(function(t){return o(t).toString()});return(0,n.createScript)("(".concat(i.join(""),")(").concat(r.join(","),")"))}}},28:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyles=e.safeStyle=void 0,r(0);r(6);var i=r(10);e.safeStyle=function(t){var e=t[0];return(0,i.createStyle)(e)},e.concatStyles=function(t){return(0,i.createStyle)(t.map(i.unwrapStyle).join(""))}},29:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.concatStyleSheets=e.safeStyleSheet=void 0,r(0);r(6);var i=r(12);e.safeStyleSheet=function(t){var e=t[0];return(0,i.createStyleSheet)(e)},e.concatStyleSheets=function(t){return(0,i.createStyleSheet)(t.map(i.unwrapStyleSheet).join(""))}},3:function(t,e,r){"use strict";function i(t){var e;try{e=new URL(t)}catch(t){return"https:"}return e.protocol}Object.defineProperty(e,"__esModule",{value:!0}),e.restrictivelySanitizeUrl=e.unwrapUrlOrSanitize=e.sanitizeJavascriptUrl=void 0,r(0);var n=["data:","http:","https:","mailto:","ftp:"];function o(t){if("javascript:"!==i(t))return t}e.sanitizeJavascriptUrl=o,e.unwrapUrlOrSanitize=function(t){return o(t)},e.restrictivelySanitizeUrl=function(t){var e=i(t);return void 0!==e&&-1!==n.indexOf(e.toLowerCase())?t:"about:invalid#zClosurez"}},30:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var i=r(3);e.setHref=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},31:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHref=void 0;var i=r(3);e.setHref=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)}},313:function(t,e,r){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,r,i){void 0===i&&(i=r),Object.defineProperty(t,i,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]}),n=this&&this.__exportStar||function(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||i(e,t,r)};Object.defineProperty(e,"__esModule",{value:!0}),n(r(314),e),n(r(315),e),n(r(179),e),n(r(120),e)},314:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0})},315:function(t,e,r){"use strict";var i,n=this&&this.__makeTemplateObject||function(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t},o=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.MDCTooltip=void 0;var a,s,l=r(13),c=r(17),u=r(18),d=r(120),p=r(179),h=[c.safeAttrPrefix(s=s||n(["aria-"],["aria-"]))],f=(a=l.MDCComponent,o(T,a),T.attachTo=function(t){return new T(t)},T.prototype.initialize=function(){var t=this.root.getAttribute("id");if(!t)throw new Error("MDCTooltip: Tooltip component must have an id.");var e=document.querySelector('[data-tooltip-id="'+t+'"]')||document.querySelector('[aria-describedby="'+t+'"]');if(!e)throw new Error("MDCTooltip: Tooltip component requires an anchor element annotated with [aria-describedby] or [data-tooltip-id].");this.anchorElem=e},T.prototype.initialSyncWithDOM=function(){var e=this;this.isTooltipRich=this.foundation.isRich(),this.isTooltipPersistent=this.foundation.isPersistent(),this.handleMouseEnter=function(){e.foundation.handleAnchorMouseEnter()},this.handleFocus=function(t){e.foundation.handleAnchorFocus(t)},this.handleMouseLeave=function(){e.foundation.handleAnchorMouseLeave()},this.handleTransitionEnd=function(){e.foundation.handleTransitionEnd()},this.handleClick=function(){e.foundation.handleAnchorClick()},this.handleTouchstart=function(){e.foundation.handleAnchorTouchstart()},this.handleTouchend=function(){e.foundation.handleAnchorTouchend()},this.isTooltipRich&&this.isTooltipPersistent?this.anchorElem.addEventListener("click",this.handleClick):(this.anchorElem.addEventListener("mouseenter",this.handleMouseEnter),this.anchorElem.addEventListener("focus",this.handleFocus),this.anchorElem.addEventListener("mouseleave",this.handleMouseLeave),this.anchorElem.addEventListener("touchstart",this.handleTouchstart),this.anchorElem.addEventListener("touchend",this.handleTouchend)),this.listen("transitionend",this.handleTransitionEnd)},T.prototype.destroy=function(){this.anchorElem&&(this.isTooltipRich&&this.isTooltipPersistent?this.anchorElem.removeEventListener("click",this.handleClick):(this.anchorElem.removeEventListener("mouseenter",this.handleMouseEnter),this.anchorElem.removeEventListener("focus",this.handleFocus),this.anchorElem.removeEventListener("mouseleave",this.handleMouseLeave),this.anchorElem.removeEventListener("touchstart",this.handleTouchstart),this.anchorElem.removeEventListener("touchend",this.handleTouchend))),this.unlisten("transitionend",this.handleTransitionEnd),a.prototype.destroy.call(this)},T.prototype.setTooltipPosition=function(t){this.foundation.setTooltipPosition(t)},T.prototype.setAnchorBoundaryType=function(t){this.foundation.setAnchorBoundaryType(t)},T.prototype.setShowDelay=function(t){this.foundation.setShowDelay(t)},T.prototype.setHideDelay=function(t){this.foundation.setHideDelay(t)},T.prototype.hide=function(){this.foundation.hide()},T.prototype.isShown=function(){return this.foundation.isShown()},T.prototype.attachScrollHandler=function(t){this.foundation.attachScrollHandler(t)},T.prototype.removeScrollHandler=function(t){this.foundation.removeScrollHandler(t)},T.prototype.getDefaultFoundation=function(){var n=this,t={getAttribute:function(t){return n.root.getAttribute(t)},setAttribute:function(t,e){u.safeElement.setPrefixedAttribute(h,n.root,t,e)},removeAttribute:function(t){n.root.removeAttribute(t)},addClass:function(t){n.root.classList.add(t)},hasClass:function(t){return n.root.classList.contains(t)},removeClass:function(t){n.root.classList.remove(t)},getComputedStyleProperty:function(t){return window.getComputedStyle(n.root).getPropertyValue(t)},setStyleProperty:function(t,e){n.root.style.setProperty(t,e)},setSurfaceAnimationStyleProperty:function(t,e){var r=n.root.querySelector("."+d.CssClasses.SURFACE_ANIMATION);null==r||r.style.setProperty(t,e)},getViewportWidth:function(){return window.innerWidth},getViewportHeight:function(){return window.innerHeight},getTooltipSize:function(){return{width:n.root.offsetWidth,height:n.root.offsetHeight}},getAnchorBoundingRect:function(){return n.anchorElem?n.anchorElem.getBoundingClientRect():null},getParentBoundingRect:function(){var t,e;return null!==(e=null===(t=n.root.parentElement)||void 0===t?void 0:t.getBoundingClientRect())&&void 0!==e?e:null},getAnchorAttribute:function(t){return n.anchorElem?n.anchorElem.getAttribute(t):null},setAnchorAttribute:function(t,e){n.anchorElem&&u.safeElement.setPrefixedAttribute(h,n.anchorElem,t,e)},isRTL:function(){return"rtl"===getComputedStyle(n.root).direction},anchorContainsElement:function(t){var e;return!!(null===(e=n.anchorElem)||void 0===e?void 0:e.contains(t))},tooltipContainsElement:function(t){return n.root.contains(t)},focusAnchorElement:function(){var t;null===(t=n.anchorElem)||void 0===t||t.focus()},registerEventHandler:function(t,e){n.root instanceof HTMLElement&&n.root.addEventListener(t,e)},deregisterEventHandler:function(t,e){n.root instanceof HTMLElement&&n.root.removeEventListener(t,e)},registerAnchorEventHandler:function(t,e){var r;null===(r=n.anchorElem)||void 0===r||r.addEventListener(t,e)},deregisterAnchorEventHandler:function(t,e){var r;null===(r=n.anchorElem)||void 0===r||r.removeEventListener(t,e)},registerDocumentEventHandler:function(t,e){document.body.addEventListener(t,e)},deregisterDocumentEventHandler:function(t,e){document.body.removeEventListener(t,e)},registerWindowEventHandler:function(t,e){window.addEventListener(t,e)},deregisterWindowEventHandler:function(t,e){window.removeEventListener(t,e)},notifyHidden:function(){n.emit(d.events.HIDDEN,{})},notifyShown:function(){n.emit(d.events.SHOWN,{})},getTooltipCaretBoundingRect:function(){var t=n.root.querySelector("."+d.CssClasses.TOOLTIP_CARET_TOP);return t?t.getBoundingClientRect():null},setTooltipCaretStyle:function(t,e){var r=n.root.querySelector("."+d.CssClasses.TOOLTIP_CARET_TOP),i=n.root.querySelector("."+d.CssClasses.TOOLTIP_CARET_BOTTOM);r&&i&&(r.style.setProperty(t,e),i.style.setProperty(t,e))},clearTooltipCaretStyles:function(){var t=n.root.querySelector("."+d.CssClasses.TOOLTIP_CARET_TOP),e=n.root.querySelector("."+d.CssClasses.TOOLTIP_CARET_BOTTOM);t&&e&&(t.removeAttribute("style"),e.removeAttribute("style"))},getActiveElement:function(){return document.activeElement},isInstanceOfElement:function(t){return t instanceof Element}};return new p.MDCTooltipFoundation(t)},T);function T(){return null!==a&&a.apply(this,arguments)||this}e.MDCTooltip=f},32:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var i=r(3);e.setFormaction=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},33:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=void 0;var i=r(1);e.setSrc=function(t,e){t.src=(0,i.unwrapResourceUrl)(e)}},34:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setAction=void 0;var i=r(3);e.setAction=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&(t.action=r)}},35:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrcdoc=e.setSrc=void 0;var i=r(2),n=r(1);e.setSrc=function(t,e){t.src=(0,n.unwrapResourceUrl)(e).toString()},e.setSrcdoc=function(t,e){t.srcdoc=(0,i.unwrapHtml)(e)}},36:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setFormaction=void 0;var i=r(3);e.setFormaction=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&(t.formAction=r)}},37:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setHrefAndRel=void 0;var n=r(3),o=r(1),a=["alternate","author","bookmark","canonical","cite","help","icon","license","next","prefetch","dns-prefetch","prerender","preconnect","preload","prev","search","subresource"];e.setHrefAndRel=function(t,e,r){if(e instanceof o.TrustedResourceUrl)t.href=(0,o.unwrapResourceUrl)(e).toString();else{if(-1===a.indexOf(r))throw new Error('TrustedResourceUrl href attribute required with rel="'.concat(r,'"'));var i=(0,n.unwrapUrlOrSanitize)(e);if(void 0===i)return;t.href=i}t.rel=r}},38:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setData=void 0;var i=r(1);e.setData=function(t,e){t.data=(0,i.unwrapResourceUrl)(e)}},39:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setSrc=e.setTextContent=void 0;var i=r(1),n=r(5);function o(t){var e=function(t){var e,r=t.document,i=null===(e=r.querySelector)||void 0===e?void 0:e.call(r,"script[nonce]");return i&&(i.nonce||i.getAttribute("nonce"))||""}(t.ownerDocument&&t.ownerDocument.defaultView||window);e&&t.setAttribute("nonce",e)}e.setTextContent=function(t,e){t.textContent=(0,n.unwrapScript)(e),o(t)},e.setSrc=function(t,e){t.src=(0,i.unwrapResourceUrl)(e),o(t)}},4:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ensureTokenIsValid=e.secretToken=void 0,e.secretToken={},e.ensureTokenIsValid=function(t){if(t!==e.secretToken)throw new Error("Bad secret")}},40:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setTextContent=void 0;var i=r(12);e.setTextContent=function(t,e){t.textContent=(0,i.unwrapStyleSheet)(e)}},41:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.execCommandInsertHtml=e.execCommand=e.write=void 0;var o=r(2);e.write=function(t,e){t.write((0,o.unwrapHtml)(e))},e.execCommand=function(t,e,r){var i=String(e),n=r;return"inserthtml"===i.toLowerCase()&&(n=(0,o.unwrapHtml)(r)),t.execCommand(i,!1,n)},e.execCommandInsertHtml=function(t,e){return t.execCommand("insertHTML",!1,(0,o.unwrapHtml)(e))}},42:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.parseFromString=e.parseHtml=void 0;var i=r(2);function n(t,e,r){return t.parseFromString((0,i.unwrapHtml)(e),r)}e.parseHtml=function(t,e){return n(t,e,"text/html")},e.parseFromString=n},43:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.globalEval=void 0;var n=r(5);e.globalEval=function(t,e){var r=(0,n.unwrapScript)(e),i=t.eval(r);return i===r&&(i=t.eval(r.toString())),i}},44:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assign=e.replace=e.setHref=void 0;var i=r(3);e.setHref=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&(t.href=r)},e.replace=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&t.replace(r)},e.assign=function(t,e){var r=(0,i.unwrapUrlOrSanitize)(e);void 0!==r&&t.assign(r)}},45:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createContextualFragment=void 0;var i=r(2);e.createContextualFragment=function(t,e){return t.createContextualFragment((0,i.unwrapHtml)(e))}},46:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.register=void 0;var i=r(1);e.register=function(t,e,r){return t.register((0,i.unwrapResourceUrl)(e),r)}},47:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.open=void 0;var o=r(3);e.open=function(t,e,r,i){var n=(0,o.unwrapUrlOrSanitize)(e);return void 0!==n?t.open(n,r,i):null}},48:function(t,e,r){"use strict";var i=this&&this.__read||function(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,o=r.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(i=o.next()).done;)a.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}return a},n=this&&this.__spreadArray||function(t,e,r){if(r||2===arguments.length)for(var i,n=0,o=e.length;n<o;n++)!i&&n in e||((i=i||Array.prototype.slice.call(e,0,n))[n]=e[n]);return t.concat(i||Array.prototype.slice.call(e))};Object.defineProperty(e,"__esModule",{value:!0}),e.importScripts=e.createShared=e.create=void 0;var o=r(1);e.create=function(t,e){return new Worker((0,o.unwrapResourceUrl)(t),e)},e.createShared=function(t,e){return new SharedWorker((0,o.unwrapResourceUrl)(t),e)},e.importScripts=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];t.importScripts.apply(t,n([],i(e.map(function(t){return(0,o.unwrapResourceUrl)(t)})),!1))}},5:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapScript=e.isScript=e.EMPTY_SCRIPT=e.createScript=e.SafeScript=void 0,r(0);var i=r(4),n=r(9),o=(a.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedScript.toString()},a);function a(t,e){this.privateDoNotAccessOrElseWrappedScript=t}function s(t,e){return null!=e?e:new o(t,i.secretToken)}var l=window.TrustedScript;e.SafeScript=null!=l?l:o,e.createScript=function(t){var e,r=t;return s(r,null===(e=(0,n.getTrustedTypesPolicy)())||void 0===e?void 0:e.createScript(r))},e.EMPTY_SCRIPT=function(){var t;return s("",null===(t=(0,n.getTrustedTypes)())||void 0===t?void 0:t.emptyScript)}(),e.isScript=function(t){return t instanceof e.SafeScript},e.unwrapScript=function(t){var e;if(null===(e=(0,n.getTrustedTypes)())||void 0===e?void 0:e.isScript(t))return t;if(t instanceof o)return t.privateDoNotAccessOrElseWrappedScript;throw new Error("")}},55:function(t,i,e){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.isNavigationEvent=i.normalizeKey=i.KEY=void 0,i.KEY={UNKNOWN:"Unknown",BACKSPACE:"Backspace",ENTER:"Enter",SPACEBAR:"Spacebar",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown",END:"End",HOME:"Home",ARROW_LEFT:"ArrowLeft",ARROW_UP:"ArrowUp",ARROW_RIGHT:"ArrowRight",ARROW_DOWN:"ArrowDown",DELETE:"Delete",ESCAPE:"Escape",TAB:"Tab"};var n=new Set;n.add(i.KEY.BACKSPACE),n.add(i.KEY.ENTER),n.add(i.KEY.SPACEBAR),n.add(i.KEY.PAGE_UP),n.add(i.KEY.PAGE_DOWN),n.add(i.KEY.END),n.add(i.KEY.HOME),n.add(i.KEY.ARROW_LEFT),n.add(i.KEY.ARROW_UP),n.add(i.KEY.ARROW_RIGHT),n.add(i.KEY.ARROW_DOWN),n.add(i.KEY.DELETE),n.add(i.KEY.ESCAPE),n.add(i.KEY.TAB);var r=8,o=13,a=32,s=33,l=34,c=35,u=36,d=37,p=38,h=39,f=40,T=46,E=27,y=9,m=new Map;m.set(r,i.KEY.BACKSPACE),m.set(o,i.KEY.ENTER),m.set(a,i.KEY.SPACEBAR),m.set(s,i.KEY.PAGE_UP),m.set(l,i.KEY.PAGE_DOWN),m.set(c,i.KEY.END),m.set(u,i.KEY.HOME),m.set(d,i.KEY.ARROW_LEFT),m.set(p,i.KEY.ARROW_UP),m.set(h,i.KEY.ARROW_RIGHT),m.set(f,i.KEY.ARROW_DOWN),m.set(T,i.KEY.DELETE),m.set(E,i.KEY.ESCAPE),m.set(y,i.KEY.TAB);var v=new Set;function S(t){var e=t.key;if(n.has(e))return e;var r=m.get(t.keyCode);return r||i.KEY.UNKNOWN}v.add(i.KEY.PAGE_UP),v.add(i.KEY.PAGE_DOWN),v.add(i.KEY.END),v.add(i.KEY.HOME),v.add(i.KEY.ARROW_LEFT),v.add(i.KEY.ARROW_UP),v.add(i.KEY.ARROW_RIGHT),v.add(i.KEY.ARROW_DOWN),i.normalizeKey=S,i.isNavigationEvent=function(t){return v.has(S(t))}},57:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getCorrectEventName=e.getCorrectPropertyName=void 0;var a={animation:{prefixed:"-webkit-animation",standard:"animation"},transform:{prefixed:"-webkit-transform",standard:"transform"},transition:{prefixed:"-webkit-transition",standard:"transition"}},s={animationend:{cssProperty:"animation",prefixed:"webkitAnimationEnd",standard:"animationend"},animationiteration:{cssProperty:"animation",prefixed:"webkitAnimationIteration",standard:"animationiteration"},animationstart:{cssProperty:"animation",prefixed:"webkitAnimationStart",standard:"animationstart"},transitionend:{cssProperty:"transition",prefixed:"webkitTransitionEnd",standard:"transitionend"}};function l(t){return Boolean(t.document)&&"function"==typeof t.document.createElement}e.getCorrectPropertyName=function(t,e){if(l(t)&&e in a){var r=t.document.createElement("div"),i=a[e],n=i.standard,o=i.prefixed;return n in r.style?n:o}return e},e.getCorrectEventName=function(t,e){if(l(t)&&e in s){var r=t.document.createElement("div"),i=s[e],n=i.standard,o=i.prefixed;return i.cssProperty in r.style?n:o}return e}},6:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.assertIsTemplateObject=void 0,e.assertIsTemplateObject=function(t,e,r){if(!Array.isArray(t)||!Array.isArray(t.raw)||!e&&1!==t.length)throw new TypeError(r)}},63:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AnimationFrame=void 0;var i=(n.prototype.request=function(e,r){var i=this;this.cancel(e);var t=requestAnimationFrame(function(t){i.rafIDs.delete(e),r(t)});this.rafIDs.set(e,t)},n.prototype.cancel=function(t){var e=this.rafIDs.get(t);e&&(cancelAnimationFrame(e),this.rafIDs.delete(t))},n.prototype.cancelAll=function(){var r=this;this.rafIDs.forEach(function(t,e){r.cancel(e)})},n.prototype.getQueue=function(){var r=[];return this.rafIDs.forEach(function(t,e){r.push(e)}),r},n);function n(){this.rafIDs=new Map}e.AnimationFrame=i},7:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.MDCFoundation=void 0;var i=(Object.defineProperty(n,"cssClasses",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(n,"strings",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(n,"numbers",{get:function(){return{}},enumerable:!1,configurable:!0}),Object.defineProperty(n,"defaultAdapter",{get:function(){return{}},enumerable:!1,configurable:!0}),n.prototype.init=function(){},n.prototype.destroy=function(){},n);function n(t){void 0===t&&(t={}),this.adapter=t}e.MDCFoundation=i,e.default=i},8:function(t,e,r){"use strict";var i,n=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});Object.defineProperty(e,"__esModule",{value:!0}),e.unwrapAttributePrefix=e.createAttributePrefix=e.SafeAttributePrefix=void 0,r(0);function o(){}var a=r(4);e.SafeAttributePrefix=o;var s,l=(n(c,s=o),c.prototype.toString=function(){return this.privateDoNotAccessOrElseWrappedAttrPrefix},c);function c(t,e){var r=s.call(this)||this;return r.privateDoNotAccessOrElseWrappedAttrPrefix=t,r}e.createAttributePrefix=function(t){return new l(t,a.secretToken)},e.unwrapAttributePrefix=function(t){if(t instanceof l)return t.privateDoNotAccessOrElseWrappedAttrPrefix;throw new Error("")}},9:function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TEST_ONLY=e.getTrustedTypesPolicy=e.getTrustedTypes=void 0;var i,n="google#safe";function o(){var t;return""!==n&&null!==(t=function(){if("undefined"!=typeof window)return window.trustedTypes}())&&void 0!==t?t:null}e.getTrustedTypes=o,e.getTrustedTypesPolicy=function(){var t,e;if(void 0===i)try{i=null!==(e=null===(t=o())||void 0===t?void 0:t.createPolicy(n,{createHTML:function(t){return t},createScript:function(t){return t},createScriptURL:function(t){return t}}))&&void 0!==e?e:null}catch(t){i=null}return i},e.TEST_ONLY={resetDefaults:function(){i=void 0,n="google#safe"},setTrustedTypesPolicyName:function(t){n=t}}}},n.c=i,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=313);function n(t){if(i[t])return i[t].exports;var e=i[t]={i:t,l:!1,exports:{}};return r[t].call(e.exports,e,e.exports,n),e.l=!0,e.exports}var r,i});