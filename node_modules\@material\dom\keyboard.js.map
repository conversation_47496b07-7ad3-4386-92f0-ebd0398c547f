{"version": 3, "file": "keyboard.js", "sourceRoot": "", "sources": ["keyboard.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;AAEH;;GAEG;AACH,MAAM,CAAC,IAAM,GAAG,GAAG;IACjB,OAAO,EAAE,SAAS;IAClB,SAAS,EAAE,WAAW;IACtB,KAAK,EAAE,OAAO;IACd,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,QAAQ;IACjB,SAAS,EAAE,UAAU;IACrB,GAAG,EAAE,KAAK;IACV,IAAI,EAAE,MAAM;IACZ,UAAU,EAAE,WAAW;IACvB,QAAQ,EAAE,SAAS;IACnB,WAAW,EAAE,YAAY;IACzB,UAAU,EAAE,WAAW;IACvB,MAAM,EAAE,QAAQ;IAChB,MAAM,EAAE,QAAQ;IAChB,GAAG,EAAE,KAAK;CACX,CAAC;AAEF,IAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;AACzC,8EAA8E;AAC9E,WAAW;AACX,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACjC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAChC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACjC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACpC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAE5B,IAAM,QAAQ,GAAG;IACf,SAAS,EAAE,CAAC;IACZ,KAAK,EAAE,EAAE;IACT,QAAQ,EAAE,EAAE;IACZ,OAAO,EAAE,EAAE;IACX,SAAS,EAAE,EAAE;IACb,GAAG,EAAE,EAAE;IACP,IAAI,EAAE,EAAE;IACR,UAAU,EAAE,EAAE;IACd,QAAQ,EAAE,EAAE;IACZ,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,MAAM,EAAE,EAAE;IACV,MAAM,EAAE,EAAE;IACV,GAAG,EAAE,CAAC;CACP,CAAC;AAEF,IAAM,cAAc,GAAG,IAAI,GAAG,EAAkB,CAAC;AACjD,8EAA8E;AAC9E,WAAW;AACX,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;AACtD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9C,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AACpD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;AAClD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC;AACtD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1C,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACxD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAC;AACpD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,WAAW,CAAC,CAAC;AAC1D,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;AACxD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AAChD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AAChD,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAE1C,IAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAC;AACzC,8EAA8E;AAC9E,WAAW;AACX,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAChC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC7B,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACjC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACpC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AAEnC;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,KAAoB;IACxC,IAAA,GAAG,GAAI,KAAK,IAAT,CAAU;IACpB,uDAAuD;IACvD,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QAC3B,OAAO,GAAG,CAAC;KACZ;IAED,uCAAuC;IACvC,IAAM,SAAS,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACpD,IAAI,SAAS,EAAE;QACb,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,GAAG,CAAC,OAAO,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,KAAoB;IACpD,OAAO,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;AACjD,CAAC"}