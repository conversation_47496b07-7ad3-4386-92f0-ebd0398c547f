{"version": 3, "sources": ["webpack://mdc.[name]/webpack/universalModuleDefinition", "webpack://mdc.[name]/webpack/bootstrap", "webpack://mdc.[name]/./node_modules/process/browser.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/attribute_builders.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_builders.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_sanitizer/html_sanitizer.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_sanitizer/html_sanitizer_builder.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_sanitizer/inert_fragment.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_sanitizer/no_clobber.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_sanitizer/sanitizer_table/default_sanitizer_table.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/html_sanitizer/sanitizer_table/sanitizer_table.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/resource_url_builders.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/script_builders.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/sensitive_attributes.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/style_builders.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/style_sheet_builders.js", "webpack://mdc.[name]/./node_modules/safevalues/builders/url_sanitizer.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/anchor.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/area.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/button.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/element.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/embed.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/form.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/iframe.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/input.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/link.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/object.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/script.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/elements/style.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/document.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/dom_parser.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/global.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/location.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/range.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/service_worker_container.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/window.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/globals/worker.js", "webpack://mdc.[name]/./node_modules/safevalues/dom/index.js", "webpack://mdc.[name]/./node_modules/safevalues/environment/dev.js", "webpack://mdc.[name]/./node_modules/safevalues/index.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/attribute_impl.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/html_impl.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/resource_url_impl.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/script_impl.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/secrets.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/string_literal.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/style_impl.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/style_sheet_impl.js", "webpack://mdc.[name]/./node_modules/safevalues/internals/trusted_types.js", "webpack://mdc.[name]/./packages/mdc-base/component.ts", "webpack://mdc.[name]/./packages/mdc-base/foundation.ts", "webpack://mdc.[name]/./packages/mdc-base/index.ts", "webpack://mdc.[name]/./packages/mdc-base/types.ts"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC,CAAC;AACD,O;ACVA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,kDAA0C,gCAAgC;AAC1E;AACA;;AAEA;AACA;AACA;AACA,gEAAwD,kBAAkB;AAC1E;AACA,yDAAiD,cAAc;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iDAAyC,iCAAiC;AAC1E,wHAAgH,mBAAmB,EAAE;AACrI;AACA;;AAEA;AACA;AACA;AACA,mCAA2B,0BAA0B,EAAE;AACvD,yCAAiC,eAAe;AAChD;AACA;AACA;;AAEA;AACA,8DAAsD,+DAA+D;;AAErH;AACA;;;AAGA;AACA;;;;;;;;;;;;AClFA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,qCAAqC;;AAErC;AACA;AACA;;AAEA,2BAA2B;AAC3B;AACA;AACA;AACA,4BAA4B,UAAU;;;;;;;;;;;;;ACvLzB;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,uBAAuB,mBAAO,CAAC,0FAA6B;AAC5D,uBAAuB,mBAAO,CAAC,0FAA6B;AAC5D,6BAA6B,mBAAO,CAAC,0FAAwB;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA,wCAAwC;AACxC;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;;;;;;;;;;;;;AC3Ca;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,kBAAkB,mBAAO,CAAC,gFAAwB;AAClD,0BAA0B,mBAAO,CAAC,gGAAgC;AAClE,oBAAoB,mBAAO,CAAC,oFAA0B;AACtD;AACA;AACA;AACA;AACA;AACA,sCAAsC;AACtC;AACA;AACA;AACA;AACA,6BAA6B,cAAc;AAC3C;AACA;AACA;AACA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,6BAA6B,cAAc;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,4BAA4B;AAC5B,4BAA4B;AAC5B,8BAA8B;AAC9B,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACxFa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,2EAAuB;AAC/B,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD;AACA,gBAAgB,mBAAO,CAAC,+EAAyB;AACjD,sBAAsB,mBAAO,CAAC,6EAAkB;AAChD,uBAAuB,mBAAO,CAAC,6FAAkB;AACjD,mBAAmB,mBAAO,CAAC,qFAAc;AACzC,gCAAgC,mBAAO,CAAC,+IAA2C;AACnF,wBAAwB,mBAAO,CAAC,+HAAmC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,IAAqC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,4BAA4B,EAAE;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mHAAmH,2BAA2B;AAC9I;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ,gBAAgB;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,0BAA0B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA,aAAa,EAEJ;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F,sBAAsB;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ,gBAAgB;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,0BAA0B;AAC/C;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,8BAA8B,sGAAsG,EAAE;AACtI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,8CAA8C;AACvE;AACA;;;;;;;;;;;;;ACvQa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,gBAAgB,mBAAO,CAAC,+EAAyB;AACjD,uBAAuB,mBAAO,CAAC,6FAAkB;AACjD,gCAAgC,mBAAO,CAAC,+IAA2C;AACnF,wBAAwB,mBAAO,CAAC,+HAAmC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F,sBAAsB;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ,gBAAgB;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,0BAA0B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uGAAuG,wBAAwB;AAC/H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ,gBAAgB;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,0BAA0B;AAC/C;AACA;AACA;AACA,4DAA4D,UAAU;AACtE;AACA;AACA;AACA;AACA,qDAAqD,UAAU;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,QAAQ,gBAAgB;AACvD;AACA;AACA;AACA;AACA,6BAA6B,0BAA0B;AACvD;AACA;AACA;AACA;AACA,uBAAuB,QAAQ,gBAAgB;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,0BAA0B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F,sBAAsB;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,QAAQ,gBAAgB;AAC/C;AACA;AACA;AACA;AACA,qBAAqB,0BAA0B;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,gFAAgF;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,6DAA6D;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,6DAA6D;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;;;;;;;ACpNa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,gBAAgB,mBAAO,CAAC,qFAA4B;AACpD,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACtBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC/Ba;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,wBAAwB,mBAAO,CAAC,+GAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,8EAA8E;AACpG;AACA;AACA;AACA;AACA,sBAAsB,8EAA8E;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB,8EAA8E;AACnG;AACA;AACA;AACA;AACA,qBAAqB,8EAA8E;AACnG;AACA;AACA;AACA;AACA,qBAAqB,8EAA8E;AACnG;AACA;AACA;AACA;AACA,qBAAqB,8EAA8E;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,8EAA8E;AAC5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,8EAA8E;AAC9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjPa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA,gCAAgC;AAChC;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,8FAA8F;;;;;;;;;;;;;ACnDlF;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,0BAA0B,mBAAO,CAAC,gGAAgC;AAClE,oBAAoB,mBAAO,CAAC,oFAA0B;AACtD,uBAAuB,mBAAO,CAAC,0FAA6B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oDAAoD,IAAI;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,iBAAiB;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAmB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,0BAA0B;AACpE;AACA;AACA;;;;;;;;;;;;;ACzNa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,oBAAoB,mBAAO,CAAC,oFAA0B;AACtD,uBAAuB,mBAAO,CAAC,0FAA6B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA,0CAA0C;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,UAAU,YAAY;AAC1B;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,SAAS;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA,QAAQ,IAAqC;AAC7C,yCAAyC,iBAAiB,EAAE;AAC5D;AACA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AACA,wBAAwB,uBAAuB;AAC/C;AACA;AACA,iDAAiD,qCAAqC,EAAE;AACxF;AACA;AACA;AACA;;;;;;;;;;;;;AC1Fa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,uBAAuB,mBAAO,CAAC,0FAA6B;AAC5D,mBAAmB,mBAAO,CAAC,kFAAyB;AACpD;AACA;AACA;AACA;AACA;AACA,iBAAiB,cAAc;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,MAAM;AACrD;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA,yCAAyC;AACzC;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA,eAAe;AACf,8DAA8D;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACxDa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,uBAAuB,mBAAO,CAAC,0FAA6B;AAC5D,yBAAyB,mBAAO,CAAC,8FAA+B;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA,2DAA2D;AAC3D;AACA;AACA,QAAQ,IAAqC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC7Ca;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA,mBAAO,CAAC,wEAAoB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvEa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA,4EAA4E,OAAO;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mBAAO,CAAC,6FAAgC;AAC/D,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD,mBAAmB,mBAAO,CAAC,qFAA4B;AACvD;AACA,eAAe,wBAAwB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,wBAAwB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,iCAAiC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kDAAkD,uDAAuD,EAAE;AAC3G;AACA,qCAAqC,mCAAmC,EAAE;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpIa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACda;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACrBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC5Ca;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE,oBAAoB,mBAAO,CAAC,uFAA6B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC3Ca;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,yBAAyB,mBAAO,CAAC,iGAAkC;AACnE;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD;AACA,uBAAuB,qBAAqB,eAAe,eAAe;AAC1E,cAAc,eAAe;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,2BAA2B;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB,2BAA2B;AAC5C;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACpCa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACjBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,oBAAoB,mBAAO,CAAC,uFAA6B;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA,wBAAwB,oBAAoB,eAAe,eAAe;AAC1E,UAAU,UAAU;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB,eAAe;AAC/D,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,sBAAsB,eAAe;AAC7D,eAAe,UAAU;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACxCa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,kBAAkB,mBAAO,CAAC,mFAA2B;AACrD;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACZa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,sBAAsB,mBAAO,CAAC,yFAA8B;AAC5D;AACA,eAAe,kBAAkB,eAAe,aAAa;AAC7D,WAAW,UAAU;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mBAAmB,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA,iBAAiB,sBAAsB;AACvC;AACA;AACA;AACA;AACA,4EAA4E,OAAO;AACnF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,0BAA0B,mBAAO,CAAC,mGAAmC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,uBAAuB;AAC3C;AACA;AACA,uFAAuF,wDAAwD,EAAE;AACjJ;AACA;;;;;;;;;;;;;AC5Da;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,oCAAoC,aAAa,EAAE;AACjE;AACA;AACA,CAAC;AACD;AACA;AACA,CAAC;AACD;AACA,yCAAyC,6BAA6B;AACtE,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA,oCAAoC,mBAAO,CAAC,2EAAmB;AAC/D,kCAAkC,mBAAO,CAAC,uEAAiB;AAC3D,oCAAoC,mBAAO,CAAC,2EAAmB;AAC/D,mCAAmC,mBAAO,CAAC,6EAAoB;AAC/D,mCAAmC,mBAAO,CAAC,yEAAkB;AAC7D,kCAAkC,mBAAO,CAAC,uEAAiB;AAC3D,oCAAoC,mBAAO,CAAC,2EAAmB;AAC/D,mCAAmC,mBAAO,CAAC,yEAAkB;AAC7D,kCAAkC,mBAAO,CAAC,uEAAiB;AAC3D,oCAAoC,mBAAO,CAAC,2EAAmB;AAC/D,oCAAoC,mBAAO,CAAC,2EAAmB;AAC/D,mCAAmC,mBAAO,CAAC,yEAAkB;AAC7D,oCAAoC,mBAAO,CAAC,6EAAoB;AAChE,qCAAqC,mBAAO,CAAC,iFAAsB;AACnE,kCAAkC,mBAAO,CAAC,yEAAkB;AAC5D,oCAAoC,mBAAO,CAAC,6EAAoB;AAChE,iCAAiC,mBAAO,CAAC,uEAAiB;AAC1D,kDAAkD,mBAAO,CAAC,6GAAoC;AAC9F,kCAAkC,mBAAO,CAAC,yEAAkB;AAC5D,kCAAkC,mBAAO,CAAC,yEAAkB;;;;;;;;;;;;;ACrD5D,+CAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B,OAAO,0BAA0B;AAC3D;AACA,CAAC;;;;;;;;;;;;;;ACbY;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA,2BAA2B,mBAAO,CAAC,+FAA+B;AAClE,kDAAkD,qCAAqC,4CAA4C,EAAE,EAAE;AACvI,sBAAsB,mBAAO,CAAC,qFAA0B;AACxD,+CAA+C,qCAAqC,oCAAoC,EAAE,EAAE;AAC5H,gDAAgD,qCAAqC,qCAAqC,EAAE,EAAE;AAC9H,mDAAmD,qCAAqC,wCAAwC,EAAE,EAAE;AACpI,8CAA8C,qCAAqC,mCAAmC,EAAE,EAAE;AAC1H,uBAAuB,mBAAO,CAAC,qHAA0C;AACzE,gDAAgD,qCAAqC,sCAAsC,EAAE,EAAE;AAC/H,+DAA+D,qCAAqC,qDAAqD,EAAE,EAAE;AAC7J,0DAA0D,qCAAqC,gDAAgD,EAAE,EAAE;AACnJ,+BAA+B,mBAAO,CAAC,qIAAkD;AACzF,wDAAwD,qCAAqC,sDAAsD,EAAE,EAAE;AACvJ,8BAA8B,mBAAO,CAAC,qGAAkC;AACxE,gDAAgD,qCAAqC,6CAA6C,EAAE,EAAE;AACtI,qDAAqD,qCAAqC,kDAAkD,EAAE,EAAE;AAChJ,mDAAmD,qCAAqC,gDAAgD,EAAE,EAAE;AAC5I,sDAAsD,qCAAqC,mDAAmD,EAAE,EAAE;AAClJ,wBAAwB,mBAAO,CAAC,yFAA4B;AAC5D,iDAAiD,qCAAqC,wCAAwC,EAAE,EAAE;AAClI,8CAA8C,qCAAqC,qCAAqC,EAAE,EAAE;AAC5H,sDAAsD,qCAAqC,6CAA6C,EAAE,EAAE;AAC5I,kDAAkD,qCAAqC,yCAAyC,EAAE,EAAE;AACpI,uBAAuB,mBAAO,CAAC,uFAA2B;AAC1D,gDAAgD,qCAAqC,sCAAsC,EAAE,EAAE;AAC/H,6CAA6C,qCAAqC,mCAAmC,EAAE,EAAE;AACzH,6BAA6B,mBAAO,CAAC,mGAAiC;AACtE,qDAAqD,qCAAqC,iDAAiD,EAAE,EAAE;AAC/I,kDAAkD,qCAAqC,8CAA8C,EAAE,EAAE;AACzI;AACA,uBAAuB,mBAAO,CAAC,yFAA4B;AAC3D,uDAAuD,qCAAqC,6CAA6C,EAAE,EAAE;AAC7I,yDAAyD,qCAAqC,+CAA+C,EAAE,EAAE;AACjJ,kBAAkB,mBAAO,CAAC,+EAAuB;AACjD,8CAA8C,qCAAqC,+BAA+B,EAAE,EAAE;AACtH,0CAA0C,qCAAqC,2BAA2B,EAAE,EAAE;AAC9G,4CAA4C,qCAAqC,6BAA6B,EAAE,EAAE;AAClH,8CAA8C,qCAAqC,+BAA+B,EAAE,EAAE;AACtH,0BAA0B,mBAAO,CAAC,+FAA+B;AACjE,iDAAiD,qCAAqC,0CAA0C,EAAE,EAAE;AACpI,sDAAsD,qCAAqC,+CAA+C,EAAE,EAAE;AAC9I,qDAAqD,qCAAqC,8CAA8C,EAAE,EAAE;AAC5I,oBAAoB,mBAAO,CAAC,mFAAyB;AACrD,gDAAgD,qCAAqC,mCAAmC,EAAE,EAAE;AAC5H,4CAA4C,qCAAqC,+BAA+B,EAAE,EAAE;AACpH,8CAA8C,qCAAqC,iCAAiC,EAAE,EAAE;AACxH,gDAAgD,qCAAqC,mCAAmC,EAAE,EAAE;AAC5H,mBAAmB,mBAAO,CAAC,iFAAwB;AACnD,2CAA2C,qCAAqC,6BAA6B,EAAE,EAAE;AACjH,6CAA6C,qCAAqC,+BAA+B,EAAE,EAAE;AACrH,+CAA+C,qCAAqC,iCAAiC,EAAE,EAAE;AACzH,yBAAyB,mBAAO,CAAC,6FAA8B;AAC/D,gDAAgD,qCAAqC,wCAAwC,EAAE,EAAE;AACjI,kDAAkD,qCAAqC,0CAA0C,EAAE,EAAE;AACrI,oDAAoD,qCAAqC,4CAA4C,EAAE,EAAE;;;;;;;;;;;;;AC9D5H;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,gBAAgB,sCAAsC,iBAAiB,EAAE;AACvF,6BAA6B,8EAA8E;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,gBAAgB,mBAAO,CAAC,iEAAW;AACnC;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACxEa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B;AACA,gBAAgB,mBAAO,CAAC,iEAAW;AACnC,sBAAsB,mBAAO,CAAC,6EAAiB;AAC/C;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,QAAQ,gIAAgI,EAAE;AACxK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClFa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,gBAAgB,mBAAO,CAAC,iEAAW;AACnC,sBAAsB,mBAAO,CAAC,6EAAiB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AC3Ea;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B;AACA,gBAAgB,mBAAO,CAAC,iEAAW;AACnC,sBAAsB,mBAAO,CAAC,6EAAiB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B,QAAQ,oIAAoI,EAAE;AAC5K;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACnFa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACvBa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACtBa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,gBAAgB,sCAAsC,iBAAiB,EAAE;AACvF,6BAA6B,8EAA8E;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,gBAAgB,mBAAO,CAAC,iEAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClFa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,gBAAgB,sCAAsC,iBAAiB,EAAE;AACvF,6BAA6B,8EAA8E;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,sBAAsB;AAC7C;AACA;AACA,CAAC;AACD,8CAA8C,cAAc;AAC5D;AACA,mBAAO,CAAC,wEAAoB;AAC5B,gBAAgB,mBAAO,CAAC,iEAAW;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,IAAqC;AACjD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AClFa;AACb;AACA;AACA;AACA;AACA,8CAA8C,cAAc;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C,UAAU,EAAE;AAC1D,gDAAgD,UAAU,EAAE;AAC5D,mDAAmD,UAAU;AAC7D,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;;;;;;;;;;;;;;ACnDG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uCAA0C;AAC1C,gCAA2C;AAE3C,uCAA2C;AAG3C,SAAoB,YAAY;AACiB;AAC/C,kBAAkB,KAAQ,QAAa,cAAE,UAAE,GAAO;AAAK,eAAK,MAAc;AAC5E,KADe;AACd;AAEwB;AACzB;AAYE,0BAC4B,MAA6B;AACrD,mBAAkB;aAAlB,SAAkB,GAAlB,eAAkB;AAAlB,qCAAkB;;AADX,aAAI,OAAa;AAEtB,aAAW,iBAAX,+BAAqB;AACkD;AACH;AACpD;AAChB,aAAW,aACD,eAAgB,YAAK,KAAyB,yBAAY;AACpE,aAAW,WAAQ;AACnB,aACN;AAAC;AAtBM,iBAAQ,WAAf,UAAiC;AAC4C;AACA;AACN;AACK;AAC5B;AAC9C,eAAO,IAAgB,aAAK,MAAE,IAAI,aAAa,cACjD;AAAC;AAkB+B;;AACe;AAC/C,2BAAU,aAAV;AAAW,oBAAmB;aAAnB,SAAmB,GAAnB,eAAmB;AAAnB,kCAAmB;;AAC8C;AACE;AACF;AAE5E;AAAC;AAED,2BAAoB,uBAApB;AACyE;AACjC;AACtC,cAAM,IAAS,MACqE,mFAEtF;AAAC;AAED,2BAAkB,qBAAlB;AAC2E;AACA;AACF;AACC;AACA;AAE1E;AAAC;AAED,2BAAO,UAAP;AACoE;AACS;AACpB;AACnD,aAAW,WACjB;AAAC;AAYD,2BAAM,SAAN,UACqB,WAAwB,SACA;AACvC,aAAK,KAAiB,iBAAU,WAAS,SAC/C;AAAC;AAYD,2BAAQ,WAAR,UACqB,WAAwB,SACA;AACvC,aAAK,KAAoB,oBAAU,WAAS,SAClD;AAAC;AAKE;;;;AACH,2BAAI,OAAJ,UAAwC,WAAc,WAAsB;AAApB;AAAA,2BAAoB;;AAC1E,YAA0B;AAC1B,YAAI,OAAkB,gBAAe,YAAE;AAChC,wBAAkB,YAAa;AAC3B,yBAAc;AACf,wBACL;AAHmC,aAA9B;AAIT,eAAM;AACA,oBAAW,SAAY,YAAgB;AACvC,kBAAgB,gBAAU,WAAc,cAAO,OAAa;AAClE;AAEG,aAAK,KAAc,cACzB;AAAC;AAUE;;;;;;;;;AACO,2BAAgB,mBAA1B,UACwB,SACH,WACJ;AAEf,YAAa,UAAc,kBAAe,YAAE;AACnC,oBAAS,WAAS,OAAQ;AAClC,mBAAmB,UAAQ,QAAS,aAAM,GAAE;AAC3C,gBAAa,UAAc,YAAU,UAAQ,QAAS,UAAO;AACtD,oBAAQ,QAAS,WAAS;AAClC,SAHM,MAGA;AACL,kBAAW,YAAqB,qBAC5B,CAAC,aAAc,wFAAO,aAAE,aAAc,uFAAO,aACtC,SACE,WAEX;AAEN;AAAC;AACH,WAAC;AAAA;AA1IY,uBAAY;AA4IwF;AACjH,kBAA4B;;;;;;;;;;;;;;AC3JzB;;;;;;;;;;;;;;;;;;;;;;;;;AAEkB;AACrB;AA6BE,2BAA8D;AAAxC;AAAA,sBAAwC;;AAAxC,aAAO,UAAoC;AAAC;AA5BlE,0BAAW,eAAU;aAArB;AAC8E;AACL;AACpB;AACnD,mBACF;AAAC;;sBAAA;;AAED,0BAAW,eAAO;aAAlB;AAC8E;AACD;AAC9D;AACb,mBACF;AAAC;;sBAAA;;AAED,0BAAW,eAAO;aAAlB;AAC8E;AACX;AAChC;AACjC,mBACF;AAAC;;sBAAA;;AAED,0BAAW,eAAc;aAAzB;AAC2E;AACE;AACD;AAC1E,mBACF;AAAC;;sBAAA;;AAID,4BAAI,OAAJ;AAC8E;AAE9E;AAAC;AAED,4BAAO,UAAP;AACwE;AAExE;AAAC;AACH,WAAC;AAAA;AAxCY,wBAAa;AAsEuF;AACjH,kBAA6B,c;;;;;;;;;;;;;AC1E1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,yFAA4B;AAC5B,2FAA6B;AAC7B,iFAAwB,S;;;;;;;;;;;;;ACJrB;;;;;;;;;;;;;;;;;;;;;;;;AAqBoB,uB", "file": "mdc.base.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"@material/base\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"base\"] = factory();\n\telse\n\t\troot[\"mdc\"] = root[\"mdc\"] || {}, root[\"mdc\"][\"base\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./packages/mdc-base/index.ts\");\n", "// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.safeAttrPrefix = void 0;\nrequire(\"../environment/dev\");\nvar attribute_impl_1 = require(\"../internals/attribute_impl\");\nvar string_literal_1 = require(\"../internals/string_literal\");\nvar sensitive_attributes_1 = require(\"./sensitive_attributes\");\n/**\n * Creates a SafeAttributePrefix object from a template literal with no\n * interpolations for attributes that share a common prefix guaranteed to be not\n * security sensitive.\n *\n * The template literal is a prefix that makes it obvious this attribute is not\n * security sensitive. If it doesn't, this function will throw.\n */\nfunction safeAttrPrefix(templ) {\n    if (process.env.NODE_ENV !== 'production') {\n        (0, string_literal_1.assertIsTemplateObject)(templ, true, 'safeAttr is a template literal tag function ' +\n            'and should be called using the tagged template syntax. ' +\n            'For example, safeAttr`foo`;');\n    }\n    var attrPrefix = templ[0].toLowerCase();\n    if (process.env.NODE_ENV !== 'production') {\n        if (attrPrefix.indexOf('on') === 0 || 'on'.indexOf(attrPrefix) === 0) {\n            throw new Error(\"Prefix '\".concat(templ[0], \"' does not guarantee the attribute \") +\n                \"to be safe as it is also a prefix for event handler attributes\" +\n                \"Please use 'addEventListener' to set event handlers.\");\n        }\n        sensitive_attributes_1.SECURITY_SENSITIVE_ATTRIBUTES.forEach(function (sensitiveAttr) {\n            if (sensitiveAttr.indexOf(attrPrefix) === 0) {\n                throw new Error(\"Prefix '\".concat(templ[0], \"' does not guarantee the attribute \") +\n                    \"to be safe as it is also a prefix for \" +\n                    \"the security sensitive attribute '\".concat(sensitiveAttr, \"'. \") +\n                    \"Please use native or safe DOM APIs to set the attribute.\");\n            }\n        });\n    }\n    return (0, attribute_impl_1.createAttributePrefix)(attrPrefix);\n}\nexports.safeAttrPrefix = safeAttrPrefix;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.concatHtmls = exports.createScriptSrc = exports.createScript = exports.htmlEscape = void 0;\nvar html_impl_1 = require(\"../internals/html_impl\");\nvar resource_url_impl_1 = require(\"../internals/resource_url_impl\");\nvar script_impl_1 = require(\"../internals/script_impl\");\n/**\n * Returns HTML-escaped text as a `SafeHtml` object.\n *\n * Available options:\n * - `preserveSpaces` turns every second consecutive space character into its\n * HTML entity representation (`&#160;`).\n * - `preserveNewlines` turns newline characters into breaks (`<br>`).\n * - `preserveTabs` wraps tab characters in a span with style=white-space:pre.\n */\nfunction htmlEscape(text, options) {\n    if (options === void 0) { options = {}; }\n    var htmlEscapedString = htmlEscapeToString(text);\n    if (options.preserveSpaces) {\n        // Do this first to ensure we preserve spaces after newlines and tabs.\n        htmlEscapedString =\n            htmlEscapedString.replace(/(^|[\\r\\n\\t ]) /g, '$1&#160;');\n    }\n    if (options.preserveNewlines) {\n        htmlEscapedString = htmlEscapedString.replace(/(\\r\\n|\\n|\\r)/g, '<br>');\n    }\n    if (options.preserveTabs) {\n        htmlEscapedString = htmlEscapedString.replace(/(\\t+)/g, '<span style=\"white-space:pre\">$1</span>');\n    }\n    return (0, html_impl_1.createHtml)(htmlEscapedString);\n}\nexports.htmlEscape = htmlEscape;\n/**\n * Creates a `SafeHtml` representing a script tag with inline script content.\n */\nfunction createScript(script, options) {\n    if (options === void 0) { options = {}; }\n    var unwrappedScript = (0, script_impl_1.unwrapScript)(script).toString();\n    var stringTag = \"<script\";\n    if (options.id) {\n        stringTag += \" id=\\\"\".concat(htmlEscapeToString(options.id), \"\\\"\");\n    }\n    if (options.nonce) {\n        stringTag += \" nonce=\\\"\".concat(htmlEscapeToString(options.nonce), \"\\\"\");\n    }\n    if (options.type) {\n        stringTag += \" type=\\\"\".concat(htmlEscapeToString(options.type), \"\\\"\");\n    }\n    stringTag += \">\".concat(unwrappedScript, \"</script>\");\n    return (0, html_impl_1.createHtml)(stringTag);\n}\nexports.createScript = createScript;\n/**\n * Creates a `SafeHtml` representing a script tag with the src attribute.\n * This also supports CSP nonces and async loading.\n */\nfunction createScriptSrc(src, async, nonce) {\n    var unwrappedSrc = (0, resource_url_impl_1.unwrapResourceUrl)(src).toString();\n    var stringTag = \"<script src=\\\"\".concat(htmlEscapeToString(unwrappedSrc), \"\\\"\");\n    if (async) {\n        stringTag += ' async';\n    }\n    if (nonce) {\n        stringTag += \" nonce=\\\"\".concat(htmlEscapeToString(nonce), \"\\\"\");\n    }\n    stringTag += '>\\x3c/script>';\n    return (0, html_impl_1.createHtml)(stringTag);\n}\nexports.createScriptSrc = createScriptSrc;\n/**\n * HTML-escapes the given text (`&`, `<`, `>`, `\"` and `'`).\n */\nfunction htmlEscapeToString(text) {\n    var escaped = text.replace(/&/g, '&amp;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&apos;');\n    return escaped;\n}\n/** Creates a `SafeHtml` value by concatenating multiple `SafeHtml`s. */\nfunction concatHtmls(htmls) {\n    return (0, html_impl_1.createHtml)(htmls.map(html_impl_1.unwrapHtml).join(''));\n}\nexports.concatHtmls = concatHtmls;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.sanitizeHtmlToFragment = exports.sanitizeHtmlAssertUnchanged = exports.sanitizeHtml = exports.HtmlSanitizerImpl = void 0;\nrequire(\"../../environment/dev\");\nvar html_impl_1 = require(\"../../internals/html_impl\");\n/* g3_import_pure from '../../internals/pure' */\nvar secrets_1 = require(\"../../internals/secrets\");\nvar url_sanitizer_1 = require(\"../url_sanitizer\");\nvar inert_fragment_1 = require(\"./inert_fragment\");\nvar no_clobber_1 = require(\"./no_clobber\");\nvar default_sanitizer_table_1 = require(\"./sanitizer_table/default_sanitizer_table\");\nvar sanitizer_table_1 = require(\"./sanitizer_table/sanitizer_table\");\n/** Implementation for `HtmlSanitizer` */\nvar HtmlSanitizerImpl = /** @class */ (function () {\n    function HtmlSanitizerImpl(sanitizerTable, token) {\n        this.sanitizerTable = sanitizerTable;\n        this.changes = [];\n        (0, secrets_1.ensureTokenIsValid)(token);\n    }\n    HtmlSanitizerImpl.prototype.sanitizeAssertUnchanged = function (html) {\n        this.changes = [];\n        var sanitizedHtml = this.sanitize(html);\n        if (this.changes.length !== 0) {\n            var message = '';\n            if (process.env.NODE_ENV !== 'production') {\n                message =\n                    \"Unexpected change to HTML value as a result of sanitization. \" +\n                        \"Input: \\\"\".concat(html, \"\\\", sanitized output: \\\"\").concat(sanitizedHtml, \"\\\"\\n\") +\n                        \"List of changes:\".concat(this.changes.join('\\n'));\n            }\n            throw new Error(message);\n        }\n        return sanitizedHtml;\n    };\n    HtmlSanitizerImpl.prototype.sanitize = function (html) {\n        var fakeRoot = document.createElement('span');\n        fakeRoot.appendChild(this.sanitizeToFragment(html));\n        // XML serialization is preferred over HTML serialization as it is\n        // stricter and makes sure all attributes are properly escaped, avoiding\n        // cases where the tree might mutate when parsed again later due to the\n        // complexities of the HTML parsing algorithm\n        var serializedNewTree = new XMLSerializer().serializeToString(fakeRoot);\n        // We remove the outer most element as this is the span node created as\n        // the root for the sanitized tree and contains a spurious xmlns attribute\n        // from the XML serialization step.\n        serializedNewTree = serializedNewTree.slice(serializedNewTree.indexOf('>') + 1, serializedNewTree.lastIndexOf('</'));\n        return (0, html_impl_1.createHtml)(serializedNewTree);\n    };\n    HtmlSanitizerImpl.prototype.sanitizeToFragment = function (html) {\n        var _this = this;\n        var dirtyFragment = (0, inert_fragment_1.createInertFragment)(html);\n        var treeWalker = document.createTreeWalker(dirtyFragment, NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_TEXT, \n        // IE10 and IE11 won't accept a proper `NodeFilter` interface, and\n        // expect the filtering function to be passed directly. It seems that\n        // other browsers also do not mind getting the function directly. see\n        // https://stackoverflow.com/q/38245898\n        (function (n) { return _this.nodeFilter(n); }), \n        // @ts-ignore: error TS2554: Expected 1-3 arguments, but got 4.\n        false);\n        // `nextNode` is called so we skip the root `DocumentFragment`.\n        var currentNode = treeWalker.nextNode();\n        // We create a root element to attach all the children of the body to. We\n        // use div as it as a semantic-free, generic container and does not\n        // represent anything. This is removed when we serialize the tree back\n        // into a string.\n        var sanitizedFragment = document.createDocumentFragment();\n        var sanitizedParent = sanitizedFragment;\n        while (currentNode !== null) {\n            var sanitizedNode = void 0;\n            if ((0, no_clobber_1.isText)(currentNode)) {\n                sanitizedNode = this.sanitizeTextNode(currentNode);\n            }\n            else if ((0, no_clobber_1.isElement)(currentNode)) {\n                sanitizedNode = this.sanitizeElementNode(currentNode);\n            }\n            else {\n                throw new Error('Node is not of type text or element');\n            }\n            sanitizedParent.appendChild(sanitizedNode);\n            // Advance iterator while keeping track of the sanitized parent for the\n            // current node\n            currentNode = treeWalker.firstChild();\n            if (currentNode) {\n                sanitizedParent = sanitizedNode;\n            }\n            else {\n                while (!(currentNode = treeWalker.nextSibling())) {\n                    if (!(currentNode = treeWalker.parentNode())) {\n                        break;\n                    }\n                    sanitizedParent = sanitizedParent.parentNode;\n                }\n            }\n        }\n        return sanitizedFragment;\n    };\n    HtmlSanitizerImpl.prototype.sanitizeTextNode = function (textNode) {\n        return document.createTextNode(textNode.data);\n    };\n    HtmlSanitizerImpl.prototype.sanitizeElementNode = function (elementNode) {\n        var e_1, _a;\n        var elementName = (0, no_clobber_1.getNodeName)(elementNode);\n        var newNode = document.createElement(elementName);\n        var dirtyAttributes = elementNode.attributes;\n        try {\n            for (var dirtyAttributes_1 = __values(dirtyAttributes), dirtyAttributes_1_1 = dirtyAttributes_1.next(); !dirtyAttributes_1_1.done; dirtyAttributes_1_1 = dirtyAttributes_1.next()) {\n                var _b = dirtyAttributes_1_1.value, name_1 = _b.name, value = _b.value;\n                var policy = this.sanitizerTable.getAttributePolicy(name_1, elementName);\n                if (!this.satisfiesAllConditions(policy.conditions, dirtyAttributes)) {\n                    this.recordChange(\"Not all conditions satisfied for attribute: \".concat(name_1, \".\"));\n                    continue;\n                }\n                switch (policy.policyAction) {\n                    case sanitizer_table_1.AttributePolicyAction.KEEP:\n                        newNode.setAttribute(name_1, value);\n                        break;\n                    case sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL:\n                        var sanitizedAttrUrl = (0, url_sanitizer_1.restrictivelySanitizeUrl)(value);\n                        if (sanitizedAttrUrl !== value) {\n                            this.recordChange(\"Url in attribute \".concat(name_1, \" was modified during sanitization. Original url:\\\"\").concat(value, \"\\\" was sanitized to: \\\"\").concat(sanitizedAttrUrl, \"\\\"\"));\n                        }\n                        newNode.setAttribute(name_1, sanitizedAttrUrl);\n                        break;\n                    case sanitizer_table_1.AttributePolicyAction.KEEP_AND_NORMALIZE:\n                        // We don't consider changing the case of an attribute value to be a\n                        // semantic change\n                        newNode.setAttribute(name_1, value.toLowerCase());\n                        break;\n                    case sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE:\n                        newNode.setAttribute(name_1, value);\n                        break;\n                    case sanitizer_table_1.AttributePolicyAction.DROP:\n                        this.recordChange(\"Attribute: \".concat(name_1, \" was dropped\"));\n                        break;\n                    default:\n                        checkExhaustive(policy.policyAction, 'Unhandled AttributePolicyAction case');\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (dirtyAttributes_1_1 && !dirtyAttributes_1_1.done && (_a = dirtyAttributes_1.return)) _a.call(dirtyAttributes_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return newNode;\n    };\n    HtmlSanitizerImpl.prototype.nodeFilter = function (node) {\n        if ((0, no_clobber_1.isText)(node)) {\n            return NodeFilter.FILTER_ACCEPT;\n        }\n        else if (!(0, no_clobber_1.isElement)(node)) {\n            // Getting a node that is neither an `Element` or a `Text` node. This is\n            // likely due to something that is not supposed to be an element in user\n            // code but recognized as such by the TreeWalker (e.g. a polyfill for\n            // other kind of nodes). Since we can't recognize it as an element, we\n            // drop the node, but we don't record it as a meaningful change.\n            return NodeFilter.FILTER_REJECT;\n        }\n        var nodeName = (0, no_clobber_1.getNodeName)(node);\n        if (nodeName === null) {\n            this.recordChange(\"Node name was null for node: \".concat(node));\n            return NodeFilter.FILTER_REJECT;\n        }\n        if (this.sanitizerTable.isAllowedElement(nodeName)) {\n            return NodeFilter.FILTER_ACCEPT;\n        }\n        this.recordChange(\"Element: \".concat(nodeName, \" was dropped\"));\n        return NodeFilter.FILTER_REJECT;\n    };\n    HtmlSanitizerImpl.prototype.recordChange = function (errorMessage) {\n        if (process.env.NODE_ENV !== 'production') {\n            this.changes.push(errorMessage);\n        }\n        else if (this.changes.length === 0) {\n            this.changes.push('');\n        }\n    };\n    HtmlSanitizerImpl.prototype.satisfiesAllConditions = function (conditions, attrs) {\n        var e_2, _a;\n        var _b;\n        if (!conditions) {\n            return true;\n        }\n        try {\n            for (var conditions_1 = __values(conditions), conditions_1_1 = conditions_1.next(); !conditions_1_1.done; conditions_1_1 = conditions_1.next()) {\n                var _c = __read(conditions_1_1.value, 2), attrName = _c[0], expectedValues = _c[1];\n                var value = (_b = attrs.getNamedItem(attrName)) === null || _b === void 0 ? void 0 : _b.value;\n                if (value && !expectedValues.has(value)) {\n                    return false;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (conditions_1_1 && !conditions_1_1.done && (_a = conditions_1.return)) _a.call(conditions_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return true;\n    };\n    return HtmlSanitizerImpl;\n}());\nexports.HtmlSanitizerImpl = HtmlSanitizerImpl;\nvar defaultHtmlSanitizer = \n/* #__PURE__ */ (function () { return new HtmlSanitizerImpl(default_sanitizer_table_1.defaultSanitizerTable, secrets_1.secretToken); })();\n/** Sanitizes untrusted html using the default sanitizer configuration. */\nfunction sanitizeHtml(html) {\n    return defaultHtmlSanitizer.sanitize(html);\n}\nexports.sanitizeHtml = sanitizeHtml;\n/**\n * Sanitizes untrusted html using the default sanitizer configuration. Throws\n * an error if the html was changed.\n */\nfunction sanitizeHtmlAssertUnchanged(html) {\n    return defaultHtmlSanitizer.sanitizeAssertUnchanged(html);\n}\nexports.sanitizeHtmlAssertUnchanged = sanitizeHtmlAssertUnchanged;\n/**\n * Sanitizes untrusted html using the default sanitizer configuration. Throws\n * an error if the html was changed.\n */\nfunction sanitizeHtmlToFragment(html) {\n    return defaultHtmlSanitizer.sanitizeToFragment(html);\n}\nexports.sanitizeHtmlToFragment = sanitizeHtmlToFragment;\nfunction checkExhaustive(value, msg) {\n    if (msg === void 0) { msg = \"unexpected value \".concat(value, \"!\"); }\n    throw new Error(msg);\n}\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HtmlSanitizerBuilder = void 0;\nvar secrets_1 = require(\"../../internals/secrets\");\nvar html_sanitizer_1 = require(\"./html_sanitizer\");\nvar default_sanitizer_table_1 = require(\"./sanitizer_table/default_sanitizer_table\");\nvar sanitizer_table_1 = require(\"./sanitizer_table/sanitizer_table\");\n/** This class allows modifications to the default sanitizer configuration. */\nvar HtmlSanitizerBuilder = /** @class */ (function () {\n    function HtmlSanitizerBuilder() {\n        // To denote if the builder has called build() and therefore should make no\n        // further changes to the sanitizer table.\n        this.calledBuild = false;\n        this.sanitizerTable = default_sanitizer_table_1.defaultSanitizerTable;\n    }\n    /** Builder option to restrict allowed elements to a smaller subset. */\n    HtmlSanitizerBuilder.prototype.onlyAllowElements = function (elementSet) {\n        var e_1, _a;\n        var allowedElements = new Set();\n        var allowedElementPolicies = new Map();\n        try {\n            for (var elementSet_1 = __values(elementSet), elementSet_1_1 = elementSet_1.next(); !elementSet_1_1.done; elementSet_1_1 = elementSet_1.next()) {\n                var element = elementSet_1_1.value;\n                element = element.toUpperCase();\n                if (!this.sanitizerTable.isAllowedElement(element)) {\n                    throw new Error(\"Element: \".concat(element, \", is not allowed by html5_contract.textpb\"));\n                }\n                var elementPolicy = this.sanitizerTable.elementPolicies.get(element);\n                if (elementPolicy !== undefined) {\n                    allowedElementPolicies.set(element, elementPolicy);\n                }\n                else {\n                    allowedElements.add(element);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (elementSet_1_1 && !elementSet_1_1.done && (_a = elementSet_1.return)) _a.call(elementSet_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.sanitizerTable = new sanitizer_table_1.SanitizerTable(allowedElements, allowedElementPolicies, this.sanitizerTable.allowedGlobalAttributes, this.sanitizerTable.globalAttributePolicies);\n        return this;\n    };\n    /**\n     * Builder option to restrict allowed attributes to a smaller subset.\n     *\n     * If the attribute isn't currently allowed then it won't be added.\n     */\n    HtmlSanitizerBuilder.prototype.onlyAllowAttributes = function (attributeSet) {\n        var e_2, _a, e_3, _b, e_4, _c;\n        var allowedGlobalAttributes = new Set();\n        var globalAttributePolicies = new Map();\n        var elementPolicies = new Map();\n        try {\n            for (var attributeSet_1 = __values(attributeSet), attributeSet_1_1 = attributeSet_1.next(); !attributeSet_1_1.done; attributeSet_1_1 = attributeSet_1.next()) {\n                var attribute = attributeSet_1_1.value;\n                if (this.sanitizerTable.allowedGlobalAttributes.has(attribute)) {\n                    allowedGlobalAttributes.add(attribute);\n                }\n                if (this.sanitizerTable.globalAttributePolicies.has(attribute)) {\n                    globalAttributePolicies.set(attribute, this.sanitizerTable.globalAttributePolicies.get(attribute));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (attributeSet_1_1 && !attributeSet_1_1.done && (_a = attributeSet_1.return)) _a.call(attributeSet_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        try {\n            for (var _d = __values(this.sanitizerTable\n                .elementPolicies.entries()), _e = _d.next(); !_e.done; _e = _d.next()) {\n                var _f = __read(_e.value, 2), elementName = _f[0], originalElementPolicy = _f[1];\n                var newElementPolicy = new Map();\n                try {\n                    for (var _g = (e_4 = void 0, __values(originalElementPolicy\n                        .entries())), _h = _g.next(); !_h.done; _h = _g.next()) {\n                        var _j = __read(_h.value, 2), attribute = _j[0], attributePolicy = _j[1];\n                        if (attributeSet.has(attribute)) {\n                            newElementPolicy.set(attribute, attributePolicy);\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_h && !_h.done && (_c = _g.return)) _c.call(_g);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n                elementPolicies.set(elementName, newElementPolicy);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        this.sanitizerTable = new sanitizer_table_1.SanitizerTable(this.sanitizerTable.allowedElements, elementPolicies, allowedGlobalAttributes, globalAttributePolicies);\n        return this;\n    };\n    /**\n     * Allows the set of data attributes passed.\n     *\n     * These values must be prefixed with \"data-\"\n     *\n     * If called with onlyAllowElements or onlyAllowAttributes, those methods must\n     * be called first.\n     */\n    HtmlSanitizerBuilder.prototype.allowDataAttributes = function (attributes) {\n        var e_5, _a;\n        var allowedGlobalAttributes = new Set(this.sanitizerTable.allowedGlobalAttributes);\n        try {\n            for (var attributes_1 = __values(attributes), attributes_1_1 = attributes_1.next(); !attributes_1_1.done; attributes_1_1 = attributes_1.next()) {\n                var attribute = attributes_1_1.value;\n                if (attribute.indexOf('data-') !== 0) {\n                    throw new Error(\"data attribute: \".concat(attribute, \" does not begin with the prefix \\\"data-\\\"\"));\n                }\n                allowedGlobalAttributes.add(attribute);\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (attributes_1_1 && !attributes_1_1.done && (_a = attributes_1.return)) _a.call(attributes_1);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        this.sanitizerTable = new sanitizer_table_1.SanitizerTable(this.sanitizerTable.allowedElements, this.sanitizerTable.elementPolicies, allowedGlobalAttributes, this.sanitizerTable.globalAttributePolicies);\n        return this;\n    };\n    /**\n     * Preserves style attributes. Note that the sanitizer won't parse and\n     * sanitize the values but keep them as they are. In particular this means\n     * that the code will be able to call functions that could do undesirable\n     * things (e.g. `url` to trigger a network request), as well as any custom\n     * properties or functions defined by the application.\n     */\n    HtmlSanitizerBuilder.prototype.allowStyleAttributes = function () {\n        var globalAttributePolicies = new Map(this.sanitizerTable.globalAttributePolicies);\n        globalAttributePolicies.set('style', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_STYLE });\n        this.sanitizerTable = new sanitizer_table_1.SanitizerTable(this.sanitizerTable.allowedElements, this.sanitizerTable.elementPolicies, this.sanitizerTable.allowedGlobalAttributes, globalAttributePolicies);\n        return this;\n    };\n    /**\n     * Preserves the class attribute on all elements. This means contents can\n     * adopt CSS styles from other page elements and possibly mask themselves as\n     * legitimate UI elements, which can lead to phishing.\n     */\n    HtmlSanitizerBuilder.prototype.allowClassAttributes = function () {\n        var globalAttributePolicies = new Map(this.sanitizerTable.globalAttributePolicies);\n        globalAttributePolicies.set('class', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP });\n        this.sanitizerTable = new sanitizer_table_1.SanitizerTable(this.sanitizerTable.allowedElements, this.sanitizerTable.elementPolicies, this.sanitizerTable.allowedGlobalAttributes, globalAttributePolicies);\n        return this;\n    };\n    /**\n     * Preserves id attributes. This carries moderate risk as it allows an\n     * element to override other elements with the same ID.\n     */\n    HtmlSanitizerBuilder.prototype.allowIdAttributes = function () {\n        var globalAttributePolicies = new Map(this.sanitizerTable.globalAttributePolicies);\n        globalAttributePolicies.set('id', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP });\n        this.sanitizerTable = new sanitizer_table_1.SanitizerTable(this.sanitizerTable.allowedElements, this.sanitizerTable.elementPolicies, this.sanitizerTable.allowedGlobalAttributes, globalAttributePolicies);\n        return this;\n    };\n    HtmlSanitizerBuilder.prototype.build = function () {\n        if (this.calledBuild) {\n            throw new Error('this sanitizer has already called build');\n        }\n        this.calledBuild = true;\n        return new html_sanitizer_1.HtmlSanitizerImpl(this.sanitizerTable, secrets_1.secretToken);\n    };\n    return HtmlSanitizerBuilder;\n}());\nexports.HtmlSanitizerBuilder = HtmlSanitizerBuilder;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createInertFragment = void 0;\nvar element_1 = require(\"../../dom/elements/element\");\nvar html_impl_1 = require(\"../../internals/html_impl\");\n/**\n * Returns a fragment that contains the parsed HTML for `dirtyHtml` without\n * executing any of the potential payload.\n */\nfunction createInertFragment(dirtyHtml) {\n    var template = document.createElement('template');\n    // This call is only used to create an inert tree for the sanitizer to\n    // further process and is never returned directly to the caller. We can't use\n    // a reviewed conversion in order to avoid an import loop.\n    var temporarySafeHtml = (0, html_impl_1.createHtml)(dirtyHtml);\n    (0, element_1.setInnerHtml)(template, temporarySafeHtml);\n    return template.content;\n}\nexports.createInertFragment = createInertFragment;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isElement = exports.isText = exports.getNodeName = void 0;\n/**\n * Library that provides safe getters for different node properties and\n * checks for clobbering.\n */\n/** Gets a reasonable nodeName, even for clobbered nodes. */\nfunction getNodeName(node) {\n    var nodeName = node.nodeName;\n    // If the property is clobbered, assume it is an `HTMLFormElement`.\n    return (typeof nodeName === 'string') ? nodeName : 'FORM';\n}\nexports.getNodeName = getNodeName;\n/** Returns true if the object passed is a Text node. */\nfunction isText(node) {\n    // The property cannot get clobbered on Text nodes.\n    return node.nodeType === Node.TEXT_NODE;\n}\nexports.isText = isText;\n/** Returns true if the object passed is an Element node. */\nfunction isElement(node) {\n    var nodeType = node.nodeType;\n    // If the property is clobbered, we can assume it is an `HTMLFormElement`, and\n    // thus an `Element`.\n    return (nodeType === Node.ELEMENT_NODE) || (typeof nodeType !== 'number');\n}\nexports.isElement = isElement;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.defaultSanitizerTable = void 0;\nvar sanitizer_table_1 = require(\"./sanitizer_table\");\n/**\n * GENERATED CODE, DO NOT MODIFY\n * This SanitizerTable was generated from the checked in html contract:\n *  webutil/html/types/codegen/html5_contract.textpb\n *\n * You can regenerate this file with:\n * webutil/html/types/codegen/update_generated_source_files.sh\n */\nexports.defaultSanitizerTable = new sanitizer_table_1.SanitizerTable(new Set([\n    'ARTICLE', 'SECTION', 'NAV', 'ASIDE', 'H1', 'H2',\n    'H3', 'H4', 'H5', 'H6', 'HEADER', 'FOOTER',\n    'ADDRESS', 'P', 'HR', 'PRE', 'BLOCKQUOTE', 'OL',\n    'UL', 'LH', 'LI', 'DL', 'DT', 'DD',\n    'FIGURE', 'FIGCAPTION', 'MAIN', 'DIV', 'EM', 'STRONG',\n    'SMALL', 'S', 'CITE', 'Q', 'DFN', 'ABBR',\n    'RUBY', 'RB', 'RT', 'RTC', 'RP', 'DATA',\n    'TIME', 'CODE', 'VAR', 'SAMP', 'KBD', 'SUB',\n    'SUP', 'I', 'B', 'U', 'MARK', 'BDI',\n    'BDO', 'SPAN', 'BR', 'WBR', 'INS', 'DEL',\n    'PICTURE', 'PARAM', 'TRACK', 'MAP', 'TABLE', 'CAPTION',\n    'COLGROUP', 'COL', 'TBODY', 'THEAD', 'TFOOT', 'TR',\n    'TD', 'TH', 'SELECT', 'DATALIST', 'OPTGROUP', 'OPTION',\n    'OUTPUT', 'PROGRESS', 'METER', 'FIELDSET', 'LEGEND', 'DETAILS',\n    'SUMMARY', 'MENU', 'DIALOG', 'SLOT', 'CANVAS', 'FONT',\n    'CENTER',\n]), new Map([\n    [\n        'A', new Map([\n            ['href', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n        ])\n    ],\n    [\n        'AREA', new Map([\n            ['href', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n        ])\n    ],\n    [\n        'LINK', new Map([\n            [\n                'href', {\n                    policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL,\n                    conditions: new Map([[\n                            'rel', new Set([\n                                'alternate',\n                                'author',\n                                'bookmark',\n                                'canonical',\n                                'cite',\n                                'help',\n                                'icon',\n                                'license',\n                                'next',\n                                'prefetch',\n                                'dns-prefetch',\n                                'prerender',\n                                'preconnect',\n                                'preload',\n                                'prev',\n                                'search',\n                                'subresource',\n                            ])\n                        ]])\n                }\n            ],\n        ])\n    ],\n    [\n        'SOURCE', new Map([\n            ['src', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n        ])\n    ],\n    [\n        'IMG', new Map([\n            ['src', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n        ])\n    ],\n    [\n        'VIDEO', new Map([\n            ['src', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n        ])\n    ],\n    [\n        'AUDIO', new Map([\n            ['src', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n        ])\n    ],\n]), new Set([\n    'title',\n    'aria-atomic',\n    'aria-autocomplete',\n    'aria-busy',\n    'aria-checked',\n    'aria-current',\n    'aria-disabled',\n    'aria-dropeffect',\n    'aria-expanded',\n    'aria-haspopup',\n    'aria-hidden',\n    'aria-invalid',\n    'aria-label',\n    'aria-level',\n    'aria-live',\n    'aria-multiline',\n    'aria-multiselectable',\n    'aria-orientation',\n    'aria-posinset',\n    'aria-pressed',\n    'aria-readonly',\n    'aria-relevant',\n    'aria-required',\n    'aria-selected',\n    'aria-setsize',\n    'aria-sort',\n    'aria-valuemax',\n    'aria-valuemin',\n    'aria-valuenow',\n    'aria-valuetext',\n    'alt',\n    'align',\n    'autocapitalize',\n    'autocomplete',\n    'autocorrect',\n    'autofocus',\n    'autoplay',\n    'bgcolor',\n    'border',\n    'cellpadding',\n    'cellspacing',\n    'checked',\n    'color',\n    'cols',\n    'colspan',\n    'controls',\n    'datetime',\n    'disabled',\n    'download',\n    'draggable',\n    'enctype',\n    'face',\n    'formenctype',\n    'frameborder',\n    'height',\n    'hreflang',\n    'hidden',\n    'ismap',\n    'label',\n    'lang',\n    'loop',\n    'max',\n    'maxlength',\n    'media',\n    'minlength',\n    'min',\n    'multiple',\n    'muted',\n    'nonce',\n    'open',\n    'placeholder',\n    'preload',\n    'rel',\n    'required',\n    'reversed',\n    'role',\n    'rows',\n    'rowspan',\n    'selected',\n    'shape',\n    'size',\n    'sizes',\n    'slot',\n    'span',\n    'spellcheck',\n    'start',\n    'step',\n    'summary',\n    'translate',\n    'type',\n    'valign',\n    'value',\n    'width',\n    'wrap',\n    'itemscope',\n    'itemtype',\n    'itemid',\n    'itemprop',\n    'itemref',\n]), new Map([\n    [\n        'dir', {\n            policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_NORMALIZE,\n            conditions: new Map([[\n                    'dir', new Set([\n                        'auto',\n                        'ltr',\n                        'rtl',\n                    ])\n                ]])\n        }\n    ],\n    [\n        'async', {\n            policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_NORMALIZE,\n            conditions: new Map([[\n                    'async', new Set([\n                        'async',\n                    ])\n                ]])\n        }\n    ],\n    ['cite', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n    [\n        'loading', {\n            policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_NORMALIZE,\n            conditions: new Map([[\n                    'loading', new Set([\n                        'eager',\n                        'lazy',\n                    ])\n                ]])\n        }\n    ],\n    ['poster', { policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_SANITIZE_URL }],\n    [\n        'target', {\n            policyAction: sanitizer_table_1.AttributePolicyAction.KEEP_AND_NORMALIZE,\n            conditions: new Map([[\n                    'target', new Set([\n                        '_self',\n                        '_blank',\n                    ])\n                ]])\n        }\n    ],\n]));\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AttributePolicyAction = exports.SanitizerTable = void 0;\n/** Class for holding element and attribute policies used for sanitization. */\nvar SanitizerTable = /** @class */ (function () {\n    function SanitizerTable(allowedElements, elementPolicies, allowedGlobalAttributes, globalAttributePolicies) {\n        this.allowedElements = allowedElements;\n        this.elementPolicies = elementPolicies;\n        this.allowedGlobalAttributes = allowedGlobalAttributes;\n        this.globalAttributePolicies = globalAttributePolicies;\n    }\n    SanitizerTable.prototype.isAllowedElement = function (elementName) {\n        // Note: `HTMLFormElement`s are always dropped, supporting them is very\n        // costly because of the DOM clobberring they can cause. The additional code\n        // size required to properly work around DOM clobberring issues is large and\n        // shouldn't be put on every user of the sanitizer. Thoroughly review\n        // b/210975025 and the CLs linked there before you start allowing form\n        // elements.\n        return elementName.toLowerCase() !== 'form' &&\n            (this.allowedElements.has(elementName) ||\n                this.elementPolicies.has(elementName));\n    };\n    SanitizerTable.prototype.getAttributePolicy = function (attributeName, elementName) {\n        var elementPolicy = this.elementPolicies.get(elementName);\n        if (elementPolicy === null || elementPolicy === void 0 ? void 0 : elementPolicy.has(attributeName)) {\n            return elementPolicy.get(attributeName);\n        }\n        if (this.allowedGlobalAttributes.has(attributeName)) {\n            return { policyAction: AttributePolicyAction.KEEP };\n        }\n        var globalPolicy = this.globalAttributePolicies.get(attributeName);\n        return globalPolicy || { policyAction: AttributePolicyAction.DROP };\n    };\n    return SanitizerTable;\n}());\nexports.SanitizerTable = SanitizerTable;\n/**\n * Values derived from\n * https://godoc.corp.google.com/pkg/google3/third_party/safehtml/sanitizer/policy#AttributePolicy\n */\nvar AttributePolicyAction;\n(function (AttributePolicyAction) {\n    AttributePolicyAction[AttributePolicyAction[\"DROP\"] = 0] = \"DROP\";\n    AttributePolicyAction[AttributePolicyAction[\"KEEP\"] = 1] = \"KEEP\";\n    AttributePolicyAction[AttributePolicyAction[\"KEEP_AND_SANITIZE_URL\"] = 2] = \"KEEP_AND_SANITIZE_URL\";\n    AttributePolicyAction[AttributePolicyAction[\"KEEP_AND_NORMALIZE\"] = 3] = \"KEEP_AND_NORMALIZE\";\n    AttributePolicyAction[AttributePolicyAction[\"KEEP_AND_SANITIZE_STYLE\"] = 4] = \"KEEP_AND_SANITIZE_STYLE\";\n})(AttributePolicyAction = exports.AttributePolicyAction || (exports.AttributePolicyAction = {}));\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.blobUrlFromScript = exports.replaceFragment = exports.appendParams = exports.trustedResourceUrl = void 0;\nrequire(\"../environment/dev\");\nvar resource_url_impl_1 = require(\"../internals/resource_url_impl\");\nvar script_impl_1 = require(\"../internals/script_impl\");\nvar string_literal_1 = require(\"../internals/string_literal\");\n/**\n * Check whether the base url contains a valid origin,\n *\n * A string for an origin must contain only alphanumeric or any of the\n * following: `-.:`, and must not be an IP address. Remember that, as per the\n * documentation for TrustedResourceUrl, the origin must be trustworthy.\n *\n * @param base The base url that contains an origin.\n */\nfunction hasValidOrigin(base) {\n    if (!(/^https:\\/\\//.test(base) || /^\\/\\//.test(base))) {\n        return false;\n    }\n    var originStart = base.indexOf('//') + 2;\n    var originEnd = base.indexOf('/', originStart);\n    // If the base url only contains the prefix (e.g. //), or the slash\n    // for the origin is right after the prefix (e.g. ///), the origin is\n    // missing.\n    if (originEnd <= originStart) {\n        throw new Error(\"Can't interpolate data in a url's origin, \" +\n            \"Please make sure to fully specify the origin, terminated with '/'.\");\n    }\n    var origin = base.substring(originStart, originEnd);\n    if (!/^[0-9a-z.:-]+$/i.test(origin)) {\n        throw new Error('The origin contains unsupported characters.');\n    }\n    if (!/^[^:]*(:[0-9]+)?$/i.test(origin)) {\n        throw new Error('Invalid port number.');\n    }\n    if (!/(^|\\.)[a-z][^.]*$/i.test(origin)) {\n        throw new Error('The top-level domain must start with a letter.');\n    }\n    return true;\n}\n/**\n * Check whether the base url contains a valid about url at its beginning.\n *\n * An about url is either exactly 'about:blank' or 'about:blank#<str>' where\n * <str> can be an arbitrary string.\n *\n * @param base The base url.\n */\nfunction isValidAboutUrl(base) {\n    if (!/^about:blank/.test(base)) {\n        return false;\n    }\n    if (base !== 'about:blank' && !/^about:blank#/.test(base)) {\n        throw new Error('The about url is invalid.');\n    }\n    return true;\n}\n/**\n * Check whether the base url contains a valid path start at its beginning.\n *\n * A valid path start is either a '/' or a '/' followed by at least one\n * character that is not '/' or '\\'.\n *\n * @param base The base url.\n */\nfunction isValidPathStart(base) {\n    if (!/^\\//.test(base)) {\n        return false;\n    }\n    if ((base === '/') ||\n        (base.length > 1 && base[1] !== '/' && base[1] !== '\\\\')) {\n        return true;\n    }\n    throw new Error('The path start in the url is invalid.');\n}\n/**\n * Builds TrustedResourceUrl from a template literal.\n *\n * This factory is a template literal tag function. It should be called with\n * a template literal, with or without embedded expressions. For example,\n *               trustedResourceUrl`//example.com/${bar}`;\n * or\n *               trustedResourceUrl`//example.com`;\n *\n * When this function is called with a template literal without any embedded\n * expressions, the template string may contain anything as the whole URL is\n * a compile-time string constant.\n *\n * When this function is called with a template literal that contains embedded\n * expressions, the template must start with one of the following:\n * - `https://<origin>/`\n * - `//<origin>/`\n * - `/<pathStart>`\n * - `about:blank`\n * - `data:`\n *\n * `<origin>` must contain only alphanumeric or any of the following: `-.:`.\n * Remember that, as per the documentation for TrustedResourceUrl, the origin\n * must be trustworthy. An origin of \"example.com\" could be set with this\n * method, but would tie the security of your site to the security of\n * example.com. Similarly, formats that potentially cover redirects hosted\n * on a trusted origin are problematic, since that could lead to untrusted\n * origins.\n *\n * `<pathStart>` is either an empty string, or a non empty string that does not\n * start with '/' or '\\'.\n * In other words, `/<pathStart>` is either a '/' or a\n * '/' followed by at least one character that is not '/' or '\\'.\n *\n * `data:` (data URL) does not allow embedded expressions in the template\n * literal input.\n *\n * All embedded expressions are URL encoded when they are interpolated. Do not\n * embed expressions that are already URL encoded as they will be double encoded\n * by the builder.\n *\n * @param templateObj This contains the literal part of the template literal.\n * @param rest This represents the template's embedded expressions.\n */\nfunction trustedResourceUrl(templateObj) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        rest[_i - 1] = arguments[_i];\n    }\n    // Check if templateObj is actually from a template literal.\n    if (process.env.NODE_ENV !== 'production') {\n        (0, string_literal_1.assertIsTemplateObject)(templateObj, true, 'trustedResourceUrl is a template literal tag function and ' +\n            'can only be called as such (e.g. trustedResourceUrl`/somepath.js`)');\n    }\n    if (rest.length === 0) {\n        return (0, resource_url_impl_1.createResourceUrl)(templateObj[0]);\n    }\n    var base = templateObj[0].toLowerCase();\n    if (process.env.NODE_ENV !== 'production') {\n        if (/^data:/.test(base)) {\n            throw new Error('Data URLs cannot have expressions in the template literal input.');\n        }\n        if (!hasValidOrigin(base) && !isValidPathStart(base) &&\n            !isValidAboutUrl(base)) {\n            throw new Error('Trying to interpolate expressions in an unsupported url format.');\n        }\n    }\n    var urlParts = [templateObj[0]];\n    for (var i = 0; i < rest.length; i++) {\n        urlParts.push(encodeURIComponent(rest[i]));\n        urlParts.push(templateObj[i + 1]);\n    }\n    return (0, resource_url_impl_1.createResourceUrl)(urlParts.join(''));\n}\nexports.trustedResourceUrl = trustedResourceUrl;\n/**\n * Creates a new TrustedResourceUrl with params added to the URL's search\n * parameters.\n *\n * @param params What to add to the URL. Parameters with value `null` or\n * `undefined` are skipped. Both keys and values will be encoded. Do not pass\n * pre-encoded values as this will result them being double encoded. If the\n * value is an array then the same parameter is added for every element in the\n * array.\n */\nfunction appendParams(trustedUrl, params) {\n    var url = (0, resource_url_impl_1.unwrapResourceUrl)(trustedUrl).toString();\n    if (/#/.test(url)) {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = \"Found a hash in url (\".concat(url, \"), appending not supported\");\n        }\n        throw new Error(message);\n    }\n    var separator = /\\?/.test(url) ? '&' : '?';\n    // for-of has a big polyfill.\n    // tslint:disable-next-line:ban-iterable-foreach\n    params.forEach(function (value, key) {\n        var values = (value instanceof Array) ? value : [value];\n        for (var i = 0; i < values.length; i++) {\n            var v = values[i];\n            if (v === null || v === undefined) {\n                continue;\n            }\n            url += separator + encodeURIComponent(key) + '=' +\n                encodeURIComponent(String(v));\n            separator = '&';\n        }\n    });\n    return (0, resource_url_impl_1.createResourceUrl)(url);\n}\nexports.appendParams = appendParams;\nvar BEFORE_FRAGMENT_REGEXP = /[^#]*/;\n/**\n * Creates a new TrustedResourceUrl based on an existing one but with the\n * addition of a fragment (the part after `#`). If the URL already has a\n * fragment, it is replaced with the new one.\n * @param fragment The fragment to add to the URL, verbatim, without the leading\n * `#`. No additional escaping is applied.\n */\nfunction replaceFragment(trustedUrl, fragment) {\n    var urlString = (0, resource_url_impl_1.unwrapResourceUrl)(trustedUrl).toString();\n    return (0, resource_url_impl_1.createResourceUrl)(BEFORE_FRAGMENT_REGEXP.exec(urlString)[0] + '#' + fragment);\n}\nexports.replaceFragment = replaceFragment;\n/**\n * Creates a `TrustedResourceUrl` by generating a `Blob` from a\n * `SafeScript` and then calling `URL.createObjectURL` with that `Blob`.\n *\n * Caller must call `URL.revokeObjectUrl()` on the stringified url to\n * release the underlying `Blob`.\n */\nfunction blobUrlFromScript(safeScript) {\n    var scriptContent = (0, script_impl_1.unwrapScript)(safeScript).toString();\n    var blob = new Blob([scriptContent], { type: 'text/javascript' });\n    return (0, resource_url_impl_1.createResourceUrl)(URL.createObjectURL(blob));\n}\nexports.blobUrlFromScript = blobUrlFromScript;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.safeScriptWithArgs = exports.scriptFromJson = exports.concatScripts = exports.safeScript = void 0;\nrequire(\"../environment/dev\");\nvar script_impl_1 = require(\"../internals/script_impl\");\nvar string_literal_1 = require(\"../internals/string_literal\");\n/**\n * Creates a SafeScript object from a template literal (without any embedded\n * expressions).\n *\n * This function is a template literal tag function. It should be called with\n * a template literal that does not contain any expressions. For example,\n *                           safeScript`foo`;\n *\n * @param templateObj This contains the literal part of the template literal.\n */\nfunction safeScript(templateObj) {\n    if (process.env.NODE_ENV !== 'production') {\n        (0, string_literal_1.assertIsTemplateObject)(templateObj, false, 'safeScript is a template literal tag function ' +\n            'that only accepts template literals without expressions. ' +\n            'For example, safeScript`foo`;');\n    }\n    return (0, script_impl_1.createScript)(templateObj[0]);\n}\nexports.safeScript = safeScript;\n/** Creates a `SafeScript` value by concatenating multiple `SafeScript`s. */\nfunction concatScripts(scripts) {\n    return (0, script_impl_1.createScript)(scripts.map(script_impl_1.unwrapScript).join(''));\n}\nexports.concatScripts = concatScripts;\n/**\n * Converts a serializable value into JSON that is safe to interpolate into a\n * script context. In particular it escapes < characters so that a value of\n * \"</script>\" doesn't break out of the context.\n * @param value The value to serialize.\n */\nfunction scriptFromJson(value) {\n    return (0, script_impl_1.createScript)(JSON.stringify(value).replace(/</g, '\\\\x3c'));\n}\nexports.scriptFromJson = scriptFromJson;\n/**\n * Creates a `SafeScript` object from a template literal (without any embedded\n * expressions) along with additional arguments that the script should have\n * access to. These arguments will be JSON-encoded and passed to the script as\n * a function call.\n * @example\n * ```ts\n * safeScriptWithArgs`function (name, props) {\n *  console.log(name + ' is ' + props.age);\n * }`('Bob', { 'age': 42 })\n * ```\n * would return a `SafeScript` that represents the following code:\n * ```js\n * (function (name, props) {\n *  console.log(name + ' is ' + props.age);\n * })(\"Bob\",{\"age\":42})\n * ```\n * @note Be careful when passing objects as arguments, as unquoted property\n * names may be changed during compilation.\n * @param templateObj This contains the literal part of the template literal.\n * @param emptyArgs Expressions that evaluate to the empty string to enable\n *     inline comments.\n */\nfunction safeScriptWithArgs(templateObj) {\n    var emptyArgs = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        emptyArgs[_i - 1] = arguments[_i];\n    }\n    if (process.env.NODE_ENV !== 'production') {\n        if (emptyArgs.some(function (a) { return a !== ''; })) {\n            throw new Error('safeScriptWithArgs only allows empty string expressions ' +\n                'to enable inline comments.');\n        }\n        (0, string_literal_1.assertIsTemplateObject)(templateObj, true, 'safeScriptWithArgs is a template literal tag function ' +\n            'that only accepts template literals. ' +\n            'For example, safeScriptWithArgs`foo`;');\n    }\n    return function () {\n        var argValues = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            argValues[_i] = arguments[_i];\n        }\n        var values = argValues.map(function (v) { return scriptFromJson(v).toString(); });\n        return (0, script_impl_1.createScript)(\"(\".concat(templateObj.join(''), \")(\").concat(values.join(','), \")\"));\n    };\n}\nexports.safeScriptWithArgs = safeScriptWithArgs;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.SECURITY_SENSITIVE_ATTRIBUTES = void 0;\n// AUTOGENERATED. DO NOT EDIT.\n/**\n * Security sensitive attribute names that should not be set through\n * `setAttribute` or similar functions.\n */\nexports.SECURITY_SENSITIVE_ATTRIBUTES = [\n    'href',\n    'rel',\n    'src',\n    'srcdoc',\n    'action',\n    'formaction',\n    'sandbox',\n    'cite',\n    'poster',\n    'icon',\n];\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.concatStyles = exports.safeStyle = void 0;\nrequire(\"../environment/dev\");\nvar string_literal_1 = require(\"../internals/string_literal\");\nvar style_impl_1 = require(\"../internals/style_impl\");\n/**\n * Creates a SafeStyle object from a template literal (without any embedded\n * expressions).\n *\n * ` style` should be in the format\n * ` name: value; [name: value; ...]` and must not have any < or >\n * characters in it. This is so that SafeStyle's contract is preserved,\n * allowing the SafeStyle to correctly be interpreted as a sequence of CSS\n * declarations and without affecting the syntactic structure of any\n * surrounding CSS and HTML.\n *\n * This function is a template literal tag function. It should be called with\n * a template literal that does not contain any expressions. For example,\n *                          safeStyle`foo`;\n * This function first checks if it is called with a literal template, and\n * then performs basic sanity checks on the format of ` style`\n * but does not constrain the format of ` name} and {@code value`, except\n * for disallowing tag characters.\n *\n * @param templateObj This contains the literal part of the template literal.\n */\nfunction safeStyle(templateObj) {\n    if (process.env.NODE_ENV !== 'production') {\n        (0, string_literal_1.assertIsTemplateObject)(templateObj, false, 'safeStyle is a template literal tag function ' +\n            'that only accepts template literals without expressions. ' +\n            'For example, safeStyle`foo`;');\n    }\n    var style = templateObj[0];\n    if (process.env.NODE_ENV !== 'production') {\n        if (/[<>]/.test(style)) {\n            throw new Error('Forbidden characters in style string: ' + style);\n        }\n        if (!/;$/.test(style)) {\n            throw new Error('Style string does not end with \";\": ' + style);\n        }\n        if (!/:/.test(style)) {\n            throw new Error('Style string should contain one or more \":\": ' + style);\n        }\n    }\n    return (0, style_impl_1.createStyle)(style);\n}\nexports.safeStyle = safeStyle;\n/** Creates a `SafeStyle` value by concatenating multiple `SafeStyle`s. */\nfunction concatStyles(styles) {\n    return (0, style_impl_1.createStyle)(styles.map(style_impl_1.unwrapStyle).join(''));\n}\nexports.concatStyles = concatStyles;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.concatStyleSheets = exports.safeStyleSheet = void 0;\nrequire(\"../environment/dev\");\nvar string_literal_1 = require(\"../internals/string_literal\");\nvar style_sheet_impl_1 = require(\"../internals/style_sheet_impl\");\n/**\n * Creates a SafeStyleSheet object from a template literal (without any\n * embedded expressions).\n *\n * This function is a template literal tag function. It should be called with\n * a template literal that does not contain any expressions. For example,\n *                         safeStyleSheet`foo`;\n * The argument must not have any < or > characters in it. This is so that\n * SafeStyleSheet's contract is preserved, allowing the SafeStyleSheet to\n * correctly be interpreted as a sequence of CSS declarations and without\n * affecting the syntactic structure of any surrounding CSS and HTML.\n *\n * @param templateObj This contains the literal part of the template literal.\n */\nfunction safeStyleSheet(templateObj) {\n    if (process.env.NODE_ENV !== 'production') {\n        (0, string_literal_1.assertIsTemplateObject)(templateObj, false, 'safeStyleSheet is a template literal tag ' +\n            'function that only accepts template literals without ' +\n            'expressions. For example, safeStyleSheet`foo`;');\n    }\n    var styleSheet = templateObj[0];\n    if (process.env.NODE_ENV !== 'production') {\n        if (/[<>]/.test(styleSheet)) {\n            throw new Error('Forbidden characters in styleSheet string: ' + styleSheet);\n        }\n    }\n    return (0, style_sheet_impl_1.createStyleSheet)(styleSheet);\n}\nexports.safeStyleSheet = safeStyleSheet;\n/**\n * Creates a `SafeStyleSheet` value by concatenating multiple `SafeStyleSheet`s.\n */\nfunction concatStyleSheets(sheets) {\n    return (0, style_sheet_impl_1.createStyleSheet)(sheets.map(style_sheet_impl_1.unwrapStyleSheet).join(''));\n}\nexports.concatStyleSheets = concatStyleSheets;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.restrictivelySanitizeUrl = exports.unwrapUrlOrSanitize = exports.sanitizeJavascriptUrl = void 0;\n/**\n * @fileoverview Provides functions to enforce the SafeUrl contract at the sink\n * level.\n */\nrequire(\"../environment/dev\");\nfunction extractScheme(url) {\n    var parsedUrl;\n    try {\n        parsedUrl = new URL(url);\n    }\n    catch (e) {\n        // According to https://url.spec.whatwg.org/#constructors, the URL\n        // constructor with one parameter throws if `url` is not absolute. In this\n        // case, we are sure that no explicit scheme (javascript: ) is set.\n        // This can also be a URL parsing error, but in this case the URL won't be\n        // run anyway.\n        return 'https:';\n    }\n    return parsedUrl.protocol;\n}\n// We can't use an ES6 Set here because gws somehow depends on this code and\n// doesn't want to pay the cost of a polyfill.\nvar ALLOWED_SCHEMES = ['data:', 'http:', 'https:', 'mailto:', 'ftp:'];\n/**\n * Checks that the URL scheme is not javascript.\n * The URL parsing relies on the URL API in browsers that support it.\n * @param url The URL to sanitize for a SafeUrl sink.\n * @return undefined if url has a javascript: scheme, the original URL\n *     otherwise.\n */\nfunction sanitizeJavascriptUrl(url) {\n    var parsedScheme = extractScheme(url);\n    if (parsedScheme === 'javascript:') {\n        if (process.env.NODE_ENV !== 'production') {\n            console.error(\"A URL with content '\".concat(url, \"' was sanitized away.\"));\n        }\n        return undefined;\n    }\n    return url;\n}\nexports.sanitizeJavascriptUrl = sanitizeJavascriptUrl;\n/**\n * Adapter to sanitize string URLs in DOM sink wrappers.\n * @return undefined if the URL was sanitized.\n */\nfunction unwrapUrlOrSanitize(url) {\n    return sanitizeJavascriptUrl(url);\n}\nexports.unwrapUrlOrSanitize = unwrapUrlOrSanitize;\n/**\n * Sanitizes a URL restrictively.\n * This sanitizer protects against XSS and potentially other uncommon and\n * undesirable schemes that an attacker could use for e.g. phishing (tel:,\n * callto: ssh: etc schemes). This sanitizer is primarily meant to be used by\n * the HTML sanitizer.\n */\nfunction restrictivelySanitizeUrl(url) {\n    var parsedScheme = extractScheme(url);\n    if (parsedScheme !== undefined &&\n        ALLOWED_SCHEMES.indexOf(parsedScheme.toLowerCase()) !== -1) {\n        return url;\n    }\n    return 'about:invalid#zClosurez';\n}\nexports.restrictivelySanitizeUrl = restrictivelySanitizeUrl;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setHref = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * Sets the Href attribute from the given Url.\n */\nfunction setHref(anchor, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        anchor.href = sanitizedUrl;\n    }\n}\nexports.setHref = setHref;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setHref = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * Sets the Href attribute from the given Url.\n */\nfunction setHref(area, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        area.href = sanitizedUrl;\n    }\n}\nexports.setHref = setHref;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setFormaction = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * Sets the Formaction attribute from the given Url.\n */\nfunction setFormaction(button, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        button.formAction = sanitizedUrl;\n    }\n}\nexports.setFormaction = setFormaction;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setPrefixedAttribute = exports.buildPrefixedAttributeSetter = exports.insertAdjacentHtml = exports.setCssText = exports.setOuterHtml = exports.setInnerHtml = void 0;\n/**\n * @fileoverview This contains safe wrappers for properties that aren't specific\n * to one kind of HTMLElement (like innerHTML), plus other setters and functions\n * that are not tied to elements (like location.href or Worker constructor).\n */\nvar attribute_impl_1 = require(\"../../internals/attribute_impl\");\nvar html_impl_1 = require(\"../../internals/html_impl\");\nvar style_impl_1 = require(\"../../internals/style_impl\");\n/**\n * Safely set {@link Element.innerHTML} on a given ShadowRoot or Element which\n * may not be a `<script>` element or a `<style>` element.\n */\nfunction setInnerHtml(elOrRoot, v) {\n    if (isElement(elOrRoot)) {\n        throwIfScriptOrStyle(elOrRoot);\n    }\n    elOrRoot.innerHTML = (0, html_impl_1.unwrapHtml)(v);\n}\nexports.setInnerHtml = setInnerHtml;\n/**\n * Safely set {@link Element.outerHTML} for the given Element.\n */\nfunction setOuterHtml(e, v) {\n    var parent = e.parentElement;\n    if (parent !== null) {\n        throwIfScriptOrStyle(parent);\n    }\n    e.outerHTML = (0, html_impl_1.unwrapHtml)(v);\n}\nexports.setOuterHtml = setOuterHtml;\n/**\n * Set `ElementCSSInlineStyle.cssText` for the given `ElementCSSInlineStyle`.\n */\nfunction setCssText(e, v) {\n    e.style.cssText = (0, style_impl_1.unwrapStyle)(v);\n}\nexports.setCssText = setCssText;\n/**\n * Safely call {@link Element.insertAdjacentHTML} for the given Element.\n */\nfunction insertAdjacentHtml(element, position, v) {\n    var tagContext = (position === 'beforebegin' || position === 'afterend') ?\n        element.parentElement :\n        element;\n    if (tagContext !== null) {\n        throwIfScriptOrStyle(tagContext);\n    }\n    element.insertAdjacentHTML(position, (0, html_impl_1.unwrapHtml)(v));\n}\nexports.insertAdjacentHtml = insertAdjacentHtml;\n/**\n * Given a set of known-to-be-safe prefixes (e.g., \"data-\", \"aria-\", \"js\"),\n * return a setter function that allows you to set attributes on an element,\n * as long as the names of the attributes to be set has one of the prefixes.\n *\n * The returned setter ensures that setting any dangerous attribute, e.g.,\n * \"src\", \"href\" will cause an exception. This is intended to be used as the\n * safe alterantive of `Element#setAttribute`, when applications need to set\n * attributes that do not have security implications and do not have a\n * corresponding DOM property.\n */\nfunction buildPrefixedAttributeSetter(prefix) {\n    var otherPrefixes = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        otherPrefixes[_i - 1] = arguments[_i];\n    }\n    var prefixes = __spreadArray([prefix], __read(otherPrefixes), false);\n    return function (e, attr, value) {\n        setPrefixedAttribute(prefixes, e, attr, value);\n    };\n}\nexports.buildPrefixedAttributeSetter = buildPrefixedAttributeSetter;\n/**\n * The safe alternative to Element#setAttribute. The function takes a list of\n * `SafeAttributePrefix`, making developer intention explicit. The attribute\n * to be set must has one of the safe prefixes, otherwise the function throws\n * an Error.\n */\nfunction setPrefixedAttribute(attrPrefixes, e, attr, value) {\n    if (attrPrefixes.length === 0) {\n        throw new Error('No prefixes are provided');\n    }\n    var prefixes = attrPrefixes.map(function (s) { return (0, attribute_impl_1.unwrapAttributePrefix)(s); });\n    var attrLower = attr.toLowerCase();\n    if (prefixes.every(function (p) { return attrLower.indexOf(p) !== 0; })) {\n        throw new Error(\"Attribute \\\"\".concat(attr, \"\\\" does not match any of the allowed prefixes.\"));\n    }\n    e.setAttribute(attr, value);\n}\nexports.setPrefixedAttribute = setPrefixedAttribute;\nfunction throwIfScriptOrStyle(element) {\n    if (element.tagName.toLowerCase() === 'script') {\n        throw new Error('Use setTextContent with a SafeScript.');\n    }\n    else if (element.tagName.toLowerCase() === 'style') {\n        throw new Error('Use setTextContent with a SafeStyleSheet.');\n    }\n}\nfunction isElement(elOrRoot) {\n    return elOrRoot.tagName !== undefined;\n}\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setSrc = void 0;\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\n/**\n * Sets the Src attribute from the given SafeUrl.\n */\nfunction setSrc(embedEl, url) {\n    embedEl.src = (0, resource_url_impl_1.unwrapResourceUrl)(url);\n}\nexports.setSrc = setSrc;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setAction = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * Sets the Action attribute from the given Url.\n */\nfunction setAction(form, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        form.action = sanitizedUrl;\n    }\n}\nexports.setAction = setAction;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setSrcdoc = exports.setSrc = void 0;\n/**\n * @fileoverview Safe iframe helpers and go/intents-for-iframes-for-closure\n */\nvar html_impl_1 = require(\"../../internals/html_impl\");\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\n/** Sets the Src attribute using a TrustedResourceUrl */\nfunction setSrc(iframe, v) {\n    iframe.src = (0, resource_url_impl_1.unwrapResourceUrl)(v).toString();\n}\nexports.setSrc = setSrc;\n/** Sets the Srcdoc attribute using a SafeHtml */\nfunction setSrcdoc(iframe, v) {\n    iframe.srcdoc = (0, html_impl_1.unwrapHtml)(v);\n}\nexports.setSrcdoc = setSrcdoc;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setFormaction = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * Sets the Formaction attribute from the given Url.\n */\nfunction setFormaction(input, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        input.formAction = sanitizedUrl;\n    }\n}\nexports.setFormaction = setFormaction;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setHrefAndRel = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\nvar SAFE_URL_REL_VALUES = [\n    'alternate',\n    'author',\n    'bookmark',\n    'canonical',\n    'cite',\n    'help',\n    'icon',\n    'license',\n    'next',\n    'prefetch',\n    'dns-prefetch',\n    'prerender',\n    'preconnect',\n    'preload',\n    'prev',\n    'search',\n    'subresource',\n];\nfunction setHrefAndRel(link, url, rel) {\n    if (url instanceof resource_url_impl_1.TrustedResourceUrl) {\n        link.href = (0, resource_url_impl_1.unwrapResourceUrl)(url).toString();\n    }\n    else {\n        if (SAFE_URL_REL_VALUES.indexOf(rel) === -1) {\n            throw new Error(\"TrustedResourceUrl href attribute required with rel=\\\"\".concat(rel, \"\\\"\"));\n        }\n        var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n        if (sanitizedUrl === undefined) {\n            return;\n        }\n        link.href = sanitizedUrl;\n    }\n    link.rel = rel;\n}\nexports.setHrefAndRel = setHrefAndRel;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setData = void 0;\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\n/** Sets the data attribute using a TrustedResourceUrl */\nfunction setData(obj, v) {\n    obj.data = (0, resource_url_impl_1.unwrapResourceUrl)(v);\n}\nexports.setData = setData;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setSrc = exports.setTextContent = void 0;\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\nvar script_impl_1 = require(\"../../internals/script_impl\");\n/** Returns CSP nonce, if set for any script tag. */\nfunction getScriptNonceFromWindow(win) {\n    var _a;\n    var doc = win.document;\n    // document.querySelector can be undefined in non-browser environments.\n    var script = (_a = doc.querySelector) === null || _a === void 0 ? void 0 : _a.call(doc, 'script[nonce]');\n    if (script) {\n        // Try to get the nonce from the IDL property first, because browsers that\n        // implement additional nonce protection features (currently only Chrome) to\n        // prevent nonce stealing via CSS do not expose the nonce via attributes.\n        // See https://github.com/whatwg/html/issues/2369\n        return script['nonce'] || script.getAttribute('nonce') || '';\n    }\n    return '';\n}\n/** Propagates CSP nonce to dynamically created scripts. */\nfunction setNonceForScriptElement(script) {\n    var win = script.ownerDocument && script.ownerDocument.defaultView;\n    var nonce = getScriptNonceFromWindow(win || window);\n    if (nonce) {\n        script.setAttribute('nonce', nonce);\n    }\n}\n/** Sets textContent from the given SafeScript. */\nfunction setTextContent(script, v) {\n    script.textContent = (0, script_impl_1.unwrapScript)(v);\n    setNonceForScriptElement(script);\n}\nexports.setTextContent = setTextContent;\n/** Sets the Src attribute using a TrustedResourceUrl */\nfunction setSrc(script, v) {\n    script.src = (0, resource_url_impl_1.unwrapResourceUrl)(v);\n    setNonceForScriptElement(script);\n}\nexports.setSrc = setSrc;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.setTextContent = void 0;\nvar style_sheet_impl_1 = require(\"../../internals/style_sheet_impl\");\n/** Safe setters for `HTMLStyleElement`s. */\nfunction setTextContent(elem, safeStyleSheet) {\n    elem.textContent = (0, style_sheet_impl_1.unwrapStyleSheet)(safeStyleSheet);\n}\nexports.setTextContent = setTextContent;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.execCommandInsertHtml = exports.execCommand = exports.write = void 0;\nvar html_impl_1 = require(\"../../internals/html_impl\");\n/**\n * write safely calls {@link Document.write} on the given {@link Document} with\n * the given {@link SafeHtml}.\n */\nfunction write(doc, text) {\n    doc.write((0, html_impl_1.unwrapHtml)(text));\n}\nexports.write = write;\n/**\n * Safely calls {@link Document.execCommand}. When command is insertHtml, a\n * SafeHtml must be passed in as value.\n */\nfunction execCommand(doc, command, value) {\n    var commandString = String(command);\n    var valueArgument = value;\n    if (commandString.toLowerCase() === 'inserthtml') {\n        valueArgument = (0, html_impl_1.unwrapHtml)(value);\n    }\n    return doc.execCommand(commandString, /* showUi= */ false, valueArgument);\n}\nexports.execCommand = execCommand;\n/**\n * Safely calls {@link Document.execCommand}('insertHtml').\n * @deprecated Use safeDocument.execCommand.\n */\nfunction execCommandInsertHtml(doc, html) {\n    return doc.execCommand('insertHTML', /* showUi= */ false, (0, html_impl_1.unwrapHtml)(html));\n}\nexports.execCommandInsertHtml = execCommandInsertHtml;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.parseFromString = exports.parseHtml = void 0;\nvar html_impl_1 = require(\"../../internals/html_impl\");\n/** Safely parses a string using the HTML parser. */\nfunction parseHtml(parser, html) {\n    return parseFromString(parser, html, 'text/html');\n}\nexports.parseHtml = parseHtml;\n/** Safely parses a string using the HTML or XML parser. */\nfunction parseFromString(parser, content, contentType) {\n    return parser.parseFromString((0, html_impl_1.unwrapHtml)(content), contentType);\n}\nexports.parseFromString = parseFromString;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.globalEval = void 0;\nvar script_impl_1 = require(\"../../internals/script_impl\");\n/**\n * Evaluates a SafeScript value in the given scope using eval.\n *\n * Strongly consider avoiding this, as eval blocks CSP adoption and does not\n * benefit from compiler optimizations.\n */\nfunction globalEval(win, script) {\n    var trustedScript = (0, script_impl_1.unwrapScript)(script);\n    var result = win.eval(trustedScript);\n    if (result === trustedScript) {\n        // https://crbug.com/1024786 manifesting in workers.\n        result = win.eval(trustedScript.toString());\n    }\n    return result;\n}\nexports.globalEval = globalEval;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.assign = exports.replace = exports.setHref = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * setHref safely sets {@link Location.href} on the given {@link Location} with\n * given {@link Url}.\n */\nfunction setHref(loc, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        loc.href = sanitizedUrl;\n    }\n}\nexports.setHref = setHref;\n/**\n * replace safely calls {@link Location.replace} on the given {@link Location}\n * with given {@link Url}.\n */\nfunction replace(loc, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        loc.replace(sanitizedUrl);\n    }\n}\nexports.replace = replace;\n/**\n * assign safely calls {@link Location.assign} on the given {@link Location}\n * with given {@link Url}.\n */\nfunction assign(loc, url) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        loc.assign(sanitizedUrl);\n    }\n}\nexports.assign = assign;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createContextualFragment = void 0;\nvar html_impl_1 = require(\"../../internals/html_impl\");\n/** Safely creates a contextualFragment. */\nfunction createContextualFragment(range, html) {\n    return range.createContextualFragment((0, html_impl_1.unwrapHtml)(html));\n}\nexports.createContextualFragment = createContextualFragment;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.register = void 0;\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\n/** Safely registers a service worker by URL */\nfunction register(container, scriptURL, options) {\n    return container.register((0, resource_url_impl_1.unwrapResourceUrl)(scriptURL), options);\n}\nexports.register = register;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.open = void 0;\nvar url_sanitizer_1 = require(\"../../builders/url_sanitizer\");\n/**\n * open calls {@link Window.open} on the given {@link Window}, given a\n * target {@link Url}.\n */\nfunction open(win, url, target, features) {\n    var sanitizedUrl = (0, url_sanitizer_1.unwrapUrlOrSanitize)(url);\n    if (sanitizedUrl !== undefined) {\n        return win.open(sanitizedUrl, target, features);\n    }\n    return null;\n}\nexports.open = open;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.importScripts = exports.createShared = exports.create = void 0;\nvar resource_url_impl_1 = require(\"../../internals/resource_url_impl\");\n/**\n * Safely creates a Web Worker.\n *\n * Example usage:\n *   const trustedResourceUrl = trustedResourceUrl`/safe_script.js`;\n *   safedom.safeWorker.create(trustedResourceUrl);\n * which is a safe alternative to\n *   new Worker(url);\n * The latter can result in loading untrusted code.\n */\nfunction create(url, options) {\n    return new Worker((0, resource_url_impl_1.unwrapResourceUrl)(url), options);\n}\nexports.create = create;\n/** Safely creates a shared Web Worker. */\nfunction createShared(url, options) {\n    return new SharedWorker((0, resource_url_impl_1.unwrapResourceUrl)(url), options);\n}\nexports.createShared = createShared;\n/** Safely calls importScripts */\nfunction importScripts(scope) {\n    var urls = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        urls[_i - 1] = arguments[_i];\n    }\n    scope.importScripts.apply(scope, __spreadArray([], __read(urls.map(function (url) { return (0, resource_url_impl_1.unwrapResourceUrl)(url); })), false));\n}\nexports.importScripts = importScripts;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.safeWorker = exports.safeWindow = exports.safeServiceWorkerContainer = exports.safeRange = exports.safeLocation = exports.safeGlobal = exports.safeDomParser = exports.safeDocument = exports.safeStyleEl = exports.safeScriptEl = exports.safeObjectEl = exports.safeLinkEl = exports.safeInputEl = exports.safeIframeEl = exports.safeFormEl = exports.safeEmbedEl = exports.safeElement = exports.safeButtonEl = exports.safeAreaEl = exports.safeAnchorEl = void 0;\n/**\n * @fileoverview This file re-exports all of the wrappers to ensure that we have\n * a clearly defined interface.\n */\nexports.safeAnchorEl = __importStar(require(\"./elements/anchor\"));\nexports.safeAreaEl = __importStar(require(\"./elements/area\"));\nexports.safeButtonEl = __importStar(require(\"./elements/button\"));\nexports.safeElement = __importStar(require(\"./elements/element\"));\nexports.safeEmbedEl = __importStar(require(\"./elements/embed\"));\nexports.safeFormEl = __importStar(require(\"./elements/form\"));\nexports.safeIframeEl = __importStar(require(\"./elements/iframe\"));\nexports.safeInputEl = __importStar(require(\"./elements/input\"));\nexports.safeLinkEl = __importStar(require(\"./elements/link\"));\nexports.safeObjectEl = __importStar(require(\"./elements/object\"));\nexports.safeScriptEl = __importStar(require(\"./elements/script\"));\nexports.safeStyleEl = __importStar(require(\"./elements/style\"));\nexports.safeDocument = __importStar(require(\"./globals/document\"));\nexports.safeDomParser = __importStar(require(\"./globals/dom_parser\"));\nexports.safeGlobal = __importStar(require(\"./globals/global\"));\nexports.safeLocation = __importStar(require(\"./globals/location\"));\nexports.safeRange = __importStar(require(\"./globals/range\"));\nexports.safeServiceWorkerContainer = __importStar(require(\"./globals/service_worker_container\"));\nexports.safeWindow = __importStar(require(\"./globals/window\"));\nexports.safeWorker = __importStar(require(\"./globals/worker\"));\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\n/**\n * Ensure process.env.NODE_ENV is set even when not running under Webpack or\n * Node. Terser will strip this out of production binaries.\n */\n/*#__PURE__*/ (function () {\n    if (typeof process === 'undefined') {\n        window.process = { env: { NODE_ENV: 'development' } };\n    }\n})();\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapStyleSheet = exports.SafeStyleSheet = exports.isStyleSheet = exports.unwrapStyle = exports.SafeStyle = exports.isStyle = exports.unwrapScript = exports.SafeScript = exports.isScript = exports.EMPTY_SCRIPT = exports.unwrapResourceUrl = exports.TrustedResourceUrl = exports.isResourceUrl = exports.unwrapHtml = exports.SafeHtml = exports.isHtml = exports.EMPTY_HTML = exports.unwrapAttributePrefix = exports.SafeAttributePrefix = exports.safeStyleSheet = exports.concatStyleSheets = exports.safeStyle = exports.concatStyles = exports.scriptFromJson = exports.safeScriptWithArgs = exports.safeScript = exports.concatScripts = exports.trustedResourceUrl = exports.replaceFragment = exports.blobUrlFromScript = exports.appendParams = exports.HtmlSanitizerBuilder = exports.sanitizeHtmlToFragment = exports.sanitizeHtmlAssertUnchanged = exports.sanitizeHtml = exports.htmlEscape = exports.createScriptSrc = exports.createScript = exports.concatHtmls = exports.safeAttrPrefix = void 0;\n/** Safe builders */\nvar attribute_builders_1 = require(\"./builders/attribute_builders\");\nObject.defineProperty(exports, \"safeAttrPrefix\", { enumerable: true, get: function () { return attribute_builders_1.safeAttrPrefix; } });\nvar html_builders_1 = require(\"./builders/html_builders\");\nObject.defineProperty(exports, \"concatHtmls\", { enumerable: true, get: function () { return html_builders_1.concatHtmls; } });\nObject.defineProperty(exports, \"createScript\", { enumerable: true, get: function () { return html_builders_1.createScript; } });\nObject.defineProperty(exports, \"createScriptSrc\", { enumerable: true, get: function () { return html_builders_1.createScriptSrc; } });\nObject.defineProperty(exports, \"htmlEscape\", { enumerable: true, get: function () { return html_builders_1.htmlEscape; } });\nvar html_sanitizer_1 = require(\"./builders/html_sanitizer/html_sanitizer\");\nObject.defineProperty(exports, \"sanitizeHtml\", { enumerable: true, get: function () { return html_sanitizer_1.sanitizeHtml; } });\nObject.defineProperty(exports, \"sanitizeHtmlAssertUnchanged\", { enumerable: true, get: function () { return html_sanitizer_1.sanitizeHtmlAssertUnchanged; } });\nObject.defineProperty(exports, \"sanitizeHtmlToFragment\", { enumerable: true, get: function () { return html_sanitizer_1.sanitizeHtmlToFragment; } });\nvar html_sanitizer_builder_1 = require(\"./builders/html_sanitizer/html_sanitizer_builder\");\nObject.defineProperty(exports, \"HtmlSanitizerBuilder\", { enumerable: true, get: function () { return html_sanitizer_builder_1.HtmlSanitizerBuilder; } });\nvar resource_url_builders_1 = require(\"./builders/resource_url_builders\");\nObject.defineProperty(exports, \"appendParams\", { enumerable: true, get: function () { return resource_url_builders_1.appendParams; } });\nObject.defineProperty(exports, \"blobUrlFromScript\", { enumerable: true, get: function () { return resource_url_builders_1.blobUrlFromScript; } });\nObject.defineProperty(exports, \"replaceFragment\", { enumerable: true, get: function () { return resource_url_builders_1.replaceFragment; } });\nObject.defineProperty(exports, \"trustedResourceUrl\", { enumerable: true, get: function () { return resource_url_builders_1.trustedResourceUrl; } });\nvar script_builders_1 = require(\"./builders/script_builders\");\nObject.defineProperty(exports, \"concatScripts\", { enumerable: true, get: function () { return script_builders_1.concatScripts; } });\nObject.defineProperty(exports, \"safeScript\", { enumerable: true, get: function () { return script_builders_1.safeScript; } });\nObject.defineProperty(exports, \"safeScriptWithArgs\", { enumerable: true, get: function () { return script_builders_1.safeScriptWithArgs; } });\nObject.defineProperty(exports, \"scriptFromJson\", { enumerable: true, get: function () { return script_builders_1.scriptFromJson; } });\nvar style_builders_1 = require(\"./builders/style_builders\");\nObject.defineProperty(exports, \"concatStyles\", { enumerable: true, get: function () { return style_builders_1.concatStyles; } });\nObject.defineProperty(exports, \"safeStyle\", { enumerable: true, get: function () { return style_builders_1.safeStyle; } });\nvar style_sheet_builders_1 = require(\"./builders/style_sheet_builders\");\nObject.defineProperty(exports, \"concatStyleSheets\", { enumerable: true, get: function () { return style_sheet_builders_1.concatStyleSheets; } });\nObject.defineProperty(exports, \"safeStyleSheet\", { enumerable: true, get: function () { return style_sheet_builders_1.safeStyleSheet; } });\n/** Types, constants and unwrappers */\nvar attribute_impl_1 = require(\"./internals/attribute_impl\");\nObject.defineProperty(exports, \"SafeAttributePrefix\", { enumerable: true, get: function () { return attribute_impl_1.SafeAttributePrefix; } });\nObject.defineProperty(exports, \"unwrapAttributePrefix\", { enumerable: true, get: function () { return attribute_impl_1.unwrapAttributePrefix; } });\nvar html_impl_1 = require(\"./internals/html_impl\");\nObject.defineProperty(exports, \"EMPTY_HTML\", { enumerable: true, get: function () { return html_impl_1.EMPTY_HTML; } });\nObject.defineProperty(exports, \"isHtml\", { enumerable: true, get: function () { return html_impl_1.isHtml; } });\nObject.defineProperty(exports, \"SafeHtml\", { enumerable: true, get: function () { return html_impl_1.SafeHtml; } });\nObject.defineProperty(exports, \"unwrapHtml\", { enumerable: true, get: function () { return html_impl_1.unwrapHtml; } });\nvar resource_url_impl_1 = require(\"./internals/resource_url_impl\");\nObject.defineProperty(exports, \"isResourceUrl\", { enumerable: true, get: function () { return resource_url_impl_1.isResourceUrl; } });\nObject.defineProperty(exports, \"TrustedResourceUrl\", { enumerable: true, get: function () { return resource_url_impl_1.TrustedResourceUrl; } });\nObject.defineProperty(exports, \"unwrapResourceUrl\", { enumerable: true, get: function () { return resource_url_impl_1.unwrapResourceUrl; } });\nvar script_impl_1 = require(\"./internals/script_impl\");\nObject.defineProperty(exports, \"EMPTY_SCRIPT\", { enumerable: true, get: function () { return script_impl_1.EMPTY_SCRIPT; } });\nObject.defineProperty(exports, \"isScript\", { enumerable: true, get: function () { return script_impl_1.isScript; } });\nObject.defineProperty(exports, \"SafeScript\", { enumerable: true, get: function () { return script_impl_1.SafeScript; } });\nObject.defineProperty(exports, \"unwrapScript\", { enumerable: true, get: function () { return script_impl_1.unwrapScript; } });\nvar style_impl_1 = require(\"./internals/style_impl\");\nObject.defineProperty(exports, \"isStyle\", { enumerable: true, get: function () { return style_impl_1.isStyle; } });\nObject.defineProperty(exports, \"SafeStyle\", { enumerable: true, get: function () { return style_impl_1.SafeStyle; } });\nObject.defineProperty(exports, \"unwrapStyle\", { enumerable: true, get: function () { return style_impl_1.unwrapStyle; } });\nvar style_sheet_impl_1 = require(\"./internals/style_sheet_impl\");\nObject.defineProperty(exports, \"isStyleSheet\", { enumerable: true, get: function () { return style_sheet_impl_1.isStyleSheet; } });\nObject.defineProperty(exports, \"SafeStyleSheet\", { enumerable: true, get: function () { return style_sheet_impl_1.SafeStyleSheet; } });\nObject.defineProperty(exports, \"unwrapStyleSheet\", { enumerable: true, get: function () { return style_sheet_impl_1.unwrapStyleSheet; } });\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapAttributePrefix = exports.createAttributePrefix = exports.SafeAttributePrefix = void 0;\nrequire(\"../environment/dev\");\nvar secrets_1 = require(\"./secrets\");\n/** A prefix with which an attribute is safe to set using plain strings. */\nvar SafeAttributePrefix = /** @class */ (function () {\n    function SafeAttributePrefix() {\n    }\n    return SafeAttributePrefix;\n}());\nexports.SafeAttributePrefix = SafeAttributePrefix;\n/** Implementation for `SafeAttributePrefix` */\nvar AttributePrefixImpl = /** @class */ (function (_super) {\n    __extends(AttributePrefixImpl, _super);\n    function AttributePrefixImpl(attrPrefix, token) {\n        var _this = _super.call(this) || this;\n        if (process.env.NODE_ENV !== 'production') {\n            (0, secrets_1.ensureTokenIsValid)(token);\n        }\n        _this.privateDoNotAccessOrElseWrappedAttrPrefix = attrPrefix;\n        return _this;\n    }\n    AttributePrefixImpl.prototype.toString = function () {\n        return this.privateDoNotAccessOrElseWrappedAttrPrefix;\n    };\n    return AttributePrefixImpl;\n}(SafeAttributePrefix));\n/**\n * Builds a new `SafeAttribute` from the given string, without enforcing\n * safety guarantees. This shouldn't be exposed to application developers, and\n * must only be used as a step towards safe builders or safe constants.\n */\nfunction createAttributePrefix(attrPrefix) {\n    return new AttributePrefixImpl(attrPrefix, secrets_1.secretToken);\n}\nexports.createAttributePrefix = createAttributePrefix;\n/**\n * Returns the string value of the passed `SafeAttributePrefix` object while\n * ensuring it has the correct type.\n */\nfunction unwrapAttributePrefix(value) {\n    if (value instanceof AttributePrefixImpl) {\n        return value.privateDoNotAccessOrElseWrappedAttrPrefix;\n    }\n    else {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = 'Unexpected type when unwrapping SafeAttributePrefix';\n        }\n        throw new Error(message);\n    }\n}\nexports.unwrapAttributePrefix = unwrapAttributePrefix;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapHtml = exports.isHtml = exports.EMPTY_HTML = exports.createHtml = exports.SafeHtml = void 0;\nrequire(\"../environment/dev\");\n/* g3_import_pure from './pure' */\nvar secrets_1 = require(\"./secrets\");\nvar trusted_types_1 = require(\"./trusted_types\");\n/**\n * Runtime implementation of `TrustedHTML` in browsers that don't support it.\n */\nvar HtmlImpl = /** @class */ (function () {\n    function HtmlImpl(html, token) {\n        if (process.env.NODE_ENV !== 'production') {\n            (0, secrets_1.ensureTokenIsValid)(token);\n        }\n        this.privateDoNotAccessOrElseWrappedHtml = html;\n    }\n    HtmlImpl.prototype.toString = function () {\n        return this.privateDoNotAccessOrElseWrappedHtml.toString();\n    };\n    return HtmlImpl;\n}());\nfunction createHtmlInternal(html, trusted) {\n    return (trusted !== null && trusted !== void 0 ? trusted : new HtmlImpl(html, secrets_1.secretToken));\n}\nvar GlobalTrustedHTML = (typeof window !== undefined) ? window.TrustedHTML : undefined;\n/**\n * Also exports the constructor so that instanceof checks work.\n */\nexports.SafeHtml = (GlobalTrustedHTML !== null && GlobalTrustedHTML !== void 0 ? GlobalTrustedHTML : HtmlImpl);\n/**\n * Builds a new `SafeHtml` from the given string, without enforcing safety\n * guarantees. It may cause side effects by creating a Trusted Types policy.\n * This shouldn't be exposed to application developers, and must only be used as\n * a step towards safe builders or safe constants.\n */\nfunction createHtml(html) {\n    var _a;\n    /** @noinline */\n    var noinlineHtml = html;\n    return createHtmlInternal(noinlineHtml, (_a = (0, trusted_types_1.getTrustedTypesPolicy)()) === null || _a === void 0 ? void 0 : _a.createHTML(noinlineHtml));\n}\nexports.createHtml = createHtml;\n/**\n * An empty `SafeHtml` constant.\n * Unlike the function above, using this will not create a policy.\n */\nexports.EMPTY_HTML = \n/* #__PURE__ */ (function () { var _a; return createHtmlInternal('', (_a = (0, trusted_types_1.getTrustedTypes)()) === null || _a === void 0 ? void 0 : _a.emptyHTML); })();\n/**\n * Checks if the given value is a `SafeHtml` instance.\n */\nfunction isHtml(value) {\n    return value instanceof exports.SafeHtml;\n}\nexports.isHtml = isHtml;\n/**\n * Returns the value of the passed `SafeHtml` object while ensuring it\n * has the correct type.\n *\n * Returns a native `TrustedHTML` or a string if Trusted Types are disabled.\n */\nfunction unwrapHtml(value) {\n    var _a;\n    if ((_a = (0, trusted_types_1.getTrustedTypes)()) === null || _a === void 0 ? void 0 : _a.isHTML(value)) {\n        return value;\n    }\n    else if (value instanceof HtmlImpl) {\n        return value.privateDoNotAccessOrElseWrappedHtml;\n    }\n    else {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = 'Unexpected type when unwrapping SafeHtml';\n        }\n        throw new Error(message);\n    }\n}\nexports.unwrapHtml = unwrapHtml;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapResourceUrl = exports.isResourceUrl = exports.createResourceUrl = exports.TrustedResourceUrl = void 0;\nrequire(\"../environment/dev\");\nvar secrets_1 = require(\"./secrets\");\nvar trusted_types_1 = require(\"./trusted_types\");\n/**\n * Runtime implementation of `TrustedScriptURL` in browsers that don't support\n * it.\n */\nvar ResourceUrlImpl = /** @class */ (function () {\n    function ResourceUrlImpl(url, token) {\n        if (process.env.NODE_ENV !== 'production') {\n            (0, secrets_1.ensureTokenIsValid)(token);\n        }\n        this.privateDoNotAccessOrElseWrappedResourceUrl = url;\n    }\n    ResourceUrlImpl.prototype.toString = function () {\n        return this.privateDoNotAccessOrElseWrappedResourceUrl.toString();\n    };\n    return ResourceUrlImpl;\n}());\nvar GlobalTrustedScriptURL = (typeof window !== undefined) ? window.TrustedScriptURL : undefined;\n/**\n * Also exports the constructor so that instanceof checks work.\n */\nexports.TrustedResourceUrl = (GlobalTrustedScriptURL !== null && GlobalTrustedScriptURL !== void 0 ? GlobalTrustedScriptURL : ResourceUrlImpl);\n/**\n * Builds a new `TrustedResourceUrl` from the given string, without\n * enforcing safety guarantees. It may cause side effects by creating a Trusted\n * Types policy. This shouldn't be exposed to application developers, and must\n * only be used as a step towards safe builders or safe constants.\n */\nfunction createResourceUrl(url) {\n    var _a;\n    /** @noinline */\n    var noinlineUrl = url;\n    var trustedScriptURL = (_a = (0, trusted_types_1.getTrustedTypesPolicy)()) === null || _a === void 0 ? void 0 : _a.createScriptURL(noinlineUrl);\n    return (trustedScriptURL !== null && trustedScriptURL !== void 0 ? trustedScriptURL : new ResourceUrlImpl(noinlineUrl, secrets_1.secretToken));\n}\nexports.createResourceUrl = createResourceUrl;\n/**\n * Checks if the given value is a `TrustedResourceUrl` instance.\n */\nfunction isResourceUrl(value) {\n    return value instanceof exports.TrustedResourceUrl;\n}\nexports.isResourceUrl = isResourceUrl;\n/**\n * Returns the value of the passed `TrustedResourceUrl` object while ensuring it\n * has the correct type.\n *\n * Returns a native `TrustedScriptURL` or a string if Trusted Types are\n * disabled.\n */\nfunction unwrapResourceUrl(value) {\n    var _a;\n    if ((_a = (0, trusted_types_1.getTrustedTypes)()) === null || _a === void 0 ? void 0 : _a.isScriptURL(value)) {\n        return value;\n    }\n    else if (value instanceof ResourceUrlImpl) {\n        return value.privateDoNotAccessOrElseWrappedResourceUrl;\n    }\n    else {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = 'Unexpected type when unwrapping TrustedResourceUrl';\n        }\n        throw new Error(message);\n    }\n}\nexports.unwrapResourceUrl = unwrapResourceUrl;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapScript = exports.isScript = exports.EMPTY_SCRIPT = exports.createScript = exports.SafeScript = void 0;\nrequire(\"../environment/dev\");\n/* g3_import_pure from './pure' */\nvar secrets_1 = require(\"./secrets\");\nvar trusted_types_1 = require(\"./trusted_types\");\n/**\n * Runtime implementation of `TrustedScript` in browswers that don't support it.\n * script element.\n */\nvar ScriptImpl = /** @class */ (function () {\n    function ScriptImpl(script, token) {\n        if (process.env.NODE_ENV !== 'production') {\n            (0, secrets_1.ensureTokenIsValid)(token);\n        }\n        this.privateDoNotAccessOrElseWrappedScript = script;\n    }\n    ScriptImpl.prototype.toString = function () {\n        return this.privateDoNotAccessOrElseWrappedScript.toString();\n    };\n    return ScriptImpl;\n}());\nfunction createScriptInternal(script, trusted) {\n    return (trusted !== null && trusted !== void 0 ? trusted : new ScriptImpl(script, secrets_1.secretToken));\n}\nvar GlobalTrustedScript = (typeof window !== undefined) ? window.TrustedScript : undefined;\n/**\n * Also exports the constructor so that instanceof checks work.\n */\nexports.SafeScript = (GlobalTrustedScript !== null && GlobalTrustedScript !== void 0 ? GlobalTrustedScript : ScriptImpl);\n/**\n * Builds a new `SafeScript` from the given string, without enforcing\n * safety guarantees. It may cause side effects by creating a Trusted Types\n * policy. This shouldn't be exposed to application developers, and must only be\n * used as a step towards safe builders or safe constants.\n */\nfunction createScript(script) {\n    var _a;\n    /** @noinline */\n    var noinlineScript = script;\n    return createScriptInternal(noinlineScript, (_a = (0, trusted_types_1.getTrustedTypesPolicy)()) === null || _a === void 0 ? void 0 : _a.createScript(noinlineScript));\n}\nexports.createScript = createScript;\n/**\n * An empty `SafeScript` constant.\n * Unlike the functions above, using this will not create a policy.\n */\nexports.EMPTY_SCRIPT = \n/* #__PURE__ */ (function () { var _a; return createScriptInternal('', (_a = (0, trusted_types_1.getTrustedTypes)()) === null || _a === void 0 ? void 0 : _a.emptyScript); })();\n/**\n * Checks if the given value is a `SafeScript` instance.\n */\nfunction isScript(value) {\n    return value instanceof exports.SafeScript;\n}\nexports.isScript = isScript;\n/**\n * Returns the value of the passed `SafeScript` object while ensuring it\n * has the correct type.\n *\n * Returns a native `TrustedScript` or a string if Trusted Types are disabled.\n */\nfunction unwrapScript(value) {\n    var _a;\n    if ((_a = (0, trusted_types_1.getTrustedTypes)()) === null || _a === void 0 ? void 0 : _a.isScript(value)) {\n        return value;\n    }\n    else if (value instanceof ScriptImpl) {\n        return value.privateDoNotAccessOrElseWrappedScript;\n    }\n    else {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = 'Unexpected type when unwrapping SafeScript';\n        }\n        throw new Error(message);\n    }\n}\nexports.unwrapScript = unwrapScript;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ensureTokenIsValid = exports.secretToken = void 0;\n/**\n * A secret token that must be passed to safe type constructors. It is only\n * accessible from within safevalues, ensuring that unrestricted safe type\n * creation is only possible within safevalues. In particular, this prevents\n * forgery such as `safeHtmlValue.constructor('javascript:evil')`.\n */\nexports.secretToken = {};\n/**\n * Asserts that the given token matches the secret safevalues token. An\n * exception is thrown if that is not the case.\n */\nfunction ensureTokenIsValid(token) {\n    if (token !== exports.secretToken) {\n        throw new Error('Bad secret');\n    }\n}\nexports.ensureTokenIsValid = ensureTokenIsValid;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.assertIsTemplateObject = void 0;\n/**\n * An object of type TemplateStringsArray represents the literal part(s) of a\n * template literal. This function checks if a TemplateStringsArray object is\n * actually from a template literal.\n *\n * @param templateObj This contains the literal part of the template literal.\n * @param hasExprs If true, the input template may contain embedded expressions.\n * @param errorMsg The custom error message in case any checks fail.\n */\nfunction assertIsTemplateObject(templateObj, hasExprs, errorMsg) {\n    if (!Array.isArray(templateObj) || !Array.isArray(templateObj.raw) ||\n        (!hasExprs && templateObj.length !== 1)) {\n        throw new TypeError(errorMsg);\n    }\n}\nexports.assertIsTemplateObject = assertIsTemplateObject;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapStyle = exports.isStyle = exports.createStyle = exports.SafeStyle = void 0;\nrequire(\"../environment/dev\");\nvar secrets_1 = require(\"./secrets\");\n/**\n * Sequence of CSS declarations safe to use in style contexts in an HTML\n * document or in DOM APIs.\n */\nvar SafeStyle = /** @class */ (function () {\n    function SafeStyle() {\n    }\n    return SafeStyle;\n}());\nexports.SafeStyle = SafeStyle;\n/** Implementation for `SafeStyle` */\nvar StyleImpl = /** @class */ (function (_super) {\n    __extends(StyleImpl, _super);\n    function StyleImpl(style, token) {\n        var _this = _super.call(this) || this;\n        if (process.env.NODE_ENV !== 'production') {\n            (0, secrets_1.ensureTokenIsValid)(token);\n        }\n        _this.privateDoNotAccessOrElseWrappedStyle = style;\n        return _this;\n    }\n    StyleImpl.prototype.toString = function () {\n        return this.privateDoNotAccessOrElseWrappedStyle;\n    };\n    return StyleImpl;\n}(SafeStyle));\n/**\n * Builds a new `SafeStyle` from the given string, without enforcing\n * safety guarantees. This shouldn't be exposed to application developers, and\n * must only be used as a step towards safe builders or safe constants.\n */\nfunction createStyle(style) {\n    return new StyleImpl(style, secrets_1.secretToken);\n}\nexports.createStyle = createStyle;\n/**\n * Checks if the given value is a `SafeStyle` instance.\n */\nfunction isStyle(value) {\n    return value instanceof StyleImpl;\n}\nexports.isStyle = isStyle;\n/**\n * Returns the string value of the passed `SafeStyle` object while ensuring it\n * has the correct type.\n */\nfunction unwrapStyle(value) {\n    if (value instanceof StyleImpl) {\n        return value.privateDoNotAccessOrElseWrappedStyle;\n    }\n    else {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = 'Unexpected type when unwrapping SafeStyle';\n        }\n        throw new Error(message);\n    }\n}\nexports.unwrapStyle = unwrapStyle;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.unwrapStyleSheet = exports.isStyleSheet = exports.createStyleSheet = exports.SafeStyleSheet = void 0;\nrequire(\"../environment/dev\");\nvar secrets_1 = require(\"./secrets\");\n/**\n * A complete CSS style sheet, safe to use in style contexts in an HTML document\n * or DOM APIs.\n */\nvar SafeStyleSheet = /** @class */ (function () {\n    function SafeStyleSheet() {\n    }\n    return SafeStyleSheet;\n}());\nexports.SafeStyleSheet = SafeStyleSheet;\n/** Implementation for `SafeStyleSheet` */\nvar StyleSheetImpl = /** @class */ (function (_super) {\n    __extends(StyleSheetImpl, _super);\n    function StyleSheetImpl(styleSheet, token) {\n        var _this = _super.call(this) || this;\n        if (process.env.NODE_ENV !== 'production') {\n            (0, secrets_1.ensureTokenIsValid)(token);\n        }\n        _this.privateDoNotAccessOrElseWrappedStyleSheet = styleSheet;\n        return _this;\n    }\n    StyleSheetImpl.prototype.toString = function () {\n        return this.privateDoNotAccessOrElseWrappedStyleSheet;\n    };\n    return StyleSheetImpl;\n}(SafeStyleSheet));\n/**\n * Builds a new `SafeStyleSheet` from the given string, without enforcing\n * safety guarantees. This shouldn't be exposed to application developers, and\n * must only be used as a step towards safe builders or safe constants.\n */\nfunction createStyleSheet(styleSheet) {\n    return new StyleSheetImpl(styleSheet, secrets_1.secretToken);\n}\nexports.createStyleSheet = createStyleSheet;\n/**\n * Checks if the given value is a `SafeStyleSheet` instance.\n */\nfunction isStyleSheet(value) {\n    return value instanceof StyleSheetImpl;\n}\nexports.isStyleSheet = isStyleSheet;\n/**\n * Returns the string value of the passed `SafeStyleSheet` object while\n * ensuring it has the correct type.\n */\nfunction unwrapStyleSheet(value) {\n    if (value instanceof StyleSheetImpl) {\n        return value.privateDoNotAccessOrElseWrappedStyleSheet;\n    }\n    else {\n        var message = '';\n        if (process.env.NODE_ENV !== 'production') {\n            message = 'Unexpected type when unwrapping SafeStyleSheet';\n        }\n        throw new Error(message);\n    }\n}\nexports.unwrapStyleSheet = unwrapStyleSheet;\n", "\"use strict\";\n/**\n * @license\n * SPDX-License-Identifier: Apache-2.0\n */\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TEST_ONLY = exports.getTrustedTypesPolicy = exports.getTrustedTypes = void 0;\n/**\n * The name of the Trusted Types policy used by TS safevalues, or empty\n * to disable Trusted Types. This duplicates the 'google#safe', but\n * can be overridden in tests.\n */\nvar trustedTypesPolicyName = 'google#safe';\n/** Helper to retrieve the value of `window.trustedTypes`. */\nfunction trustedTypes() {\n    if (typeof window !== 'undefined') {\n        return window.trustedTypes;\n    }\n    return undefined;\n}\n/**\n * Returns window.trustedTypes if Trusted Types are enabled and supported, or\n * null otherwise.\n */\nfunction getTrustedTypes() {\n    var _a;\n    return (trustedTypesPolicyName !== '') ? ((_a = trustedTypes()) !== null && _a !== void 0 ? _a : null) : null;\n}\nexports.getTrustedTypes = getTrustedTypes;\n/**\n * The Trusted Types policy used by TS safevalues, or null if Trusted Types\n * are not enabled/supported, or undefined if the policy has not been created\n * yet.\n */\nvar trustedTypesPolicy;\n/**\n * Returns the Trusted Types policy used by TS safevalues, or null if Trusted\n * Types are not enabled/supported. The first call to this function will\n * create the policy.\n */\nfunction getTrustedTypesPolicy() {\n    var _a, _b;\n    if (trustedTypesPolicy === undefined) {\n        try {\n            trustedTypesPolicy =\n                (_b = (_a = getTrustedTypes()) === null || _a === void 0 ? void 0 : _a.createPolicy(trustedTypesPolicyName, {\n                    createHTML: function (s) { return s; },\n                    createScript: function (s) { return s; },\n                    createScriptURL: function (s) { return s; }\n                })) !== null && _b !== void 0 ? _b : null;\n        }\n        catch (_c) {\n            // In Chromium versions before 81, trustedTypes.createPolicy throws if\n            // called with a name that is already registered, even if no CSP is set.\n            // Until users have largely migrated to 81 or above, catch the error not\n            // to break the applications functionally. In such case, the code will\n            // fall back to using regular Safe Types.\n            trustedTypesPolicy = null;\n        }\n    }\n    return trustedTypesPolicy;\n}\nexports.getTrustedTypesPolicy = getTrustedTypesPolicy;\n/** Helpers for tests. */\nexports.TEST_ONLY = {\n    resetDefaults: function () {\n        trustedTypesPolicy = undefined;\n        trustedTypesPolicyName = 'google#safe';\n    },\n    setTrustedTypesPolicyName: function (name) {\n        trustedTypesPolicyName = name;\n    },\n};\n", "/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nimport {safeAttrPrefix} from 'safevalues';\nimport {safeElement} from 'safevalues/dom';\n\nimport {MDCFoundation} from './foundation';\nimport {CustomEventListener, EventType, SpecificEventListener} from './types';\n\nfunction toCamelCase(str: string) {\n  // tslint:disable-next-line:enforce-name-casing\n  return String(str).replace(/\\-([a-z])/g, (_, match) => match.toUpperCase());\n}\n\n/** MDC Component base */\nexport class MDCComponent<FoundationType extends MDCFoundation> {\n  static attachTo(root: HTMLElement): MDCComponent<MDCFoundation<{}>> {\n    // Subclasses which extend MDCBase should provide an attachTo() method that\n    // takes a root element and returns an instantiated component with its root\n    // set to that element. Also note that in the cases of subclasses, an\n    // explicit foundation class will not have to be passed in; it will simply\n    // be initialized from getDefaultFoundation().\n    return new MDCComponent(root, new MDCFoundation({}));\n  }\n\n  protected foundation: FoundationType;\n\n  constructor(\n      public root: HTMLElement, foundation?: FoundationType,\n      ...args: unknown[]) {\n    this.initialize(...args);\n    // Note that we initialize foundation here and not within the constructor's\n    // default param so that this.root is defined and can be used within the\n    // foundation class.\n    this.foundation =\n        foundation === undefined ? this.getDefaultFoundation() : foundation;\n    this.foundation.init();\n    this.initialSyncWithDOM();\n  }\n\n  /* istanbul ignore next: method param only exists for typing purposes; it does\n   * not need to be unit tested */\n  // tslint:disable-next-line:enforce-name-casing\n  initialize(..._args: unknown[]) {\n    // Subclasses can override this to do any additional setup work that would\n    // be considered part of a \"constructor\". Essentially, it is a hook into the\n    // parent constructor before the foundation is initialized. Any additional\n    // arguments besides root and foundation will be passed in here.\n  }\n\n  getDefaultFoundation(): FoundationType {\n    // Subclasses must override this method to return a properly configured\n    // foundation class for the component.\n    throw new Error(\n        'Subclasses must override getDefaultFoundation to return a properly configured ' +\n        'foundation class');\n  }\n\n  initialSyncWithDOM() {\n    // Subclasses should override this method if they need to perform work to\n    // synchronize with a host DOM object. An example of this would be a form\n    // control wrapper that needs to synchronize its internal state to some\n    // property or attribute of the host DOM. Please note: this is *not* the\n    // place to perform DOM reads/writes that would cause layout / paint, as\n    // this is called synchronously from within the constructor.\n  }\n\n  destroy() {\n    // Subclasses may implement this method to release any resources /\n    // deregister any listeners they have attached. An example of this might be\n    // deregistering a resize event from the window object.\n    this.foundation.destroy();\n  }\n\n  /**\n   * Wrapper method to add an event listener to the component's root element.\n   * This is most useful when listening for custom events.\n   */\n  listen<K extends EventType>(\n      eventType: K, handler: SpecificEventListener<K>,\n      options?: AddEventListenerOptions|boolean): void;\n  listen<E extends Event>(\n      eventType: string, handler: CustomEventListener<E>,\n      options?: AddEventListenerOptions|boolean): void;\n  listen(\n      eventType: string, handler: EventListener,\n      options?: AddEventListenerOptions|boolean) {\n    this.root.addEventListener(eventType, handler, options);\n  }\n\n  /**\n   * Wrapper method to remove an event listener to the component's root element.\n   * This is most useful when unlistening for custom events.\n   */\n  unlisten<K extends EventType>(\n      eventType: K, handler: SpecificEventListener<K>,\n      options?: AddEventListenerOptions|boolean): void;\n  unlisten<E extends Event>(\n      eventType: string, handler: CustomEventListener<E>,\n      options?: AddEventListenerOptions|boolean): void;\n  unlisten(\n      eventType: string, handler: EventListener,\n      options?: AddEventListenerOptions|boolean) {\n    this.root.removeEventListener(eventType, handler, options);\n  }\n\n  /**\n   * Fires a cross-browser-compatible custom event from the component root of\n   * the given type, with the given data.\n   */\n  emit<T extends object>(eventType: string, eventData: T, shouldBubble = false) {\n    let event: CustomEvent<T>;\n    if (typeof CustomEvent === 'function') {\n      event = new CustomEvent<T>(eventType, {\n        bubbles: shouldBubble,\n        detail: eventData,\n      });\n    } else {\n      event = document.createEvent('CustomEvent');\n      event.initCustomEvent(eventType, shouldBubble, false, eventData);\n    }\n\n    this.root.dispatchEvent(event);\n  }\n\n  /**\n   * This is a intermediate fix to allow components to use safevalues. This\n   * limits setAttribute to setting tabindex, data attributes, and aria\n   * attributes.\n   *\n   * TODO(b/263990206): remove this method and add these directly in each\n   * component. This will remove this abstraction and make it clear that the\n   * caller can't set any attribute.\n   */\n  protected safeSetAttribute(\n      element: HTMLElement,\n      attribute: string,\n      value: string,\n  ) {\n    if (attribute.toLowerCase() === 'tabindex') {\n      element.tabIndex = Number(value);\n    } else if (attribute.indexOf('data-') === 0) {\n      const dataKey = toCamelCase(attribute.replace(/^data-/, ''));\n      element.dataset[dataKey] = value;\n    } else {\n      safeElement.setPrefixedAttribute(\n          [safeAttrPrefix`aria-`, safeAttrPrefix`role`],\n          element,\n          attribute,\n          value,\n      );\n    }\n  }\n}\n\n// tslint:disable-next-line:no-default-export Needed for backward compatibility with MDC Web v0.44.0 and earlier.\nexport default MDCComponent;\n", "/**\n * @license\n * Copyright 2016 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/** MDC Foundation */\nexport class MDCFoundation<AdapterType extends {} = {}> {\n  static get cssClasses(): {[key: string]: string} {\n    // Classes extending MDCFoundation should implement this method to return an\n    // object which exports every CSS class the foundation class needs as a\n    // property. e.g. {ACTIVE: 'mdc-component--active'}\n    return {};\n  }\n\n  static get strings(): {[key: string]: string} {\n    // Classes extending MDCFoundation should implement this method to return an\n    // object which exports all semantic strings as constants. e.g. {ARIA_ROLE:\n    // 'tablist'}\n    return {};\n  }\n\n  static get numbers(): {[key: string]: number} {\n    // Classes extending MDCFoundation should implement this method to return an\n    // object which exports all of its semantic numbers as constants.\n    // e.g. {ANIMATION_DELAY_MS: 350}\n    return {};\n  }\n\n  static get defaultAdapter(): {} {\n    // Classes extending MDCFoundation may choose to implement this getter in\n    // order to provide a convenient way of viewing the necessary methods of an\n    // adapter. In the future, this could also be used for adapter validation.\n    return {};\n  }\n\n  constructor(protected adapter: AdapterType = {} as AdapterType) {}\n\n  init() {\n    // Subclasses should override this method to perform initialization routines\n    // (registering events, etc.)\n  }\n\n  destroy() {\n    // Subclasses should override this method to perform de-initialization\n    // routines (de-registering events, etc.)\n  }\n}\n\n/**\n * The constructor for MDCFoundation.\n */\nexport interface MDCFoundationConstructor<AdapterType extends object = any> {\n  new(adapter: AdapterType): MDCFoundation<AdapterType>;\n  readonly prototype: MDCFoundation<AdapterType>;\n}\n\n/**\n * The deprecated constructor for MDCFoundation.\n */\nexport interface MDCFoundationDeprecatedConstructor<\n    AdapterType extends object = any> {\n  readonly cssClasses: Record<string, string>;\n  readonly strings: Record<string, string>;\n  readonly numbers: Record<string, number>;\n  readonly defaultAdapter: AdapterType;\n\n  new(adapter?: Partial<AdapterType>): MDCFoundation<AdapterType>;\n  readonly prototype: MDCFoundation<AdapterType>;\n}\n\n/**\n * Retrieves the AdapaterType from the provided MDCFoundation generic type.\n */\nexport type MDCFoundationAdapter<T> =\n    T extends MDCFoundation<infer A>? A : never;\n\n// tslint:disable-next-line:no-default-export Needed for backward compatibility with MDC Web v0.44.0 and earlier.\nexport default MDCFoundation;\n", "/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nexport * from './component';\nexport * from './foundation';\nexport * from './types';\n", "/**\n * @license\n * Copyright 2019 Google Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\nexport type EventType = keyof GlobalEventHandlersEventMap;\nexport type SpecificEventListener<K extends EventType> =\n    (event: GlobalEventHandlersEventMap[K]) => void;\nexport type CustomEventListener<E extends Event> = (event: E) => void;\nexport type WindowEventType = keyof WindowEventMap;\nexport type SpecificWindowEventListener<K extends WindowEventType> =\n    (event: WindowEventMap[K]) => void;\n\n// `any` is required for mixin constructors\n// tslint:disable:no-any\n/**\n * A generic type for the constructor of an instance type. Note that this type\n * does not preserve accurate constructor parameters.\n *\n * @template T The instance type.\n */\nexport type Constructor<T = any> = {\n  new (...args: any[]): T;\n};\n// tslint:enable:no-any\n"], "sourceRoot": ""}