import { Routes } from '@angular/router';

export const routes: Routes = [
  // Public routes
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },

  // Protected routes
  {
    path: 'dashboard',
    loadComponent: () => import('./features/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },

  // Inventory module
  {
    path: 'inventory',
    children: [
      {
        path: 'products',
        loadComponent: () => import('./features/inventory/products/product-list.component').then(m => m.ProductListComponent)
      },
      {
        path: 'categories',
        loadComponent: () => import('./features/inventory/categories/category-list.component').then(m => m.CategoryListComponent)
      },
      {
        path: 'stock',
        loadComponent: () => import('./features/inventory/stock/stock-list.component').then(m => m.StockListComponent)
      },
      { path: '', redirectTo: 'products', pathMatch: 'full' }
    ]
  },

  // Sales module
  {
    path: 'sales',
    children: [
      {
        path: 'pos',
        loadComponent: () => import('./features/sales/pos/pos.component').then(m => m.PosComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./features/sales/history/sales-history.component').then(m => m.SalesHistoryComponent)
      },
      { path: '', redirectTo: 'pos', pathMatch: 'full' }
    ]
  },

  // Default redirects
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  { path: '**', redirectTo: '/dashboard' }
];
