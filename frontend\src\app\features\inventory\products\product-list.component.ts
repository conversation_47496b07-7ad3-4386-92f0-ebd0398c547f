import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { RouterModule } from '@angular/router';

import { ApiService } from '../../../core/services/api.service';
import { ProductWithStock, ProductConnection } from '../../../core/models/product.model';

@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule
  ],
  template: `
    <div class="product-list-container">
      <div class="header">
        <h1>Products</h1>
        <button mat-raised-button color="primary" routerLink="/inventory/products/new">
          <mat-icon>add</mat-icon>
          Add Product
        </button>
      </div>

      <!-- Filters -->
      <mat-card class="filters-card">
        <mat-card-content>
          <form [formGroup]="filterForm" class="filters-form">
            <mat-form-field appearance="outline">
              <mat-label>Search</mat-label>
              <input matInput 
                     formControlName="search"
                     placeholder="Search by name, SKU, or barcode"
                     (keyup.enter)="loadProducts()">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>

            <div class="filter-actions">
              <button mat-raised-button (click)="loadProducts()">
                <mat-icon>search</mat-icon>
                Search
              </button>
              <button mat-button (click)="clearFilters()">
                <mat-icon>clear</mat-icon>
                Clear
              </button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <!-- Products Table -->
      <mat-card class="table-card">
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="products" class="products-table">
              <!-- Image Column -->
              <ng-container matColumnDef="image">
                <th mat-header-cell *matHeaderCellDef>Image</th>
                <td mat-cell *matCellDef="let product">
                  <div class="product-image">
                    <mat-icon>inventory</mat-icon>
                  </div>
                </td>
              </ng-container>

              <!-- Product Info Column -->
              <ng-container matColumnDef="product">
                <th mat-header-cell *matHeaderCellDef>Product</th>
                <td mat-cell *matCellDef="let product">
                  <div class="product-info">
                    <div class="product-name">{{ product.name }}</div>
                    <div class="product-details">
                      <span class="sku">SKU: {{ product.sku }}</span>
                      <span class="category" *ngIf="product.category">
                        {{ product.category.name }}
                      </span>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Price Column -->
              <ng-container matColumnDef="price">
                <th mat-header-cell *matHeaderCellDef>Price</th>
                <td mat-cell *matCellDef="let product">
                  <div class="price-info">
                    <div class="retail-price">{{ product.price | currency }}</div>
                    <div class="cost-price" *ngIf="product.cost">
                      Cost: {{ product.cost | currency }}
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Stock Column -->
              <ng-container matColumnDef="stock">
                <th mat-header-cell *matHeaderCellDef>Stock</th>
                <td mat-cell *matCellDef="let product">
                  <div class="stock-info">
                    <mat-chip-set>
                      <mat-chip [class]="getStockStatusClass(product.stock)">
                        {{ product.stock?.quantity || 0 }}
                      </mat-chip>
                    </mat-chip-set>
                    <div class="stock-status" *ngIf="product.stock">
                      <span [class]="getStockStatusClass(product.stock)">
                        {{ getStockStatusText(product.stock) }}
                      </span>
                    </div>
                  </div>
                </td>
              </ng-container>

              <!-- Status Column -->
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef>Status</th>
                <td mat-cell *matCellDef="let product">
                  <mat-chip-set>
                    <mat-chip [class]="product.isActive ? 'status-active' : 'status-inactive'">
                      {{ product.isActive ? 'Active' : 'Inactive' }}
                    </mat-chip>
                  </mat-chip-set>
                </td>
              </ng-container>

              <!-- Actions Column -->
              <ng-container matColumnDef="actions">
                <th mat-header-cell *matHeaderCellDef>Actions</th>
                <td mat-cell *matCellDef="let product">
                  <div class="actions">
                    <button mat-icon-button 
                            [routerLink]="['/inventory/products', product.id]"
                            matTooltip="View Details">
                      <mat-icon>visibility</mat-icon>
                    </button>
                    <button mat-icon-button 
                            [routerLink]="['/inventory/products', product.id, 'edit']"
                            matTooltip="Edit Product">
                      <mat-icon>edit</mat-icon>
                    </button>
                    <button mat-icon-button 
                            color="warn"
                            (click)="deleteProduct(product)"
                            matTooltip="Delete Product">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>

            <!-- No Data -->
            <div class="no-data" *ngIf="products.length === 0 && !loading">
              <mat-icon>inventory</mat-icon>
              <h3>No products found</h3>
              <p>Start by adding your first product</p>
              <button mat-raised-button color="primary" routerLink="/inventory/products/new">
                Add Product
              </button>
            </div>

            <!-- Loading -->
            <div class="loading" *ngIf="loading">
              <mat-spinner></mat-spinner>
              <p>Loading products...</p>
            </div>
          </div>

          <!-- Pagination -->
          <mat-paginator 
            [length]="totalProducts"
            [pageSize]="pageSize"
            [pageSizeOptions]="[10, 25, 50, 100]"
            (page)="onPageChange($event)"
            showFirstLastButtons>
          </mat-paginator>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .product-list-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }

    .filters-card {
      margin-bottom: 20px;
    }

    .filters-form {
      display: flex;
      gap: 20px;
      align-items: center;
    }

    .filters-form mat-form-field {
      flex: 1;
    }

    .filter-actions {
      display: flex;
      gap: 10px;
    }

    .table-card {
      overflow: hidden;
    }

    .table-container {
      overflow-x: auto;
    }

    .products-table {
      width: 100%;
      min-width: 800px;
    }

    .product-image {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;
      border-radius: 4px;
    }

    .product-info {
      display: flex;
      flex-direction: column;
    }

    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .product-details {
      display: flex;
      gap: 15px;
      font-size: 0.8em;
      color: #666;
    }

    .price-info {
      display: flex;
      flex-direction: column;
    }

    .retail-price {
      font-weight: 500;
    }

    .cost-price {
      font-size: 0.8em;
      color: #666;
    }

    .stock-info {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
    }

    .stock-status {
      font-size: 0.8em;
    }

    .stock-normal {
      color: #4caf50;
    }

    .stock-low {
      color: #ff9800;
    }

    .stock-out {
      color: #f44336;
    }

    .status-active {
      background-color: #e8f5e8;
      color: #2e7d32;
    }

    .status-inactive {
      background-color: #ffebee;
      color: #c62828;
    }

    .actions {
      display: flex;
      gap: 5px;
    }

    .no-data {
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .no-data mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 20px;
    }

    .loading {
      text-align: center;
      padding: 40px;
    }

    @media (max-width: 768px) {
      .header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
      }

      .filters-form {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-actions {
        justify-content: center;
      }
    }
  `]
})
export class ProductListComponent implements OnInit {
  private fb = inject(FormBuilder);
  private apiService = inject(ApiService);

  filterForm: FormGroup;
  products: ProductWithStock[] = [];
  displayedColumns = ['image', 'product', 'price', 'stock', 'status', 'actions'];
  
  loading = false;
  totalProducts = 0;
  pageSize = 10;
  currentPage = 0;

  constructor() {
    this.filterForm = this.fb.group({
      search: ['']
    });
  }

  ngOnInit(): void {
    this.loadProducts();
  }

  loadProducts(): void {
    this.loading = true;
    
    const filters = this.filterForm.value;
    const params = {
      page: this.currentPage + 1,
      limit: this.pageSize,
      ...filters
    };

    this.apiService.getPaginated<ProductConnection>('/inventory/products', params).subscribe({
      next: (response) => {
        this.products = response.data.products;
        this.totalProducts = response.data.pagination.total;
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load products:', error);
        this.loading = false;
      }
    });
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 0;
    this.loadProducts();
  }

  onPageChange(event: any): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadProducts();
  }

  getStockStatusClass(stock: any): string {
    if (!stock) return 'stock-out';
    if (stock.quantity === 0) return 'stock-out';
    if (stock.quantity <= stock.minThreshold) return 'stock-low';
    return 'stock-normal';
  }

  getStockStatusText(stock: any): string {
    if (!stock) return 'No Stock Data';
    if (stock.quantity === 0) return 'Out of Stock';
    if (stock.quantity <= stock.minThreshold) return 'Low Stock';
    return 'In Stock';
  }

  deleteProduct(product: ProductWithStock): void {
    if (confirm(`Are you sure you want to delete "${product.name}"?`)) {
      this.apiService.delete(`/inventory/products/${product.id}`).subscribe({
        next: () => {
          this.loadProducts();
        },
        error: (error) => {
          console.error('Failed to delete product:', error);
        }
      });
    }
  }
}
