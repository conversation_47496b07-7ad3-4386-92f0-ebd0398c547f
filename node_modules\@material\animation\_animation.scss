//
// Copyright 2017 Google Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
//

$deceleration-curve-timing-function: cubic-bezier(0, 0, 0.2, 1) !default;
$standard-curve-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !default;
$acceleration-curve-timing-function: cubic-bezier(0.4, 0, 1, 1) !default;
$sharp-curve-timing-function: cubic-bezier(0.4, 0, 0.6, 1) !default;

@function enter($name, $duration, $delay: 0ms) {
  @return $name $duration $delay $deceleration-curve-timing-function;
}

@function exit-permanent($name, $duration, $delay: 0ms) {
  @return $name $duration $delay $acceleration-curve-timing-function;
}

@function exit-temporary($name, $duration, $delay: 0ms) {
  @return $name $duration $delay $sharp-curve-timing-function;
}

@function standard($name, $duration, $delay: 0ms) {
  @return $name $duration $delay $standard-curve-timing-function;
}

@function linear($name, $duration, $delay: 0ms) {
  @return $name $duration $delay linear;
}
