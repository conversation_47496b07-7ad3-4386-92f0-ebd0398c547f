<div class="app-container">
  <!-- Header -->
  <mat-toolbar color="primary" class="app-header">
    <button mat-icon-button (click)="toggleSidenav()" *ngIf="isAuthenticated">
      <mat-icon>menu</mat-icon>
    </button>
    <span class="app-title">Shop Management System</span>
    <span class="spacer"></span>
    <div *ngIf="isAuthenticated" class="user-info">
      <span>{{ currentUser?.username }}</span>
      <button mat-icon-button [matMenuTriggerFor]="userMenu">
        <mat-icon>account_circle</mat-icon>
      </button>
      <mat-menu #userMenu="matMenu">
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Logout</span>
        </button>
      </mat-menu>
    </div>
  </mat-toolbar>

  <!-- Main Content -->
  <div class="app-content" [class.with-sidenav]="isAuthenticated">
    <!-- Sidebar Navigation -->
    <mat-sidenav-container *ngIf="isAuthenticated" class="sidenav-container">
      <mat-sidenav #sidenav mode="side" opened="true" class="app-sidenav">
        <mat-nav-list>
          <!-- Dashboard -->
          <a mat-list-item routerLink="/dashboard" routerLinkActive="active">
            <mat-icon matListItemIcon>dashboard</mat-icon>
            <span matListItemTitle>Dashboard</span>
          </a>

          <!-- Inventory Management -->
          <div *ngIf="hasPermission('inventory:read')">
            <h3 matSubheader>Inventory</h3>
            <a mat-list-item routerLink="/inventory/products" routerLinkActive="active">
              <mat-icon matListItemIcon>inventory</mat-icon>
              <span matListItemTitle>Products</span>
            </a>
            <a mat-list-item routerLink="/inventory/categories" routerLinkActive="active">
              <mat-icon matListItemIcon>category</mat-icon>
              <span matListItemTitle>Categories</span>
            </a>
            <a mat-list-item routerLink="/inventory/stock" routerLinkActive="active">
              <mat-icon matListItemIcon>storage</mat-icon>
              <span matListItemTitle>Stock Levels</span>
            </a>
          </div>

          <!-- Sales -->
          <div *ngIf="hasPermission('sales:read')">
            <h3 matSubheader>Sales</h3>
            <a mat-list-item routerLink="/sales/pos" routerLinkActive="active">
              <mat-icon matListItemIcon>point_of_sale</mat-icon>
              <span matListItemTitle>Point of Sale</span>
            </a>
            <a mat-list-item routerLink="/sales/history" routerLinkActive="active">
              <mat-icon matListItemIcon>history</mat-icon>
              <span matListItemTitle>Sales History</span>
            </a>
          </div>

          <!-- Orders -->
          <div *ngIf="hasPermission('orders:read')">
            <h3 matSubheader>Orders</h3>
            <a mat-list-item routerLink="/orders" routerLinkActive="active">
              <mat-icon matListItemIcon>shopping_cart</mat-icon>
              <span matListItemTitle>Orders</span>
            </a>
          </div>

          <!-- Customers -->
          <div *ngIf="hasPermission('customers:read')">
            <h3 matSubheader>Customers</h3>
            <a mat-list-item routerLink="/customers" routerLinkActive="active">
              <mat-icon matListItemIcon>people</mat-icon>
              <span matListItemTitle>Customers</span>
            </a>
          </div>

          <!-- Reports -->
          <div *ngIf="hasPermission('reports:read')">
            <h3 matSubheader>Reports</h3>
            <a mat-list-item routerLink="/reports" routerLinkActive="active">
              <mat-icon matListItemIcon>analytics</mat-icon>
              <span matListItemTitle>Reports</span>
            </a>
          </div>

          <!-- Admin -->
          <div *ngIf="hasPermission('admin:read')">
            <h3 matSubheader>Administration</h3>
            <a mat-list-item routerLink="/admin/users" routerLinkActive="active">
              <mat-icon matListItemIcon>manage_accounts</mat-icon>
              <span matListItemTitle>User Management</span>
            </a>
            <a mat-list-item routerLink="/admin/settings" routerLinkActive="active">
              <mat-icon matListItemIcon>settings</mat-icon>
              <span matListItemTitle>Settings</span>
            </a>
          </div>
        </mat-nav-list>
      </mat-sidenav>

      <mat-sidenav-content class="main-content">
        <router-outlet></router-outlet>
      </mat-sidenav-content>
    </mat-sidenav-container>

    <!-- Login Page (when not authenticated) -->
    <div *ngIf="!isAuthenticated" class="login-container">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>

<!-- Loading Spinner -->
<div *ngIf="isLoading" class="loading-overlay">
  <mat-spinner></mat-spinner>
</div>

<!-- Notifications -->
<div class="notifications-container">
  <!-- Notifications will be handled by a separate service -->
</div>
