{"name": "@tufjs/models", "version": "2.0.1", "description": "TUF metadata models", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc --build", "clean": "rm -rf dist && rm tsconfig.tsbuildinfo", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/theupdateframework/tuf-js.git"}, "keywords": ["tuf", "security", "update"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/theupdateframework/tuf-js/issues"}, "homepage": "https://github.com/theupdateframework/tuf-js/tree/main/packages/models#readme", "dependencies": {"@tufjs/canonical-json": "2.0.0", "minimatch": "^9.0.4"}, "engines": {"node": "^16.14.0 || >=18.0.0"}}