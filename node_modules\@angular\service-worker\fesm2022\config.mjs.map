{"version": 3, "file": "config.mjs", "sources": ["../../../../../../packages/service-worker/config/src/duration.ts", "../../../../../../packages/service-worker/config/src/glob.ts", "../../../../../../packages/service-worker/config/src/generator.ts", "../../../../../../packages/service-worker/config/index.ts", "../../../../../../packages/service-worker/config/config.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst PARSE_TO_PAIRS = /([0-9]+[^0-9]+)/g;\nconst PAIR_SPLIT = /^([0-9]+)([dhmsu]+)$/;\n\nexport function parseDurationToMs(duration: string): number {\n  const matches: string[] = [];\n\n  let array: RegExpExecArray | null;\n  while ((array = PARSE_TO_PAIRS.exec(duration)) !== null) {\n    matches.push(array[0]);\n  }\n  return matches\n    .map((match) => {\n      const res = PAIR_SPLIT.exec(match);\n      if (res === null) {\n        throw new Error(`Not a valid duration: ${match}`);\n      }\n      let factor: number = 0;\n      switch (res[2]) {\n        case 'd':\n          factor = 86400000;\n          break;\n        case 'h':\n          factor = 3600000;\n          break;\n        case 'm':\n          factor = 60000;\n          break;\n        case 's':\n          factor = 1000;\n          break;\n        case 'u':\n          factor = 1;\n          break;\n        default:\n          throw new Error(`Not a valid duration unit: ${res[2]}`);\n      }\n      return parseInt(res[1]) * factor;\n    })\n    .reduce((total, value) => total + value, 0);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nconst QUESTION_MARK = '[^/]';\nconst WILD_SINGLE = '[^/]*';\nconst WILD_OPEN = '(?:.+\\\\/)?';\n\nconst TO_ESCAPE_BASE = [\n  {replace: /\\./g, with: '\\\\.'},\n  {replace: /\\+/g, with: '\\\\+'},\n  {replace: /\\*/g, with: WILD_SINGLE},\n];\nconst TO_ESCAPE_WILDCARD_QM = [...TO_ESCAPE_BASE, {replace: /\\?/g, with: QUESTION_MARK}];\nconst TO_ESCAPE_LITERAL_QM = [...TO_ESCAPE_BASE, {replace: /\\?/g, with: '\\\\?'}];\n\nexport function globToRegex(glob: string, literalQuestionMark = false): string {\n  const toEscape = literalQuestionMark ? TO_ESCAPE_LITERAL_QM : TO_ESCAPE_WILDCARD_QM;\n  const segments = glob.split('/').reverse();\n  let regex: string = '';\n  while (segments.length > 0) {\n    const segment = segments.pop()!;\n    if (segment === '**') {\n      if (segments.length > 0) {\n        regex += WILD_OPEN;\n      } else {\n        regex += '.*';\n      }\n    } else {\n      const processed = toEscape.reduce(\n        (segment, escape) => segment.replace(escape.replace, escape.with),\n        segment,\n      );\n      regex += processed;\n      if (segments.length > 0) {\n        regex += '\\\\/';\n      }\n    }\n  }\n  return regex;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {parseDurationToMs} from './duration';\nimport {Filesystem} from './filesystem';\nimport {globToRegex} from './glob';\nimport {AssetGroup, Config} from './in';\n\nconst DEFAULT_NAVIGATION_URLS = [\n  '/**', // Include all URLs.\n  '!/**/*.*', // Exclude URLs to files (containing a file extension in the last segment).\n  '!/**/*__*', // Exclude URLs containing `__` in the last segment.\n  '!/**/*__*/**', // Exclude URLs containing `__` in any other segment.\n];\n\n/**\n * Consumes service worker configuration files and processes them into control files.\n *\n * @publicApi\n */\nexport class Generator {\n  constructor(\n    readonly fs: Filesystem,\n    private baseHref: string,\n  ) {}\n\n  async process(config: Config): Promise<Object> {\n    const unorderedHashTable = {};\n    const assetGroups = await this.processAssetGroups(config, unorderedHashTable);\n\n    return {\n      configVersion: 1,\n      timestamp: Date.now(),\n      appData: config.appData,\n      index: joinUrls(this.baseHref, config.index),\n      assetGroups,\n      dataGroups: this.processDataGroups(config),\n      hashTable: withOrderedKeys(unorderedHashTable),\n      navigationUrls: processNavigationUrls(this.baseHref, config.navigationUrls),\n      navigationRequestStrategy: config.navigationRequestStrategy ?? 'performance',\n    };\n  }\n\n  private async processAssetGroups(\n    config: Config,\n    hashTable: {[file: string]: string | undefined},\n  ): Promise<Object[]> {\n    // Retrieve all files of the build.\n    const allFiles = await this.fs.list('/');\n    const seenMap = new Set<string>();\n    const filesPerGroup = new Map<AssetGroup, string[]>();\n\n    // Computed which files belong to each asset-group.\n    for (const group of config.assetGroups || []) {\n      if ((group.resources as any).versionedFiles) {\n        throw new Error(\n          `Asset-group '${group.name}' in 'ngsw-config.json' uses the 'versionedFiles' option, ` +\n            \"which is no longer supported. Use 'files' instead.\",\n        );\n      }\n\n      const fileMatcher = globListToMatcher(group.resources.files || []);\n      const matchedFiles = allFiles\n        .filter(fileMatcher)\n        .filter((file) => !seenMap.has(file))\n        .sort();\n\n      matchedFiles.forEach((file) => seenMap.add(file));\n      filesPerGroup.set(group, matchedFiles);\n    }\n\n    // Compute hashes for all matched files and add them to the hash-table.\n    const allMatchedFiles = ([] as string[]).concat(...Array.from(filesPerGroup.values())).sort();\n    const allMatchedHashes = await processInBatches(allMatchedFiles, 500, (file) =>\n      this.fs.hash(file),\n    );\n    allMatchedFiles.forEach((file, idx) => {\n      hashTable[joinUrls(this.baseHref, file)] = allMatchedHashes[idx];\n    });\n\n    // Generate and return the processed asset-groups.\n    return Array.from(filesPerGroup.entries()).map(([group, matchedFiles]) => ({\n      name: group.name,\n      installMode: group.installMode || 'prefetch',\n      updateMode: group.updateMode || group.installMode || 'prefetch',\n      cacheQueryOptions: buildCacheQueryOptions(group.cacheQueryOptions),\n      urls: matchedFiles.map((url) => joinUrls(this.baseHref, url)),\n      patterns: (group.resources.urls || []).map((url) => urlToRegex(url, this.baseHref, true)),\n    }));\n  }\n\n  private processDataGroups(config: Config): Object[] {\n    return (config.dataGroups || []).map((group) => {\n      return {\n        name: group.name,\n        patterns: group.urls.map((url) => urlToRegex(url, this.baseHref, true)),\n        strategy: group.cacheConfig.strategy || 'performance',\n        maxSize: group.cacheConfig.maxSize,\n        maxAge: parseDurationToMs(group.cacheConfig.maxAge),\n        timeoutMs: group.cacheConfig.timeout && parseDurationToMs(group.cacheConfig.timeout),\n        cacheOpaqueResponses: group.cacheConfig.cacheOpaqueResponses,\n        cacheQueryOptions: buildCacheQueryOptions(group.cacheQueryOptions),\n        version: group.version !== undefined ? group.version : 1,\n      };\n    });\n  }\n}\n\nexport function processNavigationUrls(\n  baseHref: string,\n  urls = DEFAULT_NAVIGATION_URLS,\n): {positive: boolean; regex: string}[] {\n  return urls.map((url) => {\n    const positive = !url.startsWith('!');\n    url = positive ? url : url.slice(1);\n    return {positive, regex: `^${urlToRegex(url, baseHref)}$`};\n  });\n}\n\nasync function processInBatches<I, O>(\n  items: I[],\n  batchSize: number,\n  processFn: (item: I) => O | Promise<O>,\n): Promise<O[]> {\n  const batches = [];\n\n  for (let i = 0; i < items.length; i += batchSize) {\n    batches.push(items.slice(i, i + batchSize));\n  }\n\n  return batches.reduce(\n    async (prev, batch) =>\n      (await prev).concat(await Promise.all(batch.map((item) => processFn(item)))),\n    Promise.resolve<O[]>([]),\n  );\n}\n\nfunction globListToMatcher(globs: string[]): (file: string) => boolean {\n  const patterns = globs.map((pattern) => {\n    if (pattern.startsWith('!')) {\n      return {\n        positive: false,\n        regex: new RegExp('^' + globToRegex(pattern.slice(1)) + '$'),\n      };\n    } else {\n      return {\n        positive: true,\n        regex: new RegExp('^' + globToRegex(pattern) + '$'),\n      };\n    }\n  });\n  return (file: string) => matches(file, patterns);\n}\n\nfunction matches(file: string, patterns: {positive: boolean; regex: RegExp}[]): boolean {\n  return patterns.reduce((isMatch, pattern) => {\n    if (pattern.positive) {\n      return isMatch || pattern.regex.test(file);\n    } else {\n      return isMatch && !pattern.regex.test(file);\n    }\n  }, false);\n}\n\nfunction urlToRegex(url: string, baseHref: string, literalQuestionMark?: boolean): string {\n  if (!url.startsWith('/') && url.indexOf('://') === -1) {\n    // Prefix relative URLs with `baseHref`.\n    // Strip a leading `.` from a relative `baseHref` (e.g. `./foo/`), since it would result in an\n    // incorrect regex (matching a literal `.`).\n    url = joinUrls(baseHref.replace(/^\\.(?=\\/)/, ''), url);\n  }\n\n  return globToRegex(url, literalQuestionMark);\n}\n\nfunction joinUrls(a: string, b: string): string {\n  if (a.endsWith('/') && b.startsWith('/')) {\n    return a + b.slice(1);\n  } else if (!a.endsWith('/') && !b.startsWith('/')) {\n    return a + '/' + b;\n  }\n  return a + b;\n}\n\nfunction withOrderedKeys<T extends {[key: string]: any}>(unorderedObj: T): T {\n  const orderedObj = {} as {[key: string]: any};\n  Object.keys(unorderedObj)\n    .sort()\n    .forEach((key) => (orderedObj[key] = unorderedObj[key]));\n  return orderedObj as T;\n}\n\nfunction buildCacheQueryOptions(\n  inOptions?: Pick<CacheQueryOptions, 'ignoreSearch'>,\n): CacheQueryOptions {\n  return {\n    ignoreVary: true,\n    ...inOptions,\n  };\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": [], "mappings": ";;;;;;AAQA,MAAM,cAAc,GAAG,kBAAkB,CAAC;AAC1C,MAAM,UAAU,GAAG,sBAAsB,CAAC;AAEpC,SAAU,iBAAiB,CAAC,QAAgB,EAAA;IAChD,MAAM,OAAO,GAAa,EAAE,CAAC;AAE7B,IAAA,IAAI,KAA6B,CAAC;AAClC,IAAA,OAAO,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,EAAE;QACvD,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KACxB;AACD,IAAA,OAAO,OAAO;AACX,SAAA,GAAG,CAAC,CAAC,KAAK,KAAI;QACb,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnC,QAAA,IAAI,GAAG,KAAK,IAAI,EAAE;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,CAAA,CAAE,CAAC,CAAC;SACnD;QACD,IAAI,MAAM,GAAW,CAAC,CAAC;AACvB,QAAA,QAAQ,GAAG,CAAC,CAAC,CAAC;AACZ,YAAA,KAAK,GAAG;gBACN,MAAM,GAAG,QAAQ,CAAC;gBAClB,MAAM;AACR,YAAA,KAAK,GAAG;gBACN,MAAM,GAAG,OAAO,CAAC;gBACjB,MAAM;AACR,YAAA,KAAK,GAAG;gBACN,MAAM,GAAG,KAAK,CAAC;gBACf,MAAM;AACR,YAAA,KAAK,GAAG;gBACN,MAAM,GAAG,IAAI,CAAC;gBACd,MAAM;AACR,YAAA,KAAK,GAAG;gBACN,MAAM,GAAG,CAAC,CAAC;gBACX,MAAM;AACR,YAAA;gBACE,MAAM,IAAI,KAAK,CAAC,CAA8B,2BAAA,EAAA,GAAG,CAAC,CAAC,CAAC,CAAE,CAAA,CAAC,CAAC;SAC3D;QACD,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AACnC,KAAC,CAAC;AACD,SAAA,MAAM,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC;AAChD;;ACvCA,MAAM,aAAa,GAAG,MAAM,CAAC;AAC7B,MAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,MAAM,SAAS,GAAG,YAAY,CAAC;AAE/B,MAAM,cAAc,GAAG;AACrB,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC;AAC7B,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC;AAC7B,IAAA,EAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAC;CACpC,CAAC;AACF,MAAM,qBAAqB,GAAG,CAAC,GAAG,cAAc,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAC,CAAC,CAAC;AACzF,MAAM,oBAAoB,GAAG,CAAC,GAAG,cAAc,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAC,CAAC,CAAC;SAEhE,WAAW,CAAC,IAAY,EAAE,mBAAmB,GAAG,KAAK,EAAA;IACnE,MAAM,QAAQ,GAAG,mBAAmB,GAAG,oBAAoB,GAAG,qBAAqB,CAAC;IACpF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;IAC3C,IAAI,KAAK,GAAW,EAAE,CAAC;AACvB,IAAA,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,QAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,EAAG,CAAC;AAChC,QAAA,IAAI,OAAO,KAAK,IAAI,EAAE;AACpB,YAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,KAAK,IAAI,SAAS,CAAC;aACpB;iBAAM;gBACL,KAAK,IAAI,IAAI,CAAC;aACf;SACF;aAAM;AACL,YAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,CAC/B,CAAC,OAAO,EAAE,MAAM,KAAK,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,EACjE,OAAO,CACR,CAAC;YACF,KAAK,IAAI,SAAS,CAAC;AACnB,YAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvB,KAAK,IAAI,KAAK,CAAC;aAChB;SACF;KACF;AACD,IAAA,OAAO,KAAK,CAAC;AACf;;AC/BA,MAAM,uBAAuB,GAAG;AAC9B,IAAA,KAAK;AACL,IAAA,UAAU;AACV,IAAA,WAAW;AACX,IAAA,cAAc;CACf,CAAC;AAEF;;;;AAIG;MACU,SAAS,CAAA;IACpB,WACW,CAAA,EAAc,EACf,QAAgB,EAAA;QADf,IAAE,CAAA,EAAA,GAAF,EAAE,CAAY;QACf,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;KACtB;IAEJ,MAAM,OAAO,CAAC,MAAc,EAAA;QAC1B,MAAM,kBAAkB,GAAG,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAE9E,OAAO;AACL,YAAA,aAAa,EAAE,CAAC;AAChB,YAAA,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC;YAC5C,WAAW;AACX,YAAA,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;AAC1C,YAAA,SAAS,EAAE,eAAe,CAAC,kBAAkB,CAAC;YAC9C,cAAc,EAAE,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,cAAc,CAAC;AAC3E,YAAA,yBAAyB,EAAE,MAAM,CAAC,yBAAyB,IAAI,aAAa;SAC7E,CAAC;KACH;AAEO,IAAA,MAAM,kBAAkB,CAC9B,MAAc,EACd,SAA+C,EAAA;;QAG/C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzC,QAAA,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;AAClC,QAAA,MAAM,aAAa,GAAG,IAAI,GAAG,EAAwB,CAAC;;QAGtD,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,WAAW,IAAI,EAAE,EAAE;AAC5C,YAAA,IAAK,KAAK,CAAC,SAAiB,CAAC,cAAc,EAAE;AAC3C,gBAAA,MAAM,IAAI,KAAK,CACb,gBAAgB,KAAK,CAAC,IAAI,CAA4D,0DAAA,CAAA;AACpF,oBAAA,oDAAoD,CACvD,CAAC;aACH;AAED,YAAA,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YACnE,MAAM,YAAY,GAAG,QAAQ;iBAC1B,MAAM,CAAC,WAAW,CAAC;AACnB,iBAAA,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACpC,iBAAA,IAAI,EAAE,CAAC;AAEV,YAAA,YAAY,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAClD,YAAA,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;SACxC;;QAGD,MAAM,eAAe,GAAI,EAAe,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9F,MAAM,gBAAgB,GAAG,MAAM,gBAAgB,CAAC,eAAe,EAAE,GAAG,EAAE,CAAC,IAAI,KACzE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CACnB,CAAC;QACF,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,GAAG,KAAI;AACpC,YAAA,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACnE,SAAC,CAAC,CAAC;;QAGH,OAAO,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,YAAY,CAAC,MAAM;YACzE,IAAI,EAAE,KAAK,CAAC,IAAI;AAChB,YAAA,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,UAAU;YAC5C,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,IAAI,UAAU;AAC/D,YAAA,iBAAiB,EAAE,sBAAsB,CAAC,KAAK,CAAC,iBAAiB,CAAC;AAClE,YAAA,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;AAC7D,YAAA,QAAQ,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC1F,SAAA,CAAC,CAAC,CAAC;KACL;AAEO,IAAA,iBAAiB,CAAC,MAAc,EAAA;AACtC,QAAA,OAAO,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,KAAK,KAAI;YAC7C,OAAO;gBACL,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACvE,gBAAA,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,QAAQ,IAAI,aAAa;AACrD,gBAAA,OAAO,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO;gBAClC,MAAM,EAAE,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;AACnD,gBAAA,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO,IAAI,iBAAiB,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;AACpF,gBAAA,oBAAoB,EAAE,KAAK,CAAC,WAAW,CAAC,oBAAoB;AAC5D,gBAAA,iBAAiB,EAAE,sBAAsB,CAAC,KAAK,CAAC,iBAAiB,CAAC;AAClE,gBAAA,OAAO,EAAE,KAAK,CAAC,OAAO,KAAK,SAAS,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC;aACzD,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AACF,CAAA;SAEe,qBAAqB,CACnC,QAAgB,EAChB,IAAI,GAAG,uBAAuB,EAAA;AAE9B,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAI;QACtB,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACtC,QAAA,GAAG,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACpC,QAAA,OAAO,EAAC,QAAQ,EAAE,KAAK,EAAE,CAAI,CAAA,EAAA,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA,CAAA,CAAG,EAAC,CAAC;AAC7D,KAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,gBAAgB,CAC7B,KAAU,EACV,SAAiB,EACjB,SAAsC,EAAA;IAEtC,MAAM,OAAO,GAAG,EAAE,CAAC;AAEnB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE;AAChD,QAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;KAC7C;IAED,OAAO,OAAO,CAAC,MAAM,CACnB,OAAO,IAAI,EAAE,KAAK,KAChB,CAAC,MAAM,IAAI,EAAE,MAAM,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC9E,OAAO,CAAC,OAAO,CAAM,EAAE,CAAC,CACzB,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,KAAe,EAAA;IACxC,MAAM,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,KAAI;AACrC,QAAA,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAC3B,OAAO;AACL,gBAAA,QAAQ,EAAE,KAAK;AACf,gBAAA,KAAK,EAAE,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;aAC7D,CAAC;SACH;aAAM;YACL,OAAO;AACL,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,KAAK,EAAE,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;aACpD,CAAC;SACH;AACH,KAAC,CAAC,CAAC;IACH,OAAO,CAAC,IAAY,KAAK,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AACnD,CAAC;AAED,SAAS,OAAO,CAAC,IAAY,EAAE,QAA8C,EAAA;IAC3E,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,OAAO,KAAI;AAC1C,QAAA,IAAI,OAAO,CAAC,QAAQ,EAAE;YACpB,OAAO,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC5C;aAAM;YACL,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7C;KACF,EAAE,KAAK,CAAC,CAAC;AACZ,CAAC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,QAAgB,EAAE,mBAA6B,EAAA;AAC9E,IAAA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;;;;AAIrD,QAAA,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;KACxD;AAED,IAAA,OAAO,WAAW,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,QAAQ,CAAC,CAAS,EAAE,CAAS,EAAA;AACpC,IAAA,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACxC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACvB;AAAM,SAAA,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACjD,QAAA,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;KACpB;IACD,OAAO,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CAAiC,YAAe,EAAA;IACtE,MAAM,UAAU,GAAG,EAA0B,CAAC;AAC9C,IAAA,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;AACtB,SAAA,IAAI,EAAE;AACN,SAAA,OAAO,CAAC,CAAC,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAA,OAAO,UAAe,CAAC;AACzB,CAAC;AAED,SAAS,sBAAsB,CAC7B,SAAmD,EAAA;IAEnD,OAAO;AACL,QAAA,UAAU,EAAE,IAAI;AAChB,QAAA,GAAG,SAAS;KACb,CAAC;AACJ;;ACpMA;;ACRA;;AAEG;;;;"}