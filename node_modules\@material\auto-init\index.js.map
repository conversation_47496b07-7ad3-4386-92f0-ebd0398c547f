{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;GAqBG;;AAOH,OAAO,EAAC,OAAO,EAAC,MAAM,aAAa,CAAC;AAE7B,IAAA,cAAc,GAAgD,OAAO,eAAvD,EAAE,uBAAuB,GAAuB,OAAO,wBAA9B,EAAE,iBAAiB,GAAI,OAAO,kBAAX,CAAY;AAW7E,IAAM,QAAQ,GAA8B,EAAE,CAAC;AAE/C,sCAAsC;AACtC,IAAM,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAEhD,SAAS,IAAI,CACT,SAAiB,EAAE,SAAY,EAAE,YAAoB;IAApB,6BAAA,EAAA,oBAAoB;IACvD,IAAI,KAAK,CAAC;IACV,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;QACrC,KAAK,GAAG,IAAI,WAAW,CAAI,SAAS,EAAE;YACpC,OAAO,EAAE,YAAY;YACrB,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;KACJ;SAAM;QACL,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAC5C,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;KAClE;IAED,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED,uEAAuE;AACvE;;GAEG;AACH,SAAS,WAAW,CAAC,IAA2B;;IAA3B,qBAAA,EAAA,eAA2B;IAC9C,IAAM,UAAU,GAAG,EAAE,CAAC;IACtB,IAAI,KAAK,GACL,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAc,MAAI,cAAc,MAAG,CAAC,CAAC,CAAC;IAC1E,KAAK,GAAG,KAAK,CAAC,MAAM,CAChB,UAAC,IAAI,IAAK,OAAA,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,iBAAiB,EAA3D,CAA2D,CAAC,CAAC;;QAE3E,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;YAArB,IAAM,IAAI,kBAAA;YACb,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;aACpE;YAED,+CAA+C;YAC/C,IAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACvC,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;gBACrC,MAAM,IAAI,KAAK,CACX,gEACI,QAAU,CAAC,CAAC;aACrB;YAED,uEAAuE;YACvE,0EAA0E;YAC1E,oCAAoC;YACpC,IAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE;gBACpC,YAAY,EAAE,IAAI;gBAClB,UAAU,EAAE,KAAK;gBACjB,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC;YACH,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,GAAG,iBAAiB,CAAC;SAC3D;;;;;;;;;IAED,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IAC5B,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,0EAA0E;AAC1E,sCAAsC;AACtC,WAAW,CAAC,QAAQ,GAAG,UACnB,aAAqB;AACrB,+CAA+C;AAC/C,WAA0B,EAAE,IAAmB;IAAnB,qBAAA,EAAA,mBAAmB;IACjD,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;QACrC,MAAM,IAAI,KAAK,CAAC,gDACZ,WAAW,yBAAsB,CAAC,CAAC;KACxC;IACD,IAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC;IAC9C,IAAI,aAAa,EAAE;QACjB,IAAI,CAAC,iDAA+C,aAAa,cAC7D,WAAW,eAAU,aAAe,CAAC,CAAC;KAC3C;IACD,QAAQ,CAAC,aAAa,CAAC,GAAG,WAAW,CAAC;AACxC,CAAC,CAAC;AAEF,WAAW,CAAC,UAAU,GAAG,UAAS,aAAqB;IACrD,OAAO,QAAQ,CAAC,aAAa,CAAC,CAAC;AACjC,CAAC,CAAC;AAEF,kBAAkB;AAClB,WAAW,CAAC,aAAa,GAAG;;;QAC1B,KAA4B,IAAA,KAAA,SAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA,gBAAA,4BAAE;YAA9C,IAAM,aAAa,WAAA;YACtB,WAAW,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;SACvC;;;;;;;;;AACH,CAAC,CAAC;AAEF,iHAAiH;AACjH,eAAe,WAAW,CAAC;AAC3B,OAAO,EAAC,WAAW,EAAC,CAAC"}