"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.importsProvidersFrom = exports.findBootstrapApplicationCall = exports.callsProvidersFunction = exports.addFunctionalProvidersToStandaloneBootstrap = exports.addModuleImportToStandaloneBootstrap = void 0;
var standalone_1 = require("./standalone");
Object.defineProperty(exports, "addModuleImportToStandaloneBootstrap", { enumerable: true, get: function () { return standalone_1.addModuleImportToStandaloneBootstrap; } });
Object.defineProperty(exports, "addFunctionalProvidersToStandaloneBootstrap", { enumerable: true, get: function () { return standalone_1.addFunctionalProvidersToStandaloneBootstrap; } });
Object.defineProperty(exports, "callsProvidersFunction", { enumerable: true, get: function () { return standalone_1.callsProvidersFunction; } });
Object.defineProperty(exports, "findBootstrapApplicationCall", { enumerable: true, get: function () { return standalone_1.findBootstrapApplicationCall; } });
Object.defineProperty(exports, "importsProvidersFrom", { enumerable: true, get: function () { return standalone_1.importsProvidersFrom; } });
