/**
 * MIT License
 * 
 * Copyright (c) 2014-present, <PERSON> and other contributors.
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Immutable={})}(this,function(t){"use strict";var e="delete",d=5,l=1<<d,g=l-1,v={};function u(){return{value:!1}}function _(t){t&&(t.value=!0)}function m(){}function c(t){return void 0===t.size&&(t.size=t.__iterate(r)),t.size}function h(t,e){if("number"!=typeof e){var r=e>>>0;if(""+r!==e||4294967295==r)return NaN;e=r}return e<0?c(t)+e:e}function r(){return!0}function p(t,e,r){return(0===t&&!i(t)||void 0!==r&&t<=-r)&&(void 0===e||void 0!==r&&r<=e)}function y(t,e){return n(t,e,0)}function w(t,e){return n(t,e,e)}function n(t,e,r){return void 0===t?r:i(t)?e===1/0?e:0|Math.max(0,e+t):void 0===e||e===t?t:0|Math.min(e,t)}function i(t){return t<0||0===t&&1/t==-1/0}var o="@@__IMMUTABLE_ITERABLE__@@";function f(t){return!(!t||!t[o])}var s="@@__IMMUTABLE_KEYED__@@";function a(t){return!(!t||!t[s])}var S="@@__IMMUTABLE_INDEXED__@@";function z(t){return!(!t||!t[S])}function b(t){return a(t)||z(t)}function I(t){return f(t)?t:F(t)}var O=function(t){function e(t){return a(t)?t:G(t)}return e.__proto__=t,(e.prototype=Object.create(t.prototype)).constructor=e}(I),E=function(t){function e(t){return z(t)?t:Z(t)}return e.__proto__=t,(e.prototype=Object.create(t.prototype)).constructor=e}(I),j=function(t){function e(t){return f(t)&&!b(t)?t:$(t)}return e.__proto__=t,(e.prototype=Object.create(t.prototype)).constructor=e}(I);I.Keyed=O,I.Indexed=E,I.Set=j;var q="@@__IMMUTABLE_SEQ__@@";function M(t){return!(!t||!t[q])}var D="@@__IMMUTABLE_RECORD__@@";function x(t){return!(!t||!t[D])}function A(t){return f(t)||x(t)}var k="@@__IMMUTABLE_ORDERED__@@";function R(t){return!(!t||!t[k])}var U=0,T=1,K=2,L="function"==typeof Symbol&&Symbol.iterator,C="@@iterator",B=L||C,P=function(t){this.next=t};function W(t,e,r,n){r=0===t?e:1===t?r:[e,r];return n?n.value=r:n={value:r,done:!1},n}function N(){return{value:void 0,done:!0}}function H(t){return Array.isArray(t
)||Y(t)}function J(t){return t&&"function"==typeof t.next}function V(t){var e=Y(t);return e&&e.call(t)}function Y(t){t=t&&(L&&t[L]||t[C]);if("function"==typeof t)return t}P.prototype.toString=function(){return"[Iterator]"},P.KEYS=U,P.VALUES=T,P.ENTRIES=K,P.prototype.inspect=P.prototype.toSource=function(){return""+this},P.prototype[B]=function(){return this};var Q=Object.prototype.hasOwnProperty;function X(t){return Array.isArray(t)||"string"==typeof t||t&&"object"==typeof t&&Number.isInteger(t.length)&&0<=t.length&&(0===t.length?1===Object.keys(t).length:t.hasOwnProperty(t.length-1))}var F=function(t){function e(t){return null==t?it():A(t)?t.toSeq():function(t){var e=st(t);if(e)return function(t){var e=Y(t);return e&&e===t.entries}(t)?e.fromEntrySeq():function(t){var e=Y(t);return e&&e===t.keys}(t)?e.toSetSeq():e;if("object"!=typeof t)throw new TypeError("Expected Array or collection object of values, or keyed object: "+t);return new et(t)}(t)}return e.__proto__=t,((e.prototype=Object.create(t.prototype)).constructor=e).prototype.toSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq {","}")},e.prototype.cacheResult=function(){return!this._cache&&this.__iterateUncached&&(this._cache=this.entrySeq().toArray(),this.size=this._cache.length),this},e.prototype.__iterate=function(t,e){var r=this._cache;if(r){for(var n=r.length,i=0;i!==n;){var o=r[e?n-++i:i++];if(!1===t(o[1],o[0],this))break}return i}return this.__iterateUncached(t,e)},e.prototype.__iterator=function(e,r){var n=this._cache;if(n){var i=n.length,o=0;return new P(function(){if(o===i)return N();var t=n[r?i-++o:o++];return W(e,t[0],t[1])})}return this.__iteratorUncached(e,r)},e}(I),G=function(t){function e(t){return null==t?it().toKeyedSeq():f(t)?a(t)?t.toSeq():t.fromEntrySeq():x(t)?t.toSeq():ot(t)}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.toKeyedSeq=function(){return this},e}(F),Z=function(t){function e(t){return null==t?it():f(t)?a(t)?t.entrySeq():t.toIndexedSeq():x(t
)?t.toSeq().entrySeq():ut(t)}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).of=function(){return e(arguments)},e.prototype.toIndexedSeq=function(){return this},e.prototype.toString=function(){return this.__toString("Seq [","]")},e}(F),$=function(t){function e(t){return(f(t)&&!b(t)?t:Z(t)).toSetSeq()}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).of=function(){return e(arguments)},e.prototype.toSetSeq=function(){return this},e}(F);F.isSeq=M,F.Keyed=G,F.Set=$,F.Indexed=Z,F.prototype[q]=!0;var tt=function(t){function e(t){this._array=t,this.size=t.length}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.get=function(t,e){return this.has(t)?this._array[h(this,t)]:e},e.prototype.__iterate=function(t,e){for(var r=this._array,n=r.length,i=0;i!==n;){var o=e?n-++i:i++;if(!1===t(r[o],o,this))break}return i},e.prototype.__iterator=function(e,r){var n=this._array,i=n.length,o=0;return new P(function(){if(o===i)return N();var t=r?i-++o:o++;return W(e,t,n[t])})},e}(Z),et=function(t){function e(t){var e=Object.keys(t).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(t):[]);this._object=t,this._keys=e,this.size=e.length}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.get=function(t,e){return void 0===e||this.has(t)?this._object[t]:e},e.prototype.has=function(t){return Q.call(this._object,t)},e.prototype.__iterate=function(t,e){for(var r=this._object,n=this._keys,i=n.length,o=0;o!==i;){var u=n[e?i-++o:o++];if(!1===t(r[u],u,this))break}return o},e.prototype.__iterator=function(e,r){var n=this._object,i=this._keys,o=i.length,u=0;return new P(function(){if(u===o)return N();var t=i[r?o-++u:u++];return W(e,t,n[t])})},e}(G);et.prototype[k]=!0;var rt,nt=function(t){function e(t){this._collection=t,this.size=t.length||t.size}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.__iterateUncached=function(t,e){
if(e)return this.cacheResult().__iterate(t,e);var r,n=V(this._collection),i=0;if(J(n))for(;!(r=n.next()).done&&!1!==t(r.value,i++,this););return i},e.prototype.__iteratorUncached=function(e,t){if(t)return this.cacheResult().__iterator(e,t);var r=V(this._collection);if(!J(r))return new P(N);var n=0;return new P(function(){var t=r.next();return t.done?t:W(e,n++,t.value)})},e}(Z);function it(){return rt=rt||new tt([])}function ot(t){var e=st(t);if(e)return e.fromEntrySeq();if("object"==typeof t)return new et(t);throw new TypeError("Expected Array or collection object of [k, v] entries, or keyed object: "+t)}function ut(t){var e=st(t);if(e)return e;throw new TypeError("Expected Array or collection object of values: "+t)}function st(t){return X(t)?new tt(t):H(t)?new nt(t):void 0}var at="@@__IMMUTABLE_MAP__@@";function ct(t){return!(!t||!t[at])}function ft(t){return ct(t)&&R(t)}function ht(t){return!(!t||"function"!=typeof t.equals||"function"!=typeof t.hashCode)}function _t(t,e){if(t===e||t!=t&&e!=e)return!0;if(!t||!e)return!1;if("function"==typeof t.valueOf&&"function"==typeof e.valueOf){if((t=t.valueOf())===(e=e.valueOf())||t!=t&&e!=e)return!0;if(!t||!e)return!1}return!!(ht(t)&&ht(e)&&t.equals(e))}var pt="function"==typeof Math.imul&&-2==Math.imul(4294967295,2)?Math.imul:function(t,e){var r=65535&(t|=0),n=65535&(e|=0);return r*n+((t>>>16)*n+r*(e>>>16)<<16>>>0)|0};function lt(t){return t>>>1&1073741824|3221225471&t}var vt=Object.prototype.valueOf;function yt(t){if(null==t)return dt(t);if("function"==typeof t.hashCode)return lt(t.hashCode(t));var e,r=(e=t).valueOf!==vt&&"function"==typeof e.valueOf?e.valueOf(e):e;if(null==r)return dt(r);switch(typeof r){case"boolean":return r?1108378657:1108378656;case"number":return function(t){if(t!=t||t===1/0)return 0;var e=0|t;e!==t&&(e^=4294967295*t);for(;4294967295<t;)e^=t/=4294967295;return lt(e)}(r);case"string":return(jt<r.length?function(t){var e=Dt[t];void 0===e&&(e=gt(t),Mt===qt&&(Mt=0,Dt={}),Mt++,Dt[t]=e);return e}:gt)(r);case"object":case"function":return function(t){
var e;if(bt&&void 0!==(e=zt.get(t)))return e;if(void 0!==(e=t[Et]))return e;if(!wt){if(void 0!==(e=t.propertyIsEnumerable&&t.propertyIsEnumerable[Et]))return e;if(void 0!==(e=function(t){if(t&&0<t.nodeType)switch(t.nodeType){case 1:return t.uniqueID;case 9:return t.documentElement&&t.documentElement.uniqueID}}(t)))return e}if(e=St(),bt)zt.set(t,e);else{if(void 0!==mt&&!1===mt(t))throw Error("Non-extensible objects are not allowed as keys.");if(wt)Object.defineProperty(t,Et,{enumerable:!1,configurable:!1,writable:!1,value:e});else if(void 0!==t.propertyIsEnumerable&&t.propertyIsEnumerable===t.constructor.prototype.propertyIsEnumerable)t.propertyIsEnumerable=function(){return this.constructor.prototype.propertyIsEnumerable.apply(this,arguments)},t.propertyIsEnumerable[Et]=e;else{if(void 0===t.nodeType)throw Error("Unable to set a non-enumerable property on object.");t[Et]=e}}return e}(r);case"symbol":return void 0===(e=It[t=r])?(e=St(),It[t]=e):e;default:if("function"==typeof r.toString)return gt(""+r);throw Error("Value type "+typeof r+" cannot be hashed.")}}function dt(t){return null===t?1108378658:1108378659}function gt(t){for(var e=0,r=0;r<t.length;r++)e=31*e+t.charCodeAt(r)|0;return lt(e)}var mt=Object.isExtensible,wt=function(){try{return Object.defineProperty({},"@",{}),!0}catch(t){return!1}}();function St(){var t=++Ot;return 1073741824&Ot&&(Ot=0),t}var zt,bt="function"==typeof WeakMap;bt&&(zt=new WeakMap);var It=Object.create(null),Ot=0,Et="__immutablehash__";"function"==typeof Symbol&&(Et=Symbol(Et));var jt=16,qt=255,Mt=0,Dt={},xt=function(t){function e(t,e){this._iter=t,this._useKeys=e,this.size=t.size}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.get=function(t,e){return this._iter.get(t,e)},e.prototype.has=function(t){return this._iter.has(t)},e.prototype.valueSeq=function(){return this._iter.valueSeq()},e.prototype.reverse=function(){var t=this,e=Kt(this,!0);return this._useKeys||(e.valueSeq=function(){return t._iter.toSeq().reverse()}),e},
e.prototype.map=function(t,e){var r=this,n=Tt(this,t,e);return this._useKeys||(n.valueSeq=function(){return r._iter.toSeq().map(t,e)}),n},e.prototype.__iterate=function(r,t){var n=this;return this._iter.__iterate(function(t,e){return r(t,e,n)},t)},e.prototype.__iterator=function(t,e){return this._iter.__iterator(t,e)},e}(G);xt.prototype[k]=!0;var At=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.includes=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(e,r){var n=this,i=0;return r&&c(this),this._iter.__iterate(function(t){return e(t,r?n.size-++i:i++,n)},r)},e.prototype.__iterator=function(e,r){var n=this,i=this._iter.__iterator(T,r),o=0;return r&&c(this),new P(function(){var t=i.next();return t.done?t:W(e,r?n.size-++o:o++,t.value,t)})},e}(Z),kt=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.has=function(t){return this._iter.includes(t)},e.prototype.__iterate=function(e,t){var r=this;return this._iter.__iterate(function(t){return e(t,t,r)},t)},e.prototype.__iterator=function(e,t){var r=this._iter.__iterator(T,t);return new P(function(){var t=r.next();return t.done?t:W(e,t.value,t.value,t)})},e}($),Rt=function(t){function e(t){this._iter=t,this.size=t.size}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.entrySeq=function(){return this._iter.toSeq()},e.prototype.__iterate=function(r,t){var n=this;return this._iter.__iterate(function(t){if(t){Yt(t);var e=f(t);return r(e?t.get(1):t[1],e?t.get(0):t[0],n)}},t)},e.prototype.__iterator=function(n,t){var i=this._iter.__iterator(T,t);return new P(function(){for(;;){var t=i.next();if(t.done)return t;var e=t.value;if(e){Yt(e);var r=f(e);return W(n,r?e.get(0):e[0],r?e.get(1):e[1],t)}}})},e}(G);function Ut(i){var t=Xt(i);return t._iter=i,t.size=i.size,t.flip=function(){return i},t.reverse=function(){
var t=i.reverse.apply(this);return t.flip=function(){return i.reverse()},t},t.has=function(t){return i.includes(t)},t.includes=function(t){return i.has(t)},t.cacheResult=Ft,t.__iterateUncached=function(r,t){var n=this;return i.__iterate(function(t,e){return!1!==r(e,t,n)},t)},t.__iteratorUncached=function(t,e){if(t!==K)return i.__iterator(t===T?U:T,e);var r=i.__iterator(t,e);return new P(function(){var t,e=r.next();return e.done||(t=e.value[0],e.value[0]=e.value[1],e.value[1]=t),e})},t}function Tt(o,u,s){var t=Xt(o);return t.size=o.size,t.has=function(t){return o.has(t)},t.get=function(t,e){var r=o.get(t,v);return r===v?e:u.call(s,r,t,o)},t.__iterateUncached=function(n,t){var i=this;return o.__iterate(function(t,e,r){return!1!==n(u.call(s,t,e,r),e,i)},t)},t.__iteratorUncached=function(n,t){var i=o.__iterator(K,t);return new P(function(){var t=i.next();if(t.done)return t;var e=t.value,r=e[0];return W(n,r,u.call(s,e[1],r,o),t)})},t}function Kt(u,s){var a=this,t=Xt(u);return t._iter=u,t.size=u.size,t.reverse=function(){return u},u.flip&&(t.flip=function(){var t=Ut(u);return t.reverse=function(){return u.flip()},t}),t.get=function(t,e){return u.get(s?t:-1-t,e)},t.has=function(t){return u.has(s?t:-1-t)},t.includes=function(t){return u.includes(t)},t.cacheResult=Ft,t.__iterate=function(r,n){var i=this,o=0;return n&&c(u),u.__iterate(function(t,e){return r(t,s?e:n?i.size-++o:o++,i)},!n)},t.__iterator=function(r,n){var i=0;n&&c(u);var o=u.__iterator(K,!n);return new P(function(){var t=o.next();if(t.done)return t;var e=t.value;return W(r,s?e[0]:n?a.size-++i:i++,e[1],t)})},t}function Lt(u,s,a,c){var t=Xt(u);return c&&(t.has=function(t){var e=u.get(t,v);return e!==v&&!!s.call(a,e,t,u)},t.get=function(t,e){var r=u.get(t,v);return r!==v&&s.call(a,r,t,u)?r:e}),t.__iterateUncached=function(n,t){var i=this,o=0;return u.__iterate(function(t,e,r){if(s.call(a,t,e,r))return o++,n(t,c?e:o-1,i)},t),o},t.__iteratorUncached=function(n,t){var i=u.__iterator(K,t),o=0;return new P(function(){for(;;){var t=i.next();if(t.done)return t
;var e=t.value,r=e[0],e=e[1];if(s.call(a,e,r,u))return W(n,c?r:o++,e,t)}})},t}function Ct(s,t,e,a){var r=s.size;if(p(t,e,r))return s;if(void 0===r&&(t<0||e<0))return Ct(s.toSeq().cacheResult(),t,e,a);var c,f=y(t,r),r=w(e,r)-f;r==r&&(c=r<0?0:r);r=Xt(s);return r.size=0===c?c:s.size&&c||void 0,!a&&M(s)&&0<=c&&(r.get=function(t,e){return 0<=(t=h(this,t))&&t<c?s.get(t+f,e):e}),r.__iterateUncached=function(r,t){var n=this;if(0===c)return 0;if(t)return this.cacheResult().__iterate(r,t);var i=0,o=!0,u=0;return s.__iterate(function(t,e){if(!(o=o&&i++<f))return u++,!1!==r(t,a?e:u-1,n)&&u!==c}),u},r.__iteratorUncached=function(e,t){if(0!==c&&t)return this.cacheResult().__iterator(e,t);if(0===c)return new P(N);var r=s.__iterator(e,t),n=0,i=0;return new P(function(){for(;n++<f;)r.next();if(++i>c)return N();var t=r.next();return a||e===T||t.done?t:W(e,i-1,e===U?void 0:t.value[1],t)})},r}function Bt(e,c,f,h){var t=Xt(e);return t.__iterateUncached=function(n,t){var i=this;if(t)return this.cacheResult().__iterate(n,t);var o=!0,u=0;return e.__iterate(function(t,e,r){if(!(o=o&&c.call(f,t,e,r)))return u++,n(t,h?e:u-1,i)}),u},t.__iteratorUncached=function(i,t){var o=this;if(t)return this.cacheResult().__iterator(i,t);var u=e.__iterator(K,t),s=!0,a=0;return new P(function(){var t;do{if((t=u.next()).done)return h||i===T?t:W(i,a++,i===U?void 0:t.value[1],t);var e=t.value,r=e[0],n=e[1];s=s&&c.call(f,n,r,o)}while(s);return i===K?t:W(i,r,n,t)})},t}function Pt(t,s,a){var c=Xt(t);return c.__iterateUncached=function(i,e){if(e)return this.cacheResult().__iterate(i,e);var o=0,u=!1;return function r(t,n){t.__iterate(function(t,e){return(!s||n<s)&&f(t)?r(t,n+1):(o++,!1===i(t,a?e:o-1,c)&&(u=!0)),!u},e)}(t,0),o},c.__iteratorUncached=function(r,n){if(n)return this.cacheResult().__iterator(r,n);var i=t.__iterator(r,n),o=[],u=0;return new P(function(){for(;i;){var t=i.next();if(!1===t.done){var e=t.value;if(r===K&&(e=e[1]),s&&!(o.length<s)||!f(e))return a?t:W(r,u++,e,t);o.push(i),i=e.__iterator(r,n)}else i=o.pop()}return N()})},c}function Wt(r,n,i){
n=n||Gt;var t=a(r),o=0,u=r.toSeq().map(function(t,e){return[e,t,o++,i?i(t,e,r):t]}).valueSeq().toArray();return u.sort(function(t,e){return n(t[3],e[3])||t[2]-e[2]}).forEach(t?function(t,e){u[e].length=2}:function(t,e){u[e]=t[1]}),(t?G:z(r)?Z:$)(u)}function Nt(r,n,i){if(n=n||Gt,i){var t=r.toSeq().map(function(t,e){return[t,i(t,e,r)]}).reduce(function(t,e){return Ht(n,t[1],e[1])?e:t});return t&&t[0]}return r.reduce(function(t,e){return Ht(n,t,e)?e:t})}function Ht(t,e,r){t=t(r,e);return 0===t&&r!==e&&(null==r||r!=r)||0<t}function Jt(t,u,s,a){var e=Xt(t),t=new tt(s).map(function(t){return t.size});return e.size=a?t.max():t.min(),e.__iterate=function(t,e){for(var r,n=this.__iterator(T,e),i=0;!(r=n.next()).done&&!1!==t(r.value,i++,this););return i},e.__iteratorUncached=function(e,r){var n=s.map(function(t){return t=I(t),V(r?t.reverse():t)}),i=0,o=!1;return new P(function(){var t;return o||(t=n.map(function(t){return t.next()}),o=a?t.every(function(t){return t.done}):t.some(function(t){return t.done})),o?N():W(e,i++,u.apply(null,t.map(function(t){return t.value})))})},e}function Vt(t,e){return t===e?t:M(t)?e:t.constructor(e)}function Yt(t){if(t!==Object(t))throw new TypeError("Expected [K, V] tuple: "+t)}function Qt(t){return a(t)?O:z(t)?E:j}function Xt(t){return Object.create((a(t)?G:z(t)?Z:$).prototype)}function Ft(){return this._iter.cacheResult?(this._iter.cacheResult(),this.size=this._iter.size,this):F.prototype.cacheResult.call(this)}function Gt(t,e){return void 0===t&&void 0===e?0:void 0===t?1:void 0===e?-1:e<t?1:t<e?-1:0}function Zt(t,e){for(var r=Math.max(0,t.length-(e=e||0)),n=Array(r),i=0;i<r;i++)n[i]=t[i+e];return n}function $t(t,e){if(!t)throw Error(e)}function te(t){$t(t!==1/0,"Cannot perform this action with an infinite size.")}function ee(t){if(X(t)&&"string"!=typeof t)return t;if(R(t))return t.toArray();throw new TypeError("Invalid keyPath: expected Ordered Collection or Array: "+t)}At.prototype.cacheResult=xt.prototype.cacheResult=kt.prototype.cacheResult=Rt.prototype.cacheResult=Ft
;var re=Object.prototype.toString;function ne(t){if(!t||"object"!=typeof t||"[object Object]"!==re.call(t))return!1;t=Object.getPrototypeOf(t);if(null===t)return!0;for(var e=t,r=Object.getPrototypeOf(t);null!==r;)r=Object.getPrototypeOf(e=r);return e===t}function ie(t){return"object"==typeof t&&(A(t)||Array.isArray(t)||ne(t))}function oe(e){try{return"string"==typeof e?JSON.stringify(e):e+""}catch(t){return JSON.stringify(e)}}function ue(t,e){return A(t)?t.has(e):ie(t)&&Q.call(t,e)}function se(t,e,r){return A(t)?t.get(e,r):ue(t,e)?"function"==typeof t.get?t.get(e):t[e]:r}function ae(t){if(Array.isArray(t))return Zt(t);var e,r={};for(e in t)Q.call(t,e)&&(r[e]=t[e]);return r}function ce(t,e){if(!ie(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(A(t)){if(!t.remove)throw new TypeError("Cannot update immutable value without .remove() method: "+t);return t.remove(e)}if(!Q.call(t,e))return t;t=ae(t);return Array.isArray(t)?t.splice(e,1):delete t[e],t}function fe(t,e,r){if(!ie(t))throw new TypeError("Cannot update non-data-structure value: "+t);if(A(t)){if(!t.set)throw new TypeError("Cannot update immutable value without .set() method: "+t);return t.set(e,r)}if(Q.call(t,e)&&r===t[e])return t;t=ae(t);return t[e]=r,t}function he(t,e,r,n){n||(n=r,r=void 0);n=function t(e,r,n,i,o,u){var s=r===v;if(i===n.length){var a=s?o:r,c=u(a);return c===a?r:c}if(!s&&!ie(r))throw new TypeError("Cannot update within non-data-structure value in path ["+n.slice(0,i).map(oe)+"]: "+r);var a=n[i];var c=s?v:se(r,a,v);var u=t(c===v?e:A(c),c,n,i+1,o,u);return u===c?r:u===v?ce(r,a):fe(s?e?Qe():{}:r,a,u)}(A(t),t,ee(e),0,r,n);return n===v?r:n}function _e(t,e,r){return he(t,e,v,function(){return r})}function pe(t,e){return _e(this,t,e)}function le(t,e){return he(t,e,function(){return v})}function ve(t){return le(this,t)}function ye(t,e,r,n){return he(t,[e],r,n)}function de(t,e,r){return 1===arguments.length?t(this):ye(this,t,e,r)}function ge(t,e,r){return he(this,t,e,r)}function me(){for(var t=[],e=arguments.length;e--;
)t[e]=arguments[e];return Se(this,t)}function we(t){for(var e=[],r=arguments.length-1;0<r--;)e[r]=arguments[r+1];if("function"!=typeof t)throw new TypeError("Invalid merger function: "+t);return Se(this,e,t)}function Se(t,e,i){for(var r=[],n=0;n<e.length;n++){var o=O(e[n]);0!==o.size&&r.push(o)}return 0===r.length?t:0!==t.toSeq().size||t.__ownerID||1!==r.length?t.withMutations(function(n){for(var t=i?function(e,r){ye(n,r,v,function(t){return t===v?e:i(t,e,r)})}:function(t,e){n.set(e,t)},e=0;e<r.length;e++)r[e].forEach(t)}):t.constructor(r[0])}function ze(t){for(var e=[],r=arguments.length-1;0<r--;)e[r]=arguments[r+1];return je(t,e)}function be(t,e){for(var r=[],n=arguments.length-2;0<n--;)r[n]=arguments[n+2];return je(e,r,t)}function Ie(t){for(var e=[],r=arguments.length-1;0<r--;)e[r]=arguments[r+1];return Ee(t,e)}function Oe(t,e){for(var r=[],n=arguments.length-2;0<n--;)r[n]=arguments[n+2];return Ee(e,r,t)}function Ee(t,e,r){return je(t,e,(i=r,function t(e,r,n){return ie(e)&&ie(r)&&function(t,e){return t=F(t),e=F(e),z(t)===z(e)&&a(t)===a(e)}(e,r)?je(e,[r],t):i?i(e,r,n):r}));var i}function je(n,t,i){if(!ie(n))throw new TypeError("Cannot merge into non-data-structure value: "+n);if(A(n))return"function"==typeof i&&n.mergeWith?n.mergeWith.apply(n,[i].concat(t)):(n.merge?n.merge:n.concat).apply(n,t);for(var e=Array.isArray(n),o=n,r=e?E:O,u=e?function(t){o===n&&(o=ae(o)),o.push(t)}:function(t,e){var r=Q.call(o,e),t=r&&i?i(o[e],t,e):t;r&&t===o[e]||(o===n&&(o=ae(o)),o[e]=t)},s=0;s<t.length;s++)r(t[s]).forEach(u);return o}function qe(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Ee(this,t)}function Me(t){for(var e=[],r=arguments.length-1;0<r--;)e[r]=arguments[r+1];return Ee(this,e,t)}function De(t){for(var e=[],r=arguments.length-1;0<r--;)e[r]=arguments[r+1];return he(this,t,Qe(),function(t){return je(t,e)})}function xe(t){for(var e=[],r=arguments.length-1;0<r--;)e[r]=arguments[r+1];return he(this,t,Qe(),function(t){return Ee(t,e)})}function Ae(t){var e=this.asMutable();return t(e),e.wasAltered(
)?e.__ensureOwner(this.__ownerID):this}function ke(){return this.__ownerID?this:this.__ensureOwner(new m)}function Re(){return this.__ensureOwner()}function Ue(){return this.__altered}var Te=function(n){function t(e){return null==e?Qe():ct(e)&&!R(e)?e:Qe().withMutations(function(r){var t=n(e);te(t.size),t.forEach(function(t,e){return r.set(e,t)})})}return n&&(t.__proto__=n),((t.prototype=Object.create(n&&n.prototype)).constructor=t).of=function(){for(var r=[],t=arguments.length;t--;)r[t]=arguments[t];return Qe().withMutations(function(t){for(var e=0;e<r.length;e+=2){if(r.length<=e+1)throw Error("Missing value for key: "+r[e]);t.set(r[e],r[e+1])}})},t.prototype.toString=function(){return this.__toString("Map {","}")},t.prototype.get=function(t,e){return this._root?this._root.get(0,void 0,t,e):e},t.prototype.set=function(t,e){return Xe(this,t,e)},t.prototype.remove=function(t){return Xe(this,t,v)},t.prototype.deleteAll=function(t){var r=I(t);return 0===r.size?this:this.withMutations(function(e){r.forEach(function(t){return e.remove(t)})})},t.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._root=null,this.__hash=void 0,this.__altered=!0,this):Qe()},t.prototype.sort=function(t){return wr(Wt(this,t))},t.prototype.sortBy=function(t,e){return wr(Wt(this,e,t))},t.prototype.map=function(n,i){var o=this;return this.withMutations(function(r){r.forEach(function(t,e){r.set(e,n.call(i,t,e,o))})})},t.prototype.__iterator=function(t,e){return new He(this,t,e)},t.prototype.__iterate=function(e,t){var r=this,n=0;return this._root&&this._root.iterate(function(t){return n++,e(t[1],t[0],r)},t),n},t.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Ye(this.size,this._root,t,this.__hash):0===this.size?Qe():(this.__ownerID=t,this.__altered=!1,this)},t}(O);Te.isMap=ct;var Ke=Te.prototype;Ke[at]=!0,Ke[e]=Ke.remove,Ke.removeAll=Ke.deleteAll,Ke.setIn=pe,Ke.removeIn=Ke.deleteIn=ve,Ke.update=de,Ke.updateIn=ge,Ke.merge=Ke.concat=me,Ke.mergeWith=we,Ke.mergeDeep=qe,Ke.mergeDeepWith=Me,
Ke.mergeIn=De,Ke.mergeDeepIn=xe,Ke.withMutations=Ae,Ke.wasAltered=Ue,Ke.asImmutable=Re,Ke["@@transducer/init"]=Ke.asMutable=ke,Ke["@@transducer/step"]=function(t,e){return t.set(e[0],e[1])},Ke["@@transducer/result"]=function(t){return t.asImmutable()};var Le=function(t,e){this.ownerID=t,this.entries=e};Le.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(_t(r,i[o][0]))return i[o][1];return n},Le.prototype.update=function(t,e,r,n,i,o,u){for(var s=i===v,a=this.entries,c=0,f=a.length;c<f&&!_t(n,a[c][0]);c++);var h=c<f;if(h?a[c][1]===i:s)return this;if(_(u),!s&&h||_(o),!s||1!==a.length){if(!h&&!s&&er<=a.length)return function(t,e,r,n){t=t||new m;for(var i=new We(t,yt(r),[r,n]),o=0;o<e.length;o++){var u=e[o];i=i.update(t,0,void 0,u[0],u[1])}return i}(t,a,n,i);u=t&&t===this.ownerID,o=u?a:Zt(a);return h?s?c===f-1?o.pop():o[c]=o.pop():o[c]=[n,i]:o.push([n,i]),u?(this.entries=o,this):new Le(t,o)}};var Ce=function(t,e,r){this.ownerID=t,this.bitmap=e,this.nodes=r};Ce.prototype.get=function(t,e,r,n){void 0===e&&(e=yt(r));var i=1<<((0===t?e:e>>>t)&g),o=this.bitmap;return 0==(o&i)?n:this.nodes[$e(o&i-1)].get(t+d,e,r,n)},Ce.prototype.update=function(t,e,r,n,i,o,u){void 0===r&&(r=yt(n));var s=(0===e?r:r>>>e)&g,a=1<<s,c=this.bitmap,f=0!=(c&a);if(!f&&i===v)return this;var h=$e(c&a-1),_=this.nodes,p=f?_[h]:void 0,u=Fe(p,t,e+d,r,n,i,o,u);if(u===p)return this;if(!f&&u&&rr<=_.length)return function(t,e,r,n,i){for(var o=0,u=Array(l),s=0;0!==r;s++,r>>>=1)u[s]=1&r?e[o++]:void 0;return u[n]=i,new Be(t,o+1,u)}(t,_,c,s,u);if(f&&!u&&2===_.length&&Ge(_[1^h]))return _[1^h];if(f&&u&&1===_.length&&Ge(u))return u;s=t&&t===this.ownerID,a=f?u?c:c^a:c|a,u=f?u?tr(_,h,u,s):function(t,e,r){var n=t.length-1;if(r&&e===n)return t.pop(),t;for(var i=Array(n),o=0,u=0;u<n;u++)u===e&&(o=1),i[u]=t[u+o];return i}(_,h,s):function(t,e,r,n){var i=t.length+1;if(n&&e+1===i)return t[e]=r,t;for(var o=Array(i),u=0,s=0;s<i;s++)s===e?(o[s]=r,u=-1):o[s]=t[s+u];return o}(_,h,u,s);return s?(this.bitmap=a,this.nodes=u,this):new Ce(t,a,u)}
;var Be=function(t,e,r){this.ownerID=t,this.count=e,this.nodes=r};Be.prototype.get=function(t,e,r,n){void 0===e&&(e=yt(r));var i=this.nodes[(0===t?e:e>>>t)&g];return i?i.get(t+d,e,r,n):n},Be.prototype.update=function(t,e,r,n,i,o,u){void 0===r&&(r=yt(n));var s=(0===e?r:r>>>e)&g,a=this.nodes,c=a[s];if(i===v&&!c)return this;o=Fe(c,t,e+d,r,n,i,o,u);if(o===c)return this;u=this.count;if(c){if(!o&&--u<nr)return function(t,e,r,n){for(var i=0,o=0,u=Array(r),s=0,a=1,c=e.length;s<c;s++,a<<=1){var f=e[s];void 0!==f&&s!==n&&(i|=a,u[o++]=f)}return new Ce(t,i,u)}(t,a,u,s)}else u++;c=t&&t===this.ownerID,o=tr(a,s,o,c);return c?(this.count=u,this.nodes=o,this):new Be(t,u,o)};var Pe=function(t,e,r){this.ownerID=t,this.keyHash=e,this.entries=r};Pe.prototype.get=function(t,e,r,n){for(var i=this.entries,o=0,u=i.length;o<u;o++)if(_t(r,i[o][0]))return i[o][1];return n},Pe.prototype.update=function(t,e,r,n,i,o,u){void 0===r&&(r=yt(n));var s=i===v;if(r!==this.keyHash)return s?this:(_(u),_(o),Ze(this,t,e,r,[n,i]));for(var a=this.entries,c=0,f=a.length;c<f&&!_t(n,a[c][0]);c++);r=c<f;if(r?a[c][1]===i:s)return this;if(_(u),!s&&r||_(o),s&&2===f)return new We(t,this.keyHash,a[1^c]);u=t&&t===this.ownerID,o=u?a:Zt(a);return r?s?c===f-1?o.pop():o[c]=o.pop():o[c]=[n,i]:o.push([n,i]),u?(this.entries=o,this):new Pe(t,this.keyHash,o)};var We=function(t,e,r){this.ownerID=t,this.keyHash=e,this.entry=r};We.prototype.get=function(t,e,r,n){return _t(r,this.entry[0])?this.entry[1]:n},We.prototype.update=function(t,e,r,n,i,o,u){var s=i===v,a=_t(n,this.entry[0]);return(a?i===this.entry[1]:s)?this:(_(u),s?void _(o):a?t&&t===this.ownerID?(this.entry[1]=i,this):new We(t,this.keyHash,[n,i]):(_(o),Ze(this,t,e,yt(n),[n,i])))},Le.prototype.iterate=Pe.prototype.iterate=function(t,e){for(var r=this.entries,n=0,i=r.length-1;n<=i;n++)if(!1===t(r[e?i-n:n]))return!1},Ce.prototype.iterate=Be.prototype.iterate=function(t,e){for(var r=this.nodes,n=0,i=r.length-1;n<=i;n++){var o=r[e?i-n:n];if(o&&!1===o.iterate(t,e))return!1}},We.prototype.iterate=function(t,e){return t(
this.entry)};var Ne,He=function(t){function e(t,e,r){this._type=e,this._reverse=r,this._stack=t._root&&Ve(t._root)}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).prototype.next=function(){for(var t=this._type,e=this._stack;e;){var r=e.node,n=e.index++,i=void 0;if(r.entry){if(0==n)return Je(t,r.entry)}else if(r.entries){if(n<=(i=r.entries.length-1))return Je(t,r.entries[this._reverse?i-n:n])}else if(n<=(i=r.nodes.length-1)){n=r.nodes[this._reverse?i-n:n];if(n){if(n.entry)return Je(t,n.entry);e=this._stack=Ve(n,e)}continue}e=this._stack=this._stack.__prev}return N()},e}(P);function Je(t,e){return W(t,e[0],e[1])}function Ve(t,e){return{node:t,index:0,__prev:e}}function Ye(t,e,r,n){var i=Object.create(Ke);return i.size=t,i._root=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Qe(){return Ne=Ne||Ye(0)}function Xe(t,e,r){if(t._root){var n=u(),i=u(),o=Fe(t._root,t.__ownerID,0,void 0,e,r,n,i);if(!i.value)return t;n=t.size+(n.value?r===v?-1:1:0)}else{if(r===v)return t;n=1,o=new Le(t.__ownerID,[[e,r]])}return t.__ownerID?(t.size=n,t._root=o,t.__hash=void 0,t.__altered=!0,t):o?Ye(n,o):Qe()}function Fe(t,e,r,n,i,o,u,s){return t?t.update(e,r,n,i,o,u,s):o===v?t:(_(s),_(u),new We(e,n,[i,o]))}function Ge(t){return t.constructor===We||t.constructor===Pe}function Ze(t,e,r,n,i){if(t.keyHash===n)return new Pe(e,n,[t.entry,i]);var o=(0===r?t.keyHash:t.keyHash>>>r)&g,u=(0===r?n:n>>>r)&g,t=o==u?[Ze(t,e,r+d,n,i)]:(i=new We(e,n,i),o<u?[t,i]:[i,t]);return new Ce(e,1<<o|1<<u,t)}function $e(t){return t=(t=(858993459&(t-=t>>1&1431655765))+(t>>2&858993459))+(t>>4)&252645135,t+=t>>8,127&(t+=t>>16)}function tr(t,e,r,n){t=n?t:Zt(t);return t[e]=r,t}var er=l/4,rr=l/2,nr=l/4,ir="@@__IMMUTABLE_LIST__@@";function or(t){return!(!t||!t[ir])}var ur=function(o){function t(t){var e=pr();if(null==t)return e;if(or(t))return t;var n=o(t),i=n.size;return 0===i?e:(te(i),0<i&&i<l?_r(0,i,d,null,new ar(n.toArray())):e.withMutations(function(r){r.setSize(i),n.forEach(function(t,e){return r.set(e,t)})}))}return o&&(
t.__proto__=o),((t.prototype=Object.create(o&&o.prototype)).constructor=t).of=function(){return this(arguments)},t.prototype.toString=function(){return this.__toString("List [","]")},t.prototype.get=function(t,e){if(0<=(t=h(this,t))&&t<this.size){var r=yr(this,t+=this._origin);return r&&r.array[t&g]}return e},t.prototype.set=function(t,e){return function(t,e,r){if((e=h(t,e))!==e)return t;if(t.size<=e||e<0)return t.withMutations(function(t){e<0?dr(t,e).set(0,r):dr(t,0,e+1).set(e,r)});var n=t._tail,i=t._root,o=u();(e+=t._origin)>=gr(t._capacity)?n=lr(n,t.__ownerID,0,e,r,o):i=lr(i,t.__ownerID,t._level,e,r,o);if(!o.value)return t;if(t.__ownerID)return t._root=i,t._tail=n,t.__hash=void 0,t.__altered=!0,t;return _r(t._origin,t._capacity,t._level,i,n)}(this,t,e)},t.prototype.remove=function(t){return this.has(t)?0===t?this.shift():t===this.size-1?this.pop():this.splice(t,1):this},t.prototype.insert=function(t,e){return this.splice(t,0,e)},t.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=this._origin=this._capacity=0,this._level=d,this._root=this._tail=this.__hash=void 0,this.__altered=!0,this):pr()},t.prototype.push=function(){var r=arguments,n=this.size;return this.withMutations(function(t){dr(t,0,n+r.length);for(var e=0;e<r.length;e++)t.set(n+e,r[e])})},t.prototype.pop=function(){return dr(this,0,-1)},t.prototype.unshift=function(){var r=arguments;return this.withMutations(function(t){dr(t,-r.length);for(var e=0;e<r.length;e++)t.set(e,r[e])})},t.prototype.shift=function(){return dr(this,1)},t.prototype.concat=function(){for(var t=arguments,r=[],e=0;e<arguments.length;e++){var n=t[e],n=o("string"!=typeof n&&H(n)?n:[n]);0!==n.size&&r.push(n)}return 0===r.length?this:0!==this.size||this.__ownerID||1!==r.length?this.withMutations(function(e){r.forEach(function(t){return t.forEach(function(t){return e.push(t)})})}):this.constructor(r[0])},t.prototype.setSize=function(t){return dr(this,0,t)},t.prototype.map=function(r,n){var i=this;return this.withMutations(function(t){for(
var e=0;e<i.size;e++)t.set(e,r.call(n,t.get(e),e,i))})},t.prototype.slice=function(t,e){var r=this.size;return p(t,e,r)?this:dr(this,y(t,r),w(e,r))},t.prototype.__iterator=function(e,r){var n=r?this.size:0,i=hr(this,r);return new P(function(){var t=i();return t===fr?N():W(e,r?--n:n++,t)})},t.prototype.__iterate=function(t,e){for(var r,n=e?this.size:0,i=hr(this,e);(r=i())!==fr&&!1!==t(r,e?--n:n++,this););return n},t.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?_r(this._origin,this._capacity,this._level,this._root,this._tail,t,this.__hash):0===this.size?pr():(this.__ownerID=t,this.__altered=!1,this)},t}(E);ur.isList=or;var sr=ur.prototype;sr[ir]=!0,sr[e]=sr.remove,sr.merge=sr.concat,sr.setIn=pe,sr.deleteIn=sr.removeIn=ve,sr.update=de,sr.updateIn=ge,sr.mergeIn=De,sr.mergeDeepIn=xe,sr.withMutations=Ae,sr.wasAltered=Ue,sr.asImmutable=Re,sr["@@transducer/init"]=sr.asMutable=ke,sr["@@transducer/step"]=function(t,e){return t.push(e)},sr["@@transducer/result"]=function(t){return t.asImmutable()};var ar=function(t,e){this.array=t,this.ownerID=e};ar.prototype.removeBefore=function(t,e,r){if(r===e?1<<e:0===this.array.length)return this;var n=r>>>e&g;if(this.array.length<=n)return new ar([],t);var i=0==n;if(0<e){var o,u=this.array[n];if((o=u&&u.removeBefore(t,e-d,r))===u&&i)return this}if(i&&!o)return this;var s=vr(this,t);if(!i)for(var a=0;a<n;a++)s.array[a]=void 0;return o&&(s.array[n]=o),s},ar.prototype.removeAfter=function(t,e,r){if(r===(e?1<<e:0)||0===this.array.length)return this;var n=r-1>>>e&g;if(this.array.length<=n)return this;if(0<e){var i,o=this.array[n];if((i=o&&o.removeAfter(t,e-d,r))===o&&n==this.array.length-1)return this}t=vr(this,t);return t.array.splice(1+n),i&&(t.array[n]=i),t};var cr,fr={};function hr(t,s){var a=t._origin,c=t._capacity,o=gr(c),u=t._tail;return f(t._root,t._level,0);function f(t,e,r){return 0===e?function(t,e){var r=e===o?u&&u.array:t&&t.array,n=a<e?0:a-e,i=c-e;l<i&&(i=l);return function(){if(n===i)return fr;var t=s?--i:n++;return r&&r[t]}}(t,r):function(t,e,r){
var n,i=t&&t.array,o=a<r?0:a-r>>e,u=1+(c-r>>e);l<u&&(u=l);return function(){for(;;){if(n){var t=n();if(t!==fr)return t;n=null}if(o===u)return fr;t=s?--u:o++;n=f(i&&i[t],e-d,r+(t<<e))}}}(t,e,r)}}function _r(t,e,r,n,i,o,u){var s=Object.create(sr);return s.size=e-t,s._origin=t,s._capacity=e,s._level=r,s._root=n,s._tail=i,s.__ownerID=o,s.__hash=u,s.__altered=!1,s}function pr(){return cr=cr||_r(0,0,d)}function lr(t,e,r,n,i,o){var u,s=n>>>r&g,a=t&&s<t.array.length;if(!a&&void 0===i)return t;if(0<r){var c=t&&t.array[s],n=lr(c,e,r-d,n,i,o);return n===c?t:((u=vr(t,e)).array[s]=n,u)}return a&&t.array[s]===i?t:(o&&_(o),u=vr(t,e),void 0===i&&s==u.array.length-1?u.array.pop():u.array[s]=i,u)}function vr(t,e){return e&&t&&e===t.ownerID?t:new ar(t?t.array.slice():[],e)}function yr(t,e){if(e>=gr(t._capacity))return t._tail;if(e<1<<t._level+d){for(var r=t._root,n=t._level;r&&0<n;)r=r.array[e>>>n&g],n-=d;return r}}function dr(t,e,r){void 0!==e&&(e|=0),void 0!==r&&(r|=0);var n=t.__ownerID||new m,i=t._origin,o=t._capacity,u=i+e,s=void 0===r?o:r<0?o+r:i+r;if(u===i&&s===o)return t;if(s<=u)return t.clear();for(var a=t._level,c=t._root,f=0;u+f<0;)c=new ar(c&&c.array.length?[void 0,c]:[],n),f+=1<<(a+=d);f&&(u+=f,i+=f,s+=f,o+=f);for(var h=gr(o),_=gr(s);1<<a+d<=_;)c=new ar(c&&c.array.length?[c]:[],n),a+=d;e=t._tail,r=_<h?yr(t,s-1):h<_?new ar([],n):e;if(e&&h<_&&u<o&&e.array.length){for(var p=c=vr(c,n),l=a;d<l;l-=d)var v=h>>>l&g,p=p.array[v]=vr(p.array[v],n);p.array[h>>>d&g]=e}if(s<o&&(r=r&&r.removeAfter(n,0,s)),_<=u)u-=_,s-=_,a=d,c=null,r=r&&r.removeBefore(n,0,u);else if(i<u||_<h){for(f=0;c;){var y=u>>>a&g;if(y!=_>>>a&g)break;y&&(f+=(1<<a)*y),a-=d,c=c.array[y]}c&&i<u&&(c=c.removeBefore(n,a,u-f)),c&&_<h&&(c=c.removeAfter(n,a,_-f)),f&&(u-=f,s-=f)}return t.__ownerID?(t.size=s-u,t._origin=u,t._capacity=s,t._level=a,t._root=c,t._tail=r,t.__hash=void 0,t.__altered=!0,t):_r(u,s,a,c,r)}function gr(t){return t<l?0:t-1>>>d<<d}var mr,wr=function(t){function e(e){return null==e?zr():ft(e)?e:zr().withMutations(function(r){var t=O(e);te(t.size),
t.forEach(function(t,e){return r.set(e,t)})})}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).of=function(){return this(arguments)},e.prototype.toString=function(){return this.__toString("OrderedMap {","}")},e.prototype.get=function(t,e){t=this._map.get(t);return void 0!==t?this._list.get(t)[1]:e},e.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._map.clear(),this._list.clear(),this.__altered=!0,this):zr()},e.prototype.set=function(t,e){return br(this,t,e)},e.prototype.remove=function(t){return br(this,t,v)},e.prototype.__iterate=function(e,t){var r=this;return this._list.__iterate(function(t){return t&&e(t[1],t[0],r)},t)},e.prototype.__iterator=function(t,e){return this._list.fromEntrySeq().__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t),r=this._list.__ensureOwner(t);return t?Sr(e,r,t,this.__hash):0===this.size?zr():(this.__ownerID=t,this.__altered=!1,this._map=e,this._list=r,this)},e}(Te);function Sr(t,e,r,n){var i=Object.create(wr.prototype);return i.size=t?t.size:0,i._map=t,i._list=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function zr(){return mr=mr||Sr(Qe(),pr())}function br(t,e,r){var n,i,o=t._map,u=t._list,s=o.get(e),a=void 0!==s;if(r===v){if(!a)return t;l<=u.size&&2*o.size<=u.size?(n=(i=u.filter(function(t,e){return void 0!==t&&s!==e})).toKeyedSeq().map(function(t){return t[0]}).flip().toMap(),t.__ownerID&&(n.__ownerID=i.__ownerID=t.__ownerID)):(n=o.remove(e),i=s===u.size-1?u.pop():u.set(s,void 0))}else if(a){if(r===u.get(s)[1])return t;n=o,i=u.set(s,[e,r])}else n=o.set(e,u.size),i=u.set(u.size,[e,r]);return t.__ownerID?(t.size=n.size,t._map=n,t._list=i,t.__hash=void 0,t.__altered=!0,t):Sr(n,i)}wr.isOrderedMap=ft,wr.prototype[k]=!0,wr.prototype[e]=wr.prototype.remove;var Ir="@@__IMMUTABLE_STACK__@@";function Or(t){return!(!t||!t[Ir])}var Er=function(i){function t(t){return null==t?Dr():Or(t)?t:Dr().pushAll(t)}return i&&(t.__proto__=i),((
t.prototype=Object.create(i&&i.prototype)).constructor=t).of=function(){return this(arguments)},t.prototype.toString=function(){return this.__toString("Stack [","]")},t.prototype.get=function(t,e){var r=this._head;for(t=h(this,t);r&&t--;)r=r.next;return r?r.value:e},t.prototype.peek=function(){return this._head&&this._head.value},t.prototype.push=function(){var t=arguments;if(0===arguments.length)return this;for(var e=this.size+arguments.length,r=this._head,n=arguments.length-1;0<=n;n--)r={value:t[n],next:r};return this.__ownerID?(this.size=e,this._head=r,this.__hash=void 0,this.__altered=!0,this):Mr(e,r)},t.prototype.pushAll=function(t){if(0===(t=i(t)).size)return this;if(0===this.size&&Or(t))return t;te(t.size);var e=this.size,r=this._head;return t.__iterate(function(t){e++,r={value:t,next:r}},!0),this.__ownerID?(this.size=e,this._head=r,this.__hash=void 0,this.__altered=!0,this):Mr(e,r)},t.prototype.pop=function(){return this.slice(1)},t.prototype.clear=function(){return 0===this.size?this:this.__ownerID?(this.size=0,this._head=void 0,this.__hash=void 0,this.__altered=!0,this):Dr()},t.prototype.slice=function(t,e){if(p(t,e,this.size))return this;var r=y(t,this.size);if(w(e,this.size)!==this.size)return i.prototype.slice.call(this,t,e);for(var e=this.size-r,n=this._head;r--;)n=n.next;return this.__ownerID?(this.size=e,this._head=n,this.__hash=void 0,this.__altered=!0,this):Mr(e,n)},t.prototype.__ensureOwner=function(t){return t===this.__ownerID?this:t?Mr(this.size,this._head,t,this.__hash):0===this.size?Dr():(this.__ownerID=t,this.__altered=!1,this)},t.prototype.__iterate=function(r,t){var n=this;if(t)return new tt(this.toArray()).__iterate(function(t,e){return r(t,e,n)},t);for(var e=0,i=this._head;i&&!1!==r(i.value,e++,this);)i=i.next;return e},t.prototype.__iterator=function(e,t){if(t)return new tt(this.toArray()).__iterator(e,t);var r=0,n=this._head;return new P(function(){if(n){var t=n.value;return n=n.next,W(e,r++,t)}return N()})},t}(E);Er.isStack=Or;var jr,qr=Er.prototype;function Mr(t,e,r,n){
var i=Object.create(qr);return i.size=t,i._head=e,i.__ownerID=r,i.__hash=n,i.__altered=!1,i}function Dr(){return jr=jr||Mr(0)}qr[Ir]=!0,qr.shift=qr.pop,qr.unshift=qr.push,qr.unshiftAll=qr.pushAll,qr.withMutations=Ae,qr.wasAltered=Ue,qr.asImmutable=Re,qr["@@transducer/init"]=qr.asMutable=ke,qr["@@transducer/step"]=function(t,e){return t.unshift(e)},qr["@@transducer/result"]=function(t){return t.asImmutable()};var xr="@@__IMMUTABLE_SET__@@";function Ar(t){return!(!t||!t[xr])}function kr(t){return Ar(t)&&R(t)}function Rr(r,t){if(r===t)return!0;if(!f(t)||void 0!==r.size&&void 0!==t.size&&r.size!==t.size||void 0!==r.__hash&&void 0!==t.__hash&&r.__hash!==t.__hash||a(r)!==a(t)||z(r)!==z(t)||R(r)!==R(t))return!1;if(0===r.size&&0===t.size)return!0;var n=!b(r);if(R(r)){var i=r.entries();return t.every(function(t,e){var r=i.next().value;return r&&_t(r[1],t)&&(n||_t(r[0],e))})&&i.next().done}var e,o=!1;void 0===r.size&&(void 0===t.size?"function"==typeof r.cacheResult&&r.cacheResult():(o=!0,e=r,r=t,t=e));var u=!0,t=t.__iterate(function(t,e){if(n?!r.has(t):o?!_t(t,r.get(e,v)):!_t(r.get(e,v),t))return u=!1});return u&&r.size===t}function Ur(e,r){function t(t){e.prototype[t]=r[t]}return Object.keys(r).forEach(t),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(r).forEach(t),e}function Tr(t){if(!t||"object"!=typeof t)return t;if(!f(t)){if(!ie(t))return t;t=F(t)}if(a(t)){var r={};return t.__iterate(function(t,e){r[e]=Tr(t)}),r}var e=[];return t.__iterate(function(t){e.push(Tr(t))}),e}var Kr=function(n){function e(r){return null==r?Wr():Ar(r)&&!R(r)?r:Wr().withMutations(function(e){var t=n(r);te(t.size),t.forEach(function(t){return e.add(t)})})}return n&&(e.__proto__=n),((e.prototype=Object.create(n&&n.prototype)).constructor=e).of=function(){return this(arguments)},e.fromKeys=function(t){return this(O(t).keySeq())},e.intersect=function(t){return(t=I(t).toArray()).length?Cr.intersect.apply(e(t.pop()),t):Wr()},e.union=function(t){return(t=I(t).toArray()).length?Cr.union.apply(e(t.pop()),t):Wr()},
e.prototype.toString=function(){return this.__toString("Set {","}")},e.prototype.has=function(t){return this._map.has(t)},e.prototype.add=function(t){return Br(this,this._map.set(t,t))},e.prototype.remove=function(t){return Br(this,this._map.remove(t))},e.prototype.clear=function(){return Br(this,this._map.clear())},e.prototype.map=function(r,n){var i=this,o=!1,t=Br(this,this._map.mapEntries(function(t){var e=t[1],t=r.call(n,e,e,i);return t!==e&&(o=!0),[t,t]},n));return o?t:this},e.prototype.union=function(){for(var r=[],t=arguments.length;t--;)r[t]=arguments[t];return 0===(r=r.filter(function(t){return 0!==t.size})).length?this:0!==this.size||this.__ownerID||1!==r.length?this.withMutations(function(e){for(var t=0;t<r.length;t++)"string"==typeof r[t]?e.add(r[t]):n(r[t]).forEach(function(t){return e.add(t)})}):this.constructor(r[0])},e.prototype.intersect=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];if(0===t.length)return this;t=t.map(function(t){return n(t)});var r=[];return this.forEach(function(e){t.every(function(t){return t.includes(e)})||r.push(e)}),this.withMutations(function(e){r.forEach(function(t){e.remove(t)})})},e.prototype.subtract=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];if(0===t.length)return this;t=t.map(function(t){return n(t)});var r=[];return this.forEach(function(e){t.some(function(t){return t.includes(e)})&&r.push(e)}),this.withMutations(function(e){r.forEach(function(t){e.remove(t)})})},e.prototype.sort=function(t){return an(Wt(this,t))},e.prototype.sortBy=function(t,e){return an(Wt(this,e,t))},e.prototype.wasAltered=function(){return this._map.wasAltered()},e.prototype.__iterate=function(e,t){var r=this;return this._map.__iterate(function(t){return e(t,t,r)},t)},e.prototype.__iterator=function(t,e){return this._map.__iterator(t,e)},e.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._map.__ensureOwner(t);return t?this.__make(e,t):0===this.size?this.__empty():(this.__ownerID=t,this._map=e,this)},e}(j);Kr.isSet=Ar
;var Lr,Cr=Kr.prototype;function Br(t,e){return t.__ownerID?(t.size=e.size,t._map=e,t):e===t._map?t:0===e.size?t.__empty():t.__make(e)}function Pr(t,e){var r=Object.create(Cr);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function Wr(){return Lr=Lr||Pr(Qe())}Cr[xr]=!0,Cr[e]=Cr.remove,Cr.merge=Cr.concat=Cr.union,Cr.withMutations=Ae,Cr.asImmutable=Re,Cr["@@transducer/init"]=Cr.asMutable=ke,Cr["@@transducer/step"]=function(t,e){return t.add(e)},Cr["@@transducer/result"]=function(t){return t.asImmutable()},Cr.__empty=Wr,Cr.__make=Pr;var Nr,Hr=function(t){function n(t,e,r){if(!(this instanceof n))return new n(t,e,r);if($t(0!==r,"Cannot step a Range by 0"),t=t||0,void 0===e&&(e=1/0),r=void 0===r?1:Math.abs(r),e<t&&(r=-r),this._start=t,this._end=e,this._step=r,this.size=Math.max(0,1+Math.ceil((e-t)/r-1)),0===this.size){if(Nr)return Nr;Nr=this}}return t&&(n.__proto__=t),((n.prototype=Object.create(t&&t.prototype)).constructor=n).prototype.toString=function(){return 0===this.size?"Range []":"Range [ "+this._start+"..."+this._end+(1!==this._step?" by "+this._step:"")+" ]"},n.prototype.get=function(t,e){return this.has(t)?this._start+h(this,t)*this._step:e},n.prototype.includes=function(t){t=(t-this._start)/this._step;return 0<=t&&t<this.size&&t==Math.floor(t)},n.prototype.slice=function(t,e){return p(t,e,this.size)?this:(t=y(t,this.size),(e=w(e,this.size))<=t?new n(0,0):new n(this.get(t,this._end),this.get(e,this._end),this._step))},n.prototype.indexOf=function(t){t-=this._start;if(t%this._step==0){t=t/this._step;if(0<=t&&t<this.size)return t}return-1},n.prototype.lastIndexOf=function(t){return this.indexOf(t)},n.prototype.__iterate=function(t,e){for(var r=this.size,n=this._step,i=e?this._start+(r-1)*n:this._start,o=0;o!==r&&!1!==t(i,e?r-++o:o++,this);)i+=e?-n:n;return o},n.prototype.__iterator=function(e,r){var n=this.size,i=this._step,o=r?this._start+(n-1)*i:this._start,u=0;return new P(function(){if(u===n)return N();var t=o;return o+=r?-i:i,W(e,r?n-++u:u++,t)})},n.prototype.equals=function(t){
return t instanceof n?this._start===t._start&&this._end===t._end&&this._step===t._step:Rr(this,t)},n}(Z);function Jr(t,e,r){for(var n=ee(e),i=0;i!==n.length;)if((t=se(t,n[i++],v))===v)return r;return t}function Vr(t,e){return Jr(this,t,e)}function Yr(t,e){return Jr(t,e,v)!==v}function Qr(){te(this.size);var r={};return this.__iterate(function(t,e){r[e]=t}),r}I.isIterable=f,I.isKeyed=a,I.isIndexed=z,I.isAssociative=b,I.isOrdered=R,I.Iterator=P,Ur(I,{toArray:function(){te(this.size);var r=Array(this.size||0),n=a(this),i=0;return this.__iterate(function(t,e){r[i++]=n?[e,t]:t}),r},toIndexedSeq:function(){return new At(this)},toJS:function(){return Tr(this)},toKeyedSeq:function(){return new xt(this,!0)},toMap:function(){return Te(this.toKeyedSeq())},toObject:Qr,toOrderedMap:function(){return wr(this.toKeyedSeq())},toOrderedSet:function(){return an(a(this)?this.valueSeq():this)},toSet:function(){return Kr(a(this)?this.valueSeq():this)},toSetSeq:function(){return new kt(this)},toSeq:function(){return z(this)?this.toIndexedSeq():a(this)?this.toKeyedSeq():this.toSetSeq()},toStack:function(){return Er(a(this)?this.valueSeq():this)},toList:function(){return ur(a(this)?this.valueSeq():this)},toString:function(){return"[Collection]"},__toString:function(t,e){return 0===this.size?t+e:t+" "+this.toSeq().map(this.__toStringMapper).join(", ")+" "+e},concat:function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return Vt(this,function(t,e){var r=a(t);if(0===(e=[t].concat(e).map(function(t){return f(t)?r&&(t=O(t)):t=r?ot(t):ut(Array.isArray(t)?t:[t]),t}).filter(function(t){return 0!==t.size})).length)return t;if(1===e.length){var n=e[0];if(n===t||r&&a(n)||z(t)&&z(n))return n}return n=new tt(e),r?n=n.toKeyedSeq():z(t)||(n=n.toSetSeq()),(n=n.flatten(!0)).size=e.reduce(function(t,e){if(void 0!==t){e=e.size;if(void 0!==e)return t+e}},0),n}(this,t))},includes:function(e){return this.some(function(t){return _t(t,e)})},entries:function(){return this.__iterator(K)},every:function(n,i){te(this.size);var o=!0
;return this.__iterate(function(t,e,r){if(!n.call(i,t,e,r))return o=!1}),o},filter:function(t,e){return Vt(this,Lt(this,t,e,!0))},partition:function(t,e){return function(r,n,i){var o=a(r),u=[[],[]];r.__iterate(function(t,e){u[n.call(i,t,e,r)?1:0].push(o?[e,t]:t)});var e=Qt(r);return u.map(function(t){return Vt(r,e(t))})}(this,t,e)},find:function(t,e,r){e=this.findEntry(t,e);return e?e[1]:r},forEach:function(t,e){return te(this.size),this.__iterate(e?t.bind(e):t)},join:function(e){te(this.size),e=void 0!==e?""+e:",";var r="",n=!0;return this.__iterate(function(t){n?n=!1:r+=e,r+=null!=t?""+t:""}),r},keys:function(){return this.__iterator(U)},map:function(t,e){return Vt(this,Tt(this,t,e))},reduce:function(t,e,r){return $r(this,t,e,r,arguments.length<2,!1)},reduceRight:function(t,e,r){return $r(this,t,e,r,arguments.length<2,!0)},reverse:function(){return Vt(this,Kt(this,!0))},slice:function(t,e){return Vt(this,Ct(this,t,e,!0))},some:function(n,i){te(this.size);var o=!1;return this.__iterate(function(t,e,r){if(n.call(i,t,e,r))return!(o=!0)}),o},sort:function(t){return Vt(this,Wt(this,t))},values:function(){return this.__iterator(T)},butLast:function(){return this.slice(0,-1)},isEmpty:function(){return void 0!==this.size?0===this.size:!this.some(function(){return!0})},count:function(t,e){return c(t?this.toSeq().filter(t,e):this)},countBy:function(t,e){return r=this,n=t,i=e,o=Te().asMutable(),r.__iterate(function(t,e){o.update(n.call(i,t,e,r),0,function(t){return t+1})}),o.asImmutable();var r,n,i,o},equals:function(t){return Rr(this,t)},entrySeq:function(){var t=this;if(t._cache)return new tt(t._cache);var e=t.toSeq().map(en).toIndexedSeq();return e.fromEntrySeq=function(){return t.toSeq()},e},filterNot:function(t,e){return this.filter(rn(t),e)},findEntry:function(n,i,t){var o=t;return this.__iterate(function(t,e,r){if(n.call(i,t,e,r))return!(o=[e,t])}),o},findKey:function(t,e){e=this.findEntry(t,e);return e&&e[0]},findLast:function(t,e,r){return this.toKeyedSeq().reverse().find(t,e,r)},findLastEntry:function(t,e,r){
return this.toKeyedSeq().reverse().findEntry(t,e,r)},findLastKey:function(t,e){return this.toKeyedSeq().reverse().findKey(t,e)},first:function(t){return this.find(r,null,t)},flatMap:function(t,e){return Vt(this,(n=t,i=e,o=Qt(r=this),r.toSeq().map(function(t,e){return o(n.call(i,t,e,r))}).flatten(!0)));var r,n,i,o},flatten:function(t){return Vt(this,Pt(this,t,!0))},fromEntrySeq:function(){return new Rt(this)},get:function(r,t){return this.find(function(t,e){return _t(e,r)},void 0,t)},getIn:Vr,groupBy:function(t,e){return function(n,t,i){var o=a(n),u=(R(n)?wr:Te)().asMutable();n.__iterate(function(e,r){u.update(t.call(i,e,r,n),function(t){return(t=t||[]).push(o?[r,e]:e),t})});var e=Qt(n);return u.map(function(t){return Vt(n,e(t))}).asImmutable()}(this,t,e)},has:function(t){return this.get(t,v)!==v},hasIn:function(t){return Yr(this,t)},isSubset:function(e){return e="function"==typeof e.includes?e:I(e),this.every(function(t){return e.includes(t)})},isSuperset:function(t){return(t="function"==typeof t.isSubset?t:I(t)).isSubset(this)},keyOf:function(e){return this.findKey(function(t){return _t(t,e)})},keySeq:function(){return this.toSeq().map(tn).toIndexedSeq()},last:function(t){return this.toSeq().reverse().first(t)},lastKeyOf:function(t){return this.toKeyedSeq().reverse().keyOf(t)},max:function(t){return Nt(this,t)},maxBy:function(t,e){return Nt(this,e,t)},min:function(t){return Nt(this,t?nn(t):un)},minBy:function(t,e){return Nt(this,e?nn(e):un,t)},rest:function(){return this.slice(1)},skip:function(t){return 0===t?this:this.slice(Math.max(0,t))},skipLast:function(t){return 0===t?this:this.slice(0,-Math.max(0,t))},skipWhile:function(t,e){return Vt(this,Bt(this,t,e,!0))},skipUntil:function(t,e){return this.skipWhile(rn(t),e)},sortBy:function(t,e){return Vt(this,Wt(this,e,t))},take:function(t){return this.slice(0,Math.max(0,t))},takeLast:function(t){return this.slice(-Math.max(0,t))},takeWhile:function(t,e){return Vt(this,(s=t,a=e,(e=Xt(r=this)).__iterateUncached=function(n,t){var i=this;if(t)return this.cacheResult(
).__iterate(n,t);var o=0;return r.__iterate(function(t,e,r){return s.call(a,t,e,r)&&++o&&n(t,e,i)}),o},e.__iteratorUncached=function(n,t){var i=this;if(t)return this.cacheResult().__iterator(n,t);var o=r.__iterator(K,t),u=!0;return new P(function(){if(!u)return N();var t=o.next();if(t.done)return t;var e=t.value,r=e[0],e=e[1];return s.call(a,e,r,i)?n===K?t:W(n,r,e,t):(u=!1,N())})},e));var r,s,a},takeUntil:function(t,e){return this.takeWhile(rn(t),e)},update:function(t){return t(this)},valueSeq:function(){return this.toIndexedSeq()},hashCode:function(){return this.__hash||(this.__hash=function(t){if(t.size===1/0)return 0;var e=R(t),r=a(t),n=e?1:0;return function(t,e){return e=pt(e,3432918353),e=pt(e<<15|e>>>-15,461845907),e=pt(e<<13|e>>>-13,5),e=pt((e=(e+3864292196|0)^t)^e>>>16,2246822507),e=lt((e=pt(e^e>>>13,3266489909))^e>>>16)}(t.__iterate(r?e?function(t,e){n=31*n+sn(yt(t),yt(e))|0}:function(t,e){n=n+sn(yt(t),yt(e))|0}:e?function(t){n=31*n+yt(t)|0}:function(t){n=n+yt(t)|0}),n)}(this))}});var Xr=I.prototype;Xr[o]=!0,Xr[B]=Xr.values,Xr.toJSON=Xr.toArray,Xr.__toStringMapper=oe,Xr.inspect=Xr.toSource=function(){return""+this},Xr.chain=Xr.flatMap,Xr.contains=Xr.includes,Ur(O,{flip:function(){return Vt(this,Ut(this))},mapEntries:function(r,n){var i=this,o=0;return Vt(this,this.toSeq().map(function(t,e){return r.call(n,[e,t],o++,i)}).fromEntrySeq())},mapKeys:function(r,n){var i=this;return Vt(this,this.toSeq().flip().map(function(t,e){return r.call(n,t,e,i)}).flip())}});var Fr=O.prototype;Fr[s]=!0,Fr[B]=Xr.entries,Fr.toJSON=Qr,Fr.__toStringMapper=function(t,e){return oe(e)+": "+oe(t)},Ur(E,{toKeyedSeq:function(){return new xt(this,!1)},filter:function(t,e){return Vt(this,Lt(this,t,e,!1))},findIndex:function(t,e){e=this.findEntry(t,e);return e?e[0]:-1},indexOf:function(t){t=this.keyOf(t);return void 0===t?-1:t},lastIndexOf:function(t){t=this.lastKeyOf(t);return void 0===t?-1:t},reverse:function(){return Vt(this,Kt(this,!1))},slice:function(t,e){return Vt(this,Ct(this,t,e,!1))},splice:function(t,e){
var r=arguments.length;if(e=Math.max(e||0,0),0===r||2===r&&!e)return this;t=y(t,t<0?this.count():this.size);var n=this.slice(0,t);return Vt(this,1===r?n:n.concat(Zt(arguments,2),this.slice(t+e)))},findLastIndex:function(t,e){e=this.findLastEntry(t,e);return e?e[0]:-1},first:function(t){return this.get(0,t)},flatten:function(t){return Vt(this,Pt(this,t,!1))},get:function(r,t){return(r=h(this,r))<0||this.size===1/0||void 0!==this.size&&this.size<r?t:this.find(function(t,e){return e===r},void 0,t)},has:function(t){return 0<=(t=h(this,t))&&(void 0!==this.size?this.size===1/0||t<this.size:!!~this.indexOf(t))},interpose:function(t){return Vt(this,(u=t,(t=Xt(o=this)).size=o.size&&2*o.size-1,t.__iterateUncached=function(e,t){var r=this,n=0;return o.__iterate(function(t){return(!n||!1!==e(u,n++,r))&&!1!==e(t,n++,r)},t),n},t.__iteratorUncached=function(t,e){var r,n=o.__iterator(T,e),i=0;return new P(function(){return(!r||i%2)&&(r=n.next()).done?r:i%2?W(t,i++,u):W(t,i++,r.value,r)})},t));var o,u},interleave:function(){var t=[this].concat(Zt(arguments)),e=Jt(this.toSeq(),Z.of,t),r=e.flatten(!0);return e.size&&(r.size=e.size*t.length),Vt(this,r)},keySeq:function(){return Hr(0,this.size)},last:function(t){return this.get(-1,t)},skipWhile:function(t,e){return Vt(this,Bt(this,t,e,!1))},zip:function(){var t=[this].concat(Zt(arguments));return Vt(this,Jt(this,on,t))},zipAll:function(){var t=[this].concat(Zt(arguments));return Vt(this,Jt(this,on,t,!0))},zipWith:function(t){var e=Zt(arguments);return Vt(e[0]=this,Jt(this,t,e))}});var Gr=E.prototype;Gr[S]=!0,Gr[k]=!0,Ur(j,{get:function(t,e){return this.has(t)?t:e},includes:function(t){return this.has(t)},keySeq:function(){return this.valueSeq()}});var Zr=j.prototype;function $r(t,n,i,o,u,e){return te(t.size),t.__iterate(function(t,e,r){i=u?(u=!1,t):n.call(o,i,t,e,r)},e),i}function tn(t,e){return e}function en(t,e){return[e,t]}function rn(t){return function(){return!t.apply(this,arguments)}}function nn(t){return function(){return-t.apply(this,arguments)}}function on(){return Zt(
arguments)}function un(t,e){return t<e?1:e<t?-1:0}function sn(t,e){return t^e+2654435769+(t<<6)+(t>>2)|0}Zr.has=Xr.includes,Zr.contains=Zr.includes,Zr.keys=Zr.values,Ur(G,Fr),Ur(Z,Gr),Ur($,Zr);var an=function(t){function e(r){return null==r?_n():kr(r)?r:_n().withMutations(function(e){var t=j(r);te(t.size),t.forEach(function(t){return e.add(t)})})}return t&&(e.__proto__=t),((e.prototype=Object.create(t&&t.prototype)).constructor=e).of=function(){return this(arguments)},e.fromKeys=function(t){return this(O(t).keySeq())},e.prototype.toString=function(){return this.__toString("OrderedSet {","}")},e}(Kr);an.isOrderedSet=kr;var cn,fn=an.prototype;function hn(t,e){var r=Object.create(fn);return r.size=t?t.size:0,r._map=t,r.__ownerID=e,r}function _n(){return cn=cn||hn(zr())}fn[k]=!0,fn.zip=Gr.zip,fn.zipWith=Gr.zipWith,fn.zipAll=Gr.zipAll,fn.__empty=_n,fn.__make=hn;Zr={LeftThenRight:-1,RightThenLeft:1};Gr=function(u,s){var a;!function(t){if(x(t))throw Error("Can not call `Record` with an immutable Record as default values. Use a plain javascript object instead.");if(A(t))throw Error("Can not call `Record` with an immutable Collection as default values. Use a plain javascript object instead.");if(null===t||"object"!=typeof t)throw Error("Can not call `Record` with a non-object as default values. Use a plain javascript object instead.")}(u);var c=function(t){var n=this;if(t instanceof c)return t;if(!(this instanceof c))return new c(t);if(!a){a=!0;var e=Object.keys(u),r=f._indices={};f._name=s,f._keys=e,f._defaultValues=u;for(var i=0;i<e.length;i++){var o=e[i];r[o]=i,f[o]?"object"==typeof console&&console.warn&&console.warn("Cannot define "+vn(this)+' with property "'+o+'" since that property name is part of the Record API.'):function(t,e){try{Object.defineProperty(t,e,{get:function(){return this.get(e)},set:function(t){$t(this.__ownerID,"Cannot set on an immutable record."),this.set(e,t)}})}catch(t){}}(f,o)}}return this.__ownerID=void 0,this._values=ur().withMutations(function(r){r.setSize(n._keys.length),O(t).forEach(
function(t,e){r.set(n._indices[e],t===n._defaultValues[e]?void 0:t)})}),this},f=c.prototype=Object.create(pn);return f.constructor=c,s&&(c.displayName=s),c};Gr.prototype.toString=function(){for(var t,e=vn(this)+" { ",r=this._keys,n=0,i=r.length;n!==i;n++)e+=(n?", ":"")+(t=r[n])+": "+oe(this.get(t));return e+" }"},Gr.prototype.equals=function(t){return this===t||x(t)&&yn(this).equals(yn(t))},Gr.prototype.hashCode=function(){return yn(this).hashCode()},Gr.prototype.has=function(t){return this._indices.hasOwnProperty(t)},Gr.prototype.get=function(t,e){if(!this.has(t))return e;e=this._values.get(this._indices[t]);return void 0===e?this._defaultValues[t]:e},Gr.prototype.set=function(t,e){if(this.has(t)){e=this._values.set(this._indices[t],e===this._defaultValues[t]?void 0:e);if(e!==this._values&&!this.__ownerID)return ln(this,e)}return this},Gr.prototype.remove=function(t){return this.set(t)},Gr.prototype.clear=function(){var t=this._values.clear().setSize(this._keys.length);return this.__ownerID?this:ln(this,t)},Gr.prototype.wasAltered=function(){return this._values.wasAltered()},Gr.prototype.toSeq=function(){return yn(this)},Gr.prototype.toJS=function(){return Tr(this)},Gr.prototype.entries=function(){return this.__iterator(K)},Gr.prototype.__iterator=function(t,e){return yn(this).__iterator(t,e)},Gr.prototype.__iterate=function(t,e){return yn(this).__iterate(t,e)},Gr.prototype.__ensureOwner=function(t){if(t===this.__ownerID)return this;var e=this._values.__ensureOwner(t);return t?ln(this,e,t):(this.__ownerID=t,this._values=e,this)},Gr.isRecord=x,Gr.getDescriptiveName=vn;var pn=Gr.prototype;function ln(t,e,r){t=Object.create(Object.getPrototypeOf(t));return t._values=e,t.__ownerID=r,t}function vn(t){return t.constructor.displayName||t.constructor.name||"Record"}function yn(e){return ot(e._keys.map(function(t){return[t,e.get(t)]}))}pn[D]=!0,pn[e]=pn.remove,pn.deleteIn=pn.removeIn=ve,pn.getIn=Vr,pn.hasIn=Xr.hasIn,pn.merge=me,pn.mergeWith=we,pn.mergeIn=De,pn.mergeDeep=qe,pn.mergeDeepWith=Me,pn.mergeDeepIn=xe,
pn.setIn=pe,pn.update=de,pn.updateIn=ge,pn.withMutations=Ae,pn.asMutable=ke,pn.asImmutable=Re,pn[B]=pn.entries,pn.toJSON=pn.toObject=Xr.toObject,pn.inspect=pn.toSource=function(){return""+this};var dn,e=function(t){function n(t,e){if(!(this instanceof n))return new n(t,e);if(this._value=t,this.size=void 0===e?1/0:Math.max(0,e),0===this.size){if(dn)return dn;dn=this}}return t&&(n.__proto__=t),((n.prototype=Object.create(t&&t.prototype)).constructor=n).prototype.toString=function(){return 0===this.size?"Repeat []":"Repeat [ "+this._value+" "+this.size+" times ]"},n.prototype.get=function(t,e){return this.has(t)?this._value:e},n.prototype.includes=function(t){return _t(this._value,t)},n.prototype.slice=function(t,e){var r=this.size;return p(t,e,r)?this:new n(this._value,w(e,r)-y(t,r))},n.prototype.reverse=function(){return this},n.prototype.indexOf=function(t){return _t(this._value,t)?0:-1},n.prototype.lastIndexOf=function(t){return _t(this._value,t)?this.size:-1},n.prototype.__iterate=function(t,e){for(var r=this.size,n=0;n!==r&&!1!==t(this._value,e?r-++n:n++,this););return n},n.prototype.__iterator=function(t,e){var r=this,n=this.size,i=0;return new P(function(){return i===n?N():W(t,e?n-++i:i++,r._value)})},n.prototype.equals=function(t){return t instanceof n?_t(this._value,t._value):Rr(this,t)},n}(Z);function gn(t,e){return function r(n,i,o,t,u,e){if("string"!=typeof o&&!A(o)&&(X(o)||H(o)||ne(o))){if(~n.indexOf(o))throw new TypeError("Cannot convert circular structure to Immutable");n.push(o),u&&""!==t&&u.push(t);var t=i.call(e,t,F(o).map(function(t,e){return r(n,i,t,e,u,o)}),u&&u.slice());return n.pop(),u&&u.pop(),t}return o}([],e||mn,t,"",e&&2<e.length?[]:void 0,{"":t})}function mn(t,e){return z(e)?e.toList():a(e)?e.toMap():e.toSet()}B={version:"4.3.7",Collection:I,Iterable:I,Seq:F,Map:Te,OrderedMap:wr,List:ur,Stack:Er,Set:Kr,OrderedSet:an,PairSorting:Zr,Record:Gr,Range:Hr,Repeat:e,is:_t,fromJS:gn,hash:yt,isImmutable:A,isCollection:f,isKeyed:a,isIndexed:z,isAssociative:b,isOrdered:R,isValueObject:ht,
isPlainObject:ne,isSeq:M,isList:or,isMap:ct,isOrderedMap:ft,isStack:Or,isSet:Ar,isOrderedSet:kr,isRecord:x,get:se,getIn:Jr,has:ue,hasIn:Yr,merge:ze,mergeDeep:Ie,mergeWith:be,mergeDeepWith:Oe,remove:ce,removeIn:le,set:fe,setIn:_e,update:ye,updateIn:he},Xr=I;t.Collection=I,t.Iterable=Xr,t.List=ur,t.Map=Te,t.OrderedMap=wr,t.OrderedSet=an,t.PairSorting=Zr,t.Range=Hr,t.Record=Gr,t.Repeat=e,t.Seq=F,t.Set=Kr,t.Stack=Er,t.default=B,t.fromJS=gn,t.get=se,t.getIn=Jr,t.has=ue,t.hasIn=Yr,t.hash=yt,t.is=_t,t.isAssociative=b,t.isCollection=f,t.isImmutable=A,t.isIndexed=z,t.isKeyed=a,t.isList=or,t.isMap=ct,t.isOrdered=R,t.isOrderedMap=ft,t.isOrderedSet=kr,t.isPlainObject=ne,t.isRecord=x,t.isSeq=M,t.isSet=Ar,t.isStack=Or,t.isValueObject=ht,t.merge=ze,t.mergeDeep=Ie,t.mergeDeepWith=Oe,t.mergeWith=be,t.remove=ce,t.removeIn=le,t.set=fe,t.setIn=_e,t.update=ye,t.updateIn=he,t.version="4.3.7",Object.defineProperty(t,"__esModule",{value:!0})});