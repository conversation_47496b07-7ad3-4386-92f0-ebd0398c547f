#!/bin/bash

echo "🚀 Setting up TypeScript Shop Software System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

print_status "Node.js version check passed: $(node -v)"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_warning "Docker is not installed. You'll need to set up PostgreSQL and Redis manually."
else
    print_status "Docker is available"
fi

# Remove any existing JavaScript files
print_info "Cleaning up JavaScript files..."
find backend -name "*.js" -not -path "*/node_modules/*" -not -path "*/dist/*" -delete 2>/dev/null || true
print_status "JavaScript files cleaned up"

# Create environment files
print_info "Creating environment files..."

# Root .env file
cat > .env << EOF
# Database
DATABASE_URL="postgresql://shop_user:shop_password@localhost:5432/shop_db"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="15m"
REFRESH_TOKEN_EXPIRES_IN="7d"

# Services
AUTH_SERVICE_URL="http://localhost:3001"
INVENTORY_SERVICE_URL="http://localhost:3002"
SALES_SERVICE_URL="http://localhost:3003"
ORDER_SERVICE_URL="http://localhost:3004"
CUSTOMER_SERVICE_URL="http://localhost:3005"
REPORTING_SERVICE_URL="http://localhost:3006"
NOTIFICATION_SERVICE_URL="http://localhost:3007"

# CORS
ALLOWED_ORIGINS="http://localhost:4200,http://localhost:8100"

# Email (optional)
SMTP_HOST="localhost"
SMTP_PORT="587"
SMTP_USER=""
SMTP_PASS=""
FROM_EMAIL="<EMAIL>"

# Store Info
STORE_NAME="Shop Software Store"
STORE_ADDRESS="123 Main St, City, State 12345"
STORE_PHONE="(*************"
STORE_EMAIL="<EMAIL>"
STORE_URL="http://localhost:4200"

# Push Notifications
VAPID_SUBJECT="mailto:<EMAIL>"
VAPID_PUBLIC_KEY=""
VAPID_PRIVATE_KEY=""

# Environment
NODE_ENV="development"
EOF

print_status "Environment file created"

# Copy environment to all backend services
for service in backend/shared backend/api-gateway backend/auth-service backend/inventory-service backend/sales-service backend/order-service backend/customer-service backend/reporting-service backend/notification-service; do
    if [ -d "$service" ]; then
        cp .env "$service/.env"
        print_status "Environment copied to $service"
    fi
done

# Frontend environment
mkdir -p frontend/src/environments
cat > frontend/src/environments/environment.ts << EOF
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000',
  wsUrl: 'ws://localhost:3000',
};
EOF

cat > frontend/src/environments/environment.prod.ts << EOF
export const environment = {
  production: true,
  apiUrl: '/api',
  wsUrl: 'wss://your-domain.com',
};
EOF

print_status "Frontend environment files created"

# Start database services
if command -v docker &> /dev/null; then
    print_info "Starting database services..."
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    print_info "Waiting for database to be ready..."
    sleep 15
    print_status "Database services started"
fi

# Install dependencies
print_info "Installing dependencies..."

# Install shared dependencies first
cd backend/shared
npm install
print_status "Shared dependencies installed"
cd ../..

# Install all backend service dependencies
for service in api-gateway auth-service inventory-service sales-service order-service customer-service reporting-service notification-service; do
    if [ -d "backend/$service" ]; then
        cd "backend/$service"
        npm install
        print_status "$service dependencies installed"
        cd ../..
    fi
done

# Install frontend dependencies
if [ -d "frontend" ]; then
    cd frontend
    npm install
    print_status "Frontend dependencies installed"
    cd ..
fi

# Generate Prisma client
print_info "Generating Prisma client..."
cd backend/shared
npx prisma generate
print_status "Prisma client generated"

# Run database migrations
if command -v docker &> /dev/null; then
    print_info "Running database migrations..."
    npx prisma migrate dev --name init
    print_status "Database migrations completed"
    
    # Seed database
    print_info "Seeding database..."
    npm run db:seed
    print_status "Database seeded"
fi

cd ../..

# Build TypeScript projects
print_info "Building TypeScript projects..."
npm run build:backend
print_status "Backend built successfully"

print_status "Setup completed successfully!"
echo ""
print_info "🚀 To start the development environment:"
echo "   ./start-dev.sh"
echo ""
print_info "🌐 Access points:"
echo "   Frontend: http://localhost:4200"
echo "   API Gateway: http://localhost:3000"
echo "   API Docs: http://localhost:3000/api-docs"
echo "   GraphQL: http://localhost:3000/graphql"
echo ""
print_info "🔑 Default credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   Worker: <EMAIL> / worker123"
echo ""
print_info "📚 Next steps:"
echo "   1. Run './start-dev.sh' to start all services"
echo "   2. Open http://localhost:4200 in your browser"
echo "   3. Login with admin credentials"
echo "   4. Explore the API docs at http://localhost:3000/api-docs"
