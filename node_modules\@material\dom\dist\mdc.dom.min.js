!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("@material/dom",[],t):"object"==typeof exports?exports.dom=t():(e.mdc=e.mdc||{},e.mdc.dom=t())}(this,function(){return r={},o.m=n={226:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.ponyfill=t.keyboard=t.focusTrap=t.events=void 0;var a=i(n(52));t.events=a;var s=i(n(68));t.focusTrap=s;var u=i(n(55));t.keyboard=u;var c=i(n(49));t.ponyfill=c},49:function(e,t,n){"use strict";function r(e,t){return(e.matches||e.webkitMatchesSelector||e.msMatchesSelector).call(e,t)}Object.defineProperty(t,"__esModule",{value:!0}),t.estimateScrollWidth=t.matches=t.closest=void 0,t.closest=function(e,t){if(e.closest)return e.closest(t);for(var n=e;n;){if(r(n,t))return n;n=n.parentElement}return null},t.matches=r,t.estimateScrollWidth=function(e){var t=e;if(null!==t.offsetParent)return t.scrollWidth;var n=t.cloneNode(!0);n.style.setProperty("position","absolute"),n.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(n);var r=n.scrollWidth;return document.documentElement.removeChild(n),r}},52:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.applyPassive=void 0,t.applyPassive=function(e){return void 0===e&&(e=window),!!function(e){void 0===e&&(e=window);var t=!1;try{var n={get passive(){return!(t=!0)}},r=function(){};e.document.addEventListener("test",r,n),e.document.removeEventListener("test",r,n)}catch(e){t=!1}return t}(e)&&{passive:!0}}},55:function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.isNavigationEvent=r.normalizeKey=r.KEY=void 0,r.KEY={UNKNOWN:"Unknown",BACKSPACE:"Backspace",ENTER:"Enter",SPACEBAR:"Spacebar",PAGE_UP:"PageUp",PAGE_DOWN:"PageDown",END:"End",HOME:"Home",ARROW_LEFT:"ArrowLeft",ARROW_UP:"ArrowUp",ARROW_RIGHT:"ArrowRight",ARROW_DOWN:"ArrowDown",DELETE:"Delete",ESCAPE:"Escape",TAB:"Tab"};var o=new Set;o.add(r.KEY.BACKSPACE),o.add(r.KEY.ENTER),o.add(r.KEY.SPACEBAR),o.add(r.KEY.PAGE_UP),o.add(r.KEY.PAGE_DOWN),o.add(r.KEY.END),o.add(r.KEY.HOME),o.add(r.KEY.ARROW_LEFT),o.add(r.KEY.ARROW_UP),o.add(r.KEY.ARROW_RIGHT),o.add(r.KEY.ARROW_DOWN),o.add(r.KEY.DELETE),o.add(r.KEY.ESCAPE),o.add(r.KEY.TAB);var n=8,i=13,a=32,s=33,u=34,c=35,d=36,l=37,f=38,E=39,p=40,v=46,m=27,h=9,A=new Map;A.set(n,r.KEY.BACKSPACE),A.set(i,r.KEY.ENTER),A.set(a,r.KEY.SPACEBAR),A.set(s,r.KEY.PAGE_UP),A.set(u,r.KEY.PAGE_DOWN),A.set(c,r.KEY.END),A.set(d,r.KEY.HOME),A.set(l,r.KEY.ARROW_LEFT),A.set(f,r.KEY.ARROW_UP),A.set(E,r.KEY.ARROW_RIGHT),A.set(p,r.KEY.ARROW_DOWN),A.set(v,r.KEY.DELETE),A.set(m,r.KEY.ESCAPE),A.set(h,r.KEY.TAB);var b=new Set;function y(e){var t=e.key;if(o.has(t))return t;var n=A.get(e.keyCode);return n||r.KEY.UNKNOWN}b.add(r.KEY.PAGE_UP),b.add(r.KEY.PAGE_DOWN),b.add(r.KEY.END),b.add(r.KEY.HOME),b.add(r.KEY.ARROW_LEFT),b.add(r.KEY.ARROW_UP),b.add(r.KEY.ARROW_RIGHT),b.add(r.KEY.ARROW_DOWN),r.normalizeKey=y,r.isNavigationEvent=function(e){return b.has(y(e))}},68:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FocusTrap=void 0;var i="mdc-dom-focus-sentinel",r=(o.prototype.trapFocus=function(){var e=this.getFocusableElements(this.root);if(0===e.length)throw new Error("FocusTrap: Element must have at least one focusable child.");this.elFocusedBeforeTrapFocus=document.activeElement instanceof HTMLElement?document.activeElement:null,this.wrapTabFocus(this.root),this.options.skipInitialFocus||this.focusInitialElement(e,this.options.initialFocusEl)},o.prototype.releaseFocus=function(){Array.from(this.root.querySelectorAll("."+i)).forEach(function(e){e.parentElement.removeChild(e)}),!this.options.skipRestoreFocus&&this.elFocusedBeforeTrapFocus&&this.elFocusedBeforeTrapFocus.focus()},o.prototype.wrapTabFocus=function(t){var n=this,e=this.createSentinel(),r=this.createSentinel();e.addEventListener("focus",function(){var e=n.getFocusableElements(t);0<e.length&&e[e.length-1].focus()}),r.addEventListener("focus",function(){var e=n.getFocusableElements(t);0<e.length&&e[0].focus()}),t.insertBefore(e,t.children[0]),t.appendChild(r)},o.prototype.focusInitialElement=function(e,t){var n=0;t&&(n=Math.max(e.indexOf(t),0)),e[n].focus()},o.prototype.getFocusableElements=function(e){return Array.from(e.querySelectorAll("[autofocus], [tabindex], a, input, textarea, select, button")).filter(function(e){var t="true"===e.getAttribute("aria-disabled")||null!=e.getAttribute("disabled")||null!=e.getAttribute("hidden")||"true"===e.getAttribute("aria-hidden"),n=0<=e.tabIndex&&0<e.getBoundingClientRect().width&&!e.classList.contains(i)&&!t,r=!1;if(n){var o=getComputedStyle(e);r="none"===o.display||"hidden"===o.visibility}return n&&!r})},o.prototype.createSentinel=function(){var e=document.createElement("div");return e.setAttribute("tabindex","0"),e.setAttribute("aria-hidden","true"),e.classList.add(i),e},o);function o(e,t){void 0===t&&(t={}),this.root=e,this.options=t,this.elFocusedBeforeTrapFocus=null}t.FocusTrap=r}},o.c=r,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)o.d(n,r,function(e){return t[e]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=226);function o(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,o),t.l=!0,t.exports}var n,r});