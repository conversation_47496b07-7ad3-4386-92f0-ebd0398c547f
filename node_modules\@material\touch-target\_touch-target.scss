//
// Copyright 2019 Google Inc.
//
// Permission is hereby granted, free of charge, to any person obtaining a copy
// of this software and associated documentation files (the "Software"), to deal
// in the Software without restriction, including without limitation the rights
// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
// copies of the Software, and to permit persons to whom the Software is
// furnished to do so, subject to the following conditions:
//
// The above copyright notice and this permission notice shall be included in
// all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
// THE SOFTWARE.
//

// stylelint-disable selector-class-pattern --
// Selector '.mdc-*' should only be used in this project.

@use 'sass:math';
@use '@material/base/mixins' as base-mixins;
@use '@material/feature-targeting/feature-targeting';
@use '@material/rtl/rtl';
@use '@material/theme/theme';
@use '@material/theme/keys';
@use '@material/theme/custom-properties';

$height: 48px !default;
$width: $height !default;

/// Styles applied to the component's touch target wrapper element.
@mixin wrapper($query: feature-targeting.all()) {
  $feat-structure: feature-targeting.create-target($query, structure);

  .mdc-touch-target-wrapper {
    @include feature-targeting.targets($feat-structure) {
      // Ensure that styles are only emitted once across all components that
      // have increased touch targets.
      @include base-mixins.emit-once('mdc-touch-target/wrapper') {
        // NOTE: Will change to `inline-block` in the future, but keeping as is
        // temporarily for backwards-compatibility.
        display: inline;
      }
    }
  }
}

/// Styles applied to the component's inner touch target element.
/// By default, only sets the inner element height to the minimum touch target
/// height ($mdc-touch-target-height).
/// @param {Boolean} $set-width [false] - Sets the inner element width to the
///     minimum touch target width ($mdc-touch-target-width).
/// @param $height [$mdc-touch-target-height] - Touch target height.
/// @param $width [$mdc-touch-target-width] - Touch target width.
@mixin touch-target(
  $set-width: false,
  $query: feature-targeting.all(),
  $height: $height,
  $width: $width
) {
  $feat-structure: feature-targeting.create-target($query, structure);

  @include feature-targeting.targets($feat-structure) {
    position: absolute;
    top: 50%;
    height: $height;
  }

  @if $set-width {
    @include feature-targeting.targets($feat-structure) {
      @include rtl.ignore-next-line();
      left: 50%;
      width: $width;
      @include rtl.ignore-next-line();
      transform: translate(-50%, -50%);
    }
  } @else {
    @include feature-targeting.targets($feat-structure) {
      left: 0;
      right: 0;
      transform: translateY(-50%);
    }
  }
}

/// Applies margin to the component with the increased touch target,
/// to compensate for the touch target.
@mixin margin(
  $component-height,
  $component-width: null,
  $touch-target-height: $height,
  $touch-target-width: $width,
  $query: feature-targeting.all()
) {
  $feat-structure: feature-targeting.create-target($query, structure);

  @include feature-targeting.targets($feat-structure) {
    @if keys.is-key($touch-target-height) or
      keys.is-key($component-height) or
      custom-properties.is-custom-prop($touch-target-height) or
      custom-properties.is-custom-prop($component-height) or
      custom-properties.is-custom-prop-string($touch-target-height) or
      custom-properties.is-custom-prop-string($component-height)
    {
      // Custom properties
      @include theme.property(
        margin-top,
        'max((touch-target-height - component-height) / 2, 0px)',
        $replace: (
          component-height: $component-height,
          touch-target-height: $touch-target-height
        )
      );
      @include theme.property(
        margin-bottom,
        'max((touch-target-height - component-height) / 2, 0px)',
        $replace: (
          component-height: $component-height,
          touch-target-height: $touch-target-height
        )
      );
    } @else {
      // Static values
      $vertical-margin-value: math.div(
        $touch-target-height - $component-height,
        2
      );
      margin-top: $vertical-margin-value;
      margin-bottom: $vertical-margin-value;
    }
  }

  @if $component-width {
    @include feature-targeting.targets($feat-structure) {
      @if keys.is-key($touch-target-width) or
        keys.is-key($component-width) or
        custom-properties.is-custom-prop($touch-target-width) or
        custom-properties.is-custom-prop($component-width) or
        custom-properties.is-custom-prop-string($touch-target-width) or
        custom-properties.is-custom-prop-string($component-width)
      {
        // Custom properties
        @include theme.property(
          margin-right,
          'max((touch-target-width - component-width) / 2, 0px)',
          $replace: (
            component-width: $component-width,
            touch-target-width: $touch-target-width
          )
        );
        @include theme.property(
          margin-left,
          'max((touch-target-width - component-width) / 2), 0px',
          $replace: (
            component-width: $component-width,
            touch-target-width: $touch-target-width
          )
        );
      } @else {
        // Static values
        $horizontal-margin-value: math.div(
          $touch-target-width - $component-width,
          2
        );
        margin-right: $horizontal-margin-value;
        margin-left: $horizontal-margin-value;
      }
    }
  }
}
